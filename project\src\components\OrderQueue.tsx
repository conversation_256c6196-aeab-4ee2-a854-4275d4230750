import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Clock, CheckCircle, AlertTriangle, Timer, Users, TrendingUp, BarChart3, RefreshCw } from 'lucide-react';

interface QueueOrder {
  id: string;
  order_number: number;
  table_number?: number;
  order_type: 'dine_in' | 'takeout' | 'delivery';
  customer_name?: string;
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'cancelled';
  priority: 'normal' | 'high' | 'urgent';
  order_time: string;
  estimated_completion: string;
  actual_completion?: string;
  prep_time_minutes: number;
  items_count: number;
  total_amount: number;
  assigned_staff?: string;
  queue_position: number;
}

const OrderQueue: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [orders, setOrders] = useState<QueueOrder[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'preparing' | 'ready'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [queueStats, setQueueStats] = useState({
    averageWaitTime: 0,
    totalOrders: 0,
    completedToday: 0,
    averagePrepTime: 0
  });

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Load order queue
  useEffect(() => {
    const loadOrderQueue = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📋 Loading order queue...');
        
        const response = await apiCall('/api/orders/queue');
        if (response.ok) {
          const data = await response.json();
          setOrders(data.orders || []);
          setQueueStats(data.stats || queueStats);
          console.log('✅ Order queue loaded:', data.orders?.length || 0);
        }
      } catch (error) {
        console.error('❌ Error loading order queue:', error);
        setError('Failed to load order queue. Using mock data.');
        
        // Fallback to mock data
        const mockOrders: QueueOrder[] = [
          {
            id: 'order_1',
            order_number: 101,
            table_number: 5,
            order_type: 'dine_in',
            status: 'preparing',
            priority: 'normal',
            order_time: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            estimated_completion: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
            prep_time_minutes: 20,
            items_count: 3,
            total_amount: 45.50,
            assigned_staff: 'Chef Mike',
            queue_position: 1
          },
          {
            id: 'order_2',
            order_number: 102,
            order_type: 'takeout',
            customer_name: 'John Smith',
            status: 'pending',
            priority: 'high',
            order_time: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            estimated_completion: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
            prep_time_minutes: 18,
            items_count: 2,
            total_amount: 28.75,
            queue_position: 2
          },
          {
            id: 'order_3',
            order_number: 103,
            table_number: 12,
            order_type: 'dine_in',
            status: 'ready',
            priority: 'urgent',
            order_time: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
            estimated_completion: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            actual_completion: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            prep_time_minutes: 23,
            items_count: 4,
            total_amount: 67.25,
            assigned_staff: 'Chef Sarah',
            queue_position: 3
          }
        ];
        setOrders(mockOrders);
        setQueueStats({
          averageWaitTime: 18,
          totalOrders: 45,
          completedToday: 42,
          averagePrepTime: 16
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrderQueue();
    
    // Refresh queue every 30 seconds
    const interval = setInterval(loadOrderQueue, 30000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const updateOrderStatus = async (orderId: string, newStatus: QueueOrder['status']) => {
    try {
      console.log(`📋 Updating order ${orderId} status to: ${newStatus}`);
      
      const response = await apiCall(`/api/orders/${orderId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status: newStatus })
      });
      
      if (response.ok) {
        setOrders(prev => prev.map(order => 
          order.id === orderId 
            ? { 
                ...order, 
                status: newStatus,
                actual_completion: newStatus === 'ready' || newStatus === 'served' 
                  ? new Date().toISOString() 
                  : order.actual_completion
              } 
            : order
        ));
        console.log('✅ Order status updated successfully');
      }
    } catch (error) {
      console.error('❌ Error updating order status:', error);
      alert('Failed to update order status. Please try again.');
    }
  };

  const updateOrderPriority = async (orderId: string, newPriority: QueueOrder['priority']) => {
    try {
      console.log(`📋 Updating order ${orderId} priority to: ${newPriority}`);
      
      const response = await apiCall(`/api/orders/${orderId}/priority`, {
        method: 'PUT',
        body: JSON.stringify({ priority: newPriority })
      });
      
      if (response.ok) {
        setOrders(prev => prev.map(order => 
          order.id === orderId ? { ...order, priority: newPriority } : order
        ));
        console.log('✅ Order priority updated successfully');
      }
    } catch (error) {
      console.error('❌ Error updating order priority:', error);
    }
  };

  const getTimeElapsed = (orderTime: string): string => {
    const orderDate = new Date(orderTime);
    const diffMs = currentTime.getTime() - orderDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return '< 1m';
    if (diffMins < 60) return `${diffMins}m`;
    const diffHours = Math.floor(diffMins / 60);
    return `${diffHours}h ${diffMins % 60}m`;
  };

  const getEstimatedWaitTime = (estimatedCompletion: string): string => {
    const completionDate = new Date(estimatedCompletion);
    const diffMs = completionDate.getTime() - currentTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins <= 0) return 'Ready';
    if (diffMins < 60) return `${diffMins}m left`;
    const diffHours = Math.floor(diffMins / 60);
    return `${diffHours}h ${diffMins % 60}m left`;
  };

  const getStatusColor = (status: QueueOrder['status']) => {
    switch (status) {
      case 'pending': return 'bg-blue-100 text-blue-800';
      case 'preparing': return 'bg-yellow-100 text-yellow-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'served': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: QueueOrder['priority']) => {
    switch (priority) {
      case 'urgent': return 'border-l-4 border-red-500';
      case 'high': return 'border-l-4 border-orange-500';
      case 'normal': return 'border-l-4 border-gray-300';
      default: return 'border-l-4 border-gray-300';
    }
  };

  const filteredOrders = orders.filter(order => {
    if (selectedFilter === 'all') return order.status !== 'served' && order.status !== 'cancelled';
    return order.status === selectedFilter;
  });

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading order queue...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Order Queue & Time Tracking</h2>
            <p className="text-sm text-gray-500">
              {currentTime.toLocaleTimeString()} • {filteredOrders.length} active orders
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Active</option>
              <option value="pending">Pending</option>
              <option value="preparing">Preparing</option>
              <option value="ready">Ready</option>
            </select>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Queue"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Avg Wait Time</p>
                <p className="text-xl font-bold text-gray-900">{queueStats.averageWaitTime}m</p>
              </div>
              <Clock className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Orders</p>
                <p className="text-xl font-bold text-gray-900">{queueStats.totalOrders}</p>
              </div>
              <BarChart3 className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Completed Today</p>
                <p className="text-xl font-bold text-gray-900">{queueStats.completedToday}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-purple-500" />
            </div>
          </div>
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Avg Prep Time</p>
                <p className="text-xl font-bold text-gray-900">{queueStats.averagePrepTime}m</p>
              </div>
              <Timer className="h-6 w-6 text-orange-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Order Queue */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 space-y-3">
          {filteredOrders.map((order) => (
            <div
              key={order.id}
              className={`bg-white border border-gray-200 rounded-lg p-4 ${getPriorityColor(order.priority)}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-bold text-lg">#{order.order_number}</span>
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(order.status)}`}>
                        {order.status.toUpperCase()}
                      </span>
                      {order.priority !== 'normal' && (
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          order.priority === 'urgent' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                        }`}>
                          {order.priority.toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {order.order_type === 'dine_in' && order.table_number && `Table ${order.table_number}`}
                      {order.order_type === 'takeout' && `Takeout${order.customer_name ? ` - ${order.customer_name}` : ''}`}
                      {order.order_type === 'delivery' && `Delivery${order.customer_name ? ` - ${order.customer_name}` : ''}`}
                      {order.assigned_staff && ` • Assigned to ${order.assigned_staff}`}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Elapsed</div>
                    <div className="font-semibold">{getTimeElapsed(order.order_time)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Est. Completion</div>
                    <div className="font-semibold">{getEstimatedWaitTime(order.estimated_completion)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Items</div>
                    <div className="font-semibold">{order.items_count}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Total</div>
                    <div className="font-semibold">${order.total_amount.toFixed(2)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Queue #</div>
                    <div className="font-semibold">{order.queue_position}</div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  {order.status === 'pending' && (
                    <button
                      onClick={() => updateOrderStatus(order.id, 'preparing')}
                      className="bg-yellow-500 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-600 transition-colors"
                    >
                      Start
                    </button>
                  )}
                  {order.status === 'preparing' && (
                    <button
                      onClick={() => updateOrderStatus(order.id, 'ready')}
                      className="bg-green-500 text-white px-3 py-1 rounded text-sm font-medium hover:bg-green-600 transition-colors"
                    >
                      Ready
                    </button>
                  )}
                  {order.status === 'ready' && (
                    <button
                      onClick={() => updateOrderStatus(order.id, 'served')}
                      className="bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium hover:bg-gray-600 transition-colors"
                    >
                      Served
                    </button>
                  )}
                  
                  {/* Priority Controls */}
                  <select
                    value={order.priority}
                    onChange={(e) => updateOrderPriority(order.id, e.target.value as QueueOrder['priority'])}
                    className="text-xs border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="normal">Normal</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
          
          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No orders in queue</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderQueue;
