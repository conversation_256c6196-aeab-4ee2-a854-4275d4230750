import React, { useState, useEffect } from 'react';
import { 
  X, 
  Calendar, 
  Clock, 
  Users, 
  Phone, 
  Mail, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle,
  AlertCircle,
  Search,
  Filter
} from 'lucide-react';
import { Reservation, Table } from '../types';

interface ReservationManagerProps {
  isOpen: boolean;
  onClose: () => void;
  tables: Table[];
}

const ReservationManager: React.FC<ReservationManagerProps> = ({
  isOpen,
  onClose,
  tables
}) => {
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingReservation, setEditingReservation] = useState<Reservation | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    tableId: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    partySize: 2,
    reservationTime: '',
    duration: 120,
    specialRequests: '',
    notes: ''
  });

  useEffect(() => {
    if (isOpen) {
      fetchReservations();
    }
  }, [isOpen, selectedDate]);

  const fetchReservations = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/reservations?date=${selectedDate}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setReservations(data);
    } catch (error) {
      console.error('Error fetching reservations:', error);
    } finally {
      setLoading(false);
    }
  };

  const createReservation = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/reservations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          specialRequests: formData.specialRequests ? formData.specialRequests.split(',').map(s => s.trim()) : []
        })
      });
      
      if (response.ok) {
        fetchReservations();
        setShowCreateForm(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error creating reservation:', error);
    }
  };

  const updateReservation = async (id: string, updates: Partial<Reservation>) => {
    try {
      const token = localStorage.getItem('authToken');
      await fetch(`http://localhost:4000/api/reservations/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });
      fetchReservations();
    } catch (error) {
      console.error('Error updating reservation:', error);
    }
  };

  const cancelReservation = async (id: string) => {
    try {
      const token = localStorage.getItem('authToken');
      await fetch(`http://localhost:4000/api/reservations/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      fetchReservations();
    } catch (error) {
      console.error('Error cancelling reservation:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      tableId: '',
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      partySize: 2,
      reservationTime: '',
      duration: 120,
      specialRequests: '',
      notes: ''
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'text-green-500 bg-green-100';
      case 'seated': return 'text-blue-500 bg-blue-100';
      case 'completed': return 'text-gray-500 bg-gray-100';
      case 'cancelled': return 'text-red-500 bg-red-100';
      case 'no-show': return 'text-orange-500 bg-orange-100';
      default: return 'text-gray-500 bg-gray-100';
    }
  };

  const getAvailableTables = (partySize: number, reservationTime: string) => {
    return tables.filter(table => 
      table.seats >= partySize && 
      table.status === 'available'
    );
  };

  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         reservation.customerPhone.includes(searchTerm);
    const matchesStatus = filterStatus === 'all' || reservation.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Calendar className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Reservation Manager</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="bg-gray-700 text-white pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="confirmed">Confirmed</option>
                <option value="seated">Seated</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
              </select>
            </div>

            <button
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>New Reservation</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-400 mt-2">Loading reservations...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
              {filteredReservations.map((reservation) => {
                const table = tables.find(t => t.id === reservation.tableId);
                return (
                  <div key={reservation.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-white font-medium">{reservation.customerName}</h3>
                        <p className="text-gray-400 text-sm">Table {table?.number || 'TBD'}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(reservation.status)}`}>
                        {reservation.status}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2 text-gray-300">
                        <Clock className="h-4 w-4" />
                        <span>{new Date(reservation.reservationTime).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-gray-300">
                        <Users className="h-4 w-4" />
                        <span>{reservation.partySize} guests</span>
                      </div>
                      <div className="flex items-center space-x-2 text-gray-300">
                        <Phone className="h-4 w-4" />
                        <span>{reservation.customerPhone}</span>
                      </div>
                      {reservation.specialRequests && reservation.specialRequests.length > 0 && (
                        <div className="text-yellow-400 text-xs">
                          {reservation.specialRequests.join(', ')}
                        </div>
                      )}
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <div className="flex space-x-2">
                        {reservation.status === 'confirmed' && (
                          <button
                            onClick={() => updateReservation(reservation.id, { status: 'seated' })}
                            className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700"
                          >
                            Seat
                          </button>
                        )}
                        {reservation.status === 'seated' && (
                          <button
                            onClick={() => updateReservation(reservation.id, { status: 'completed' })}
                            className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                          >
                            Complete
                          </button>
                        )}
                      </div>
                      
                      <div className="flex space-x-1">
                        <button
                          onClick={() => setEditingReservation(reservation)}
                          className="text-gray-400 hover:text-white"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => cancelReservation(reservation.id)}
                          className="text-gray-400 hover:text-red-400"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {filteredReservations.length === 0 && !loading && (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">No reservations found for the selected criteria.</p>
            </div>
          )}
        </div>

        {/* Create/Edit Form Modal */}
        {(showCreateForm || editingReservation) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
            <div className="bg-gray-800 rounded-lg w-full max-w-md">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {editingReservation ? 'Edit Reservation' : 'New Reservation'}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">
                      Customer Name
                    </label>
                    <input
                      type="text"
                      value={formData.customerName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                      className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter customer name"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={formData.customerPhone}
                        onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
                        className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Phone number"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">
                        Party Size
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="20"
                        value={formData.partySize}
                        onChange={(e) => setFormData(prev => ({ ...prev, partySize: parseInt(e.target.value) }))}
                        className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">
                      Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      value={formData.reservationTime}
                      onChange={(e) => setFormData(prev => ({ ...prev, reservationTime: e.target.value }))}
                      className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">
                      Preferred Table
                    </label>
                    <select
                      value={formData.tableId}
                      onChange={(e) => setFormData(prev => ({ ...prev, tableId: e.target.value }))}
                      className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Auto-assign best table</option>
                      {getAvailableTables(formData.partySize, formData.reservationTime).map(table => (
                        <option key={table.id} value={table.id}>
                          Table {table.number} ({table.seats} seats)
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">
                      Special Requests
                    </label>
                    <input
                      type="text"
                      value={formData.specialRequests}
                      onChange={(e) => setFormData(prev => ({ ...prev, specialRequests: e.target.value }))}
                      className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Birthday, window seat, etc."
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      setShowCreateForm(false);
                      setEditingReservation(null);
                      resetForm();
                    }}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={createReservation}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    {editingReservation ? 'Update' : 'Create'} Reservation
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReservationManager;
