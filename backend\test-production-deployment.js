// Production Deployment Testing Script
// Phase 7: Comprehensive production testing and validation

const fetch = require('node-fetch');
const { Pool } = require('pg');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:4000',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000
};

// PostgreSQL connection
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class ProductionDeploymentTester {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0,
      startTime: new Date(),
      tests: []
    };
    
    this.authToken = null;
  }

  // =====================================================
  // TEST ORCHESTRATION
  // =====================================================

  async runAllTests() {
    console.log('🧪 Production Deployment Testing Suite');
    console.log('=' .repeat(60));
    console.log(`🕒 Started at: ${this.testResults.startTime.toISOString()}`);
    console.log('');

    try {
      // Phase 1: Infrastructure Tests
      await this.runInfrastructureTests();
      
      // Phase 2: Authentication Tests
      await this.runAuthenticationTests();
      
      // Phase 3: Core API Tests
      await this.runCoreAPITests();
      
      // Phase 4: AI Services Tests
      await this.runAIServicesTests();
      
      // Phase 5: Global Services Tests
      await this.runGlobalServicesTests();
      
      // Phase 6: Payment Processing Tests
      await this.runPaymentProcessingTests();
      
      // Phase 7: Performance Tests
      await this.runPerformanceTests();
      
      // Phase 8: Security Tests
      await this.runSecurityTests();
      
      // Phase 9: Monitoring Tests
      await this.runMonitoringTests();
      
      // Phase 10: Integration Tests
      await this.runIntegrationTests();
      
      // Generate final report
      this.generateTestReport();
      
      return this.testResults;

    } catch (error) {
      console.error('❌ Test suite execution failed:', error);
      this.testResults.criticalError = error.message;
      return this.testResults;
    }
  }

  // =====================================================
  // INFRASTRUCTURE TESTS
  // =====================================================

  async runInfrastructureTests() {
    console.log('🏗️ Phase 1: Infrastructure Tests');
    console.log('-' .repeat(40));

    // Test 1.1: Server Health
    await this.testServerHealth();
    
    // Test 1.2: Database Connectivity
    await this.testDatabaseConnectivity();
    
    // Test 1.3: Database Schema
    await this.testDatabaseSchema();
    
    // Test 1.4: Environment Configuration
    await this.testEnvironmentConfiguration();
    
    console.log('');
  }

  async testServerHealth() {
    try {
      const response = await this.makeRequest('GET', '/health');
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Server Health Check', 'PASS', `Status: ${data.status || 'healthy'}`);
      } else {
        this.logTest('Server Health Check', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Server Health Check', 'FAIL', error.message);
    }
  }

  async testDatabaseConnectivity() {
    try {
      const client = await pool.connect();
      const result = await client.query('SELECT NOW() as current_time');
      client.release();
      
      this.logTest('Database Connectivity', 'PASS', `Connected at ${result.rows[0].current_time}`);
    } catch (error) {
      this.logTest('Database Connectivity', 'FAIL', error.message);
    }
  }

  async testDatabaseSchema() {
    try {
      const client = await pool.connect();
      
      // Check critical tables exist
      const tables = [
        'tenants', 'employees', 'orders', 'order_items', 'products',
        'payment_transactions', 'ai_fraud_analysis', 'global_currencies',
        'global_exchange_rates', 'global_payment_gateways'
      ];
      
      let missingTables = [];
      
      for (const table of tables) {
        const result = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = $1
          )
        `, [table]);
        
        if (!result.rows[0].exists) {
          missingTables.push(table);
        }
      }
      
      client.release();
      
      if (missingTables.length === 0) {
        this.logTest('Database Schema', 'PASS', `All ${tables.length} critical tables exist`);
      } else {
        this.logTest('Database Schema', 'FAIL', `Missing tables: ${missingTables.join(', ')}`);
      }
    } catch (error) {
      this.logTest('Database Schema', 'FAIL', error.message);
    }
  }

  async testEnvironmentConfiguration() {
    const requiredVars = ['NODE_ENV', 'JWT_SECRET', 'DB_HOST', 'DB_NAME'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      this.logTest('Environment Configuration', 'PASS', 'All required variables set');
    } else {
      this.logTest('Environment Configuration', 'FAIL', `Missing: ${missingVars.join(', ')}`);
    }
  }

  // =====================================================
  // AUTHENTICATION TESTS
  // =====================================================

  async runAuthenticationTests() {
    console.log('🔐 Phase 2: Authentication Tests');
    console.log('-' .repeat(40));

    // Test 2.1: Login Functionality
    await this.testLogin();
    
    // Test 2.2: JWT Token Validation
    await this.testJWTValidation();
    
    // Test 2.3: Protected Route Access
    await this.testProtectedRoutes();
    
    console.log('');
  }

  async testLogin() {
    try {
      const response = await this.makeRequest('POST', '/api/auth/login', {
        pin: '888888',
        tenant_slug: 'demo-restaurant'
      });
      
      if (response.ok) {
        const data = await response.json();
        this.authToken = data.token;
        this.logTest('Login Functionality', 'PASS', 'Authentication successful');
      } else {
        this.logTest('Login Functionality', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Login Functionality', 'FAIL', error.message);
    }
  }

  async testJWTValidation() {
    if (!this.authToken) {
      this.logTest('JWT Token Validation', 'SKIP', 'No auth token available');
      return;
    }

    try {
      const response = await this.makeRequest('GET', '/api/auth/verify', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        this.logTest('JWT Token Validation', 'PASS', 'Token validation successful');
      } else {
        this.logTest('JWT Token Validation', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('JWT Token Validation', 'FAIL', error.message);
    }
  }

  async testProtectedRoutes() {
    const protectedRoutes = [
      '/api/orders',
      '/api/products',
      '/api/employees',
      '/api/admin/tenants'
    ];

    let passedRoutes = 0;

    for (const route of protectedRoutes) {
      try {
        const response = await this.makeRequest('GET', route, null, {
          'Authorization': `Bearer ${this.authToken}`
        });
        
        if (response.ok || response.status === 404) {
          passedRoutes++;
        }
      } catch (error) {
        // Route might not exist, which is acceptable
      }
    }

    if (passedRoutes >= protectedRoutes.length * 0.8) {
      this.logTest('Protected Routes Access', 'PASS', `${passedRoutes}/${protectedRoutes.length} routes accessible`);
    } else {
      this.logTest('Protected Routes Access', 'WARN', `Only ${passedRoutes}/${protectedRoutes.length} routes accessible`);
    }
  }

  // =====================================================
  // CORE API TESTS
  // =====================================================

  async runCoreAPITests() {
    console.log('🔧 Phase 3: Core API Tests');
    console.log('-' .repeat(40));

    if (!this.authToken) {
      this.logTest('Core API Tests', 'SKIP', 'No authentication token');
      return;
    }

    // Test 3.1: Orders API
    await this.testOrdersAPI();
    
    // Test 3.2: Products API
    await this.testProductsAPI();
    
    // Test 3.3: Employees API
    await this.testEmployeesAPI();
    
    console.log('');
  }

  async testOrdersAPI() {
    try {
      const response = await this.makeRequest('GET', '/api/orders', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Orders API', 'PASS', `Retrieved ${data.length || 0} orders`);
      } else {
        this.logTest('Orders API', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Orders API', 'FAIL', error.message);
    }
  }

  async testProductsAPI() {
    try {
      const response = await this.makeRequest('GET', '/api/products', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Products API', 'PASS', `Retrieved ${data.length || 0} products`);
      } else {
        this.logTest('Products API', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Products API', 'FAIL', error.message);
    }
  }

  async testEmployeesAPI() {
    try {
      const response = await this.makeRequest('GET', '/api/employees', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Employees API', 'PASS', `Retrieved ${data.length || 0} employees`);
      } else {
        this.logTest('Employees API', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Employees API', 'FAIL', error.message);
    }
  }

  // =====================================================
  // AI SERVICES TESTS
  // =====================================================

  async runAIServicesTests() {
    console.log('🤖 Phase 4: AI Services Tests');
    console.log('-' .repeat(40));

    if (!this.authToken) {
      this.logTest('AI Services Tests', 'SKIP', 'No authentication token');
      return;
    }

    // Test 4.1: Fraud Detection
    await this.testFraudDetection();
    
    // Test 4.2: Sales Forecasting
    await this.testSalesForecasting();
    
    // Test 4.3: Automation Workflows
    await this.testAutomationWorkflows();
    
    console.log('');
  }

  async testFraudDetection() {
    try {
      const response = await this.makeRequest('POST', '/api/ai/fraud/analyze-transaction', {
        transaction_id: `test_${Date.now()}`,
        total_amount: 150.00,
        payment_method_name: 'credit_card',
        customer_info: {
          email: '<EMAIL>'
        }
      }, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Fraud Detection', 'PASS', `Risk score: ${data.risk_score || 'N/A'}`);
      } else {
        this.logTest('Fraud Detection', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Fraud Detection', 'FAIL', error.message);
    }
  }

  async testSalesForecasting() {
    try {
      const response = await this.makeRequest('GET', '/api/ai/predictions/sales-forecast?timeframe=daily&days_ahead=7', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Sales Forecasting', 'PASS', `Forecast generated: ${data.forecast_period || 'N/A'}`);
      } else {
        this.logTest('Sales Forecasting', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Sales Forecasting', 'FAIL', error.message);
    }
  }

  async testAutomationWorkflows() {
    try {
      const response = await this.makeRequest('GET', '/api/ai/automation/workflows', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Automation Workflows', 'PASS', `${data.workflows?.length || 0} workflows available`);
      } else {
        this.logTest('Automation Workflows', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Automation Workflows', 'FAIL', error.message);
    }
  }

  // =====================================================
  // GLOBAL SERVICES TESTS
  // =====================================================

  async runGlobalServicesTests() {
    console.log('🌍 Phase 5: Global Services Tests');
    console.log('-' .repeat(40));

    if (!this.authToken) {
      this.logTest('Global Services Tests', 'SKIP', 'No authentication token');
      return;
    }

    // Test 5.1: Currency Support
    await this.testCurrencySupport();
    
    // Test 5.2: Exchange Rates
    await this.testExchangeRates();
    
    // Test 5.3: Currency Conversion
    await this.testCurrencyConversion();
    
    // Test 5.4: Compliance Validation
    await this.testComplianceValidation();
    
    console.log('');
  }

  async testCurrencySupport() {
    try {
      const response = await this.makeRequest('GET', '/api/global/currencies/supported', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Currency Support', 'PASS', `${data.count || 0} currencies supported`);
      } else {
        this.logTest('Currency Support', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Currency Support', 'FAIL', error.message);
    }
  }

  async testExchangeRates() {
    try {
      const response = await this.makeRequest('GET', '/api/global/currencies/exchange-rates?from_currency=USD&to_currency=EUR', null, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Exchange Rates', 'PASS', `USD/EUR rate: ${data.exchange_rate || 'N/A'}`);
      } else {
        this.logTest('Exchange Rates', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Exchange Rates', 'FAIL', error.message);
    }
  }

  async testCurrencyConversion() {
    try {
      const response = await this.makeRequest('POST', '/api/global/currencies/convert', {
        amount: 100,
        from_currency: 'USD',
        to_currency: 'EUR'
      }, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Currency Conversion', 'PASS', `Converted: ${data.conversion?.converted_amount || 'N/A'} EUR`);
      } else {
        this.logTest('Currency Conversion', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Currency Conversion', 'FAIL', error.message);
    }
  }

  async testComplianceValidation() {
    try {
      const response = await this.makeRequest('POST', '/api/global/compliance/validate', {
        region: 'EU',
        regulation_type: 'gdpr',
        data: {
          lawful_basis: 'consent',
          consent_given: true
        }
      }, {
        'Authorization': `Bearer ${this.authToken}`
      });
      
      if (response.ok) {
        const data = await response.json();
        this.logTest('Compliance Validation', 'PASS', `Compliance score: ${data.compliance_validation?.compliance_score || 'N/A'}`);
      } else {
        this.logTest('Compliance Validation', 'FAIL', `HTTP ${response.status}`);
      }
    } catch (error) {
      this.logTest('Compliance Validation', 'FAIL', error.message);
    }
  }

  // =====================================================
  // PERFORMANCE TESTS
  // =====================================================

  async runPerformanceTests() {
    console.log('⚡ Phase 7: Performance Tests');
    console.log('-' .repeat(40));

    // Test 7.1: Response Time
    await this.testResponseTime();
    
    // Test 7.2: Concurrent Requests
    await this.testConcurrentRequests();
    
    // Test 7.3: Database Performance
    await this.testDatabasePerformance();
    
    console.log('');
  }

  async testResponseTime() {
    const endpoints = [
      '/health',
      '/api/orders',
      '/api/products'
    ];

    let totalTime = 0;
    let successfulRequests = 0;

    for (const endpoint of endpoints) {
      try {
        const start = Date.now();
        const response = await this.makeRequest('GET', endpoint, null, 
          this.authToken ? { 'Authorization': `Bearer ${this.authToken}` } : {});
        const responseTime = Date.now() - start;
        
        if (response.ok || response.status === 401) {
          totalTime += responseTime;
          successfulRequests++;
        }
      } catch (error) {
        // Ignore errors for performance testing
      }
    }

    const averageTime = successfulRequests > 0 ? totalTime / successfulRequests : 0;
    
    if (averageTime < 1000) {
      this.logTest('Response Time', 'PASS', `Average: ${averageTime.toFixed(0)}ms`);
    } else if (averageTime < 3000) {
      this.logTest('Response Time', 'WARN', `Average: ${averageTime.toFixed(0)}ms (slow)`);
    } else {
      this.logTest('Response Time', 'FAIL', `Average: ${averageTime.toFixed(0)}ms (too slow)`);
    }
  }

  async testConcurrentRequests() {
    const concurrentRequests = 10;
    const promises = [];

    for (let i = 0; i < concurrentRequests; i++) {
      promises.push(this.makeRequest('GET', '/health'));
    }

    try {
      const start = Date.now();
      const results = await Promise.all(promises);
      const totalTime = Date.now() - start;
      
      const successfulRequests = results.filter(r => r.ok).length;
      
      if (successfulRequests === concurrentRequests) {
        this.logTest('Concurrent Requests', 'PASS', `${concurrentRequests} requests in ${totalTime}ms`);
      } else {
        this.logTest('Concurrent Requests', 'WARN', `${successfulRequests}/${concurrentRequests} successful`);
      }
    } catch (error) {
      this.logTest('Concurrent Requests', 'FAIL', error.message);
    }
  }

  async testDatabasePerformance() {
    try {
      const start = Date.now();
      const client = await pool.connect();
      
      await client.query('SELECT COUNT(*) FROM orders');
      await client.query('SELECT COUNT(*) FROM products');
      await client.query('SELECT COUNT(*) FROM employees');
      
      client.release();
      const queryTime = Date.now() - start;
      
      if (queryTime < 500) {
        this.logTest('Database Performance', 'PASS', `3 queries in ${queryTime}ms`);
      } else if (queryTime < 1000) {
        this.logTest('Database Performance', 'WARN', `3 queries in ${queryTime}ms (slow)`);
      } else {
        this.logTest('Database Performance', 'FAIL', `3 queries in ${queryTime}ms (too slow)`);
      }
    } catch (error) {
      this.logTest('Database Performance', 'FAIL', error.message);
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async makeRequest(method, endpoint, body = null, headers = {}) {
    const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: TEST_CONFIG.timeout
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    return await fetch(url, options);
  }

  logTest(testName, status, details = '') {
    this.testResults.total++;
    
    switch (status) {
      case 'PASS':
        this.testResults.passed++;
        console.log(`✅ ${testName}: ${details}`);
        break;
      case 'FAIL':
        this.testResults.failed++;
        console.log(`❌ ${testName}: ${details}`);
        break;
      case 'WARN':
        this.testResults.warnings++;
        console.log(`⚠️ ${testName}: ${details}`);
        break;
      case 'SKIP':
        console.log(`⏭️ ${testName}: ${details}`);
        break;
    }
    
    this.testResults.tests.push({
      name: testName,
      status: status,
      details: details,
      timestamp: new Date()
    });
  }

  generateTestReport() {
    const endTime = new Date();
    const duration = endTime - this.testResults.startTime;
    
    console.log('\n📊 Test Results Summary');
    console.log('=' .repeat(60));
    console.log(`🕒 Duration: ${Math.round(duration / 1000)}s`);
    console.log(`📈 Total Tests: ${this.testResults.total}`);
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`⚠️ Warnings: ${this.testResults.warnings}`);
    
    const successRate = this.testResults.total > 0 
      ? (this.testResults.passed / this.testResults.total) * 100 
      : 0;
    
    console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);
    
    if (successRate >= 90) {
      console.log('\n🎉 Production deployment validation: PASSED');
      console.log('✅ System is ready for production use');
    } else if (successRate >= 75) {
      console.log('\n⚠️ Production deployment validation: PASSED WITH WARNINGS');
      console.log('🔧 Some issues detected - review recommended');
    } else {
      console.log('\n❌ Production deployment validation: FAILED');
      console.log('🚨 Critical issues detected - deployment not recommended');
    }
    
    console.log('');
  }

  // Placeholder methods for remaining test phases
  async runPaymentProcessingTests() {
    console.log('💳 Phase 6: Payment Processing Tests');
    console.log('-' .repeat(40));
    this.logTest('Payment Processing Tests', 'SKIP', 'Not implemented in this version');
    console.log('');
  }

  async runSecurityTests() {
    console.log('🔒 Phase 8: Security Tests');
    console.log('-' .repeat(40));
    this.logTest('Security Tests', 'SKIP', 'Not implemented in this version');
    console.log('');
  }

  async runMonitoringTests() {
    console.log('📊 Phase 9: Monitoring Tests');
    console.log('-' .repeat(40));
    this.logTest('Monitoring Tests', 'SKIP', 'Not implemented in this version');
    console.log('');
  }

  async runIntegrationTests() {
    console.log('🔗 Phase 10: Integration Tests');
    console.log('-' .repeat(40));
    this.logTest('Integration Tests', 'SKIP', 'Not implemented in this version');
    console.log('');
  }
}

// =====================================================
// TEST EXECUTION
// =====================================================

async function main() {
  const tester = new ProductionDeploymentTester();
  const results = await tester.runAllTests();
  
  // Exit with appropriate code
  const successRate = results.total > 0 ? (results.passed / results.total) * 100 : 0;
  process.exit(successRate >= 75 ? 0 : 1);
}

// Run tests if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ProductionDeploymentTester;
