import React from 'react';
import { cn, formatNumber, formatCurrency, formatPercentage, getChangeColor, getChangeIcon } from "@/lib/utils";

interface EnhancedCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon?: React.ReactNode;
  className?: string;
  loading?: boolean;
  type?: 'number' | 'currency' | 'percentage';
}

export function EnhancedCard({ 
  title, 
  value, 
  change, 
  icon, 
  className,
  loading = false,
  type = 'number'
}: EnhancedCardProps) {
  if (loading) {
    return (
      <div className={cn("bg-white rounded-lg border border-gray-200 p-6 animate-pulse", className)}>
        <div className="flex items-center justify-between mb-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-5 w-5 bg-gray-200 rounded"></div>
        </div>
        <div className="space-y-2">
          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    );
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'string') return val;
    
    switch (type) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return `${val}%`;
      default:
        return formatNumber(val);
    }
  };

  return (
    <div className={cn(
      "bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 hover:border-gray-300",
      className
    )}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        {icon && (
          <div className="text-gray-400 hover:text-gray-600 transition-colors">
            {icon}
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="text-2xl font-bold text-gray-900">
          {formatValue(value)}
        </div>
        
        {change !== undefined && (
          <div className={cn(
            "text-xs flex items-center font-medium",
            getChangeColor(change)
          )}>
            <span className="mr-1 text-sm">
              {getChangeIcon(change)}
            </span>
            {formatPercentage(Math.abs(change))} from last month
          </div>
        )}
      </div>
    </div>
  );
}
