import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Activity,
  Zap,
  Database,
  Server,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
  Monitor
} from 'lucide-react';

interface PerformanceMetrics {
  responseTime: {
    current: number;
    average: number;
    trend: 'up' | 'down' | 'stable';
  };
  throughput: {
    requestsPerSecond: number;
    trend: 'up' | 'down' | 'stable';
  };
  errorRate: {
    current: number;
    threshold: number;
    trend: 'up' | 'down' | 'stable';
  };
  databasePerformance: {
    connectionPool: {
      active: number;
      idle: number;
      total: number;
    };
    queryTime: {
      average: number;
      slowest: number;
    };
  };
  systemResources: {
    cpu: number;
    memory: number;
    disk: number;
  };
  apiEndpoints: Array<{
    endpoint: string;
    responseTime: number;
    errorRate: number;
    requestCount: number;
    status: 'healthy' | 'warning' | 'critical';
  }>;
}

export function Phase2CPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchPerformanceMetrics = async () => {
    try {
      setLoading(true);
      
      // Simulate API call - in production, this would fetch real metrics
      const mockMetrics: PerformanceMetrics = {
        responseTime: {
          current: Math.floor(Math.random() * 50) + 10,
          average: 25,
          trend: Math.random() > 0.5 ? 'down' : 'up'
        },
        throughput: {
          requestsPerSecond: Math.floor(Math.random() * 100) + 50,
          trend: Math.random() > 0.5 ? 'up' : 'stable'
        },
        errorRate: {
          current: Math.random() * 2,
          threshold: 5,
          trend: Math.random() > 0.7 ? 'up' : 'down'
        },
        databasePerformance: {
          connectionPool: {
            active: Math.floor(Math.random() * 15) + 5,
            idle: Math.floor(Math.random() * 10) + 2,
            total: 20
          },
          queryTime: {
            average: Math.floor(Math.random() * 10) + 2,
            slowest: Math.floor(Math.random() * 50) + 10
          }
        },
        systemResources: {
          cpu: Math.floor(Math.random() * 30) + 20,
          memory: Math.floor(Math.random() * 40) + 30,
          disk: Math.floor(Math.random() * 20) + 15
        },
        apiEndpoints: [
          {
            endpoint: '/api/auth/login',
            responseTime: Math.floor(Math.random() * 20) + 5,
            errorRate: Math.random() * 1,
            requestCount: Math.floor(Math.random() * 1000) + 500,
            status: Math.random() > 0.8 ? 'warning' : 'healthy'
          },
          {
            endpoint: '/api/admin/metrics',
            responseTime: Math.floor(Math.random() * 30) + 10,
            errorRate: Math.random() * 0.5,
            requestCount: Math.floor(Math.random() * 500) + 200,
            status: 'healthy'
          },
          {
            endpoint: '/api/admin/tenants',
            responseTime: Math.floor(Math.random() * 25) + 8,
            errorRate: Math.random() * 0.3,
            requestCount: Math.floor(Math.random() * 300) + 100,
            status: 'healthy'
          },
          {
            endpoint: '/api/products',
            responseTime: Math.floor(Math.random() * 15) + 3,
            errorRate: Math.random() * 2,
            requestCount: Math.floor(Math.random() * 2000) + 1000,
            status: Math.random() > 0.9 ? 'critical' : 'healthy'
          }
        ]
      };

      setMetrics(mockMetrics);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchPerformanceMetrics, 10000); // 10 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && !metrics) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Performance Monitor</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2C Performance Monitor</h2>
          <p className="text-gray-600">Real-time system performance and optimization metrics</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
          >
            <Monitor className="h-4 w-4 mr-2" />
            Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPerformanceMetrics}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Last Update */}
      <div className="text-sm text-gray-500">
        Last updated: {lastUpdate.toLocaleTimeString()}
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.responseTime.current}ms</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(metrics?.responseTime.trend || 'stable')}
              <span className="ml-1">Avg: {metrics?.responseTime.average}ms</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Throughput</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.throughput.requestsPerSecond}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(metrics?.throughput.trend || 'stable')}
              <span className="ml-1">req/sec</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.errorRate.current.toFixed(2)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(metrics?.errorRate.trend || 'stable')}
              <span className="ml-1">Threshold: {metrics?.errorRate.threshold}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DB Connections</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.databasePerformance.connectionPool.active}/{metrics?.databasePerformance.connectionPool.total}
            </div>
            <div className="text-xs text-muted-foreground">
              {metrics?.databasePerformance.connectionPool.idle} idle
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Resources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="h-5 w-5 mr-2" />
            System Resources
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">CPU Usage</span>
                <span className="text-sm text-gray-600">{metrics?.systemResources.cpu}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics?.systemResources.cpu}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm text-gray-600">{metrics?.systemResources.memory}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics?.systemResources.memory}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Disk Usage</span>
                <span className="text-sm text-gray-600">{metrics?.systemResources.disk}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics?.systemResources.disk}%` }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Endpoints Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            API Endpoints Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics?.apiEndpoints.map((endpoint, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">{endpoint.endpoint}</code>
                    <Badge className={getStatusColor(endpoint.status)}>
                      {endpoint.status}
                    </Badge>
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    {endpoint.requestCount.toLocaleString()} requests
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{endpoint.responseTime}ms</div>
                  <div className="text-xs text-gray-500">{endpoint.errorRate.toFixed(2)}% errors</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
