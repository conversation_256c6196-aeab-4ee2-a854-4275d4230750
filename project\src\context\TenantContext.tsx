import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Tenant, CreateTenantRequest, UpdateTenantRequest, SubscriptionPlan, TenantStatus } from '../types/tenant';
import { tenantService } from '../services/tenant';

interface TenantState {
  tenants: Tenant[];
  currentTenant: Tenant | null;
  loading: boolean;
  error: string | null;
}

type TenantAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TENANTS'; payload: Tenant[] }
  | { type: 'SET_CURRENT_TENANT'; payload: Tenant | null }
  | { type: 'ADD_TENANT'; payload: Tenant }
  | { type: 'UPDATE_TENANT'; payload: Tenant }
  | { type: 'DELETE_TENANT'; payload: string };

interface TenantContextType {
  state: TenantState;
  actions: {
    loadTenants: () => Promise<void>;
    createTenant: (data: CreateTenantRequest) => Promise<boolean>;
    updateTenant: (id: string, data: UpdateTenantRequest) => Promise<boolean>;
    deleteTenant: (id: string) => Promise<boolean>;
    setCurrentTenant: (tenant: Tenant | null) => void;
    updateTenantStatus: (id: string, status: TenantStatus) => Promise<boolean>;
    updateTenantFeatures: (id: string, features: Record<string, boolean>) => Promise<boolean>;
  };
}

const initialState: TenantState = {
  tenants: [],
  currentTenant: null,
  loading: false,
  error: null,
};

function tenantReducer(state: TenantState, action: TenantAction): TenantState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_TENANTS':
      return { ...state, tenants: action.payload, loading: false };
    case 'SET_CURRENT_TENANT':
      return { ...state, currentTenant: action.payload };
    case 'ADD_TENANT':
      return { ...state, tenants: [...state.tenants, action.payload] };
    case 'UPDATE_TENANT':
      return {
        ...state,
        tenants: state.tenants.map(tenant =>
          tenant.id === action.payload.id ? action.payload : tenant
        ),
        currentTenant: state.currentTenant?.id === action.payload.id ? action.payload : state.currentTenant,
      };
    case 'DELETE_TENANT':
      return {
        ...state,
        tenants: state.tenants.filter(tenant => tenant.id !== action.payload),
        currentTenant: state.currentTenant?.id === action.payload ? null : state.currentTenant,
      };
    default:
      return state;
  }
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(tenantReducer, initialState);

  const actions = {
    loadTenants: async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        console.log('🔄 TenantContext: Loading tenants...');
        const token = localStorage.getItem('authToken');
        console.log('🔑 TenantContext: Token available:', !!token);

        if (token) {
          tenantService.setToken(token);
        }

        const tenants = await tenantService.getAllTenants();
        console.log('✅ TenantContext: Loaded tenants:', tenants.length, tenants);
        dispatch({ type: 'SET_TENANTS', payload: tenants });
      } catch (error) {
        console.error('❌ TenantContext: Failed to load tenants:', error);
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load tenants' });
      }
    },

    createTenant: async (data: CreateTenantRequest): Promise<boolean> => {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        const newTenant = await tenantService.createTenant(data);
        if (newTenant) {
          dispatch({ type: 'ADD_TENANT', payload: newTenant });
          dispatch({ type: 'SET_LOADING', payload: false });
          return true;
        }
        return false;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to create tenant' });
        return false;
      }
    },

    updateTenant: async (id: string, data: UpdateTenantRequest): Promise<boolean> => {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        const updatedTenant = await tenantService.updateTenant(id, data);
        if (updatedTenant) {
          dispatch({ type: 'UPDATE_TENANT', payload: updatedTenant });
          dispatch({ type: 'SET_LOADING', payload: false });
          return true;
        }
        return false;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to update tenant' });
        return false;
      }
    },

    deleteTenant: async (id: string): Promise<boolean> => {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        const success = await tenantService.deleteTenant(id);
        if (success) {
          dispatch({ type: 'DELETE_TENANT', payload: id });
          dispatch({ type: 'SET_LOADING', payload: false });
          return true;
        }
        return false;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to delete tenant' });
        return false;
      }
    },

    setCurrentTenant: (tenant: Tenant | null) => {
      dispatch({ type: 'SET_CURRENT_TENANT', payload: tenant });
    },

    updateTenantStatus: async (id: string, status: TenantStatus): Promise<boolean> => {
      try {
        const updatedTenant = await tenantService.updateTenantStatus(id, status);
        if (updatedTenant) {
          dispatch({ type: 'UPDATE_TENANT', payload: updatedTenant });
          return true;
        }
        return false;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to update tenant status' });
        return false;
      }
    },

    updateTenantFeatures: async (id: string, features: Record<string, boolean>): Promise<boolean> => {
      try {
        const updatedTenant = await tenantService.updateTenantFeatures(id, features);
        if (updatedTenant) {
          dispatch({ type: 'UPDATE_TENANT', payload: updatedTenant });
          return true;
        }
        return false;
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to update tenant features' });
        return false;
      }
    },
  };

  useEffect(() => {
    // Set token from localStorage when context initializes
    const token = localStorage.getItem('authToken');
    if (token) {
      tenantService.setToken(token);
    }
    actions.loadTenants();
  }, []);

  return (
    <TenantContext.Provider value={{ state, actions }}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}
