import React, { useState } from 'react';
import { Alert<PERSON>riangle, CheckCircle, X } from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  is_active: boolean;
  product_count: number;
  sort_order: number;
}

const CategoryCreationTest: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const fetchCategories = async () => {
    try {
      addTestResult('🔍 Fetching categories...');
      const response = await apiCall('/api/tenant/categories');
      
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
        addTestResult(`✅ Fetched ${data.length} categories successfully`);
        return data;
      } else {
        addTestResult(`❌ Failed to fetch categories: ${response.status}`);
        return [];
      }
    } catch (error) {
      addTestResult(`💥 Error fetching categories: ${error}`);
      return [];
    }
  };

  const testCategoryCreation = async (categoryData: Partial<Category>) => {
    try {
      clearMessages();
      setIsLoading(true);
      
      addTestResult(`🧪 Testing category creation: ${JSON.stringify(categoryData)}`);
      
      const response = await apiCall('/api/tenant/categories', {
        method: 'POST',
        body: JSON.stringify(categoryData)
      });

      addTestResult(`📡 Response status: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const newCategory = await response.json();
        addTestResult(`✅ Category created successfully: ${JSON.stringify(newCategory)}`);
        setSuccess(`Category "${newCategory.name}" created successfully!`);
        
        // Refresh categories
        await fetchCategories();
        
        return newCategory;
      } else {
        const errorText = await response.text();
        addTestResult(`❌ Category creation failed: ${errorText}`);
        setError(`Failed to create category: ${response.status} ${response.statusText}`);
        return null;
      }
    } catch (error) {
      addTestResult(`💥 Exception during category creation: ${error}`);
      setError(`Error creating category: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const runComprehensiveTest = async () => {
    addTestResult('🚀 Starting comprehensive category creation test...');
    setTestResults([]);
    
    // Test 1: Valid category creation
    await testCategoryCreation({
      name: 'Test Category 1',
      description: 'This is a test category',
      color: '#FF5733',
      icon: 'test',
      is_active: true
    });

    // Test 2: Minimal category creation
    await testCategoryCreation({
      name: 'Minimal Category',
      is_active: true
    });

    // Test 3: Invalid category (empty name)
    await testCategoryCreation({
      name: '',
      description: 'This should fail'
    });

    // Test 4: Invalid category (no name)
    await testCategoryCreation({
      description: 'This should also fail'
    });

    // Test 5: Category with special characters
    await testCategoryCreation({
      name: 'Special & Characters! @#$%',
      description: 'Testing special characters',
      color: '#00FF00'
    });

    addTestResult('🏁 Comprehensive test completed!');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Category Creation Test</h2>
        <p className="text-gray-600 mb-6">
          This component tests the category creation functionality to debug any issues.
        </p>

        {/* Error and Success Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-800">{success}</p>
              </div>
              <button
                onClick={() => setSuccess(null)}
                className="ml-auto text-green-400 hover:text-green-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}

        {/* Test Controls */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={fetchCategories}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Fetch Categories
          </button>
          
          <button
            onClick={runComprehensiveTest}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Run Comprehensive Test
          </button>

          <button
            onClick={() => testCategoryCreation({
              name: 'Quick Test Category',
              description: 'Quick test',
              color: '#3B82F6',
              is_active: true
            })}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
          >
            Quick Test
          </button>

          <button
            onClick={() => setTestResults([])}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Testing...</span>
          </div>
        )}
      </div>

      {/* Current Categories */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Categories ({categories.length})</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category) => (
            <div key={category.id} className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <div 
                  className="w-4 h-4 rounded-full" 
                  style={{ backgroundColor: category.color }}
                ></div>
                <h4 className="font-medium text-gray-900">{category.name}</h4>
              </div>
              <p className="text-sm text-gray-600 mb-2">{category.description}</p>
              <div className="flex justify-between text-xs text-gray-500">
                <span>Products: {category.product_count}</span>
                <span>{category.is_active ? 'Active' : 'Inactive'}</span>
              </div>
            </div>
          ))}
        </div>
        
        {categories.length === 0 && (
          <p className="text-gray-500 text-center py-4">No categories found. Try fetching or creating some!</p>
        )}
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
        <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">No test results yet. Run a test to see results here.</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono text-gray-700">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* API Endpoint Information */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">API Endpoint Information</h3>
        <div className="space-y-2 text-sm">
          <div><strong>Endpoint:</strong> POST /api/tenant/categories</div>
          <div><strong>Required Fields:</strong> name (string, min 2 characters)</div>
          <div><strong>Optional Fields:</strong> description, color, icon, is_active</div>
          <div><strong>Expected Response:</strong> 201 Created with category object</div>
          <div><strong>Error Responses:</strong> 400 Bad Request for validation errors, 500 Internal Server Error</div>
        </div>
      </div>
    </div>
  );
};

export default CategoryCreationTest;
