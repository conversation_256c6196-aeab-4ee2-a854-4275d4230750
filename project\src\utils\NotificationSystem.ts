// Comprehensive Notification System for RestroFlow Authentication
// Handles real-time notifications, alerts, and communication channels

interface Notification {
  id: string;
  type: 'security' | 'system' | 'user' | 'audit' | 'performance';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  userId?: number;
  tenantId?: number;
  read: boolean;
  persistent: boolean;
  actions?: NotificationAction[];
  metadata: Record<string, any>;
}

interface NotificationAction {
  id: string;
  label: string;
  action: 'acknowledge' | 'dismiss' | 'view_details' | 'take_action';
  url?: string;
  callback?: () => void;
}

interface NotificationChannel {
  type: 'email' | 'sms' | 'push' | 'webhook' | 'in_app';
  enabled: boolean;
  config: Record<string, any>;
}

interface NotificationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    eventType: string;
    conditions: Record<string, any>;
  };
  channels: string[];
  recipients: {
    roles: string[];
    users: number[];
    tenants: number[];
  };
  enabled: boolean;
  cooldown: number; // minutes
}

class NotificationSystem {
  private static instance: NotificationSystem;
  private notifications: Notification[] = [];
  private channels: Map<string, NotificationChannel> = new Map();
  private rules: NotificationRule[] = [];
  private websocket: WebSocket | null = null;
  private maxNotifications = 1000;
  private listeners: Map<string, Function[]> = new Map();

  private constructor() {
    this.initializeChannels();
    this.connectWebSocket();
    this.startPeriodicCleanup();
  }

  public static getInstance(): NotificationSystem {
    if (!NotificationSystem.instance) {
      NotificationSystem.instance = new NotificationSystem();
    }
    return NotificationSystem.instance;
  }

  // Initialize notification channels
  private initializeChannels(): void {
    this.channels.set('email', {
      type: 'email',
      enabled: true,
      config: {
        smtp: {
          host: 'smtp.gmail.com',
          port: 587,
          secure: false
        },
        templates: {
          security_alert: 'security-alert-template',
          system_notification: 'system-notification-template'
        }
      }
    });

    this.channels.set('sms', {
      type: 'sms',
      enabled: true,
      config: {
        provider: 'twilio',
        fromNumber: '+**********'
      }
    });

    this.channels.set('push', {
      type: 'push',
      enabled: true,
      config: {
        vapidKeys: {
          publicKey: 'your-vapid-public-key',
          privateKey: 'your-vapid-private-key'
        }
      }
    });

    this.channels.set('webhook', {
      type: 'webhook',
      enabled: true,
      config: {
        endpoints: [
          'https://hooks.slack.com/your-webhook-url',
          'https://discord.com/api/webhooks/your-webhook-url'
        ]
      }
    });

    this.channels.set('in_app', {
      type: 'in_app',
      enabled: true,
      config: {
        maxNotifications: 100,
        autoMarkRead: false
      }
    });

    console.log('📢 Notification channels initialized');
  }

  // Connect to WebSocket for real-time notifications
  private connectWebSocket(): void {
    try {
      const wsUrl = 'ws://localhost:4000/notifications';
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        console.log('🔗 WebSocket connected for notifications');
        
        // Authenticate WebSocket connection
        const token = localStorage.getItem('authToken');
        if (token) {
          this.websocket?.send(JSON.stringify({
            type: 'auth',
            token
          }));
        }
      };

      this.websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('🔌 WebSocket disconnected, attempting to reconnect...');
        setTimeout(() => this.connectWebSocket(), 5000);
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  // Handle incoming WebSocket messages
  private handleWebSocketMessage(data: any): void {
    switch (data.type) {
      case 'notification':
        this.addNotification(data.notification);
        break;
      case 'notification_update':
        this.updateNotification(data.notificationId, data.updates);
        break;
      case 'bulk_notifications':
        data.notifications.forEach((notification: Notification) => {
          this.addNotification(notification);
        });
        break;
      default:
        console.log('Unknown WebSocket message type:', data.type);
    }
  }

  // Add a new notification
  public addNotification(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>): void {
    const newNotification: Notification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    };

    this.notifications.unshift(newNotification);

    // Limit notifications
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    // Trigger listeners
    this.triggerListeners('notification_added', newNotification);

    // Show browser notification for critical alerts
    if (newNotification.severity === 'critical' || newNotification.severity === 'error') {
      this.showBrowserNotification(newNotification);
    }

    console.log(`📢 New ${newNotification.severity} notification: ${newNotification.title}`);
  }

  // Update an existing notification
  public updateNotification(id: string, updates: Partial<Notification>): void {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      this.notifications[index] = { ...this.notifications[index], ...updates };
      this.triggerListeners('notification_updated', this.notifications[index]);
    }
  }

  // Mark notification as read
  public markAsRead(id: string): void {
    this.updateNotification(id, { read: true });
  }

  // Mark all notifications as read
  public markAllAsRead(): void {
    this.notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
      }
    });
    this.triggerListeners('all_notifications_read', null);
  }

  // Remove notification
  public removeNotification(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      const removed = this.notifications.splice(index, 1)[0];
      this.triggerListeners('notification_removed', removed);
    }
  }

  // Clear all notifications
  public clearAllNotifications(): void {
    this.notifications = [];
    this.triggerListeners('all_notifications_cleared', null);
  }

  // Get notifications with filtering
  public getNotifications(filter: {
    type?: string;
    severity?: string;
    read?: boolean;
    userId?: number;
    tenantId?: number;
    limit?: number;
  } = {}): Notification[] {
    let filtered = [...this.notifications];

    if (filter.type) {
      filtered = filtered.filter(n => n.type === filter.type);
    }

    if (filter.severity) {
      filtered = filtered.filter(n => n.severity === filter.severity);
    }

    if (filter.read !== undefined) {
      filtered = filtered.filter(n => n.read === filter.read);
    }

    if (filter.userId) {
      filtered = filtered.filter(n => n.userId === filter.userId);
    }

    if (filter.tenantId) {
      filtered = filtered.filter(n => n.tenantId === filter.tenantId);
    }

    if (filter.limit) {
      filtered = filtered.slice(0, filter.limit);
    }

    return filtered;
  }

  // Get unread count
  public getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  // Send notification through specific channel
  public async sendNotification(notification: Notification, channels: string[]): Promise<void> {
    const promises = channels.map(channelType => {
      const channel = this.channels.get(channelType);
      if (channel && channel.enabled) {
        return this.sendThroughChannel(notification, channel);
      }
      return Promise.resolve();
    });

    try {
      await Promise.all(promises);
      console.log(`📤 Notification sent through ${channels.join(', ')}`);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  // Send notification through specific channel
  private async sendThroughChannel(notification: Notification, channel: NotificationChannel): Promise<void> {
    try {
      const token = localStorage.getItem('authToken');
      
      await fetch('http://localhost:4000/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify({
          notification,
          channel: channel.type,
          config: channel.config
        })
      });
    } catch (error) {
      console.error(`Error sending notification through ${channel.type}:`, error);
    }
  }

  // Show browser notification
  private showBrowserNotification(notification: Notification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/badge-icon.png',
        tag: notification.id,
        requireInteraction: notification.severity === 'critical'
      });

      browserNotification.onclick = () => {
        window.focus();
        browserNotification.close();
        
        // Trigger notification click action
        if (notification.actions && notification.actions.length > 0) {
          const primaryAction = notification.actions[0];
          if (primaryAction.callback) {
            primaryAction.callback();
          } else if (primaryAction.url) {
            window.open(primaryAction.url, '_blank');
          }
        }
      };

      // Auto-close after 10 seconds for non-critical notifications
      if (notification.severity !== 'critical') {
        setTimeout(() => browserNotification.close(), 10000);
      }
    }
  }

  // Request notification permission
  public async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  // Add event listener
  public addEventListener(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  // Remove event listener
  public removeEventListener(event: string, callback: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // Trigger event listeners
  private triggerListeners(event: string, data: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in notification listener:', error);
        }
      });
    }
  }

  // Create security alert notification
  public createSecurityAlert(data: {
    title: string;
    message: string;
    severity: 'warning' | 'error' | 'critical';
    userId?: number;
    tenantId?: number;
    metadata?: Record<string, any>;
  }): void {
    this.addNotification({
      type: 'security',
      severity: data.severity,
      title: data.title,
      message: data.message,
      userId: data.userId,
      tenantId: data.tenantId,
      persistent: data.severity === 'critical',
      actions: [
        {
          id: 'acknowledge',
          label: 'Acknowledge',
          action: 'acknowledge'
        },
        {
          id: 'view_details',
          label: 'View Details',
          action: 'view_details',
          url: '/admin/security'
        }
      ],
      metadata: data.metadata || {}
    });
  }

  // Create system notification
  public createSystemNotification(data: {
    title: string;
    message: string;
    severity: 'info' | 'warning' | 'error';
    metadata?: Record<string, any>;
  }): void {
    this.addNotification({
      type: 'system',
      severity: data.severity,
      title: data.title,
      message: data.message,
      persistent: false,
      actions: [
        {
          id: 'dismiss',
          label: 'Dismiss',
          action: 'dismiss'
        }
      ],
      metadata: data.metadata || {}
    });
  }

  // Create user notification
  public createUserNotification(data: {
    title: string;
    message: string;
    userId: number;
    tenantId?: number;
    severity?: 'info' | 'warning';
    metadata?: Record<string, any>;
  }): void {
    this.addNotification({
      type: 'user',
      severity: data.severity || 'info',
      title: data.title,
      message: data.message,
      userId: data.userId,
      tenantId: data.tenantId,
      persistent: false,
      metadata: data.metadata || {}
    });
  }

  // Periodic cleanup of old notifications
  private startPeriodicCleanup(): void {
    setInterval(() => {
      const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago
      
      this.notifications = this.notifications.filter(notification => {
        const notificationTime = new Date(notification.timestamp).getTime();
        return notificationTime > cutoff || notification.persistent;
      });
    }, 60 * 60 * 1000); // Run every hour
  }

  // Export notifications
  public exportNotifications(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'type', 'severity', 'title', 'message', 'read', 'userId', 'tenantId'];
      const rows = this.notifications.map(notification => [
        notification.timestamp,
        notification.type,
        notification.severity,
        notification.title,
        notification.message,
        notification.read.toString(),
        notification.userId?.toString() || '',
        notification.tenantId?.toString() || ''
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    } else {
      return JSON.stringify(this.notifications, null, 2);
    }
  }

  // Cleanup
  public cleanup(): void {
    if (this.websocket) {
      this.websocket.close();
    }
    this.listeners.clear();
    this.notifications = [];
  }
}

// Export singleton instance
export const notificationSystem = NotificationSystem.getInstance();

// Export types
export type { Notification, NotificationAction, NotificationChannel, NotificationRule };

// Utility functions
export const createSecurityAlert = (data: any) => notificationSystem.createSecurityAlert(data);
export const createSystemNotification = (data: any) => notificationSystem.createSystemNotification(data);
export const createUserNotification = (data: any) => notificationSystem.createUserNotification(data);
export const getNotifications = (filter?: any) => notificationSystem.getNotifications(filter);
export const getUnreadCount = () => notificationSystem.getUnreadCount();
export const markAsRead = (id: string) => notificationSystem.markAsRead(id);
export const markAllAsRead = () => notificationSystem.markAllAsRead();
