import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { Customer, LoyaltyReward } from '../types';
import { Search, Star, Gift, Phone, Mail, Calendar, TrendingUp, Award, Plus, Edit3 } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

const LoyaltyProgram: React.FC = () => {
  const { state, fetchCustomers, addCustomer, updateCustomer, fetchLoyaltyRewards } = useAppContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState<'customers' | 'rewards'>('customers');

  useEffect(() => {
    fetchCustomers();
    fetchLoyaltyRewards();
  }, []);

  const filteredCustomers = state.customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm) ||
    customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTierColor = (tier: Customer['tier']) => {
    switch (tier) {
      case 'bronze':
        return 'text-orange-400 bg-orange-900';
      case 'silver':
        return 'text-gray-300 bg-gray-700';
      case 'gold':
        return 'text-yellow-400 bg-yellow-900';
      case 'platinum':
        return 'text-purple-400 bg-purple-900';
      default:
        return 'text-gray-400 bg-gray-800';
    }
  };

  const getTierIcon = (tier: Customer['tier']) => {
    switch (tier) {
      case 'bronze':
        return '🥉';
      case 'silver':
        return '🥈';
      case 'gold':
        return '🥇';
      case 'platinum':
        return '💎';
      default:
        return '⭐';
    }
  };

  const calculateTier = (totalSpent: number): Customer['tier'] => {
    if (totalSpent >= 1000) return 'platinum';
    if (totalSpent >= 500) return 'gold';
    if (totalSpent >= 200) return 'silver';
    return 'bronze';
  };

  const handleAddCustomer = () => {
    setEditingCustomer({
      id: uuidv4(),
      name: '',
      phone: '',
      email: '',
      points: 0,
      totalSpent: 0,
      visits: 0,
      joinDate: new Date().toISOString(),
      tier: 'bronze'
    });
    setShowCustomerForm(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowCustomerForm(true);
  };

  const handleSaveCustomer = async () => {
    if (!editingCustomer) return;

    try {
      const customerData = {
        ...editingCustomer,
        tier: calculateTier(editingCustomer.totalSpent)
      };

      const existingCustomer = state.customers.find(c => c.id === editingCustomer.id);
      
      if (existingCustomer) {
        await updateCustomer(customerData);
      } else {
        await addCustomer(customerData);
      }

      setShowCustomerForm(false);
      setEditingCustomer(null);
    } catch (error) {
      console.error('Failed to save customer:', error);
    }
  };

  const handleLookupCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  const CustomerForm: React.FC = () => {
    if (!editingCustomer) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg p-6 w-96 max-h-[90vh] overflow-y-auto">
          <h3 className="text-xl font-semibold text-white mb-4">
            {state.customers.find(c => c.id === editingCustomer.id) ? 'Edit Customer' : 'Add Customer'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Name *
              </label>
              <input
                type="text"
                value={editingCustomer.name}
                onChange={(e) => setEditingCustomer({ ...editingCustomer, name: e.target.value })}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Customer name"
              />
            </div>

            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Phone *
              </label>
              <input
                type="tel"
                value={editingCustomer.phone}
                onChange={(e) => setEditingCustomer({ ...editingCustomer, phone: e.target.value })}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Phone number"
              />
            </div>

            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Email
              </label>
              <input
                type="email"
                value={editingCustomer.email || ''}
                onChange={(e) => setEditingCustomer({ ...editingCustomer, email: e.target.value })}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Email address"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Points
                </label>
                <input
                  type="number"
                  value={editingCustomer.points}
                  onChange={(e) => setEditingCustomer({ ...editingCustomer, points: parseInt(e.target.value) || 0 })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Total Spent
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editingCustomer.totalSpent}
                  onChange={(e) => setEditingCustomer({ ...editingCustomer, totalSpent: parseFloat(e.target.value) || 0 })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => {
                setShowCustomerForm(false);
                setEditingCustomer(null);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveCustomer}
              disabled={!editingCustomer.name || !editingCustomer.phone}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    );
  };

  const CustomerDetails: React.FC<{ customer: Customer }> = ({ customer }) => (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-semibold text-white">{customer.name}</h3>
          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierColor(customer.tier)}`}>
            <span className="mr-1">{getTierIcon(customer.tier)}</span>
            {customer.tier.toUpperCase()} MEMBER
          </div>
        </div>
        <button
          onClick={() => handleEditCustomer(customer)}
          className="p-2 text-gray-400 hover:text-white transition-colors"
        >
          <Edit3 className="h-4 w-4" />
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="flex items-center space-x-2">
          <Phone className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">{customer.phone}</span>
        </div>
        {customer.email && (
          <div className="flex items-center space-x-2">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-gray-300">{customer.email}</span>
          </div>
        )}
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-300">
            Joined {new Date(customer.joinDate).toLocaleDateString()}
          </span>
        </div>
        {customer.lastVisit && (
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-gray-300">
              Last visit {new Date(customer.lastVisit).toLocaleDateString()}
            </span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="bg-gray-700 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-400">{customer.points}</div>
          <div className="text-sm text-gray-400">Points</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-400">${customer.totalSpent.toFixed(2)}</div>
          <div className="text-sm text-gray-400">Total Spent</div>
        </div>
        <div className="bg-gray-700 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-400">{customer.visits}</div>
          <div className="text-sm text-gray-400">Visits</div>
        </div>
      </div>
    </div>
  );

  const RewardsTab: React.FC = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">Available Rewards</h3>
        <button className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Add Reward</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {state.loyaltyRewards.map(reward => (
          <div key={reward.id} className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-semibold text-white">{reward.name}</h4>
              <div className="flex items-center space-x-1 text-purple-400">
                <Star className="h-4 w-4" />
                <span className="text-sm">{reward.pointsCost}</span>
              </div>
            </div>
            <p className="text-gray-300 text-sm mb-3">{reward.description}</p>
            <div className="flex items-center justify-between">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                reward.category === 'discount' ? 'bg-blue-900 text-blue-300' :
                reward.category === 'free-item' ? 'bg-green-900 text-green-300' :
                'bg-purple-900 text-purple-300'
              }`}>
                {reward.category.replace('-', ' ').toUpperCase()}
              </span>
              <span className="text-amber-400 font-semibold">
                ${reward.value.toFixed(2)}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-gray-800 rounded-lg mb-4">
        <h2 className="text-xl font-semibold text-white">Loyalty Program</h2>
        <div className="flex items-center space-x-3">
          <div className="flex bg-gray-700 rounded-lg">
            <button
              onClick={() => setActiveTab('customers')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'customers'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Customers
            </button>
            <button
              onClick={() => setActiveTab('rewards')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'rewards'
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Rewards
            </button>
          </div>
        </div>
      </div>

      {activeTab === 'customers' ? (
        <div className="flex-grow flex gap-4">
          {/* Customer List */}
          <div className="w-1/2 bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Customers</h3>
              <button
                onClick={handleAddCustomer}
                className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>Add</span>
              </button>
            </div>

            {/* Search */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-700 text-white pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            {/* Customer List */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredCustomers.map(customer => (
                <div
                  key={customer.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedCustomer?.id === customer.id
                      ? 'bg-purple-600'
                      : 'bg-gray-700 hover:bg-gray-600'
                  }`}
                  onClick={() => handleLookupCustomer(customer)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-white">{customer.name}</h4>
                      <p className="text-sm text-gray-300">{customer.phone}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-purple-400 font-semibold">{customer.points} pts</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${getTierColor(customer.tier)}`}>
                        {getTierIcon(customer.tier)} {customer.tier}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Customer Details */}
          <div className="w-1/2">
            {selectedCustomer ? (
              <CustomerDetails customer={selectedCustomer} />
            ) : (
              <div className="bg-gray-800 rounded-lg p-6 flex flex-col items-center justify-center h-full text-center">
                <Award className="h-12 w-12 text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Select a Customer</h3>
                <p className="text-gray-400">
                  Choose a customer from the list to view their loyalty details
                </p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="flex-grow">
          <RewardsTab />
        </div>
      )}

      {/* Customer Form Modal */}
      {showCustomerForm && <CustomerForm />}
    </div>
  );
};

export default LoyaltyProgram;
