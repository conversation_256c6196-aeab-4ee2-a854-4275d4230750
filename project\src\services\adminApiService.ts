// Real API service for Super Admin Dashboard
// Replaces mock data with actual database queries

const API_BASE_URL = 'http://localhost:4000/api';

// Get authentication token from localStorage
const getAuthToken = (): string | null => {
  return localStorage.getItem('authToken');
};

// Create headers with authentication
const createHeaders = (): HeadersInit => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Handle API responses with error handling
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  return response.json();
};

// Retry mechanism for failed requests
const retryRequest = async (
  requestFn: () => Promise<Response>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<any> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await requestFn();
      return await handleResponse(response);
    } catch (error) {
      lastError = error as Error;
      console.warn(`API request attempt ${attempt} failed:`, error);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }
  
  throw lastError!;
};

// System Metrics Interface
export interface SystemMetrics {
  totalTenants: number;
  activeTenants: number;
  totalRevenue: number;
  systemUptime: number;
  activeUsers: number;
  transactionsToday: number;
  averageResponseTime: number;
  errorRate: number;
}

// Tenant Interface
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  email: string;
  phone: string;
  status: 'active' | 'suspended' | 'canceled';
  employeeCount: number;
  locationCount: number;
  totalRevenue: number;
  totalOrders: number;
  lastOrderDate: string | null;
  createdAt: string;
  updatedAt: string;
}

// Analytics Interface
export interface SystemAnalytics {
  revenueTrends: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  topTenants: Array<{
    id: string;
    name: string;
    revenue: number;
    orders: number;
  }>;
  performanceMetrics: Array<{
    hour: string;
    requests: number;
    avgResponseTime: number;
  }>;
}

// Activity Interface
export interface SystemActivity {
  action: string;
  tenant: string;
  time: string;
  type: 'success' | 'warning' | 'error' | 'info';
}

// User Interface
export interface User {
  id: number;
  tenantId: number;
  name: string;
  email: string;
  role: string;
  pin: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Admin API Service Class
export class AdminApiService {
  private static instance: AdminApiService;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 seconds

  static getInstance(): AdminApiService {
    if (!AdminApiService.instance) {
      AdminApiService.instance = new AdminApiService();
    }
    return AdminApiService.instance;
  }

  // Check if cached data is still valid
  private isCacheValid(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < this.CACHE_DURATION;
  }

  // Get cached data or fetch new data
  private async getCachedOrFetch<T>(
    key: string,
    fetchFn: () => Promise<T>,
    forceRefresh: boolean = false
  ): Promise<T> {
    if (!forceRefresh && this.isCacheValid(key)) {
      console.log(`📦 Using cached data for ${key}`);
      return this.cache.get(key)!.data;
    }

    console.log(`🔄 Fetching fresh data for ${key}`);
    const data = await fetchFn();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }

  // Fetch system metrics
  async getSystemMetrics(forceRefresh: boolean = false): Promise<SystemMetrics> {
    return this.getCachedOrFetch(
      'systemMetrics',
      () => retryRequest(() => 
        fetch(`${API_BASE_URL}/admin/metrics`, {
          method: 'GET',
          headers: createHeaders()
        })
      ),
      forceRefresh
    );
  }

  // Fetch all tenants
  async getTenants(forceRefresh: boolean = false): Promise<Tenant[]> {
    return this.getCachedOrFetch(
      'tenants',
      () => retryRequest(() => 
        fetch(`${API_BASE_URL}/admin/tenants`, {
          method: 'GET',
          headers: createHeaders()
        })
      ),
      forceRefresh
    );
  }

  // Fetch system analytics
  async getSystemAnalytics(forceRefresh: boolean = false): Promise<SystemAnalytics> {
    return this.getCachedOrFetch(
      'systemAnalytics',
      () => retryRequest(() => 
        fetch(`${API_BASE_URL}/admin/analytics`, {
          method: 'GET',
          headers: createHeaders()
        })
      ),
      forceRefresh
    );
  }

  // Fetch system activity
  async getSystemActivity(forceRefresh: boolean = false): Promise<SystemActivity[]> {
    return this.getCachedOrFetch(
      'systemActivity',
      () => retryRequest(() =>
        fetch(`${API_BASE_URL}/admin/activity`, {
          method: 'GET',
          headers: createHeaders()
        })
      ),
      forceRefresh
    );
  }

  // Fetch all users
  async getUsers(page: number = 1, limit: number = 20, forceRefresh: boolean = false): Promise<{ users: User[]; total: number; page: number; limit: number }> {
    const cacheKey = `users_${page}_${limit}`;
    return this.getCachedOrFetch(
      cacheKey,
      () => retryRequest(() =>
        fetch(`${API_BASE_URL}/admin/users?page=${page}&limit=${limit}`, {
          method: 'GET',
          headers: createHeaders()
        })
      ),
      forceRefresh
    );
  }

  // Create a new user
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(userData)
      })
    );

    // Clear users cache after creating
    this.clearCachePattern('users_');
    return response;
  }

  // Update an existing user
  async updateUser(id: number, userData: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>): Promise<User> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/${id}`, {
        method: 'PUT',
        headers: createHeaders(),
        body: JSON.stringify(userData)
      })
    );

    // Clear users cache after updating
    this.clearCachePattern('users_');
    return response;
  }

  // Delete a user (soft delete by default)
  async deleteUser(id: number, options?: { permanent?: boolean; reason?: string }): Promise<any> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/${id}`, {
        method: 'DELETE',
        headers: createHeaders(),
        body: JSON.stringify(options || {})
      })
    );

    // Clear users cache after deleting
    this.clearCachePattern('users_');
    return response;
  }

  // Reset user password/PIN
  async resetUserPassword(id: number, options?: { newPin?: string; generateRandom?: boolean }): Promise<any> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/${id}/reset-password`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify(options || {})
      })
    );

    // Clear users cache after password reset
    this.clearCachePattern('users_');
    return response;
  }

  // Generate random PIN for user
  async generateRandomPin(id: number): Promise<any> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/${id}/generate-random-pin`, {
        method: 'POST',
        headers: createHeaders()
      })
    );

    // Clear users cache after PIN generation
    this.clearCachePattern('users_');
    return response;
  }

  // Toggle user status (activate/deactivate)
  async toggleUserStatus(id: number, isActive: boolean, reason?: string): Promise<any> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/${id}/status`, {
        method: 'PATCH',
        headers: createHeaders(),
        body: JSON.stringify({ is_active: isActive, reason })
      })
    );

    // Clear users cache after status change
    this.clearCachePattern('users_');
    return response;
  }

  // Bulk user actions
  async bulkUserActions(action: string, userIds: number[], reason?: string): Promise<any> {
    const response = await retryRequest(() =>
      fetch(`${API_BASE_URL}/admin/users/bulk-actions`, {
        method: 'POST',
        headers: createHeaders(),
        body: JSON.stringify({ action, userIds, reason })
      })
    );

    // Clear users cache after bulk actions
    this.clearCachePattern('users_');
    return response;
  }

  // Clear all cached data
  clearCache(): void {
    console.log('🗑️ Clearing admin API cache');
    this.cache.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(key: string): void {
    console.log(`🗑️ Clearing cache for ${key}`);
    this.cache.delete(key);
  }

  // Clear cache entries matching a pattern
  clearCachePattern(pattern: string): void {
    console.log(`🗑️ Clearing cache entries matching pattern: ${pattern}`);
    for (const key of this.cache.keys()) {
      if (key.startsWith(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  // Check if user is authenticated and has super admin role
  async checkAuthentication(): Promise<boolean> {
    const token = getAuthToken();
    if (!token) {
      console.warn('⚠️ No authentication token found');
      return false;
    }

    try {
      // Try to fetch metrics to verify authentication
      await this.getSystemMetrics(true);
      return true;
    } catch (error) {
      console.error('❌ Authentication check failed:', error);
      return false;
    }
  }

  // Auto-refresh data at regular intervals
  startAutoRefresh(intervalMs: number = 120000): () => void {
    console.log(`🔄 Starting auto-refresh every ${intervalMs}ms (${intervalMs/1000}s)`);
    
    const intervalId = setInterval(() => {
      console.log('🔄 Auto-refreshing admin dashboard data...');
      this.clearCache();
    }, intervalMs);

    // Return cleanup function
    return () => {
      console.log('⏹️ Stopping auto-refresh');
      clearInterval(intervalId);
    };
  }
}

// Export singleton instance
export const adminApiService = AdminApiService.getInstance();

// Export types for use in components
export type {
  SystemMetrics,
  Tenant,
  SystemAnalytics,
  SystemActivity,
  User
};
