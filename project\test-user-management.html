<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .test-button:disabled {
            background: #9CA3AF;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
    </style>
</head>
<body>
    <h1>🔧 User Management Functionality Test</h1>
    
    <div class="test-container">
        <h2>Backend Connectivity Test</h2>
        <button class="test-button" onclick="testBackendConnection()">Test Backend Connection</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-container">
        <h2>User Management API Tests</h2>
        <button class="test-button" onclick="testGetUsers()">Test Get Users</button>
        <button class="test-button" onclick="testCreateUser()">Test Create User</button>
        <button class="test-button" onclick="testUpdateUser()">Test Update User</button>
        <button class="test-button" onclick="testDeleteUser()">Test Delete User</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-container">
        <h2>Frontend Component Test</h2>
        <button class="test-button" onclick="testComponentLoad()">Test Component Loading</button>
        <button class="test-button" onclick="openUserManagement()">Open User Management</button>
        <div id="component-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000';
        let authToken = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testBackendConnection() {
            log('backend-result', 'Testing backend connection...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();

                if (response.ok) {
                    log('backend-result', `✅ Backend connected successfully!\nStatus: ${data.status}\nTimestamp: ${data.timestamp}`, 'success');

                    // Auto-login to get auth token
                    await autoLogin();
                } else {
                    log('backend-result', `❌ Backend responded with error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log('backend-result', `❌ Failed to connect to backend: ${error.message}`, 'error');
            }
        }

        async function autoLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pin: '123456' // Super admin PIN
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    log('backend-result', `✅ Auto-login successful!\nRole: ${data.employee.role}\nTenant: ${data.tenant.name}`, 'success');
                } else {
                    log('backend-result', `❌ Auto-login failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log('backend-result', `❌ Auto-login error: ${error.message}`, 'error');
            }
        }

        async function testGetUsers() {
            log('api-result', 'Testing Get Users API...', 'info');

            if (!authToken) {
                log('api-result', '❌ No auth token available. Please test backend connection first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const users = data.users || data;
                    log('api-result', `✅ Get Users successful!\nFound ${users.length} users:\n${JSON.stringify(users.slice(0, 3), null, 2)}\n... and ${users.length - 3} more`, 'success');
                } else {
                    const error = await response.text();
                    log('api-result', `❌ Get Users failed: ${response.status} ${response.statusText}\n${error}`, 'error');
                }
            } catch (error) {
                log('api-result', `❌ Get Users error: ${error.message}`, 'error');
            }
        }

        async function testCreateUser() {
            log('api-result', 'Testing Create User API...', 'info');

            if (!authToken) {
                log('api-result', '❌ No auth token available. Please test backend connection first.', 'error');
                return;
            }

            const testUser = {
                tenantId: '1',
                name: 'Test User',
                email: `test${Date.now()}@example.com`,
                role: 'employee',
                pin: '567890',
                permissions: ['pos_access']
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testUser)
                });

                if (response.ok) {
                    const newUser = await response.json();
                    log('api-result', `✅ Create User successful!\nCreated user: ${newUser.name}\nID: ${newUser.id}\nDefault Password: ${newUser.default_password}`, 'success');
                } else {
                    const error = await response.text();
                    log('api-result', `❌ Create User failed: ${response.status} ${response.statusText}\n${error}`, 'error');
                }
            } catch (error) {
                log('api-result', `❌ Create User error: ${error.message}`, 'error');
            }
        }

        async function testUpdateUser() {
            log('api-result', 'Testing Update User API...', 'info');

            if (!authToken) {
                log('api-result', '❌ No auth token available. Please test backend connection first.', 'error');
                return;
            }

            const updateData = {
                name: 'Updated Test User',
                phone: '+1234567890'
            };

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/1`, {
                    method: 'PATCH',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const updatedUser = await response.json();
                    log('api-result', `✅ Update User successful!\nUpdated data: ${JSON.stringify(updatedUser, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    log('api-result', `❌ Update User failed: ${response.status} ${response.statusText}\n${error}`, 'error');
                }
            } catch (error) {
                log('api-result', `❌ Update User error: ${error.message}`, 'error');
            }
        }

        async function testDeleteUser() {
            log('api-result', 'Testing Delete User API...', 'info');

            if (!authToken) {
                log('api-result', '❌ No auth token available. Please test backend connection first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/admin/users/14`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    log('api-result', `✅ Delete User successful!\nResult: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const error = await response.text();
                    log('api-result', `❌ Delete User failed: ${response.status} ${response.statusText}\n${error}`, 'error');
                }
            } catch (error) {
                log('api-result', `❌ Delete User error: ${error.message}`, 'error');
            }
        }

        async function testComponentLoad() {
            log('component-result', 'Testing component loading...', 'info');
            
            try {
                // Test if React components are available
                if (typeof React !== 'undefined') {
                    log('component-result', '✅ React is available', 'success');
                } else {
                    log('component-result', '❌ React is not available', 'error');
                    return;
                }
                
                // Test if the EnhancedUserManager component exists
                const componentExists = window.EnhancedUserManager || 
                                      (window.React && window.React.createElement);
                
                if (componentExists) {
                    log('component-result', '✅ Component system is working', 'success');
                } else {
                    log('component-result', '❌ Component system not found', 'error');
                }
                
            } catch (error) {
                log('component-result', `❌ Component test error: ${error.message}`, 'error');
            }
        }

        function openUserManagement() {
            log('component-result', 'Opening User Management page...', 'info');
            window.open('http://localhost:5174/super-admin.html', '_blank');
        }

        // Auto-run backend test on page load
        window.addEventListener('load', () => {
            setTimeout(testBackendConnection, 1000);
        });
    </script>
</body>
</html>
