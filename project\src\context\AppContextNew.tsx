import React, { createContext, useEffect } from 'react';
import { createAppContext } from '../hooks/useAppContext';

const initialState = {
  products: [],
  categories: [],
  employees: [],
  currentOrder: null
};

export const AppContext = createContext<ReturnType<typeof createAppContext> | undefined>(undefined);

export const AppContextProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const context = createAppContext(initialState);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        const [products, categories, employees] = await Promise.all([
          context.fetchProducts(),
          context.fetchCategories(),
          context.fetchEmployees()
        ]);

        context.dispatch({ type: 'SET_PRODUCTS', payload: products });
        context.dispatch({ type: 'SET_CATEGORIES', payload: categories });
        context.dispatch({ type: 'SET_EMPLOYEES', payload: employees });
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    initializeData();
  }, []);

  return (
    <AppContext.Provider value={context}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContextProvider;
