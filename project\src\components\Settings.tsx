import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { Settings as SettingsIcon, Users, FileText, Shield, X, Plus, Edit2, Trash } from 'lucide-react';
import { Product, Category, Employee } from '../types';
import { v4 as uuidv4 } from 'uuid';

const Settings: React.FC = () => {
  const { 
    state, 
    addProduct, 
    updateProduct, 
    deleteProduct, 
    addCategory,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateSettings,
    fetchEmployees,
    downloadTemplate,
    importProducts,
    exportProducts
  } = useAppContext();
  const categories = state.categories;

  // Modal states
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [showEmployeeModal, setShowEmployeeModal] = useState(false);
  const [showConfigModal, setShowConfigModal] = useState(false);

  // Form states
  const [newCategoryInput, setNewCategoryInput] = useState('');
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  
  const [configForm, setConfigForm] = useState({
    tax_rate: state.systemConfig.tax_rate,
    receipt_header: state.systemConfig.receipt_header || '',
    receipt_footer: state.systemConfig.receipt_footer || '',
    business_name: state.systemConfig.business_name || '',
    business_address: state.systemConfig.business_address || '',
    business_phone: state.systemConfig.business_phone || ''
  });

  const [productForm, setProductForm] = useState<Partial<Product>>({
    name: '',
    price: 0,
    category: categories[0] || 'beer',
    description: '',
    inStock: true
  });

  const [employeeForm, setEmployeeForm] = useState<{
    name: string;
    pin: string;
    role: Employee['role'];
  }>({
    name: '',
    pin: '',
    role: 'server' as const
  });

  // Loading states
  const [isLoading, setIsLoading] = useState({
    employees: false,
    products: false,
    config: false
  });

  // Error states
  const [error, setError] = useState<string | null>(null);

  // Check if user has admin access
  const isAdmin = state.currentEmployee?.role === 'super_admin' || state.currentEmployee?.role === 'tenant_admin' || state.currentEmployee?.role === 'manager';

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setProductForm(product);
    setShowMenuModal(true);
  };

  const handleAddNewProduct = () => {
    setEditingProduct(null);
    setProductForm({
      name: '',
      price: 0,
      category: categories[0] || 'beer',
      description: '',
      inStock: true
    });
    setShowMenuModal(true);
  };

  const handleSaveProduct = async () => {
    if (!productForm.name || !productForm.name.trim()) {
      alert('Product name is required.');
      return;
    }
    if (productForm.price === undefined || productForm.price === null) {
      alert('Product price is required.');
      return;
    }
    if (productForm.price < 0) {
      alert('Price cannot be negative.');
      return;
    }
    if (!productForm.category) {
      alert('Product category is required.');
      return;
    }

    const sanitizedName = productForm.name.trim();
    const sanitizedDescription = productForm.description ? productForm.description.trim() : '';

    const newProduct: Product = {
      id: editingProduct?.id || uuidv4(),
      name: sanitizedName,
      price: Number(productForm.price),
      category: productForm.category as Category,
      description: sanitizedDescription,
      inStock: productForm.inStock ?? true
    };

    try {
      if (editingProduct) {
        await updateProduct(newProduct);
        alert('Product updated successfully.');
      } else {
        await addProduct(newProduct);
        alert('Product added successfully.');
      }
    } catch (error) {
      alert('Failed to save product. Please try again.');
      return;
    }

    setEditingProduct(null);
    setProductForm({
      name: '',
      price: 0,
      category: categories[0] || 'beer',
      description: '',
      inStock: true
    });
    setShowMenuModal(false);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(productId);
      } catch (error) {
        alert('Failed to delete product. Please try again.');
      }
    }
  };

  // New category addition handlers:
  const handleAddCategory = async () => {
    const newCat = newCategoryInput.trim().toLowerCase().replace(/\s+/g, '-');
    if (!newCat) {
      alert('Category name cannot be empty.');
      return;
    }
    if (categories.includes(newCat as Category)) {
      alert('This category already exists.');
      return;
    }
    try {
      await addCategory(newCat as Category);
      setNewCategoryInput('');
      setShowAddCategoryModal(false);
      alert(`Category "${newCat}" added successfully.`);
    } catch (error: any) {
      alert(`Failed to add category: ${error.message}`);
    }
  };

  // Handle system config updates
  const handleSaveConfig = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading({ ...isLoading, config: true });
    
    try {
      await updateSettings(configForm);
      setShowConfigModal(false);
      alert('Settings updated successfully');
    } catch (error) {
      console.error('Failed to update settings:', error);
      alert(error instanceof Error ? error.message : 'Failed to update settings');
    } finally {
      setIsLoading({ ...isLoading, config: false });
    }
  };

  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <Shield className="h-12 w-12 text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Access Restricted</h3>
        <p className="text-gray-400">
          You need administrator privileges to access settings
        </p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-shrink-0 p-4 border-b border-gray-700">
        <h2 className="text-xl font-semibold text-white">System Settings</h2>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-700">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
          {/* Employee Management */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <div className="bg-blue-900 p-2 rounded-md mr-3">
              <Users className="h-5 w-5 text-blue-400" />
            </div>
            <h3 className="text-lg font-medium text-white">Employee Management</h3>
          </div>

          <p className="text-gray-400 text-sm mb-4">
            Manage employee accounts, roles, and permissions
          </p>

          <div className="overflow-x-auto">
            <table className="w-full mb-4">
              <thead>
                <tr className="text-left text-gray-400 border-b border-gray-700">
                  <th className="pb-2">Name</th>
                  <th className="pb-2">Role</th>
                  <th className="pb-2">Created</th>
                  <th className="pb-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {state.employees.map(employee => (
                  <tr key={employee.id} className="border-b border-gray-700 last:border-0">
                    <td className="py-2 text-white">{employee.name}</td>
                    <td className="py-2 text-gray-300">
                      <span className="capitalize">{employee.role}</span>
                    </td>
                    <td className="py-2 text-gray-400 text-sm">
                      {employee.created_at && new Date(employee.created_at).toLocaleDateString()}
                    </td>
                    <td className="py-2">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setEditingEmployee(employee);
                            setEmployeeForm({
                              name: employee.name,
                              pin: '',
                              role: employee.role
                            });
                            setShowEmployeeModal(true);
                          }}
                          className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                          aria-label={`Edit ${employee.name}`}
                        >
                          <Edit2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={async () => {
                            if (window.confirm(`Are you sure you want to delete ${employee.name}?`)) {
                              setIsLoading({ ...isLoading, employees: true });
                              try {
                                await deleteEmployee(employee.id!);
                                await fetchEmployees();
                              } catch (err) {
                                setError(err instanceof Error ? err.message : 'Failed to delete employee');
                              } finally {
                                setIsLoading({ ...isLoading, employees: false });
                              }
                            }
                          }}
                          className="text-red-400 hover:text-red-300 transition-colors p-1"
                          aria-label={`Delete ${employee.name}`}
                        >
                          <Trash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <button 
            className="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors w-full"
            onClick={() => {
              setEditingEmployee(null);
              setEmployeeForm({
                name: '',
                pin: '',
                role: 'server'
              });
              setShowEmployeeModal(true);
            }}
          >
            Manage Employees
          </button>
        </div>

        {/* Menu Management */}
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <div className="bg-amber-900 p-2 rounded-md mr-3">
              <FileText className="h-5 w-5 text-amber-400" />
            </div>
            <h3 className="text-lg font-medium text-white">Menu Management</h3>
          </div>

          <p className="text-gray-400 text-sm mb-4">
            Update menu items, prices, and categories
          </p>

          <div className="mb-4">
            <div className="flex justify-between text-gray-400 mb-2">
              <span>Total Menu Items</span>
              <span className="font-medium text-white">{state.products.length}</span>
            </div>
            <div className="flex justify-between items-center text-gray-400 mb-2">
              <span>Categories</span>
              <span className="font-medium text-white">{categories.length}</span>
              <button
                className="ml-2 text-green-500 hover:text-green-400 transition-colors"
                onClick={() => setShowAddCategoryModal(true)}
                aria-label="Add New Category"
                title="Add New Category"
              >
                <Plus className="h-5 w-5" />
              </button>
            </div>
            <div className="flex justify-between text-gray-400">
              <span>Last Updated</span>
              <span className="font-medium text-white">Today</span>
            </div>
          </div>

          <div className="space-y-2 mb-4">
            <button 
              className="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors w-full"
              onClick={handleAddNewProduct}
            >
              Manage Menu
            </button>
            <div className="flex space-x-2">
              <button
                className="bg-green-600 hover:bg-green-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                onClick={() => downloadTemplate()}
              >
                Download Template
              </button>
              <button
                className="bg-blue-600 hover:bg-blue-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.xlsx';
                  input.onchange = async (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0];
                    if (file) {
                      try {
                        const result = await importProducts(file);
                        alert(`Successfully imported ${result.success.length} products. ${result.errors.length} errors.`);
                      } catch (error) {
                        alert('Failed to import products: ' + (error instanceof Error ? error.message : 'Unknown error'));
                      }
                    }
                  };
                  input.click();
                }}
              >
                Import Excel
              </button>
              <button
                className="bg-amber-600 hover:bg-amber-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                onClick={() => exportProducts()}
              >
                Export to Excel
              </button>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* System Settings */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <div className="flex items-center mb-4">
          <div className="bg-purple-900 p-2 rounded-md mr-3">
            <SettingsIcon className="h-5 w-5 text-purple-400" />
          </div>
          <h3 className="text-lg font-medium text-white">System Configuration</h3>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-white font-medium">Tax Rate</h4>
              <p className="text-gray-400 text-sm">Default tax rate for orders</p>
            </div>
            <div className="flex items-center space-x-2">
              <input 
                type="text" 
                value={`${(state.systemConfig.tax_rate * 100).toFixed(2)}%`}
                className="bg-gray-700 text-white px-3 py-2 rounded-md w-20 text-right"
                readOnly
              />
              <button 
                onClick={() => setShowConfigModal(true)}
                className="bg-gray-700 hover:bg-gray-600 px-2 py-2 rounded-md text-gray-300 transition-colors"
              >
                Edit
              </button>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-white font-medium">Receipt Settings</h4>
              <p className="text-gray-400 text-sm">Customize receipt format and footer</p>
            </div>
            <button 
              onClick={() => setShowConfigModal(true)}
              className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-md text-gray-300 transition-colors"
            >
              Configure
            </button>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-white font-medium">Backup Database</h4>
              <p className="text-gray-400 text-sm">Create a backup of all system data</p>
            </div>
            <button className="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-md text-gray-300 transition-colors">
              Backup Now
            </button>
          </div>
        </div>
      </div>

      {/* About */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-medium text-white mb-2">About</h3>
        <p className="text-gray-400 mb-4">Bar POS System v1.0.0</p>

        <div className="flex justify-between text-gray-400 text-sm">
          <span>© 2025 Bar POS System</span>
          <button className="text-purple-400 hover:text-purple-300 transition-colors">
            Check for Updates
          </button>
        </div>
      </div>

      {/* System Configuration Modal */}
      {showConfigModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-white">System Configuration</h3>
              <button
                onClick={() => {
                  setShowConfigModal(false);
                  setConfigForm({
                    tax_rate: state.systemConfig.tax_rate,
                    receipt_header: state.systemConfig.receipt_header || '',
                    receipt_footer: state.systemConfig.receipt_footer || '',
                    business_name: state.systemConfig.business_name || '',
                    business_address: state.systemConfig.business_address || '',
                    business_phone: state.systemConfig.business_phone || ''
                  });
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSaveConfig} className="space-y-4">
              <div>
                <label htmlFor="tax-rate" className="block text-gray-300 mb-1">Tax Rate (%)</label>
                <input
                  id="tax-rate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={(configForm.tax_rate * 100).toFixed(2)}
                  onChange={(e) => setConfigForm({ ...configForm, tax_rate: parseFloat(e.target.value) / 100 })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label htmlFor="business-name" className="block text-gray-300 mb-1">Business Name</label>
                <input
                  id="business-name"
                  type="text"
                  value={configForm.business_name}
                  onChange={(e) => setConfigForm({ ...configForm, business_name: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label htmlFor="business-address" className="block text-gray-300 mb-1">Business Address</label>
                <input
                  id="business-address"
                  type="text"
                  value={configForm.business_address}
                  onChange={(e) => setConfigForm({ ...configForm, business_address: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label htmlFor="business-phone" className="block text-gray-300 mb-1">Business Phone</label>
                <input
                  id="business-phone"
                  type="text"
                  value={configForm.business_phone}
                  onChange={(e) => setConfigForm({ ...configForm, business_phone: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label htmlFor="receipt-header" className="block text-gray-300 mb-1">Receipt Header</label>
                <textarea
                  id="receipt-header"
                  value={configForm.receipt_header}
                  onChange={(e) => setConfigForm({ ...configForm, receipt_header: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={2}
                />
              </div>

              <div>
                <label htmlFor="receipt-footer" className="block text-gray-300 mb-1">Receipt Footer</label>
                <textarea
                  id="receipt-footer"
                  value={configForm.receipt_footer}
                  onChange={(e) => setConfigForm({ ...configForm, receipt_footer: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={2}
                />
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowConfigModal(false);
                    setConfigForm({
                      tax_rate: state.systemConfig.tax_rate,
                      receipt_header: state.systemConfig.receipt_header || '',
                      receipt_footer: state.systemConfig.receipt_footer || '',
                      business_name: state.systemConfig.business_name || '',
                      business_address: state.systemConfig.business_address || '',
                      business_phone: state.systemConfig.business_phone || ''
                    });
                  }}
                  className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors"
                  disabled={isLoading.config}
                >
                  {isLoading.config ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Menu Management Modal */}
      {showMenuModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-white">
                Menu Management
              </h3>
              <button
                onClick={() => {
                  setShowMenuModal(false);
                  setEditingProduct(null);
                  setProductForm({
                    name: '',
                    price: 0,
                    category: categories[0] || 'beer',
                    description: '',
                    inStock: true
                  });
                }}
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Close Menu Management Modal"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="product-name" className="block text-gray-300 mb-1">Name *</label>
                <input
                  id="product-name"
                  type="text"
                  value={productForm.name}
                  onChange={(e) => setProductForm({ ...productForm, name: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="product-price" className="block text-gray-300 mb-1">Price *</label>
                <input
                  id="product-price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={productForm.price}
                  onChange={(e) => setProductForm({ ...productForm, price: Number(e.target.value) })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <label htmlFor="product-category" className="block text-gray-300 mb-1">Category *</label>
                  <select
                    id="product-category"
                    value={productForm.category}
                    onChange={(e) => setProductForm({ ...productForm, category: e.target.value as Category })}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                      </option>
                    ))}
                  </select>
                </div>
                <button
                  className="ml-3 mt-6 bg-green-600 hover:bg-green-500 text-white px-3 py-2 rounded-md transition-colors whitespace-nowrap"
                  onClick={() => setShowAddCategoryModal(true)}
                  title="Add New Category"
                >
                  + Add Category
                </button>
              </div>

              <div>
                <label htmlFor="product-description" className="block text-gray-300 mb-1">Description</label>
                <textarea
                  id="product-description"
                  value={productForm.description}
                  onChange={(e) => setProductForm({ ...productForm, description: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                />
              </div>

              <div className="flex items-center">
                <input
                  id="product-instock"
                  type="checkbox"
                  checked={productForm.inStock}
                  onChange={(e) => setProductForm({ ...productForm, inStock: e.target.checked })}
                  className="mr-2"
                />
                <label htmlFor="product-instock" className="text-gray-300">In Stock</label>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  onClick={() => {
                    setEditingProduct(null);
                    setProductForm({
                      name: '',
                      price: 0,
                      category: categories[0] || 'beer',
                      description: '',
                      inStock: true
                    });
                    setShowMenuModal(false);
                  }}
                  className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveProduct}
                  className="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors"
                >
                  {editingProduct ? 'Update Product' : 'Add Product'}
                </button>
              </div>
            </div>

            {!productForm.name && !editingProduct && (
              <div className="mt-6 space-y-2">
                {state.products.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center justify-between bg-gray-700 p-3 rounded-md"
                  >
                    <div>
                      <h4 className="text-white font-medium">{product.name}</h4>
                      <div className="text-sm">
                        <span className="text-amber-400">${product.price.toFixed(2)}</span>
                        <span className="text-gray-400 ml-2">{product.category}</span>
                        {!product.inStock && (
                          <span className="text-red-400 ml-2">Out of Stock</span>
                        )}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditProduct(product)}
                        className="text-blue-400 hover:text-blue-300 transition-colors p-1"
                        aria-label={`Edit ${product.name}`}
                      >
                        <Edit2 className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="text-red-400 hover:text-red-300 transition-colors p-1"
                        aria-label={`Delete ${product.name}`}
                      >
                        <Trash className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Add New Category Modal */}
      {showAddCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-60">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">Add New Category</h3>
              <button
                onClick={() => {
                  setShowAddCategoryModal(false);
                  setNewCategoryInput('');
                }}
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Close Add Category Modal"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <input
              type="text"
              value={newCategoryInput}
              onChange={(e) => setNewCategoryInput(e.target.value)}
              placeholder="Enter category name"
              className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            />

            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowAddCategoryModal(false);
                  setNewCategoryInput('');
                }}
                className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddCategory}
                className="bg-green-600 hover:bg-green-500 text-white py-2 px-4 rounded-md transition-colors"
              >
                Add Category
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Employee Management Modal */}
      {showEmployeeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-white">
                {editingEmployee ? 'Edit Employee' : 'Add New Employee'}
              </h3>
              <button
                onClick={() => {
                  setShowEmployeeModal(false);
                  setEditingEmployee(null);
                  setEmployeeForm({
                    name: '',
                    pin: '',
                    role: 'server'
                  });
                }}
                className="text-gray-400 hover:text-white transition-colors"
                aria-label="Close Employee Modal"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={async (e) => {
              e.preventDefault();
              if (!employeeForm.name.trim()) {
                alert('Employee name is required.');
                return;
              }
              if (!employeeForm.pin.trim()) {
                alert('Employee PIN is required.');
                return;
              }
              if (employeeForm.pin.length < 4) {
                alert('PIN must be at least 4 characters.');
                return;
              }

              setIsLoading({ ...isLoading, employees: true });
              try {
                if (editingEmployee) {
                  await updateEmployee({
                    ...editingEmployee,
                    name: employeeForm.name.trim(),
                    pin: employeeForm.pin,
                    role: employeeForm.role
                  });
                  alert('Employee updated successfully.');
                } else {
                  await addEmployee({
                    name: employeeForm.name.trim(),
                    pin: employeeForm.pin,
                    role: employeeForm.role
                  });
                  alert('Employee added successfully.');
                }
                await fetchEmployees();
                setShowEmployeeModal(false);
                setEditingEmployee(null);
                setEmployeeForm({
                  name: '',
                  pin: '',
                  role: 'server'
                });
              } catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to save employee');
                alert(err instanceof Error ? err.message : 'Failed to save employee');
              } finally {
                setIsLoading({ ...isLoading, employees: false });
              }
            }} className="space-y-4">
              <div>
                <label htmlFor="employee-name" className="block text-gray-300 mb-1">Name *</label>
                <input
                  id="employee-name"
                  type="text"
                  value={employeeForm.name}
                  onChange={(e) => setEmployeeForm({ ...employeeForm, name: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="employee-pin" className="block text-gray-300 mb-1">PIN *</label>
                <input
                  id="employee-pin"
                  type="password"
                  value={employeeForm.pin}
                  onChange={(e) => setEmployeeForm({ ...employeeForm, pin: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  minLength={4}
                  required
                />
              </div>

              <div>
                <label htmlFor="employee-role" className="block text-gray-300 mb-1">Role *</label>
                <select
                  id="employee-role"
                  value={employeeForm.role}
                  onChange={(e) => setEmployeeForm({ ...employeeForm, role: e.target.value as Employee['role'] })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                >
                  <option value="server">Server</option>
                  <option value="bartender">Bartender</option>
                  <option value="cashier">Cashier</option>
                  <option value="kitchen">Kitchen</option>
                  <option value="manager">Manager</option>
                  <option value="tenant_admin">Tenant Admin</option>
                  <option value="super_admin">Super Admin</option>
                </select>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowEmployeeModal(false);
                    setEditingEmployee(null);
                    setEmployeeForm({
                      name: '',
                      pin: '',
                      role: 'server'
                    });
                  }}
                  className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors"
                  disabled={isLoading.employees}
                >
                  {isLoading.employees ? 'Saving...' : (editingEmployee ? 'Update Employee' : 'Add Employee')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
