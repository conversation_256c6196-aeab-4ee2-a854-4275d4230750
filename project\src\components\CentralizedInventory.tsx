import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Package, AlertTriangle, TrendingDown, TrendingUp, Building, Truck, RefreshCw, Download, Upload } from 'lucide-react';

interface InventoryItem {
  id: string;
  product_name: string;
  sku: string;
  category: string;
  unit_cost: number;
  supplier: string;
  locations: Array<{
    location_id: string;
    location_name: string;
    current_stock: number;
    minimum_stock: number;
    reorder_point: number;
    last_updated: string;
    status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'overstocked';
  }>;
  total_stock: number;
  total_value: number;
  reorder_suggestions: Array<{
    location_id: string;
    location_name: string;
    suggested_quantity: number;
    urgency: 'low' | 'medium' | 'high';
  }>;
}

interface TransferRequest {
  id: string;
  from_location: string;
  to_location: string;
  items: Array<{
    product_id: string;
    product_name: string;
    quantity: number;
  }>;
  status: 'pending' | 'approved' | 'in_transit' | 'completed' | 'cancelled';
  requested_by: string;
  requested_at: string;
  estimated_arrival?: string;
}

const CentralizedInventory: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [inventoryData, setInventoryData] = useState<InventoryItem[]>([]);
  const [transferRequests, setTransferRequests] = useState<TransferRequest[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'inventory' | 'transfers' | 'analytics'>('inventory');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load centralized inventory data
  useEffect(() => {
    const loadInventoryData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📦 Loading centralized inventory...');
        
        const response = await apiCall('/api/enterprise/inventory');
        if (response.ok) {
          const data = await response.json();
          setInventoryData(data.inventory || []);
          setTransferRequests(data.transfers || []);
          console.log('✅ Centralized inventory loaded');
        }
      } catch (error) {
        console.error('❌ Error loading centralized inventory:', error);
        setError('Failed to load inventory. Using mock data.');
        
        // Fallback to mock data
        const mockInventory: InventoryItem[] = [
          {
            id: 'inv_1',
            product_name: 'Coffee Beans',
            sku: 'CB-001',
            category: 'Beverages',
            unit_cost: 12.50,
            supplier: 'Coffee Suppliers Inc',
            locations: [
              {
                location_id: 'loc_1',
                location_name: 'Downtown Restaurant',
                current_stock: 45,
                minimum_stock: 20,
                reorder_point: 30,
                last_updated: new Date().toISOString(),
                status: 'in_stock'
              },
              {
                location_id: 'loc_2',
                location_name: 'Airport Branch',
                current_stock: 8,
                minimum_stock: 25,
                reorder_point: 35,
                last_updated: new Date().toISOString(),
                status: 'low_stock'
              },
              {
                location_id: 'loc_3',
                location_name: 'Mall Food Court',
                current_stock: 0,
                minimum_stock: 15,
                reorder_point: 20,
                last_updated: new Date().toISOString(),
                status: 'out_of_stock'
              }
            ],
            total_stock: 53,
            total_value: 662.50,
            reorder_suggestions: [
              {
                location_id: 'loc_2',
                location_name: 'Airport Branch',
                suggested_quantity: 50,
                urgency: 'high'
              },
              {
                location_id: 'loc_3',
                location_name: 'Mall Food Court',
                suggested_quantity: 30,
                urgency: 'high'
              }
            ]
          },
          {
            id: 'inv_2',
            product_name: 'Burger Patties',
            sku: 'BP-001',
            category: 'Food',
            unit_cost: 3.75,
            supplier: 'Meat Distributors',
            locations: [
              {
                location_id: 'loc_1',
                location_name: 'Downtown Restaurant',
                current_stock: 120,
                minimum_stock: 50,
                reorder_point: 75,
                last_updated: new Date().toISOString(),
                status: 'in_stock'
              },
              {
                location_id: 'loc_2',
                location_name: 'Airport Branch',
                current_stock: 200,
                minimum_stock: 100,
                reorder_point: 150,
                last_updated: new Date().toISOString(),
                status: 'in_stock'
              },
              {
                location_id: 'loc_3',
                location_name: 'Mall Food Court',
                current_stock: 25,
                minimum_stock: 40,
                reorder_point: 60,
                last_updated: new Date().toISOString(),
                status: 'low_stock'
              }
            ],
            total_stock: 345,
            total_value: 1293.75,
            reorder_suggestions: [
              {
                location_id: 'loc_3',
                location_name: 'Mall Food Court',
                suggested_quantity: 80,
                urgency: 'medium'
              }
            ]
          }
        ];

        const mockTransfers: TransferRequest[] = [
          {
            id: 'transfer_1',
            from_location: 'Downtown Restaurant',
            to_location: 'Mall Food Court',
            items: [
              { product_id: 'inv_1', product_name: 'Coffee Beans', quantity: 20 }
            ],
            status: 'pending',
            requested_by: 'Mike Wilson',
            requested_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            estimated_arrival: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        ];

        setInventoryData(mockInventory);
        setTransferRequests(mockTransfers);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadInventoryData();
  }, [apiCall]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return 'bg-green-100 text-green-800';
      case 'low_stock': return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      case 'overstocked': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getTransferStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'in_transit': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredInventory = inventoryData.filter(item => {
    const locationMatch = selectedLocation === 'all' || 
      item.locations.some(loc => loc.location_id === selectedLocation);
    const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory;
    return locationMatch && categoryMatch;
  });

  const getInventoryStats = () => {
    const totalItems = inventoryData.length;
    const totalValue = inventoryData.reduce((sum, item) => sum + item.total_value, 0);
    const lowStockItems = inventoryData.filter(item => 
      item.locations.some(loc => loc.status === 'low_stock' || loc.status === 'out_of_stock')
    ).length;
    const reorderSuggestions = inventoryData.reduce((sum, item) => sum + item.reorder_suggestions.length, 0);
    
    return { totalItems, totalValue, lowStockItems, reorderSuggestions };
  };

  const stats = getInventoryStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading centralized inventory...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Centralized Inventory Control</h2>
            <p className="text-sm text-gray-500">Manage inventory across all locations</p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex bg-gray-100 rounded-md p-1">
              <button
                onClick={() => setViewMode('inventory')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'inventory' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                Inventory
              </button>
              <button
                onClick={() => setViewMode('transfers')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'transfers' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                Transfers
              </button>
              <button
                onClick={() => setViewMode('analytics')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'analytics' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                Analytics
              </button>
            </div>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors">
              <Upload className="h-4 w-4" />
              <span>Import</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalValue.toFixed(2)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Low Stock Items</p>
                <p className="text-2xl font-bold text-red-600">{stats.lowStockItems}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Reorder Suggestions</p>
                <p className="text-2xl font-bold text-orange-600">{stats.reorderSuggestions}</p>
              </div>
              <Truck className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 bg-white border-b border-gray-200">
        <div className="flex space-x-4">
          <select
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Locations</option>
            <option value="loc_1">Downtown Restaurant</option>
            <option value="loc_2">Airport Branch</option>
            <option value="loc_3">Mall Food Court</option>
          </select>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Categories</option>
            <option value="Beverages">Beverages</option>
            <option value="Food">Food</option>
            <option value="Supplies">Supplies</option>
          </select>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'inventory' && (
          <div className="p-4 space-y-4">
            {filteredInventory.map((item) => (
              <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-900">{item.product_name}</h3>
                    <p className="text-sm text-gray-600">SKU: {item.sku} • {item.category} • ${item.unit_cost}/unit</p>
                    <p className="text-sm text-gray-500">Supplier: {item.supplier}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">{item.total_stock} units</p>
                    <p className="text-sm text-gray-600">${item.total_value.toFixed(2)} total value</p>
                  </div>
                </div>

                {/* Location Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                  {item.locations.map((location) => (
                    <div key={location.location_id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{location.location_name}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(location.status)}`}>
                          {location.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <p className="text-gray-500">Current</p>
                          <p className="font-semibold">{location.current_stock}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Min</p>
                          <p className="font-semibold">{location.minimum_stock}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Reorder</p>
                          <p className="font-semibold">{location.reorder_point}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Reorder Suggestions */}
                {item.reorder_suggestions.length > 0 && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <h4 className="font-medium text-orange-900 mb-2">Reorder Suggestions</h4>
                    <div className="space-y-1">
                      {item.reorder_suggestions.map((suggestion, index) => (
                        <div key={index} className="flex justify-between items-center text-sm">
                          <span className="text-orange-800">{suggestion.location_name}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-orange-900 font-medium">{suggestion.suggested_quantity} units</span>
                            <span className={`font-medium ${getUrgencyColor(suggestion.urgency)}`}>
                              {suggestion.urgency.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {viewMode === 'transfers' && (
          <div className="p-4 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Transfer Requests</h3>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                New Transfer
              </button>
            </div>
            
            {transferRequests.map((transfer) => (
              <div key={transfer.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {transfer.from_location} → {transfer.to_location}
                    </h4>
                    <p className="text-sm text-gray-600">
                      Requested by {transfer.requested_by} • {new Date(transfer.requested_at).toLocaleDateString()}
                    </p>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${getTransferStatusColor(transfer.status)}`}>
                    {transfer.status.toUpperCase()}
                  </span>
                </div>
                
                <div className="space-y-2">
                  {transfer.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center text-sm bg-gray-50 p-2 rounded">
                      <span className="text-gray-900">{item.product_name}</span>
                      <span className="font-medium">{item.quantity} units</span>
                    </div>
                  ))}
                </div>
                
                {transfer.estimated_arrival && (
                  <p className="text-sm text-gray-600 mt-3">
                    Estimated arrival: {new Date(transfer.estimated_arrival).toLocaleDateString()}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {viewMode === 'analytics' && (
          <div className="p-4">
            <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
              <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Inventory Analytics</h3>
              <p className="text-gray-600">Advanced inventory analytics and reporting coming soon...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CentralizedInventory;
