<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Successful - RestroFlow</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            border-radius: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .success-badge {
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 16px;
        }
        h1 {
            color: #1f2937;
            margin: 0 0 8px 0;
            font-size: 24px;
        }
        .subtitle {
            color: #6b7280;
            margin: 0 0 24px 0;
        }
        .info-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #374151;
        }
        .info-value {
            color: #6b7280;
            text-align: right;
        }
        .button {
            display: inline-block;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
        }
        .security-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-notice h3 {
            color: #92400e;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .security-notice p {
            color: #b45309;
            margin: 0;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">RF</div>
            <div class="success-badge">✓ Login Successful</div>
            <h1>Welcome back, {{employeeName}}!</h1>
            <p class="subtitle">You have successfully logged into {{tenantName}}</p>
        </div>

        <div class="info-card">
            <div class="info-row">
                <span class="info-label">Login Time:</span>
                <span class="info-value">{{loginTime}}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Account Type:</span>
                <span class="info-value">{{loginType}}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Restaurant:</span>
                <span class="info-value">{{tenantName}}</span>
            </div>
            <div class="info-row">
                <span class="info-label">IP Address:</span>
                <span class="info-value">{{ipAddress}}</span>
            </div>
        </div>

        <div style="text-align: center;">
            <a href="{{dashboardUrl}}" class="button">Access Dashboard</a>
        </div>

        <div class="security-notice">
            <h3>🔒 Security Notice</h3>
            <p>If this login was not authorized by you, please contact your system administrator immediately and change your PIN.</p>
        </div>

        <div class="footer">
            <p><strong>RestroFlow POS System</strong></p>
            <p>This is an automated security notification. Please do not reply to this email.</p>
            <p>© 2024 RestroFlow. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
