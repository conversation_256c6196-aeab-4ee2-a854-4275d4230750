const http = require('http');

const productData = JSON.stringify({
  name: "Test Beer",
  price: 5.99,
  category: "beer",
  description: "Test product"
});

const options = {
  hostname: 'localhost',
  port: 4000,
  path: '/products',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': productData.length
  }
};

const req = http.request(options, res => {
  console.log(`Add Product Status: ${res.statusCode}`);
  let data = '';
  
  res.on('data', chunk => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response:', JSON.parse(data));
    
    // Now try to get all products
    http.get('http://localhost:4000/products', (getRes) => {
      let getAllData = '';
      
      getRes.on('data', (chunk) => {
        getAllData += chunk;
      });
      
      getRes.on('end', () => {
        console.log('\nAll Products:', JSON.parse(getAllData));
      });
    });
  });
});

req.on('error', error => {
  console.error('Error:', error);
});

req.write(productData);
req.end();
