# 🚀 BARPOS Production Deployment Script
# Phase 7: Production Deployment & Market Launch
# PowerShell Version for Windows

param(
    [string]$Environment = "production",
    [switch]$SkipBuild = $false,
    [switch]$SkipMigrations = $false,
    [switch]$Force = $false
)

# Color functions
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Blue }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Main deployment function
function Deploy-Production {
    Write-Info "🚀 Starting BARPOS Production Deployment"
    Write-Info "Environment: $Environment"
    Write-Info "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    
    # Check prerequisites
    Test-Prerequisites
    
    # Load environment variables
    Load-Environment
    
    # Pre-deployment checks
    if (-not $Force) {
        Confirm-Deployment
    }
    
    # Stop existing services
    Stop-Services
    
    # Build applications
    if (-not $SkipBuild) {
        Build-Applications
    }
    
    # Deploy services
    Deploy-Services
    
    # Run database migrations
    if (-not $SkipMigrations) {
        Run-Migrations
    }
    
    # Health checks
    Test-Deployment
    
    # Post-deployment tasks
    Post-Deployment
    
    Write-Success "🎉 Production deployment completed successfully!"
}

function Test-Prerequisites {
    Write-Info "📋 Checking prerequisites..."
    
    # Check Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker is required but not installed"
        exit 1
    }
    
    # Check Docker Compose
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose is required but not installed"
        exit 1
    }
    
    # Check if production.env exists
    if (-not (Test-Path "production/config/env/production.env")) {
        Write-Warning "production.env not found. Please copy from template and configure."
        Write-Info "Copy-Item 'production/config/env/production.env.template' 'production/config/env/production.env'"
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

function Load-Environment {
    Write-Info "🔧 Loading environment configuration..."
    
    # Load environment variables from file
    if (Test-Path "production/config/env/production.env") {
        Get-Content "production/config/env/production.env" | ForEach-Object {
            if ($_ -match '^([^#][^=]+)=(.*)$') {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
        Write-Success "Environment variables loaded"
    }
}

function Confirm-Deployment {
    Write-Warning "⚠️  You are about to deploy to PRODUCTION environment!"
    Write-Info "This will:"
    Write-Info "  • Stop current production services"
    Write-Info "  • Deploy new application versions"
    Write-Info "  • Run database migrations"
    Write-Info "  • Restart all services"
    
    $confirmation = Read-Host "Are you sure you want to continue? (yes/no)"
    if ($confirmation -ne "yes") {
        Write-Info "Deployment cancelled by user"
        exit 0
    }
}

function Stop-Services {
    Write-Info "🛑 Stopping existing services..."
    
    try {
        Set-Location "production"
        docker-compose down --remove-orphans
        Write-Success "Services stopped successfully"
    }
    catch {
        Write-Warning "Error stopping services: $($_.Exception.Message)"
    }
    finally {
        Set-Location ".."
    }
}

function Build-Applications {
    Write-Info "🔨 Building applications..."
    
    # Build backend
    Write-Info "Building backend application..."
    if (Test-Path "backend/Dockerfile") {
        docker build -t barpos-backend:latest ./backend
        Write-Success "Backend built successfully"
    }
    
    # Build frontend
    Write-Info "Building frontend application..."
    if (Test-Path "project") {
        Set-Location "project"
        try {
            # Install dependencies and build
            npm ci --production
            npm run build
            Write-Success "Frontend built successfully"
        }
        catch {
            Write-Error "Frontend build failed: $($_.Exception.Message)"
            exit 1
        }
        finally {
            Set-Location ".."
        }
    }
}

function Deploy-Services {
    Write-Info "🚀 Deploying services..."
    
    try {
        Set-Location "production"
        
        # Pull latest images
        docker-compose pull
        
        # Start services
        docker-compose up -d --build
        
        Write-Success "Services deployed successfully"
    }
    catch {
        Write-Error "Deployment failed: $($_.Exception.Message)"
        exit 1
    }
    finally {
        Set-Location ".."
    }
}

function Run-Migrations {
    Write-Info "🗄️ Running database migrations..."
    
    # Wait for database to be ready
    Write-Info "Waiting for database to be ready..."
    Start-Sleep -Seconds 30
    
    try {
        # Run migrations using backend container
        docker exec barpos-backend-prod npm run migrate
        Write-Success "Database migrations completed"
    }
    catch {
        Write-Warning "Migration error: $($_.Exception.Message)"
        Write-Info "This might be normal if migrations were already applied"
    }
}

function Test-Deployment {
    Write-Info "🏥 Running health checks..."
    
    # Wait for services to start
    Start-Sleep -Seconds 60
    
    # Test backend health
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:4000/health" -TimeoutSec 10
        if ($response) {
            Write-Success "Backend health check passed"
        }
    }
    catch {
        Write-Error "Backend health check failed: $($_.Exception.Message)"
    }
    
    # Test frontend health
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:80/health" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "Frontend health check passed"
        }
    }
    catch {
        Write-Error "Frontend health check failed: $($_.Exception.Message)"
    }
    
    # Test database connection
    try {
        docker exec barpos-postgres-prod pg_isready -U barpos_prod -d BARPOS_PROD
        Write-Success "Database health check passed"
    }
    catch {
        Write-Error "Database health check failed: $($_.Exception.Message)"
    }
}

function Post-Deployment {
    Write-Info "📋 Running post-deployment tasks..."
    
    # Create initial admin user if needed
    try {
        Write-Info "Checking for super admin user..."
        # This would run a script to create initial admin if none exists
        # docker exec barpos-backend-prod node scripts/create-initial-admin.js
    }
    catch {
        Write-Warning "Could not verify admin user: $($_.Exception.Message)"
    }
    
    # Send deployment notification
    Send-DeploymentNotification
    
    # Display service status
    Show-ServiceStatus
}

function Send-DeploymentNotification {
    Write-Info "📧 Sending deployment notification..."
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $message = "🚀 BARPOS Production deployment completed successfully at $timestamp"
    
    # You can add Slack, Discord, or email notifications here
    Write-Info $message
}

function Show-ServiceStatus {
    Write-Info "📊 Service Status:"
    
    try {
        Set-Location "production"
        docker-compose ps
    }
    catch {
        Write-Warning "Could not get service status"
    }
    finally {
        Set-Location ".."
    }
    
    Write-Info ""
    Write-Info "🌐 Application URLs:"
    Write-Info "  Frontend: http://localhost:80"
    Write-Info "  Backend API: http://localhost:4000"
    Write-Info "  Grafana: http://localhost:3000"
    Write-Info "  Prometheus: http://localhost:9090"
    Write-Info ""
    Write-Info "📋 Next Steps:"
    Write-Info "  1. Configure SSL certificates"
    Write-Info "  2. Set up domain DNS"
    Write-Info "  3. Configure monitoring alerts"
    Write-Info "  4. Test all functionality"
    Write-Info "  5. Begin customer onboarding"
}

# Error handling
trap {
    Write-Error "Deployment failed with error: $($_.Exception.Message)"
    Write-Info "Rolling back deployment..."
    
    try {
        Set-Location "production"
        docker-compose down
    }
    catch {
        Write-Warning "Rollback failed: $($_.Exception.Message)"
    }
    
    exit 1
}

# Main execution
try {
    Deploy-Production
}
catch {
    Write-Error "Deployment script failed: $($_.Exception.Message)"
    exit 1
}
