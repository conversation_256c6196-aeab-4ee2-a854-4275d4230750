import React, { useState, useEffect } from 'react';
import { 
  Truck, 
  MapPin, 
  Wifi, 
  WifiOff, 
  Cloud, 
  Sun, 
  CloudRain,
  Users,
  Clock,
  DollarSign,
  Battery,
  Signal,
  Navigation,
  Share2,
  AlertCircle,
  CheckCircle,
  Timer,
  Smartphone
} from 'lucide-react';

interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  preparationTime: number;
  description: string;
  ingredients: string[];
  allergens?: string[];
  available: boolean;
  popularity: number;
}

interface Order {
  id: string;
  orderNumber: number;
  customerName?: string;
  customerPhone?: string;
  items: {
    item: MenuItem;
    quantity: number;
    customizations?: string[];
  }[];
  total: number;
  orderTime: Date;
  estimatedReady: Date;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  paymentMethod: 'cash' | 'card' | 'mobile';
  paymentStatus: 'pending' | 'completed' | 'failed';
  isOnline: boolean;
}

interface Location {
  id: string;
  name: string;
  address: string;
  coordinates: { lat: number; lng: number };
  schedule: string;
  expectedCrowds: 'Low' | 'Medium' | 'High';
  permits: boolean;
}

interface WeatherData {
  temperature: number;
  condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy';
  windSpeed: number;
  humidity: number;
  recommendation: string;
}

const FoodTruckInterface: React.FC = () => {
  const [currentOrder, setCurrentOrder] = useState<any[]>([]);
  const [orderQueue, setOrderQueue] = useState<Order[]>([]);
  const [isOnline, setIsOnline] = useState<boolean>(false);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [batteryLevel, setBatteryLevel] = useState<number>(85);
  const [signalStrength, setSignalStrength] = useState<number>(3);
  const [selectedCategory, setSelectedCategory] = useState<string>('mains');
  const [customerInfo, setCustomerInfo] = useState({ name: '', phone: '' });

  // Mock menu data optimized for food truck
  const menuItems: Record<string, MenuItem[]> = {
    mains: [
      {
        id: '1',
        name: 'Gourmet Burger',
        price: 12.99,
        category: 'mains',
        preparationTime: 8,
        description: 'Grass-fed beef with artisan toppings',
        ingredients: ['Beef patty', 'Brioche bun', 'Lettuce', 'Tomato', 'Special sauce'],
        available: true,
        popularity: 95
      },
      {
        id: '2',
        name: 'Fish Tacos (3)',
        price: 14.99,
        category: 'mains',
        preparationTime: 6,
        description: 'Fresh catch with mango salsa',
        ingredients: ['White fish', 'Corn tortillas', 'Mango salsa', 'Cabbage slaw'],
        available: true,
        popularity: 88
      },
      {
        id: '3',
        name: 'BBQ Pulled Pork',
        price: 13.99,
        category: 'mains',
        preparationTime: 5,
        description: 'Slow-cooked pork with house BBQ sauce',
        ingredients: ['Pulled pork', 'BBQ sauce', 'Coleslaw', 'Brioche bun'],
        available: true,
        popularity: 92
      },
      {
        id: '4',
        name: 'Veggie Wrap',
        price: 10.99,
        category: 'mains',
        preparationTime: 4,
        description: 'Fresh vegetables with hummus',
        ingredients: ['Spinach tortilla', 'Hummus', 'Mixed vegetables', 'Sprouts'],
        available: true,
        popularity: 75
      }
    ],
    sides: [
      {
        id: '5',
        name: 'Truffle Fries',
        price: 6.99,
        category: 'sides',
        preparationTime: 4,
        description: 'Hand-cut fries with truffle oil',
        ingredients: ['Potatoes', 'Truffle oil', 'Parmesan', 'Herbs'],
        available: true,
        popularity: 90
      },
      {
        id: '6',
        name: 'Street Corn',
        price: 5.99,
        category: 'sides',
        preparationTime: 3,
        description: 'Mexican-style grilled corn',
        ingredients: ['Corn', 'Mayo', 'Cotija cheese', 'Chili powder', 'Lime'],
        available: true,
        popularity: 85
      }
    ],
    drinks: [
      {
        id: '7',
        name: 'Craft Lemonade',
        price: 3.99,
        category: 'drinks',
        preparationTime: 2,
        description: 'Fresh squeezed with herbs',
        ingredients: ['Lemons', 'Sugar', 'Fresh herbs', 'Sparkling water'],
        available: true,
        popularity: 80
      },
      {
        id: '8',
        name: 'Cold Brew Coffee',
        price: 4.99,
        category: 'drinks',
        preparationTime: 1,
        description: 'Smooth cold brew concentrate',
        ingredients: ['Cold brew concentrate', 'Ice', 'Milk (optional)'],
        available: true,
        popularity: 88
      }
    ]
  };

  // Mock locations
  const locations: Location[] = [
    {
      id: '1',
      name: 'Downtown Plaza',
      address: '123 Main St, Downtown',
      coordinates: { lat: 40.7128, lng: -74.0060 },
      schedule: '11:00 AM - 3:00 PM',
      expectedCrowds: 'High',
      permits: true
    },
    {
      id: '2',
      name: 'University Campus',
      address: '456 College Ave, Campus',
      coordinates: { lat: 40.7589, lng: -73.9851 },
      schedule: '12:00 PM - 8:00 PM',
      expectedCrowds: 'Medium',
      permits: true
    },
    {
      id: '3',
      name: 'Business District',
      address: '789 Corporate Blvd',
      coordinates: { lat: 40.7505, lng: -73.9934 },
      schedule: '11:30 AM - 2:30 PM',
      expectedCrowds: 'High',
      permits: true
    }
  ];

  // Mock weather data
  const mockWeather: WeatherData = {
    temperature: 72,
    condition: 'sunny',
    windSpeed: 8,
    humidity: 45,
    recommendation: 'Perfect weather for outdoor service!'
  };

  useEffect(() => {
    setCurrentLocation(locations[0]);
    setWeather(mockWeather);
    
    // Simulate connectivity changes
    const interval = setInterval(() => {
      setIsOnline(Math.random() > 0.1); // 90% online
      setSignalStrength(Math.floor(Math.random() * 4) + 1);
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const addToOrder = (item: MenuItem) => {
    setCurrentOrder([...currentOrder, { item, quantity: 1 }]);
  };

  const getOrderTotal = () => {
    return currentOrder.reduce((sum, orderItem) => sum + (orderItem.item.price * orderItem.quantity), 0);
  };

  const submitOrder = () => {
    if (currentOrder.length === 0) return;

    const newOrder: Order = {
      id: Date.now().toString(),
      orderNumber: 100 + orderQueue.length + 1,
      customerName: customerInfo.name || undefined,
      customerPhone: customerInfo.phone || undefined,
      items: [...currentOrder],
      total: getOrderTotal(),
      orderTime: new Date(),
      estimatedReady: new Date(Date.now() + Math.max(...currentOrder.map(item => item.item.preparationTime)) * 60000),
      status: 'pending',
      paymentMethod: 'card',
      paymentStatus: isOnline ? 'completed' : 'pending',
      isOnline
    };

    setOrderQueue([...orderQueue, newOrder]);
    setCurrentOrder([]);
    setCustomerInfo({ name: '', phone: '' });
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return <Sun className="w-5 h-5 text-yellow-500" />;
      case 'cloudy': return <Cloud className="w-5 h-5 text-gray-500" />;
      case 'rainy': return <CloudRain className="w-5 h-5 text-blue-500" />;
      default: return <Sun className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getSignalBars = (strength: number) => {
    return Array.from({ length: 4 }, (_, i) => (
      <div
        key={i}
        className={`w-1 h-3 ${i < strength ? 'bg-green-500' : 'bg-gray-300'} rounded-sm`}
      />
    ));
  };

  const categories = [
    { id: 'mains', name: 'Mains', icon: '🍔' },
    { id: 'sides', name: 'Sides', icon: '🍟' },
    { id: 'drinks', name: 'Drinks', icon: '🥤' }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-orange-100 via-red-100 to-pink-100 p-4">
      {/* Header with Status */}
      <div className="bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-2xl shadow-xl p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-xl">
              <Truck className="w-6 h-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Street Eats Mobile</h1>
              <p className="text-orange-100">Gourmet food on wheels</p>
            </div>
          </div>
          
          {/* Status Indicators */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              {isOnline ? <Wifi className="w-4 h-4" /> : <WifiOff className="w-4 h-4" />}
              <span className="text-sm">{isOnline ? 'Online' : 'Offline'}</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <Signal className="w-4 h-4" />
              <div className="flex space-x-0.5">
                {getSignalBars(signalStrength)}
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <Battery className="w-4 h-4" />
              <span className="text-sm">{batteryLevel}%</span>
            </div>
          </div>
        </div>
        
        {/* Location & Weather */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white/10 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <MapPin className="w-4 h-4" />
              <span className="font-semibold">Current Location</span>
            </div>
            {currentLocation && (
              <div>
                <div className="font-medium">{currentLocation.name}</div>
                <div className="text-sm text-orange-100">{currentLocation.address}</div>
                <div className="text-sm text-orange-100">
                  Expected crowds: {currentLocation.expectedCrowds}
                </div>
              </div>
            )}
          </div>
          
          <div className="bg-white/10 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              {weather && getWeatherIcon(weather.condition)}
              <span className="font-semibold">Weather</span>
            </div>
            {weather && (
              <div>
                <div className="font-medium">{weather.temperature}°F</div>
                <div className="text-sm text-orange-100">{weather.recommendation}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Order Queue */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-4 h-full">
            <div className="flex items-center space-x-3 mb-4">
              <Timer className="w-5 h-5 text-orange-600" />
              <h2 className="text-lg font-bold text-gray-900">Order Queue</h2>
            </div>
            
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {orderQueue.map((order) => (
                <div key={order.id} className={`p-3 rounded-lg border-2 ${
                  order.status === 'pending' ? 'border-yellow-200 bg-yellow-50' :
                  order.status === 'preparing' ? 'border-blue-200 bg-blue-50' :
                  order.status === 'ready' ? 'border-green-200 bg-green-50' :
                  'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-bold">#{order.orderNumber}</span>
                    <div className="flex items-center space-x-1">
                      {!order.isOnline && <WifiOff className="w-3 h-3 text-red-500" />}
                      <span className="text-sm font-medium">${order.total.toFixed(2)}</span>
                    </div>
                  </div>
                  
                  {order.customerName && (
                    <div className="text-sm font-medium mb-1">{order.customerName}</div>
                  )}
                  
                  <div className="text-xs text-gray-600 mb-2">
                    {order.items.map(item => `${item.quantity}x ${item.item.name}`).join(', ')}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span>{order.orderTime.toLocaleTimeString()}</span>
                    <span className="font-medium">
                      Ready: {order.estimatedReady.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Menu */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-4">
            <div className="flex items-center space-x-3 mb-4">
              <span className="text-2xl">🍔</span>
              <h2 className="text-lg font-bold text-gray-900">Menu</h2>
            </div>
            
            {/* Category Tabs */}
            <div className="flex space-x-2 mb-4">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-orange-600 to-red-500 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span>{category.icon}</span>
                  <span>{category.name}</span>
                </button>
              ))}
            </div>
            
            {/* Menu Items */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-80 overflow-y-auto">
              {menuItems[selectedCategory]?.map((item) => (
                <div
                  key={item.id}
                  onClick={() => addToOrder(item)}
                  className={`p-3 border rounded-lg transition-all duration-200 cursor-pointer ${
                    item.available
                      ? 'border-gray-200 hover:border-orange-300 hover:shadow-md'
                      : 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900">{item.name}</h3>
                        {item.popularity > 90 && (
                          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                            Popular
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                    </div>
                    <span className="text-lg font-bold text-orange-600">${item.price}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>⏱️ {item.preparationTime} min</span>
                    <span>📊 {item.popularity}% rating</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Current Order & Controls */}
        <div className="lg:col-span-1">
          <div className="space-y-4">
            {/* Customer Info */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Customer Info</h3>
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Customer name (optional)"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <input
                  type="tel"
                  placeholder="Phone number (optional)"
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Current Order */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Current Order</h3>
              
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {currentOrder.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No items in order</p>
                ) : (
                  currentOrder.map((orderItem, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900">{orderItem.item.name}</div>
                        <div className="text-sm text-gray-600">Qty: {orderItem.quantity}</div>
                      </div>
                      <span className="font-bold text-orange-600">
                        ${(orderItem.item.price * orderItem.quantity).toFixed(2)}
                      </span>
                    </div>
                  ))
                )}
              </div>
              
              {currentOrder.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold">Total:</span>
                    <span className="text-xl font-bold text-orange-600">${getOrderTotal().toFixed(2)}</span>
                  </div>
                  
                  <button
                    onClick={submitOrder}
                    className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                      isOnline
                        ? 'bg-gradient-to-r from-orange-600 to-red-500 hover:from-orange-700 hover:to-red-600 text-white transform hover:scale-105'
                        : 'bg-yellow-500 hover:bg-yellow-600 text-white'
                    }`}
                  >
                    {isOnline ? 'Submit Order' : 'Queue Order (Offline)'}
                  </button>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-4">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Quick Actions</h3>
              <div className="space-y-2">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2">
                  <Share2 className="w-4 h-4" />
                  <span>Share Location</span>
                </button>
                <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2">
                  <Navigation className="w-4 h-4" />
                  <span>Update Location</span>
                </button>
                <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2">
                  <Smartphone className="w-4 h-4" />
                  <span>Social Media</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodTruckInterface;
