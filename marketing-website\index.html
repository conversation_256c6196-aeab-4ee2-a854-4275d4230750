<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - Revolutionary Restaurant POS System | Start Your Free Trial</title>
    <meta name="description" content="Transform your restaurant with RestroFlow - the AI-powered, cloud-based POS system trusted by 500+ restaurants. Start your 30-day free trial today!">
    <meta name="keywords" content="restaurant POS, point of sale, restaurant management, AI POS system, cloud POS, restaurant software, RestroFlow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://restroflow.com/">
    <meta property="og:title" content="RestroFlow - Revolutionary Restaurant POS System">
    <meta property="og:description" content="Transform your restaurant with AI-powered POS technology. 30-day free trial.">
    <meta property="og:image" content="https://restroflow.com/images/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://restroflow.com/">
    <meta property="twitter:title" content="RestroFlow - Revolutionary Restaurant POS System">
    <meta property="twitter:description" content="Transform your restaurant with AI-powered POS technology. 30-day free trial.">
    <meta property="twitter:image" content="https://restroflow.com/images/twitter-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gradient-text { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .hero-animation { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
        .feature-card:hover { transform: translateY(-5px); transition: all 0.3s ease; }
        .pricing-card:hover { transform: scale(1.05); transition: all 0.3s ease; }
        .cta-button { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); transition: all 0.3s ease; }
        .cta-button:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3); }
    </style>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold gradient-text">RestroFlow</h1>
                    </div>
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="#features" class="text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium">Features</a>
                        <a href="#pricing" class="text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium">Pricing</a>
                        <a href="#demo" class="text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium">Demo</a>
                        <a href="#support" class="text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium">Support</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#login" class="text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium">Login</a>
                    <button onclick="startFreeTrial()" class="cta-button text-white px-6 py-2 rounded-lg text-sm font-medium">
                        Start Free Trial
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-20 pb-16 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                    Transform Your Restaurant with
                    <span class="block text-yellow-300">AI-Powered POS Technology</span>
                </h1>
                <p class="text-xl text-white mb-8 max-w-3xl mx-auto">
                    Join 500+ restaurants using RestroFlow to increase sales by 35%, reduce costs by 25%, and deliver exceptional customer experiences with our revolutionary cloud-based POS system.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="startFreeTrial()" class="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        🚀 Start 30-Day Free Trial
                    </button>
                    <button onclick="scheduleDemo()" class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300">
                        📅 Schedule Demo
                    </button>
                </div>
                <p class="text-white mt-4 text-sm">No credit card required • Setup in 5 minutes • Cancel anytime</p>
            </div>
            <div class="mt-16 hero-animation">
                <img src="https://via.placeholder.com/800x500/667eea/ffffff?text=RestroFlow+Dashboard+Preview" alt="RestroFlow Dashboard" class="mx-auto rounded-lg shadow-2xl">
            </div>
        </div>
    </section>

    <!-- Trust Indicators -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <p class="text-gray-600 text-lg">Trusted by restaurants worldwide</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">500+</div>
                    <div class="text-gray-600">Restaurants</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">99.9%</div>
                    <div class="text-gray-600">Uptime</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">$2M+</div>
                    <div class="text-gray-600">Processed Daily</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600">24/7</div>
                    <div class="text-gray-600">Support</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Everything You Need to Run Your Restaurant</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">From AI-powered analytics to seamless payment processing, RestroFlow provides all the tools you need to succeed.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature Cards -->
                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">🤖</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">AI-Powered Analytics</h3>
                    <p class="text-gray-600">Get intelligent insights into sales patterns, customer behavior, and inventory optimization with our advanced AI engine.</p>
                </div>

                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">💳</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Seamless Payments</h3>
                    <p class="text-gray-600">Accept all payment types including contactless, mobile wallets, and split payments with industry-leading security.</p>
                </div>

                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">☁️</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Cloud-Based</h3>
                    <p class="text-gray-600">Access your data anywhere, anytime. Automatic backups, updates, and 99.9% uptime guarantee.</p>
                </div>

                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">📊</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Real-Time Reports</h3>
                    <p class="text-gray-600">Monitor sales, inventory, and staff performance with beautiful, actionable reports and dashboards.</p>
                </div>

                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">🍽️</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Kitchen Display</h3>
                    <p class="text-gray-600">Streamline kitchen operations with digital order management, timing, and preparation tracking.</p>
                </div>

                <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl">🌍</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Multi-Location</h3>
                    <p class="text-gray-600">Manage multiple locations from one dashboard with centralized reporting and inventory management.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Simple, Transparent Pricing</h2>
                <p class="text-xl text-gray-600">Choose the plan that fits your restaurant's needs. All plans include 30-day free trial.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Starter Plan -->
                <div class="pricing-card bg-white rounded-xl shadow-lg p-8 border border-gray-200">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-4">$99<span class="text-lg text-gray-600">/month</span></div>
                        <p class="text-gray-600 mb-6">Perfect for small restaurants</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 1 Location</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Up to 5 Employees</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Basic POS Features</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Email Support</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Basic Analytics</li>
                    </ul>
                    <button onclick="selectPlan('starter')" class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                        Start Free Trial
                    </button>
                </div>

                <!-- Professional Plan -->
                <div class="pricing-card bg-white rounded-xl shadow-xl p-8 border-2 border-purple-500 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Professional</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-4">$299<span class="text-lg text-gray-600">/month</span></div>
                        <p class="text-gray-600 mb-6">Ideal for growing restaurants</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Up to 3 Locations</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Up to 25 Employees</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Advanced Analytics</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Phone Support</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Kitchen Display System</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Loyalty Programs</li>
                    </ul>
                    <button onclick="selectPlan('professional')" class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                        Start Free Trial
                    </button>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card bg-white rounded-xl shadow-lg p-8 border border-gray-200">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                        <div class="text-4xl font-bold text-purple-600 mb-4">$799<span class="text-lg text-gray-600">/month</span></div>
                        <p class="text-gray-600 mb-6">Complete solution for chains</p>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Unlimited Locations</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Unlimited Employees</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> AI Features</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Priority Support</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Custom Integrations</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Advanced Reporting</li>
                    </ul>
                    <button onclick="selectPlan('enterprise')" class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 gradient-bg">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl font-bold text-white mb-6">Ready to Transform Your Restaurant?</h2>
            <p class="text-xl text-white mb-8">Join thousands of restaurants already using RestroFlow to increase revenue and streamline operations.</p>
            <button onclick="startFreeTrial()" class="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-12 py-4 rounded-lg text-xl font-bold transition-all duration-300 transform hover:scale-105">
                🚀 Start Your Free Trial Now
            </button>
            <p class="text-white mt-4">30-day free trial • No setup fees • Cancel anytime</p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-2xl font-bold gradient-text mb-4">RestroFlow</h3>
                    <p class="text-gray-400">Revolutionary restaurant POS system where operations flow seamlessly.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white">Features</a></li>
                        <li><a href="#pricing" class="hover:text-white">Pricing</a></li>
                        <li><a href="#demo" class="hover:text-white">Demo</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Training</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Company</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">About</a></li>
                        <li><a href="#" class="hover:text-white">Careers</a></li>
                        <li><a href="#" class="hover:text-white">Privacy</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 RestroFlow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Lead Capture Modal -->
    <div id="trialModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl max-w-md w-full p-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Start Your Free Trial</h3>
                <form id="trialForm" onsubmit="submitTrial(event)">
                    <div class="space-y-4">
                        <input type="text" name="restaurantName" placeholder="Restaurant Name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <input type="text" name="contactName" placeholder="Your Name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <input type="email" name="email" placeholder="Email Address" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <input type="tel" name="phone" placeholder="Phone Number" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <select name="plan" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <option value="starter">Starter - $99/month</option>
                            <option value="professional" selected>Professional - $299/month</option>
                            <option value="enterprise">Enterprise - $799/month</option>
                        </select>
                    </div>
                    <div class="flex gap-4 mt-6">
                        <button type="button" onclick="closeTrialModal()" class="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="flex-1 bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700">
                            Start Free Trial
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="marketing.js"></script>
</body>
</html>
