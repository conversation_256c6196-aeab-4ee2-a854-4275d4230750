const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

/**
 * Fix Owner PIN Hashing Issue
 * This script properly hashes the owner's PIN and updates the database
 */
async function fixOwnerPin() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Fixing Owner PIN Hashing Issue...\n');
    
    // Step 1: Find the owner profile
    console.log('👤 Step 1: Finding owner profile...');
    const ownerResult = await client.query(`
      SELECT id, name, email, pin, role, tenant_id 
      FROM employees 
      WHERE email = '<EMAIL>' OR name LIKE '%System Owner%'
      ORDER BY created_at DESC
      LIMIT 1
    `);
    
    if (ownerResult.rows.length === 0) {
      console.log('❌ Owner profile not found. Please run create-owner-profile.js first.');
      return;
    }
    
    const owner = ownerResult.rows[0];
    console.log(`✅ Found owner profile: ${owner.name} (ID: ${owner.id})`);
    console.log(`   📧 Email: ${owner.email}`);
    console.log(`   🎭 Role: ${owner.role}`);
    console.log(`   🏛️ Tenant ID: ${owner.tenant_id}`);
    console.log(`   🔑 Current PIN: ${owner.pin}`);
    
    // Step 2: Hash the PIN properly
    console.log('\n🔐 Step 2: Hashing PIN properly...');
    const plainPin = '000000'; // Default owner PIN
    const saltRounds = 12; // Higher security for owner
    const hashedPin = await bcrypt.hash(plainPin, saltRounds);
    
    console.log(`✅ PIN hashed successfully`);
    console.log(`   🔑 Plain PIN: ${plainPin}`);
    console.log(`   🔒 Hashed PIN: ${hashedPin.substring(0, 20)}...`);
    
    // Step 3: Update the database
    console.log('\n💾 Step 3: Updating database...');
    const updateResult = await client.query(`
      UPDATE employees 
      SET pin = $1, pin_hash = $2, updated_at = NOW()
      WHERE id = $3
      RETURNING id, name, email, role
    `, [plainPin, hashedPin, owner.id]);
    
    if (updateResult.rows.length > 0) {
      console.log('✅ Database updated successfully!');
      console.log(`   👤 Updated user: ${updateResult.rows[0].name}`);
    } else {
      console.log('❌ Failed to update database');
      return;
    }
    
    // Step 4: Verify the fix
    console.log('\n🧪 Step 4: Verifying the fix...');
    const verifyResult = await client.query(`
      SELECT id, name, pin, pin_hash 
      FROM employees 
      WHERE id = $1
    `, [owner.id]);
    
    if (verifyResult.rows.length > 0) {
      const verifyOwner = verifyResult.rows[0];
      const isValidHash = await bcrypt.compare(plainPin, verifyOwner.pin_hash);
      
      if (isValidHash) {
        console.log('✅ PIN verification successful!');
        console.log('🔒 PIN hash is working correctly');
      } else {
        console.log('❌ PIN verification failed!');
        console.log('🔒 PIN hash is not working correctly');
        return;
      }
    }
    
    // Step 5: Test login simulation
    console.log('\n🎯 Step 5: Login simulation test...');
    const testResult = await client.query(`
      SELECT e.*, t.name as tenant_name, t.slug as tenant_slug
      FROM employees e
      LEFT JOIN tenants t ON e.tenant_id = t.id
      WHERE e.id = $1
    `, [owner.id]);
    
    if (testResult.rows.length > 0) {
      const testOwner = testResult.rows[0];
      const loginTest = await bcrypt.compare(plainPin, testOwner.pin_hash);
      
      if (loginTest) {
        console.log('✅ Login simulation successful!');
        console.log(`   👤 User: ${testOwner.name}`);
        console.log(`   🎭 Role: ${testOwner.role}`);
        console.log(`   🏛️ Tenant: ${testOwner.tenant_name} (${testOwner.tenant_slug})`);
      } else {
        console.log('❌ Login simulation failed!');
        return;
      }
    }
    
    // Step 6: Display final access information
    console.log('\n🎉 Step 6: Owner PIN Fix Complete!');
    console.log('=' .repeat(60));
    console.log('🏢 BARPOS Owner Access Information:');
    console.log(`   👤 Name: ${owner.name}`);
    console.log(`   📧 Email: ${owner.email}`);
    console.log(`   🔑 PIN: ${plainPin}`);
    console.log(`   🏛️ Tenant: ${testResult.rows[0].tenant_slug}`);
    console.log(`   🎭 Role: ${owner.role}`);
    console.log(`   ✅ Status: Ready for login`);
    console.log('');
    console.log('🌐 Login Instructions:');
    console.log('   1. Open: http://localhost:5175/super-admin.html');
    console.log('   2. Enter PIN: 000000');
    console.log('   3. Select tenant: bake---shake (or leave auto-detect)');
    console.log('   4. Click Login');
    console.log('');
    console.log('🔒 Security Reminder:');
    console.log('   ⚠️  CHANGE YOUR PIN IMMEDIATELY after first login!');
    console.log('   📱 Use the owner management system: node owner-management.js');
    console.log('   🔐 Choose a strong, unique 6-digit PIN');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Error fixing owner PIN:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Additional utility function to create a new secure PIN
async function createSecurePin() {
  const client = await pool.connect();
  
  try {
    console.log('🔐 Creating new secure PIN for owner...');
    
    // Generate a random 6-digit PIN
    const newPin = Math.floor(100000 + Math.random() * 900000).toString();
    const hashedPin = await bcrypt.hash(newPin, 12);
    
    const result = await client.query(`
      UPDATE employees 
      SET pin = $1, pin_hash = $2, updated_at = NOW()
      WHERE email = '<EMAIL>'
      RETURNING name
    `, [newPin, hashedPin]);
    
    if (result.rows.length > 0) {
      console.log('✅ New secure PIN created!');
      console.log(`🔑 Your new PIN: ${newPin}`);
      console.log('⚠️  Please save this PIN securely!');
    }
    
  } catch (error) {
    console.error('❌ Error creating secure PIN:', error);
  } finally {
    client.release();
  }
}

// Run the script
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--new-pin')) {
    createSecurePin()
      .then(() => {
        console.log('\n🎉 New secure PIN created successfully!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n💥 Failed to create new PIN:', error);
        process.exit(1);
      });
  } else {
    fixOwnerPin()
      .then(() => {
        console.log('\n🎉 Owner PIN fix completed successfully!');
        console.log('🚀 You can now log in to the Super Admin Dashboard!');
        process.exit(0);
      })
      .catch((error) => {
        console.error('\n💥 Failed to fix owner PIN:', error);
        process.exit(1);
      });
  }
}

module.exports = { fixOwnerPin, createSecurePin };
