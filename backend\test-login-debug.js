const axios = require('axios');

const baseURL = 'http://localhost:4000';

async function testLoginCombinations() {
  console.log('🔍 Testing Login Combinations...');
  console.log('=' .repeat(50));
  
  const testCombinations = [
    { pin: '123456', tenant: 'demo-restaurant' },
    { pin: '123456', tenant: 'acme-corp' },
    { pin: '123456', tenant: null },
    { pin: '1234', tenant: 'demo-restaurant' },
    { pin: '1234', tenant: 'acme-corp' },
    { pin: '1234', tenant: null },
    { pin: '567890', tenant: 'demo-restaurant' },
    { pin: '567890', tenant: 'acme-corp' },
    { pin: '567890', tenant: null }
  ];
  
  for (const combo of testCombinations) {
    console.log(`\nTesting PIN: ${combo.pin}, Tenant: ${combo.tenant || 'none'}`);
    
    try {
      const requestBody = { pin: combo.pin };
      if (combo.tenant) {
        requestBody.tenant_slug = combo.tenant;
      }
      
      const response = await axios.post(`${baseURL}/api/auth/login`, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000,
        validateStatus: function (status) {
          return status < 500;
        }
      });
      
      if (response.status === 200) {
        console.log(`✅ SUCCESS - Employee: ${response.data.employee?.name}, Role: ${response.data.employee?.role}`);
        console.log(`   Tenant: ${response.data.tenant?.name} (${response.data.tenant?.slug})`);
      } else {
        console.log(`❌ FAILED - Status: ${response.status}, Message: ${response.data?.message || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`💥 ERROR - ${error.message}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`);
      }
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('Testing complete!');
}

testLoginCombinations().catch(console.error);
