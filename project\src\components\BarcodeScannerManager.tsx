import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Scan, CheckCircle, AlertTriangle, RefreshCw, Plus, Search } from 'lucide-react';

interface ScannerDevice {
  id: string;
  name: string;
  brand: 'honeywell' | 'zebra' | 'datalogic' | 'symbol' | 'generic';
  model: string;
  connection_type: 'usb' | 'bluetooth' | 'wireless';
  status: 'connected' | 'disconnected' | 'scanning';
  supported_codes: string[];
  settings: {
    beep_enabled: boolean;
    led_enabled: boolean;
    scan_mode: 'auto' | 'manual' | 'continuous';
    decode_timeout: number;
  };
  last_scan: string;
  scan_count: number;
  error_message?: string;
}

interface ScannedItem {
  id: string;
  barcode: string;
  barcode_type: string;
  product_name?: string;
  price?: number;
  category?: string;
  timestamp: string;
  scanner_id: string;
  action_taken: 'added_to_cart' | 'product_lookup' | 'manual_override' | 'not_found';
}

const BarcodeScannerManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [scanners, setScanners] = useState<ScannerDevice[]>([]);
  const [scannedItems, setScannedItems] = useState<ScannedItem[]>([]);
  const [selectedScanner, setSelectedScanner] = useState<ScannerDevice | null>(null);
  const [activeView, setActiveView] = useState<'scanners' | 'history' | 'test'>('scanners');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testBarcode, setTestBarcode] = useState('');

  // Load scanner data
  useEffect(() => {
    const loadScannerData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📱 Loading barcode scanner data...');
        
        const [scannersResponse, historyResponse] = await Promise.all([
          apiCall('/api/hardware/scanners'),
          apiCall('/api/hardware/scan-history')
        ]);
        
        if (scannersResponse.ok && historyResponse.ok) {
          const scannersData = await scannersResponse.json();
          const historyData = await historyResponse.json();
          setScanners(scannersData);
          setScannedItems(historyData);
          console.log('✅ Barcode scanner data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading barcode scanner data:', error);
        setError('Failed to load scanner data. Using mock data.');
        
        // Fallback to mock data
        const mockScanners: ScannerDevice[] = [
          {
            id: 'scanner_1',
            name: 'Main Counter Scanner',
            brand: 'honeywell',
            model: 'Voyager 1470g',
            connection_type: 'usb',
            status: 'connected',
            supported_codes: ['UPC-A', 'UPC-E', 'EAN-13', 'EAN-8', 'Code128', 'Code39', 'QR Code', 'Data Matrix'],
            settings: {
              beep_enabled: true,
              led_enabled: true,
              scan_mode: 'auto',
              decode_timeout: 5000
            },
            last_scan: new Date(Date.now() - 30 * 1000).toISOString(),
            scan_count: 127
          },
          {
            id: 'scanner_2',
            name: 'Mobile Scanner',
            brand: 'zebra',
            model: 'CS4070',
            connection_type: 'bluetooth',
            status: 'connected',
            supported_codes: ['UPC-A', 'UPC-E', 'EAN-13', 'EAN-8', 'Code128', 'QR Code'],
            settings: {
              beep_enabled: true,
              led_enabled: false,
              scan_mode: 'manual',
              decode_timeout: 3000
            },
            last_scan: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            scan_count: 45
          },
          {
            id: 'scanner_3',
            name: 'Inventory Scanner',
            brand: 'datalogic',
            model: 'QuickScan QD2430',
            connection_type: 'wireless',
            status: 'disconnected',
            supported_codes: ['UPC-A', 'UPC-E', 'EAN-13', 'Code128', 'Code39'],
            settings: {
              beep_enabled: false,
              led_enabled: true,
              scan_mode: 'continuous',
              decode_timeout: 2000
            },
            last_scan: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            scan_count: 0,
            error_message: 'Wireless connection lost'
          }
        ];

        const mockScannedItems: ScannedItem[] = [
          {
            id: 'scan_1',
            barcode: '123456789012',
            barcode_type: 'UPC-A',
            product_name: 'Classic Burger',
            price: 12.99,
            category: 'Food',
            timestamp: new Date(Date.now() - 30 * 1000).toISOString(),
            scanner_id: 'scanner_1',
            action_taken: 'added_to_cart'
          },
          {
            id: 'scan_2',
            barcode: '987654321098',
            barcode_type: 'EAN-13',
            product_name: 'Caesar Salad',
            price: 9.99,
            category: 'Food',
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            scanner_id: 'scanner_1',
            action_taken: 'added_to_cart'
          },
          {
            id: 'scan_3',
            barcode: '555666777888',
            barcode_type: 'Code128',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            scanner_id: 'scanner_2',
            action_taken: 'not_found'
          }
        ];

        setScanners(mockScanners);
        setScannedItems(mockScannedItems);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadScannerData();
    
    // Refresh every 10 seconds for real-time updates
    const interval = setInterval(loadScannerData, 10000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getStatusColor = (status: ScannerDevice['status']) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'disconnected': return 'bg-red-100 text-red-800';
      case 'scanning': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: ScannerDevice['status']) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'disconnected': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'scanning': return <Scan className="h-4 w-4 text-blue-500 animate-pulse" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionColor = (action: ScannedItem['action_taken']) => {
    switch (action) {
      case 'added_to_cart': return 'bg-green-100 text-green-800';
      case 'product_lookup': return 'bg-blue-100 text-blue-800';
      case 'manual_override': return 'bg-yellow-100 text-yellow-800';
      case 'not_found': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const testScan = async (barcode: string) => {
    if (!barcode.trim()) {
      alert('Please enter a barcode to test');
      return;
    }

    try {
      console.log(`📱 Testing barcode scan: ${barcode}`);
      
      const response = await apiCall('/api/hardware/scanners/test-scan', {
        method: 'POST',
        body: JSON.stringify({ barcode })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Test scan successful:', result);
        
        // Add to scanned items
        const newScan: ScannedItem = {
          id: `scan_${Date.now()}`,
          barcode,
          barcode_type: 'Test',
          product_name: result.product_name,
          price: result.price,
          category: result.category,
          timestamp: new Date().toISOString(),
          scanner_id: 'test',
          action_taken: result.found ? 'product_lookup' : 'not_found'
        };
        
        setScannedItems(prev => [newScan, ...prev]);
        setTestBarcode('');
        alert(`Test scan completed: ${result.found ? 'Product found' : 'Product not found'}`);
      }
    } catch (error) {
      console.error('❌ Error testing scan:', error);
      alert('Failed to test scan. Please try again.');
    }
  };

  const getScannerStats = () => {
    const connectedScanners = scanners.filter(s => s.status === 'connected').length;
    const totalScans = scanners.reduce((sum, s) => sum + s.scan_count, 0);
    const recentScans = scannedItems.filter(item => 
      new Date(item.timestamp) > new Date(Date.now() - 60 * 60 * 1000)
    ).length;
    const successfulScans = scannedItems.filter(item => 
      item.action_taken === 'added_to_cart' || item.action_taken === 'product_lookup'
    ).length;
    
    return { connectedScanners, totalScans, recentScans, successfulScans };
  };

  const stats = getScannerStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading barcode scanners...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Barcode Scanner Manager</h2>
            <p className="text-sm text-gray-500">Real-time product scan integration</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Scanners"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Connected Scanners</p>
                <p className="text-2xl font-bold text-gray-900">{stats.connectedScanners}/{scanners.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Scans</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalScans}</p>
              </div>
              <Scan className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Recent Scans (1h)</p>
                <p className="text-2xl font-bold text-gray-900">{stats.recentScans}</p>
              </div>
              <Search className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {scannedItems.length > 0 ? Math.round((stats.successfulScans / scannedItems.length) * 100) : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'scanners', label: 'Scanners', icon: Scan },
            { id: 'history', label: 'Scan History', icon: Search },
            { id: 'test', label: 'Test Scan', icon: CheckCircle }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'scanners' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {scanners.map((scanner) => (
              <div
                key={scanner.id}
                onClick={() => setSelectedScanner(scanner)}
                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{scanner.name}</h3>
                    <p className="text-sm text-gray-600">{scanner.brand.toUpperCase()} {scanner.model}</p>
                    <p className="text-xs text-gray-500 capitalize">{scanner.connection_type} connection</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(scanner.status)}
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(scanner.status)}`}>
                      {scanner.status.toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Scan Mode</p>
                    <p className="text-sm font-medium capitalize">{scanner.settings.scan_mode}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Total Scans</p>
                    <p className="text-sm font-medium">{scanner.scan_count}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Beep</p>
                    <p className="text-sm font-medium">{scanner.settings.beep_enabled ? 'Enabled' : 'Disabled'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Last Scan</p>
                    <p className="text-sm font-medium">{new Date(scanner.last_scan).toLocaleTimeString()}</p>
                  </div>
                </div>

                {scanner.error_message && (
                  <div className="bg-red-50 border border-red-200 rounded p-2 mb-3">
                    <p className="text-xs text-red-800">{scanner.error_message}</p>
                  </div>
                )}

                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Supported Codes</p>
                  <div className="flex flex-wrap gap-1">
                    {scanner.supported_codes.slice(0, 3).map((code) => (
                      <span key={code} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {code}
                      </span>
                    ))}
                    {scanner.supported_codes.length > 3 && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        +{scanner.supported_codes.length - 3}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'history' && (
          <div className="space-y-3">
            {scannedItems.map((item) => (
              <div key={item.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Scan className="h-4 w-4 text-blue-500" />
                      <span className="font-mono text-sm">{item.barcode}</span>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {item.barcode_type}
                      </span>
                    </div>
                    <div>
                      {item.product_name ? (
                        <div>
                          <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                          <p className="text-sm text-gray-600">
                            {item.category} • ${item.price?.toFixed(2)}
                          </p>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">Product not found</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getActionColor(item.action_taken)}`}>
                      {item.action_taken.replace('_', ' ').toUpperCase()}
                    </span>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(item.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'test' && (
          <div className="max-w-md mx-auto">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Barcode Scan</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Enter Barcode</label>
                  <input
                    type="text"
                    value={testBarcode}
                    onChange={(e) => setTestBarcode(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="123456789012"
                  />
                </div>

                <div className="text-sm text-gray-600">
                  <p className="mb-2">Test with sample barcodes:</p>
                  <div className="space-y-1">
                    <button
                      onClick={() => setTestBarcode('123456789012')}
                      className="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
                    >
                      123456789012 (Classic Burger)
                    </button>
                    <button
                      onClick={() => setTestBarcode('987654321098')}
                      className="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
                    >
                      987654321098 (Caesar Salad)
                    </button>
                    <button
                      onClick={() => setTestBarcode('555666777888')}
                      className="block w-full text-left px-2 py-1 hover:bg-gray-100 rounded"
                    >
                      555666777888 (Not Found)
                    </button>
                  </div>
                </div>

                <button
                  onClick={() => testScan(testBarcode)}
                  disabled={!testBarcode.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-2 px-4 rounded-md transition-colors"
                >
                  Test Scan
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Scanner Details Modal */}
      {selectedScanner && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedScanner.name}</h3>
                <p className="text-gray-600">{selectedScanner.brand.toUpperCase()} {selectedScanner.model}</p>
              </div>
              <button
                onClick={() => setSelectedScanner(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedScanner.status)}
                  <span className={`text-sm px-2 py-1 rounded-full font-medium ${getStatusColor(selectedScanner.status)}`}>
                    {selectedScanner.status.toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Connection</p>
                <p className="text-sm font-medium capitalize">{selectedScanner.connection_type}</p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-2">Supported Barcode Types</p>
              <div className="flex flex-wrap gap-2">
                {selectedScanner.supported_codes.map((code) => (
                  <span key={code} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {code}
                  </span>
                ))}
              </div>
            </div>

            <button
              onClick={() => setSelectedScanner(null)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default BarcodeScannerManager;
