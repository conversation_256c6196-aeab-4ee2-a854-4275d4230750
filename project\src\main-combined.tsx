import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import CombinedApp from './CombinedApp';
import TenantLandingPage from './TenantLandingPage';
import { AppProvider } from './context/AppContext';
import './index.css';

const MainCombined: React.FC = () => {
  // Example: use a simple state or context to determine user type
  // In real app, this might come from auth context or API call
  const [userType, setUserType] = useState<'tenant' | 'pos' | null>(null);

  useEffect(() => {
    // TODO: Replace with real auth/user type detection logic
    // For now, default to 'pos' or 'tenant' for testing
    // e.g., setUserType('tenant');
    setUserType('pos');
  }, []);

  if (userType === null) {
    return <div>Loading...</div>;
  }

  return (
    <AppProvider>
      {userType === 'tenant' ? <TenantLandingPage /> : <CombinedApp />}
    </AppProvider>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <MainCombined />
  </React.StrictMode>
);

export default MainCombined;
