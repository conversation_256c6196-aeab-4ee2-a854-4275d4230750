const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const BASE_URL = 'http://localhost:4000';

async function testKitchenEndpoints() {
  console.log('Testing Kitchen Display System endpoints...\n');

  try {
    // Test 1: Get kitchen orders (should be empty initially)
    console.log('1. Testing GET /kitchen/orders');
    const ordersResponse = await axios.get(`${BASE_URL}/kitchen/orders`);
    console.log('✅ Kitchen orders fetched:', ordersResponse.data.length, 'orders');
    console.log('Orders:', ordersResponse.data);

    // Test 2: Create a sample order first
    console.log('\n2. Creating a sample order to test with');
    const sampleOrder = {
      id: uuidv4(), // Use proper UUID
      items: JSON.stringify([
        { id: '1', name: 'Burger', price: 12.99, quantity: 1 },
        { id: '2', name: 'Fries', price: 4.99, quantity: 1 }
      ]),
      timestamp: new Date().toISOString(),
      status: 'pending',
      total: 17.98,
      subtotal: 16.65,
      tax: 1.33,
      payment_method: 'cash',
      tip: 0,
      tab_name: 'Table 5',
      employee_id: null
    };

    const orderResponse = await axios.post(`${BASE_URL}/orders`, sampleOrder);
    console.log('✅ Sample order created:', orderResponse.data.id);
    const orderId = orderResponse.data.id;

    // Test 3: Send order to kitchen
    console.log('\n3. Testing POST /kitchen/orders/:orderId (send to kitchen)');
    const kitchenResponse = await axios.post(`${BASE_URL}/kitchen/orders/${orderId}`);
    console.log('✅ Order sent to kitchen:', kitchenResponse.data);
    const kitchenOrderId = kitchenResponse.data.id;

    // Test 4: Get kitchen orders again (should have 1 order now)
    console.log('\n4. Testing GET /kitchen/orders (after adding order)');
    const updatedOrdersResponse = await axios.get(`${BASE_URL}/kitchen/orders`);
    console.log('✅ Kitchen orders fetched:', updatedOrdersResponse.data.length, 'orders');
    console.log('Orders:', updatedOrdersResponse.data);

    // Test 5: Update kitchen order status
    console.log('\n5. Testing PUT /kitchen/orders/:orderId/status');
    const statusUpdateResponse = await axios.put(`${BASE_URL}/kitchen/orders/${kitchenOrderId}/status`, {
      status: 'preparing'
    });
    console.log('✅ Kitchen order status updated:', statusUpdateResponse.data.status);

    // Test 6: Get specific kitchen order
    console.log('\n6. Testing GET /kitchen/orders/:orderId');
    const specificOrderResponse = await axios.get(`${BASE_URL}/kitchen/orders/${kitchenOrderId}`);
    console.log('✅ Specific kitchen order fetched:', specificOrderResponse.data);

    // Test 7: Update kitchen order details
    console.log('\n7. Testing PUT /kitchen/orders/:orderId (update details)');
    const detailsUpdateResponse = await axios.put(`${BASE_URL}/kitchen/orders/${kitchenOrderId}`, {
      notes: 'Extra crispy fries',
      assigned_to: 'Chef John',
      estimated_time: 15,
      priority: 'high'
    });
    console.log('✅ Kitchen order details updated:', detailsUpdateResponse.data);

    // Test 8: Mark order as ready
    console.log('\n8. Testing status update to "ready"');
    const readyResponse = await axios.put(`${BASE_URL}/kitchen/orders/${kitchenOrderId}/status`, {
      status: 'ready'
    });
    console.log('✅ Kitchen order marked as ready:', readyResponse.data.status);

    // Test 9: Mark order as served
    console.log('\n9. Testing status update to "served"');
    const servedResponse = await axios.put(`${BASE_URL}/kitchen/orders/${kitchenOrderId}/status`, {
      status: 'served'
    });
    console.log('✅ Kitchen order marked as served:', servedResponse.data.status);

    // Test 10: Verify order is no longer in kitchen orders list
    console.log('\n10. Testing GET /kitchen/orders (after serving)');
    const finalOrdersResponse = await axios.get(`${BASE_URL}/kitchen/orders`);
    console.log('✅ Kitchen orders after serving:', finalOrdersResponse.data.length, 'orders');
    console.log('Orders:', finalOrdersResponse.data);

    console.log('\n🎉 All Kitchen Display System tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('Status:', error.response.status);
    }
  }
}

testKitchenEndpoints();
