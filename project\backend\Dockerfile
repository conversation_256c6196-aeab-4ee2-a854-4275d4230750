# RestroFlow Backend - Production Dockerfile
# Multi-stage build for optimized production image

# Stage 1: Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application (if needed)
RUN npm run build 2>/dev/null || echo "No build script found"

# Stage 2: Production stage
FROM node:18-alpine AS production

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S restroflow -u 1001

# Install production dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    postgresql-client \
    redis \
    ca-certificates \
    tzdata

# Set timezone
ENV TZ=UTC

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=restroflow:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=restroflow:nodejs /app/package*.json ./
COPY --chown=restroflow:nodejs . .

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/certificates /app/temp && \
    chown -R restroflow:nodejs /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=4000
ENV LOG_LEVEL=info

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:4000/api/health || exit 1

# Expose port
EXPOSE 4000

# Switch to non-root user
USER restroflow

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "working-server.js"]
