import React, { useState } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import Login from './components/Login';
import ProductGrid from './components/ProductGrid';
import OrderPanel from './components/OrderPanel';
import Tabs from './components/Tabs';
import OrderHistory from './components/OrderHistory';
import Reports from './components/Reports';
import Settings from './components/Settings';
import OnlineMenu from './components/OnlineMenu';
import QRCodeGenerator from './components/QRCodeGenerator';
import Inventory from './components/Inventory';
import { useAppContext } from './context/AppContext';
import { Wine } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { state } = useAppContext();
  const [activeTab, setActiveTab] = useState('pos');
  
  if (!state.isAuthenticated) {
    return <Login />;
  }
  
  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'inventory':
        return <Inventory />;
      case 'history':
        return <OrderHistory />;
      case 'reports':
        return <Reports />;
      case 'settings':
        return <Settings />;
      case 'menu':
        return <QRCodeGenerator />;
      case 'pos':
      default:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
            <div className="lg:col-span-2 bg-gray-800 rounded-lg p-4 h-full">
              <ProductGrid />
            </div>
            <div className="h-full">
              <OrderPanel />
            </div>
          </div>
        );
    }
  };
  
  return (
    <div className="flex flex-col h-full">
      <header className="bg-gray-900 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <div className="bg-purple-600 p-2 rounded-full mr-3">
            <Wine className="h-6 w-6 text-white" />
          </div>
          <h1 className="text-xl font-bold text-white">Bar POS System</h1>
        </div>
        <div className="w-56">
          <Login />
        </div>
      </header>
      
      <Tabs activeTab={activeTab} setActiveTab={setActiveTab} />
      
      <main className="flex-grow p-4 overflow-hidden">
        {renderContent()}
      </main>
    </div>
  );
};

function App() {
  return (
    <BrowserRouter>
      <AppProvider>
        <Routes>
          <Route path="/menu" element={<OnlineMenu />} />
          <Route path="/" element={
            <div className="bg-gray-900 min-h-screen text-white flex flex-col h-screen">
              <Dashboard />
            </div>
          } />
        </Routes>
      </AppProvider>
    </BrowserRouter>
  );
}

export default App;
