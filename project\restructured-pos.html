<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Restructured POS...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            max-width: 500px;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 14px;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #f87171;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Loading Restructured POS</h1>
        <div class="spinner"></div>
        <p>Setting up the restructured interface...</p>
        
        <div id="steps">
            <div class="step" id="step1">⏳ Setting localStorage flags...</div>
            <div class="step" id="step2">⏳ Configuring URL parameters...</div>
            <div class="step" id="step3">⏳ Redirecting to POS system...</div>
        </div>
        
        <div id="debug-info" style="margin-top: 20px; font-size: 12px; opacity: 0.8;">
            <!-- Debug info will be populated -->
        </div>
    </div>

    <script>
        function updateStep(stepId, status, message) {
            const step = document.getElementById(stepId);
            if (status === 'success') {
                step.innerHTML = `✅ ${message}`;
                step.className = 'step success';
            } else if (status === 'error') {
                step.innerHTML = `❌ ${message}`;
                step.className = 'step error';
            } else {
                step.innerHTML = `⏳ ${message}`;
            }
        }
        
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = `
                <strong>Debug Information:</strong><br>
                Current URL: ${window.location.href}<br>
                useRestructuredPOS: ${localStorage.getItem('useRestructuredPOS')}<br>
                useIndustryStandardPOS: ${localStorage.getItem('useIndustryStandardPOS')}<br>
                Target URL: http://localhost:5173/?industry=true&restructured=true<br>
                Timestamp: ${new Date().toLocaleTimeString()}
            `;
        }
        
        async function setupRestructuredPOS() {
            try {
                // Step 1: Set localStorage flags
                updateStep('step1', 'loading', 'Setting localStorage flags...');
                localStorage.setItem('useRestructuredPOS', 'true');
                localStorage.setItem('useIndustryStandardPOS', 'true');
                await new Promise(resolve => setTimeout(resolve, 500));
                updateStep('step1', 'success', 'localStorage flags set successfully');
                
                // Step 2: Configure URL parameters
                updateStep('step2', 'loading', 'Configuring URL parameters...');
                const targetUrl = new URL('http://localhost:5173/');
                targetUrl.searchParams.set('industry', 'true');
                targetUrl.searchParams.set('restructured', 'true');
                await new Promise(resolve => setTimeout(resolve, 500));
                updateStep('step2', 'success', 'URL parameters configured');
                
                // Step 3: Redirect
                updateStep('step3', 'loading', 'Redirecting to POS system...');
                updateDebugInfo();
                
                console.log('🚀 Redirecting to restructured POS:', targetUrl.toString());
                
                // Wait a moment then redirect
                await new Promise(resolve => setTimeout(resolve, 1000));
                window.location.href = targetUrl.toString();
                
            } catch (error) {
                console.error('Error setting up restructured POS:', error);
                updateStep('step1', 'error', 'Failed to set up restructured POS');
                updateStep('step2', 'error', 'Setup failed');
                updateStep('step3', 'error', 'Redirect failed');
                
                // Show manual instructions
                setTimeout(() => {
                    document.querySelector('.container').innerHTML = `
                        <h1>❌ Automatic Setup Failed</h1>
                        <p>Please follow these manual steps:</p>
                        <ol style="text-align: left;">
                            <li>Open browser console (F12)</li>
                            <li>Run: <code>localStorage.setItem('useRestructuredPOS', 'true')</code></li>
                            <li>Run: <code>localStorage.setItem('useIndustryStandardPOS', 'true')</code></li>
                            <li>Visit: <a href="http://localhost:5173/?industry=true&restructured=true" style="color: #60a5fa;">http://localhost:5173/?industry=true&restructured=true</a></li>
                        </ol>
                        <button onclick="window.location.href='http://localhost:5173/?industry=true&restructured=true'" 
                                style="background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; margin-top: 20px;">
                            Try Direct Link
                        </button>
                    `;
                }, 2000);
            }
        }
        
        // Start the setup process when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Restructured POS setup page loaded');
            updateDebugInfo();
            
            // Start setup after a brief delay
            setTimeout(setupRestructuredPOS, 1000);
        });
    </script>
</body>
</html>
