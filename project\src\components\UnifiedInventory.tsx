import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Package, AlertTriangle, XCircle, DollarSign, Plus, Edit, Trash2, Download, Search, RefreshCw } from 'lucide-react';

interface InventoryItem {
  id: string;
  product_name: string;
  current_stock: number;
  minimum_stock: number;
  reorder_point: number;
  unit_cost: number;
  supplier: string;
  last_updated: string;
}

const UnifiedInventory: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [inventoryData, setInventoryData] = useState<InventoryItem[]>([]);
  const [filteredData, setFilteredData] = useState<InventoryItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  // Load inventory data
  useEffect(() => {
    const loadInventory = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📦 Loading inventory...');
        
        const response = await apiCall('/api/inventory');
        if (response.ok) {
          const data = await response.json();
          setInventoryData(data);
          setFilteredData(data);
          console.log('✅ Inventory loaded:', data.length, 'items');
        }
      } catch (error) {
        console.error('❌ Error loading inventory:', error);
        setError('Failed to load inventory. Using mock data.');
        
        // Fallback to mock data
        const mockInventory = [
          {
            id: '1',
            product_name: 'Coffee',
            current_stock: 45,
            minimum_stock: 10,
            reorder_point: 15,
            unit_cost: 2.50,
            supplier: 'Coffee Suppliers Inc',
            last_updated: new Date().toISOString()
          },
          {
            id: '2',
            product_name: 'Sandwich Bread',
            current_stock: 8,
            minimum_stock: 20,
            reorder_point: 25,
            unit_cost: 1.25,
            supplier: 'Bakery Supply Co',
            last_updated: new Date().toISOString()
          },
          {
            id: '3',
            product_name: 'Burger Patties',
            current_stock: 2,
            minimum_stock: 15,
            reorder_point: 20,
            unit_cost: 3.75,
            supplier: 'Meat Distributors',
            last_updated: new Date().toISOString()
          }
        ];
        setInventoryData(mockInventory);
        setFilteredData(mockInventory);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadInventory();
  }, [apiCall]);

  // Filter data based on search and status
  useEffect(() => {
    let filtered = inventoryData;

    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.supplier.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterStatus !== 'all') {
      if (filterStatus === 'low') {
        filtered = filtered.filter(item => item.current_stock <= item.minimum_stock);
      } else if (filterStatus === 'out') {
        filtered = filtered.filter(item => item.current_stock === 0);
      } else if (filterStatus === 'reorder') {
        filtered = filtered.filter(item => item.current_stock <= item.reorder_point);
      }
    }

    setFilteredData(filtered);
  }, [inventoryData, searchTerm, filterStatus]);

  const getStockStatus = (item: InventoryItem) => {
    if (item.current_stock === 0) return { status: 'out', label: 'Out of Stock', color: 'text-red-500' };
    if (item.current_stock <= item.minimum_stock) return { status: 'low', label: 'Low Stock', color: 'text-yellow-500' };
    if (item.current_stock <= item.reorder_point) return { status: 'reorder', label: 'Reorder Soon', color: 'text-orange-500' };
    return { status: 'good', label: 'In Stock', color: 'text-green-500' };
  };

  const getStats = () => {
    const totalItems = inventoryData.length;
    const lowStockCount = inventoryData.filter(item => item.current_stock <= item.minimum_stock).length;
    const outOfStockCount = inventoryData.filter(item => item.current_stock === 0).length;
    const totalValue = inventoryData.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0);
    
    return { totalItems, lowStockCount, outOfStockCount, totalValue };
  };

  const stats = getStats();

  const updateStock = async (itemId: string, newStock: number) => {
    try {
      console.log(`📦 Updating stock for item ${itemId} to ${newStock}`);
      
      const response = await apiCall(`/api/inventory/${itemId}`, {
        method: 'PUT',
        body: JSON.stringify({ current_stock: newStock })
      });
      
      if (response.ok) {
        // Update local state
        setInventoryData(prev => prev.map(item => 
          item.id === itemId ? { ...item, current_stock: newStock, last_updated: new Date().toISOString() } : item
        ));
        console.log('✅ Stock updated successfully');
      }
    } catch (error) {
      console.error('❌ Error updating stock:', error);
      alert('Failed to update stock. Please try again.');
    }
  };

  const exportReport = () => {
    const headers = ['Product', 'Current Stock', 'Min Stock', 'Reorder Point', 'Unit Cost', 'Total Value', 'Status', 'Supplier'];
    const csvContent = [
      headers.join(','),
      ...inventoryData.map(item => {
        const status = getStockStatus(item);
        return [
          item.product_name,
          item.current_stock,
          item.minimum_stock,
          item.reorder_point,
          item.unit_cost.toFixed(2),
          (item.current_stock * item.unit_cost).toFixed(2),
          status.label,
          item.supplier
        ].join(',');
      })
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading inventory...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Inventory Management</h2>
            <p className="text-sm text-gray-500">Track stock levels and manage inventory</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Item</span>
            </button>
            <button
              onClick={exportReport}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
              </div>
              <Package className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Low Stock</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.lowStockCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Out of Stock</p>
                <p className="text-2xl font-bold text-red-600">{stats.outOfStockCount}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Value</p>
                <p className="text-2xl font-bold text-green-600">${stats.totalValue.toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="p-4 bg-white border-b border-gray-200">
        <div className="flex space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search products or suppliers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Items</option>
            <option value="low">Low Stock</option>
            <option value="out">Out of Stock</option>
            <option value="reorder">Needs Reorder</option>
          </select>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Product</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Current Stock</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Min Stock</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Unit Cost</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Total Value</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {filteredData.map((item) => {
              const status = getStockStatus(item);
              return (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div>
                      <div className="font-medium text-gray-900">{item.product_name}</div>
                      <div className="text-sm text-gray-500">{item.supplier}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <input
                      type="number"
                      value={item.current_stock}
                      onChange={(e) => updateStock(item.id, parseInt(e.target.value) || 0)}
                      className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </td>
                  <td className="px-4 py-3 text-gray-900">{item.minimum_stock}</td>
                  <td className="px-4 py-3 text-gray-900">${item.unit_cost.toFixed(2)}</td>
                  <td className="px-4 py-3 text-gray-900">${(item.current_stock * item.unit_cost).toFixed(2)}</td>
                  <td className="px-4 py-3">
                    <span className={`text-sm font-medium ${status.color}`}>
                      {status.label}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => updateStock(item.id, item.reorder_point)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                        title="Restock to reorder point"
                      >
                        Restock
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-3">
        <p className="text-gray-600 text-sm text-center">
          Showing {filteredData.length} of {inventoryData.length} items
        </p>
      </div>
    </div>
  );
};

export default UnifiedInventory;
