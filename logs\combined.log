{"level":"info","message":"Enhanced POS Backend server running on http://localhost:4000","service":"pos-backend","timestamp":"2025-05-31T02:30:04.813Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:31:14 +0000] \"GET / HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:31:14.617Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:31:14 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win<PERSON>; x64) AppleWebKit/537.36 (K<PERSON><PERSON>, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:31:14.845Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:31:48 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:31:48.711Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:31:55.678Z","type":"entity.parse.failed"}
{"level":"info","message":"::1 - - [31/May/2025:02:31:55 +0000] \"POST /api/employees/add-update HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:31:55.681Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:32:09.249Z","type":"entity.parse.failed"}
{"level":"info","message":"::1 - - [31/May/2025:02:32:09 +0000] \"POST /api/employees/add-update HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:32:09.251Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:32:32 +0000] \"GET /index.html HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:32:32.518Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:32:37 +0000] \"GET /pos.html HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:32:37.579Z"}
{"code":"22P02","file":"uuid.c","length":143,"level":"error","line":"141","message":"Error adding/updating employee: invalid input syntax for type uuid: \"test-tenant\"","name":"error","routine":"string_to_uuid","service":"pos-backend","severity":"ERROR","stack":"error: invalid input syntax for type uuid: \"test-tenant\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:458:22","timestamp":"2025-05-31T02:32:58.968Z","where":"unnamed portal parameter $5 = '...'"}
{"level":"info","message":"::1 - - [31/May/2025:02:32:58 +0000] \"POST /api/employees/add-update HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:32:58.969Z"}
{"code":"23503","constraint":"employees_tenant_id_fkey","detail":"Key (tenant_id)=(123e4567-e89b-12d3-a456-************) is not present in table \"tenants\".","file":"ri_triggers.c","length":301,"level":"error","line":"2610","message":"Error adding/updating employee: insert or update on table \"employees\" violates foreign key constraint \"employees_tenant_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"pos-backend","severity":"ERROR","stack":"error: insert or update on table \"employees\" violates foreign key constraint \"employees_tenant_id_fkey\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:458:22","table":"employees","timestamp":"2025-05-31T02:34:23.387Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:34:23 +0000] \"POST /api/employees/add-update HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:34:23.389Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:34:48.858Z","type":"entity.parse.failed"}
{"level":"info","message":"::1 - - [31/May/2025:02:34:48 +0000] \"POST /api/tenants HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:34:48.860Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:35:14 +0000] \"POST /api/tenants HTTP/1.1\" 201 263 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:35:14.651Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:36:09 +0000] \"POST /api/employees/add-update HTTP/1.1\" 201 155 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:36:09.657Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:36:18 +0000] \"POST /api/employees/add-update HTTP/1.1\" 201 155 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:36:18.877Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:49:31.746Z","type":"entity.parse.failed"}
{"level":"info","message":"::1 - - [31/May/2025:02:49:31 +0000] \"POST /employees/validate HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"pos-backend","timestamp":"2025-05-31T02:49:31.749Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:54:57 +0000] \"GET /pos.html HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:54:57.932Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:57:57 +0000] \"OPTIONS /auth/login HTTP/1.1\" 204 0 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:57:57.775Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:57:57 +0000] \"POST /auth/login HTTP/1.1\" 404 150 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:57:57.778Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:58:06 +0000] \"GET /api/tenants HTTP/1.1\" 404 150 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:58:06.732Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:58:06 +0000] \"GET /api/tenants HTTP/1.1\" 404 150 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:58:06.734Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:59:28 +0000] \"GET /pos.html HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:59:28.491Z"}
{"level":"info","message":"::1 - - [31/May/2025:02:59:29 +0000] \"GET /pos.html HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T02:59:29.160Z"}
{"level":"info","message":"Enhanced POS Backend server running on http://localhost:4000","service":"pos-backend","timestamp":"2025-05-31T22:02:28.005Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:02:31 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:02:31.339Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:02:31 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:02:31.716Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:03:23 +0000] \"GET /api HTTP/1.1\" 404 142 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:03:23.319Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /employees HTTP/1.1\" 404 148 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.662Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /categories HTTP/1.1\" 404 149 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.667Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /floor-layout HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.669Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /products HTTP/1.1\" 404 147 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.672Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /settings HTTP/1.1\" 404 147 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.685Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /employees HTTP/1.1\" 404 148 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.696Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /categories HTTP/1.1\" 404 149 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.700Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /floor-layout HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.705Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /products HTTP/1.1\" 404 147 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.714Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:08:58 +0000] \"GET /settings HTTP/1.1\" 404 147 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:08:58.719Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.314Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.324Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.327Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.332Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.335Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.338Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.343Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.350Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.353Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:10:20 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:10:20.358Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.939Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.950Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.954Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.956Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.959Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.961Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.973Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.975Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.977Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:11:56 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:11:56.980Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:12:08 +0000] \"GET /api HTTP/1.1\" 404 142 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:12:08.608Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.134Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.926Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.935Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.939Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.942Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.947Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.955Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.969Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.973Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.979Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:38:30 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:38:30.982Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.293Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.304Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.310Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.321Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.326Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.339Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.346Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.351Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.353Z"}
{"level":"info","message":"::1 - - [31/May/2025:22:45:38 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T22:45:38.355Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.315Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.316Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.317Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.317Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.319Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.320Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.321Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.322Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.324Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:34 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:34.325Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:51 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:51.254Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:15:51 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:15:51.256Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.526Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.541Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.542Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.543Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.544Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.545Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.551Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.554Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.556Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:22:51 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:22:51.557Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:07 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:07.980Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:07 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:07.991Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:07 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:07.993Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:07 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:07.996Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:07 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:07.998Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:08 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:08.001Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:08 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:08.012Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:08 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:08.015Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:08 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:08.017Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:08 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:08.019Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.631Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.635Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.637Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.638Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.656Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.660Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.669Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.673Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.676Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:10 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:10.679Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.179Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.180Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.180Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.181Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.181Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.183Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.186Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.186Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.187Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:23:58 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:23:58.187Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.792Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.796Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.797Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.798Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.806Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.845Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.868Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.880Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.903Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:36 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:36.910Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.201Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.217Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.234Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.246Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.261Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.270Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.281Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.295Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.312Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:44:45 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:44:45.330Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.732Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.734Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.736Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.737Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.739Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.786Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.795Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.820Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.839Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:03 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:03.850Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.385Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.420Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.449Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.468Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.499Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.523Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.543Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.575Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.600Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:12 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:12.635Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.680Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.685Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.691Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.695Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.703Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.731Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.748Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.771Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.783Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:20 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:20.803Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.387Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.408Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.412Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.423Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.428Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.465Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.489Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.499Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.519Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:30 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:30.526Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.236Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.273Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.307Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.341Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.369Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.404Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.430Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.468Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.502Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:38 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Cypress/14.4.0 Chrome/130.0.6723.137 Electron/33.2.1 Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:38.534Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.812Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.821Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.823Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.825Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.826Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.832Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.837Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.845Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.850Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:45:54 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:45:54.852Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:21 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:21.421Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:22 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:22.114Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:22 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:22.328Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:22 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:22.495Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:23 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:23.986Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:24 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:24.185Z"}
{"level":"info","message":"::1 - - [31/May/2025:23:46:24 +0000] \"GET / HTTP/1.1\" 304 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-05-31T23:46:24.370Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.193Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.197Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.197Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.203Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.204Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.205Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.207Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.208Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.208Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:02 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:02.209Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.804Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.806Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.806Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.807Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.809Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.811Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.813Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.814Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.814Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:09 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:09.816Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.626Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.627Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.628Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.629Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.667Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.672Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.689Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.694Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.696Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:16 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:16.703Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.852Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.854Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.856Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.857Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.860Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/employees HTTP/1.1\" 401 33 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.862Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/settings HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.863Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/products HTTP/1.1\" 404 151 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.864Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/categories HTTP/1.1\" 404 153 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.868Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:17 +0000] \"GET /api/floor-layout HTTP/1.1\" 404 155 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:17.869Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:34 +0000] \"OPTIONS /auth/login HTTP/1.1\" 204 0 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:34.546Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:02:47:34 +0000] \"POST /auth/login HTTP/1.1\" 404 150 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T02:47:34.583Z"}
{"level":"info","message":"Enhanced POS Backend server running on http://localhost:4000","service":"pos-backend","timestamp":"2025-06-01T03:36:27.014Z"}
{"level":"info","message":"::1 - - [01/Jun/2025:03:36:30 +0000] \"GET / HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"pos-backend","timestamp":"2025-06-01T03:36:30.251Z"}
{"level":"info","message":"🚀 Phase 3 Enhanced Multi-Tenant POS Server running on port 4000","service":"pos-backend-phase3","timestamp":"2025-06-01T03:37:09.773Z"}
{"level":"info","message":"📊 Features: Analytics, Inventory, CRM, Scheduling, Equipment Management","service":"pos-backend-phase3","timestamp":"2025-06-01T03:37:09.776Z"}
{"level":"info","message":"🔒 Security: Rate limiting, JWT auth, audit logging, input validation","service":"pos-backend-phase3","timestamp":"2025-06-01T03:37:09.776Z"}
{"level":"info","message":"🏢 Multi-tenant: Complete data isolation with advanced features","service":"pos-backend-phase3","timestamp":"2025-06-01T03:37:09.776Z"}
{"ip":"::1","level":"info","message":"GET /","service":"pos-backend-phase3","timestamp":"2025-06-01T03:37:27.423Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
