import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  ShoppingCart, 
  Users, 
  Clock, 
  DollarSign,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Receipt,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Table, OrderItem, Product } from '../types';

interface FloorLayoutPOSIntegrationProps {
  selectedTable: Table;
  onBackToFloor: () => void;
  onOrderComplete: (tableId: string, orderData: any) => void;
}

interface OrderState {
  items: OrderItem[];
  subtotal: number;
  tax: number;
  total: number;
  paymentMethod: string;
  tip: number;
}

const FloorLayoutPOSIntegration: React.FC<FloorLayoutPOSIntegrationProps> = ({
  selectedTable,
  onBackToFloor,
  onOrderComplete
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [order, setOrder] = useState<OrderState>({
    items: [],
    subtotal: 0,
    tax: 0,
    total: 0,
    paymentMethod: 'cash',
    tip: 0
  });
  const [showPayment, setShowPayment] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProducts();
    // Load existing order if table has one
    if (selectedTable.currentOrderId) {
      loadExistingOrder(selectedTable.currentOrderId);
    }
  }, [selectedTable]);

  useEffect(() => {
    calculateTotals();
  }, [order.items, order.tip]);

  const fetchProducts = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/products', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setProducts(data);
      
      // Extract unique categories
      const uniqueCategories = [...new Set(data.map((p: Product) => p.category))];
      setCategories(uniqueCategories);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching products:', error);
      setLoading(false);
    }
  };

  const loadExistingOrder = async (orderId: string) => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/orders/${orderId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const orderData = await response.json();
      
      setOrder(prev => ({
        ...prev,
        items: orderData.items || []
      }));
    } catch (error) {
      console.error('Error loading existing order:', error);
    }
  };

  const calculateTotals = () => {
    const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + tax + order.tip;

    setOrder(prev => ({
      ...prev,
      subtotal,
      tax,
      total
    }));
  };

  const addToOrder = (product: Product) => {
    setOrder(prev => {
      const existingItem = prev.items.find(item => item.id === product.id);
      
      if (existingItem) {
        return {
          ...prev,
          items: prev.items.map(item =>
            item.id === product.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          )
        };
      } else {
        return {
          ...prev,
          items: [...prev.items, {
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            category: product.category,
            notes: ''
          }]
        };
      }
    });
  };

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromOrder(itemId);
      return;
    }

    setOrder(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    }));
  };

  const removeFromOrder = (itemId: string) => {
    setOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  const processPayment = async () => {
    try {
      const token = localStorage.getItem('authToken');
      
      // Create order
      const orderData = {
        tableId: selectedTable.id,
        items: order.items,
        subtotal: order.subtotal,
        tax: order.tax,
        tip: order.tip,
        total: order.total,
        paymentMethod: order.paymentMethod,
        guestCount: selectedTable.guestCount,
        serverAssigned: selectedTable.serverAssigned
      };

      const response = await fetch('http://localhost:4000/api/orders', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const newOrder = await response.json();
        
        // Update table status
        await fetch(`http://localhost:4000/api/floor/tables/${selectedTable.id}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: 'occupied',
            substatus: 'eating',
            currentOrderId: newOrder.id,
            orderTotal: order.total,
            orderItems: order.items.length
          })
        });

        onOrderComplete(selectedTable.id, newOrder);
        onBackToFloor();
      }
    } catch (error) {
      console.error('Error processing payment:', error);
    }
  };

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(p => p.category === selectedCategory);

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-300">Loading menu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBackToFloor}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div>
              <h2 className="text-xl font-semibold text-white">Table {selectedTable.number}</h2>
              <p className="text-gray-400 text-sm">
                {selectedTable.guestCount || 0}/{selectedTable.seats} guests
                {selectedTable.serverName && ` • Server: ${selectedTable.serverName}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-white font-semibold">${order.total.toFixed(2)}</div>
              <div className="text-gray-400 text-sm">{order.items.length} items</div>
            </div>
            {order.items.length > 0 && (
              <button
                onClick={() => setShowPayment(true)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
              >
                <CreditCard className="h-4 w-4" />
                <span>Pay</span>
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Menu Section */}
        <div className="flex-1 p-4">
          {/* Category Filter */}
          <div className="flex space-x-2 mb-4 overflow-x-auto">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-md whitespace-nowrap ${
                selectedCategory === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              All Items
            </button>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-md whitespace-nowrap ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {filteredProducts.map(product => (
              <button
                key={product.id}
                onClick={() => addToOrder(product)}
                className="bg-gray-800 p-4 rounded-lg hover:bg-gray-700 transition-colors text-left"
              >
                <h3 className="text-white font-medium mb-2">{product.name}</h3>
                <p className="text-gray-400 text-sm mb-2">{product.category}</p>
                <p className="text-green-400 font-semibold">${product.price.toFixed(2)}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
          <h3 className="text-white font-semibold mb-4 flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Current Order
          </h3>

          {order.items.length === 0 ? (
            <div className="text-center text-gray-400 py-8">
              <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No items in order</p>
              <p className="text-sm">Select items from the menu to start</p>
            </div>
          ) : (
            <div className="space-y-3">
              {order.items.map(item => (
                <div key={item.id} className="bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-white font-medium">{item.name}</h4>
                    <button
                      onClick={() => removeFromOrder(item.id)}
                      className="text-gray-400 hover:text-red-400"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="bg-gray-600 text-white w-6 h-6 rounded flex items-center justify-center hover:bg-gray-500"
                      >
                        <Minus className="h-3 w-3" />
                      </button>
                      <span className="text-white font-medium w-8 text-center">{item.quantity}</span>
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="bg-gray-600 text-white w-6 h-6 rounded flex items-center justify-center hover:bg-gray-500"
                      >
                        <Plus className="h-3 w-3" />
                      </button>
                    </div>
                    <span className="text-green-400 font-semibold">
                      ${(item.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              ))}

              {/* Order Totals */}
              <div className="border-t border-gray-600 pt-3 space-y-2">
                <div className="flex justify-between text-gray-300">
                  <span>Subtotal:</span>
                  <span>${order.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-gray-300">
                  <span>Tax:</span>
                  <span>${order.tax.toFixed(2)}</span>
                </div>
                {order.tip > 0 && (
                  <div className="flex justify-between text-gray-300">
                    <span>Tip:</span>
                    <span>${order.tip.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-white font-semibold text-lg border-t border-gray-600 pt-2">
                  <span>Total:</span>
                  <span>${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      {showPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Process Payment</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">
                    Payment Method
                  </label>
                  <select
                    value={order.paymentMethod}
                    onChange={(e) => setOrder(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Credit/Debit Card</option>
                    <option value="mobile">Mobile Payment</option>
                  </select>
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">
                    Tip Amount
                  </label>
                  <div className="grid grid-cols-4 gap-2 mb-2">
                    {[15, 18, 20, 25].map(percentage => {
                      const tipAmount = order.subtotal * (percentage / 100);
                      return (
                        <button
                          key={percentage}
                          onClick={() => setOrder(prev => ({ ...prev, tip: tipAmount }))}
                          className={`py-2 px-3 rounded text-sm ${
                            Math.abs(order.tip - tipAmount) < 0.01
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                          }`}
                        >
                          {percentage}%
                        </button>
                      );
                    })}
                  </div>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={order.tip}
                    onChange={(e) => setOrder(prev => ({ ...prev, tip: parseFloat(e.target.value) || 0 }))}
                    className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Custom tip amount"
                  />
                </div>

                <div className="bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between text-white font-semibold">
                    <span>Final Total:</span>
                    <span>${order.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowPayment(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={processPayment}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Process Payment</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloorLayoutPOSIntegration;
