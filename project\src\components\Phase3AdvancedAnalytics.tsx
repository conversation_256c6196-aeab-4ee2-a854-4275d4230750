import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>3, 
  T<PERSON>dingUp, 
  DollarSign, 
  Users, 
  ShoppingCart, 
  Clock, 
  Target, 
  Brain, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Activity
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Toolt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  Line<PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Cell,
  Pie,
  AreaChart,
  Area
} from 'recharts';

interface AdvancedAnalyticsData {
  realtime_metrics: {
    current_revenue: number;
    orders_today: number;
    active_tables: number;
    avg_wait_time: number;
    customer_satisfaction: number;
    staff_efficiency: number;
  };
  revenue_analytics: {
    hourly_revenue: Array<{
      hour: number;
      revenue: number;
      orders: number;
      avg_order_value: number;
    }>;
    daily_trends: Array<{
      date: string;
      revenue: number;
      orders: number;
      growth_rate: number;
    }>;
    monthly_comparison: Array<{
      month: string;
      current_year: number;
      previous_year: number;
      growth: number;
    }>;
  };
  customer_analytics: {
    demographics: {
      age_groups: Array<{ group: string; percentage: number; value: number }>;
      visit_frequency: Array<{ frequency: string; count: number; revenue: number }>;
      spending_patterns: Array<{ range: string; customers: number; avg_spend: number }>;
    };
    behavior: {
      peak_hours: Array<{ hour: number; customer_count: number; satisfaction: number }>;
      popular_items: Array<{ item: string; orders: number; rating: number }>;
      table_preferences: Array<{ section: string; preference_score: number; avg_duration: number }>;
    };
    loyalty: {
      retention_rate: number;
      repeat_customers: number;
      churn_risk: Array<{ customer_id: string; name: string; risk_score: number; last_visit: string }>;
    };
  };
  operational_analytics: {
    kitchen_performance: {
      avg_prep_time: number;
      order_accuracy: number;
      waste_percentage: number;
      efficiency_score: number;
    };
    staff_performance: Array<{
      staff_id: string;
      name: string;
      orders_served: number;
      avg_service_time: number;
      customer_rating: number;
      sales_generated: number;
    }>;
    inventory_insights: Array<{
      item: string;
      current_stock: number;
      usage_rate: number;
      reorder_point: number;
      cost_optimization: number;
    }>;
  };
  predictive_insights: {
    revenue_forecast: Array<{
      date: string;
      predicted_revenue: number;
      confidence: number;
      factors: string[];
    }>;
    demand_prediction: Array<{
      item: string;
      predicted_demand: number;
      current_stock: number;
      action_needed: string;
    }>;
    staffing_optimization: Array<{
      date: string;
      shift: string;
      recommended_staff: number;
      current_scheduled: number;
      efficiency_gain: number;
    }>;
  };
  ai_recommendations: Array<{
    id: string;
    type: 'revenue' | 'cost' | 'customer' | 'operational';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    impact: string;
    confidence: number;
    action_items: string[];
    estimated_benefit: number;
  }>;
}

const Phase3AdvancedAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AdvancedAnalyticsData | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'revenue' | 'customers' | 'operations' | 'predictions' | 'ai'>('overview');
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month' | 'quarter'>('today');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
    
    // Auto-refresh every 30 seconds if enabled
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchAnalyticsData, 30000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timeRange, autoRefresh]);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('authToken');
      
      // In production, this would call the real API
      // const response = await fetch(`http://localhost:4000/api/analytics/advanced?range=${timeRange}`, {
      //   headers: { 'Authorization': `Bearer ${token}` }
      // });
      
      // Mock data for demonstration
      const mockData: AdvancedAnalyticsData = {
        realtime_metrics: {
          current_revenue: 2847.50,
          orders_today: 127,
          active_tables: 18,
          avg_wait_time: 12,
          customer_satisfaction: 4.7,
          staff_efficiency: 87
        },
        revenue_analytics: {
          hourly_revenue: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            revenue: Math.random() * 500 + 100,
            orders: Math.floor(Math.random() * 20 + 5),
            avg_order_value: Math.random() * 30 + 15
          })),
          daily_trends: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            revenue: Math.random() * 3000 + 1000,
            orders: Math.floor(Math.random() * 150 + 50),
            growth_rate: (Math.random() - 0.5) * 20
          })),
          monthly_comparison: [
            { month: 'Jan', current_year: 45000, previous_year: 42000, growth: 7.1 },
            { month: 'Feb', current_year: 48000, previous_year: 44000, growth: 9.1 },
            { month: 'Mar', current_year: 52000, previous_year: 47000, growth: 10.6 },
            { month: 'Apr', current_year: 49000, previous_year: 45000, growth: 8.9 },
            { month: 'May', current_year: 55000, previous_year: 48000, growth: 14.6 },
            { month: 'Jun', current_year: 58000, previous_year: 51000, growth: 13.7 }
          ]
        },
        customer_analytics: {
          demographics: {
            age_groups: [
              { group: '18-25', percentage: 25, value: 1250 },
              { group: '26-35', percentage: 35, value: 1750 },
              { group: '36-45', percentage: 22, value: 1100 },
              { group: '46-55', percentage: 12, value: 600 },
              { group: '55+', percentage: 6, value: 300 }
            ],
            visit_frequency: [
              { frequency: 'First Time', count: 45, revenue: 1350 },
              { frequency: 'Occasional', count: 67, revenue: 2010 },
              { frequency: 'Regular', count: 89, revenue: 3560 },
              { frequency: 'VIP', count: 23, revenue: 1840 }
            ],
            spending_patterns: [
              { range: '$0-25', customers: 45, avg_spend: 18.50 },
              { range: '$25-50', customers: 78, avg_spend: 37.25 },
              { range: '$50-75', customers: 34, avg_spend: 62.80 },
              { range: '$75+', customers: 19, avg_spend: 95.40 }
            ]
          },
          behavior: {
            peak_hours: Array.from({ length: 24 }, (_, i) => ({
              hour: i,
              customer_count: Math.floor(Math.random() * 50 + 10),
              satisfaction: Math.random() * 1 + 4
            })),
            popular_items: [
              { item: 'Margherita Pizza', orders: 89, rating: 4.8 },
              { item: 'Caesar Salad', orders: 67, rating: 4.6 },
              { item: 'Grilled Chicken', orders: 78, rating: 4.7 },
              { item: 'Fish & Chips', orders: 56, rating: 4.5 },
              { item: 'Chocolate Cake', orders: 45, rating: 4.9 }
            ],
            table_preferences: [
              { section: 'Window Seats', preference_score: 92, avg_duration: 75 },
              { section: 'Patio', preference_score: 88, avg_duration: 85 },
              { section: 'Main Dining', preference_score: 76, avg_duration: 65 },
              { section: 'Bar Area', preference_score: 71, avg_duration: 45 }
            ]
          },
          loyalty: {
            retention_rate: 68.5,
            repeat_customers: 156,
            churn_risk: [
              { customer_id: '1', name: 'John Smith', risk_score: 85, last_visit: '2024-05-15' },
              { customer_id: '2', name: 'Sarah Johnson', risk_score: 78, last_visit: '2024-05-20' },
              { customer_id: '3', name: 'Mike Wilson', risk_score: 72, last_visit: '2024-05-18' }
            ]
          }
        },
        operational_analytics: {
          kitchen_performance: {
            avg_prep_time: 14.5,
            order_accuracy: 96.8,
            waste_percentage: 3.2,
            efficiency_score: 89
          },
          staff_performance: [
            { staff_id: '1', name: 'Alice Johnson', orders_served: 45, avg_service_time: 8.5, customer_rating: 4.8, sales_generated: 1350 },
            { staff_id: '2', name: 'Bob Smith', orders_served: 38, avg_service_time: 9.2, customer_rating: 4.6, sales_generated: 1140 },
            { staff_id: '3', name: 'Carol Davis', orders_served: 42, avg_service_time: 7.8, customer_rating: 4.9, sales_generated: 1260 }
          ],
          inventory_insights: [
            { item: 'Tomatoes', current_stock: 45, usage_rate: 12, reorder_point: 20, cost_optimization: 8.5 },
            { item: 'Chicken Breast', current_stock: 28, usage_rate: 8, reorder_point: 15, cost_optimization: 12.3 },
            { item: 'Mozzarella', current_stock: 15, usage_rate: 6, reorder_point: 10, cost_optimization: 5.7 }
          ]
        },
        predictive_insights: {
          revenue_forecast: Array.from({ length: 7 }, (_, i) => ({
            date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            predicted_revenue: Math.random() * 1000 + 2000,
            confidence: Math.random() * 20 + 80,
            factors: ['Weather', 'Day of week', 'Historical trends']
          })),
          demand_prediction: [
            { item: 'Pizza Dough', predicted_demand: 45, current_stock: 30, action_needed: 'Order 20 units' },
            { item: 'Fresh Basil', predicted_demand: 12, current_stock: 8, action_needed: 'Order 10 units' },
            { item: 'Olive Oil', predicted_demand: 8, current_stock: 15, action_needed: 'Sufficient stock' }
          ],
          staffing_optimization: [
            { date: '2024-06-05', shift: 'Lunch', recommended_staff: 6, current_scheduled: 5, efficiency_gain: 15 },
            { date: '2024-06-05', shift: 'Dinner', recommended_staff: 8, current_scheduled: 9, efficiency_gain: -8 },
            { date: '2024-06-06', shift: 'Lunch', recommended_staff: 5, current_scheduled: 6, efficiency_gain: -12 }
          ]
        },
        ai_recommendations: [
          {
            id: '1',
            type: 'revenue',
            priority: 'high',
            title: 'Optimize Menu Pricing',
            description: 'Increase prices on high-demand items with low price sensitivity',
            impact: '+$450/week estimated revenue increase',
            confidence: 92,
            action_items: [
              'Increase Margherita Pizza price by $2',
              'Add premium toppings options',
              'Create value meal bundles'
            ],
            estimated_benefit: 450
          },
          {
            id: '2',
            type: 'operational',
            priority: 'medium',
            title: 'Kitchen Workflow Optimization',
            description: 'Reorganize prep stations to reduce average preparation time',
            impact: '18% reduction in prep time',
            confidence: 87,
            action_items: [
              'Relocate sauce station closer to pizza oven',
              'Implement parallel prep processes',
              'Train staff on new workflow'
            ],
            estimated_benefit: 320
          },
          {
            id: '3',
            type: 'customer',
            priority: 'high',
            title: 'Customer Retention Program',
            description: 'Launch targeted loyalty program for at-risk customers',
            impact: '25% reduction in customer churn',
            confidence: 89,
            action_items: [
              'Create personalized offers for at-risk customers',
              'Implement automated email campaigns',
              'Offer exclusive menu previews'
            ],
            estimated_benefit: 680
          }
        ]
      };
      
      setAnalyticsData(mockData);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-500';
    if (growth < 0) return 'text-red-500';
    return 'text-gray-500';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading advanced analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load analytics data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Brain className="h-6 w-6 mr-2 text-blue-600" />
              Advanced Analytics & AI Insights
            </h1>
            <p className="text-gray-600 mt-1">
              Real-time business intelligence with predictive analytics
              {lastUpdated && (
                <span className="ml-2 text-sm">
                  • Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
            </select>
            
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                autoRefresh 
                  ? 'bg-green-100 text-green-800 border border-green-200' 
                  : 'bg-gray-100 text-gray-800 border border-gray-200'
              }`}
            >
              <Activity className="h-4 w-4 mr-1 inline" />
              {autoRefresh ? 'Live' : 'Manual'}
            </button>
            
            <button
              onClick={fetchAnalyticsData}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'revenue', label: 'Revenue', icon: DollarSign },
            { id: 'customers', label: 'Customers', icon: Users },
            { id: 'operations', label: 'Operations', icon: Activity },
            { id: 'predictions', label: 'Predictions', icon: TrendingUp },
            { id: 'ai', label: 'AI Insights', icon: Brain }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Real-time Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Revenue Today</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(analyticsData.realtime_metrics.current_revenue)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Orders Today</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.realtime_metrics.orders_today}
                    </p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Tables</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.realtime_metrics.active_tables}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Wait Time</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.realtime_metrics.avg_wait_time}m
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Satisfaction</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.realtime_metrics.customer_satisfaction}/5
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Staff Efficiency</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analyticsData.realtime_metrics.staff_efficiency}%
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-indigo-500" />
                </div>
              </div>
            </div>

            {/* Hourly Revenue Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Hourly Revenue</h3>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analyticsData.revenue_analytics.hourly_revenue}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip formatter={(value: any) => [formatCurrency(value), 'Revenue']} />
                  <Area type="monotone" dataKey="revenue" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </div>

            {/* Quick Insights */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top AI Recommendations</h3>
                <div className="space-y-3">
                  {analyticsData.ai_recommendations.slice(0, 3).map((rec) => (
                    <div key={rec.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                        {rec.priority.toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{rec.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                        <p className="text-sm font-medium text-green-600 mt-1">{rec.impact}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Demographics</h3>
                <ResponsiveContainer width="100%" height={200}>
                  <RechartsPieChart>
                    <Pie
                      data={analyticsData.customer_analytics.demographics.age_groups}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ group, percentage }) => `${group}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {analyticsData.customer_analytics.demographics.age_groups.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {/* Additional tabs content would be implemented here */}
        {activeTab !== 'overview' && (
          <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Brain className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Analytics
              </h3>
              <p className="text-gray-600 mb-4">
                Advanced {activeTab} analytics dashboard is being implemented with real-time data visualization and AI-powered insights.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">
                  This section will include comprehensive {activeTab} metrics, trends, and actionable recommendations.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3AdvancedAnalytics;
