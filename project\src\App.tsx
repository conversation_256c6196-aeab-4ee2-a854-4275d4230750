import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { TenantProvider } from './contexts/TenantContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { TranslationProvider } from './hooks/useTranslation';
import LoginPage from './pages/LoginPage';
import POSSystem from './pages/POSSystem';
import UnifiedPOSSystem from './UnifiedPOSSystem';
import { EnhancedAppProvider } from './context/EnhancedAppContext';
import AdminDashboard from './pages/AdminDashboard';
import { EnhancedAdminDashboard } from './pages/EnhancedAdminDashboard';
import { ModernSuperAdminDashboard } from './pages/ModernSuperAdminDashboard';
import { ComprehensiveAdminDashboard } from './pages/ComprehensiveAdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import './App.css';

function App() {
  return (
    <ThemeProvider>
      <TranslationProvider>
        <AuthProvider>
          <TenantProvider>
            <EnhancedAppProvider>
              <Router>
              <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
                <Routes>
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/pos" element={
                    <ProtectedRoute>
                      <UnifiedPOSSystem />
                    </ProtectedRoute>
                  } />
                  <Route path="/basic-pos" element={
                    <ProtectedRoute>
                      <POSSystem />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin" element={
                    <ProtectedRoute>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/enhanced-admin" element={
                    <ProtectedRoute>
                      <EnhancedAdminDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/super-admin" element={
                    <ProtectedRoute>
                      <ComprehensiveAdminDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/" element={<Navigate to="/pos" replace />} />
                </Routes>

              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'var(--toast-bg)',
                    color: 'var(--toast-color)',
                    border: '1px solid var(--toast-border)',
                  },
                  success: {
                    iconTheme: {
                      primary: '#10b981',
                      secondary: '#ffffff',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#ffffff',
                    },
                  },
                }}
              />
            </div>
            </Router>
            </EnhancedAppProvider>
          </TenantProvider>
        </AuthProvider>
      </TranslationProvider>
    </ThemeProvider>
  );
}

export default App;