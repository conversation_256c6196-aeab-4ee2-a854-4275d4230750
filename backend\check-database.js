// Check database contents and fix authentication
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function checkDatabase() {
  console.log('🔍 Checking RestroFlow Database...');
  console.log('==================================');

  try {
    const client = await pool.connect();

    // Check tenants
    console.log('\n🏢 Tenants:');
    const tenants = await client.query('SELECT * FROM tenants ORDER BY id');
    tenants.rows.forEach(tenant => {
      console.log(`  ${tenant.id}: ${tenant.name} (${tenant.slug}) - ${tenant.status}`);
    });

    // Check employees
    console.log('\n👥 Employees:');
    const employees = await client.query(`
      SELECT e.*, t.name as tenant_name, t.slug as tenant_slug 
      FROM employees e 
      LEFT JOIN tenants t ON e.tenant_id = t.id 
      ORDER BY e.id
    `);
    
    for (const emp of employees.rows) {
      console.log(`  ${emp.id}: ${emp.name} (${emp.role}) - Tenant: ${emp.tenant_name} (${emp.tenant_slug})`);
      console.log(`      Email: ${emp.email || 'N/A'}`);
      console.log(`      PIN Hash: ${emp.pin ? emp.pin.substring(0, 20) + '...' : 'N/A'}`);
      console.log(`      Active: ${emp.is_active}`);
    }

    // Test PIN verification
    console.log('\n🔐 Testing PIN Verification:');
    const testPin = '123456';
    
    for (const emp of employees.rows) {
      if (emp.pin) {
        try {
          const isValid = await bcrypt.compare(testPin, emp.pin);
          console.log(`  ${emp.name}: PIN ${testPin} ${isValid ? '✅ VALID' : '❌ INVALID'}`);
        } catch (error) {
          console.log(`  ${emp.name}: PIN verification error - ${error.message}`);
        }
      }
    }

    // Check categories
    console.log('\n📂 Categories:');
    const categories = await client.query('SELECT * FROM categories ORDER BY tenant_id, id');
    categories.rows.forEach(cat => {
      console.log(`  ${cat.id}: ${cat.name} (Tenant: ${cat.tenant_id}) - ${cat.is_active ? 'Active' : 'Inactive'}`);
    });

    // Check products
    console.log('\n📦 Products:');
    const products = await client.query('SELECT * FROM products ORDER BY tenant_id, id LIMIT 10');
    products.rows.forEach(prod => {
      console.log(`  ${prod.id}: ${prod.name} - $${prod.price} (Tenant: ${prod.tenant_id})`);
    });

    // Fix authentication if needed
    console.log('\n🔧 Fixing Authentication...');
    
    // Hash the PIN properly for all employees
    const hashedPin = await bcrypt.hash('123456', 10);
    
    await client.query('UPDATE employees SET pin = $1', [hashedPin]);
    console.log('✅ Updated all employee PINs to use proper bcrypt hash');

    client.release();
    console.log('\n🎉 Database check completed!');

  } catch (error) {
    console.error('💥 Database check failed:', error);
  } finally {
    await pool.end();
  }
}

checkDatabase();
