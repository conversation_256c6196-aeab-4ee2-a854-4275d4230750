const fetch = require('node-fetch');

async function testSuperAdminFlow() {
  try {
    console.log('🚀 Testing Super Admin Dashboard Flow...');
    console.log('='.repeat(60));
    
    // Step 1: Login as Super Admin
    console.log('\n📋 Step 1: Super Admin Login');
    console.log('-'.repeat(40));
    
    const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '888888',
        tenant_slug: 'demo-restaurant'
      })
    });

    console.log(`📡 Login response: ${loginResponse.status} ${loginResponse.statusText}`);

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.log(`❌ Login failed: ${errorText}`);
      return;
    }

    const loginResult = await loginResponse.json();
    console.log(`✅ Login successful!`);
    console.log(`👤 Employee: ${loginResult.employee.name} (${loginResult.employee.role})`);
    console.log(`🏢 Tenant: ${loginResult.tenant.name}`);
    console.log(`🔑 Token: ${loginResult.token ? 'Received' : 'Missing'}`);

    if (loginResult.employee.role !== 'super_admin') {
      console.log(`❌ User is not super admin: ${loginResult.employee.role}`);
      return;
    }

    // Step 2: Fetch Tenants List
    console.log('\n📋 Step 2: Fetch Tenants List');
    console.log('-'.repeat(40));
    
    const tenantsResponse = await fetch('http://localhost:4000/api/tenants', {
      headers: {
        'Authorization': `Bearer ${loginResult.token}`
      }
    });

    console.log(`📡 Tenants response: ${tenantsResponse.status} ${tenantsResponse.statusText}`);

    if (tenantsResponse.ok) {
      const tenants = await tenantsResponse.json();
      console.log(`✅ Fetched ${tenants.length} tenants`);
      
      console.log('\n📊 Tenant Details:');
      tenants.forEach((tenant, index) => {
        console.log(`  ${index + 1}. ${tenant.business_name || tenant.name}`);
        console.log(`     Email: ${tenant.email || 'N/A'}`);
        console.log(`     Status: ${tenant.status || 'N/A'}`);
        console.log(`     Plan: ${tenant.plan_type || 'N/A'}`);
        console.log(`     Created: ${tenant.created_at ? new Date(tenant.created_at).toLocaleDateString() : 'N/A'}`);
        console.log('');
      });
      
      // Step 3: Test Frontend Data Format
      console.log('\n📋 Step 3: Frontend Data Format Check');
      console.log('-'.repeat(40));
      
      const frontendCompatibleData = tenants.map(tenant => ({
        id: tenant.id,
        name: tenant.name || tenant.business_name,
        business_name: tenant.business_name || tenant.name,
        slug: tenant.slug || (tenant.name || tenant.business_name || '').toLowerCase().replace(/[^a-z0-9]/g, '-'),
        subscription_plan: tenant.plan_type || 'basic',
        status: tenant.status || 'active',
        created_at: tenant.created_at || new Date().toISOString(),
        updated_at: tenant.updated_at || new Date().toISOString(),
        features: tenant.features || {
          inventory_management: false,
          staff_scheduling: false,
          analytics_dashboard: false,
          loyalty_program: false,
          kitchen_display: true,
          online_ordering: false,
          equipment_management: false,
          multi_location: false,
          advanced_reporting: false
        },
        settings: {
          business_hours: {
            monday: { open: '09:00', close: '22:00', is_closed: false },
            tuesday: { open: '09:00', close: '22:00', is_closed: false },
            wednesday: { open: '09:00', close: '22:00', is_closed: false },
            thursday: { open: '09:00', close: '22:00', is_closed: false },
            friday: { open: '09:00', close: '22:00', is_closed: false },
            saturday: { open: '09:00', close: '22:00', is_closed: false },
            sunday: { open: '09:00', close: '22:00', is_closed: false }
          },
          currency: 'CAD',
          timezone: 'America/New_York',
          tax_rate: 0.13,
          notification_preferences: {
            email: true,
            sms: false,
            push: true,
            low_stock_alerts: true,
            order_notifications: true,
            staff_updates: true
          },
          branding: {
            primary_color: '#3B82F6',
            secondary_color: '#1E40AF',
            logo_url: null,
            business_name: tenant.business_name || tenant.name
          }
        }
      }));
      
      console.log(`✅ Converted ${frontendCompatibleData.length} tenants to frontend format`);
      console.log('\n📊 Sample Frontend Data:');
      if (frontendCompatibleData.length > 0) {
        const sample = frontendCompatibleData[0];
        console.log(`  Name: ${sample.name}`);
        console.log(`  Business Name: ${sample.business_name}`);
        console.log(`  Slug: ${sample.slug}`);
        console.log(`  Plan: ${sample.subscription_plan}`);
        console.log(`  Status: ${sample.status}`);
        console.log(`  Features: ${Object.keys(sample.features).length} features`);
      }
      
    } else {
      const errorText = await tenantsResponse.text();
      console.log(`❌ Failed to fetch tenants: ${errorText}`);
    }
    
    // Step 4: Test Dashboard Data
    console.log('\n📋 Step 4: Test Dashboard Data Endpoint');
    console.log('-'.repeat(40));
    
    const dashboardResponse = await fetch('http://localhost:4000/api/superadmin/dashboard', {
      headers: {
        'Authorization': `Bearer ${loginResult.token}`
      }
    });

    console.log(`📡 Dashboard response: ${dashboardResponse.status} ${dashboardResponse.statusText}`);

    if (dashboardResponse.ok) {
      const dashboardData = await dashboardResponse.json();
      console.log(`✅ Dashboard data received`);
      console.log(`📊 Dashboard overview:`, JSON.stringify(dashboardData, null, 2));
    } else {
      const errorText = await dashboardResponse.text();
      console.log(`⚠️ Dashboard endpoint not available: ${errorText}`);
    }
    
    console.log('\n🏁 Super Admin Flow Test Completed!');
    console.log('='.repeat(60));
    
    console.log('\n📋 SUMMARY:');
    console.log('✅ Super Admin login working');
    console.log('✅ Tenant listing API working');
    console.log('✅ Data format compatible with frontend');
    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Check frontend console for any JavaScript errors');
    console.log('2. Verify token is being set in TenantService');
    console.log('3. Check if TenantManagement component is rendering');
    console.log('4. Verify data transformation in frontend');
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testSuperAdminFlow();
