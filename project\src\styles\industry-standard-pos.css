/* Industry Standard POS System Styles */

/* CSS Custom Properties for Theming */
:root {
  /* Brand Colors */
  --brand-primary: #3B82F6;
  --brand-secondary: #10B981;
  --brand-accent: #8B5CF6;
  
  /* Semantic Colors */
  --success: #059669;
  --warning: #D97706;
  --error: #DC2626;
  --info: #0284C7;
  
  /* Light Theme Variables */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;
  --bg-elevated: #FFFFFF;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --text-inverse: #FFFFFF;
  
  --border-primary: #E5E7EB;
  --border-secondary: #D1D5DB;
  --border-focus: #3B82F6;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Typography */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Transitions */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
.dark {
  --bg-primary: #1F2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-elevated: #1F2937;
  --bg-overlay: rgba(0, 0, 0, 0.75);
  
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-tertiary: #9CA3AF;
  --text-inverse: #111827;
  
  --border-primary: #4B5563;
  --border-secondary: #6B7280;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: var(--line-height-normal);
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0;
  color: var(--text-secondary);
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Component Styles */

/* Button Base */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  user-select: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Button Variants */
.btn-primary {
  background-color: var(--brand-primary);
  color: var(--text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563EB;
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--border-primary);
}

.btn-success {
  background-color: var(--success);
  color: var(--text-inverse);
}

.btn-success:hover:not(:disabled) {
  background-color: #047857;
}

.btn-danger {
  background-color: var(--error);
  color: var(--text-inverse);
}

.btn-danger:hover:not(:disabled) {
  background-color: #B91C1C;
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-xl);
}

/* Input Base */
.input {
  display: block;
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input::placeholder {
  color: var(--text-tertiary);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Card Component */
.card {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border-radius: var(--radius-xl);
  white-space: nowrap;
}

.badge-primary {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--brand-primary);
}

.badge-success {
  background-color: rgba(5, 150, 105, 0.1);
  color: var(--success);
}

.badge-warning {
  background-color: rgba(217, 119, 6, 0.1);
  color: var(--warning);
}

.badge-danger {
  background-color: rgba(220, 38, 38, 0.1);
  color: var(--error);
}

/* Modal Component */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  z-index: var(--z-modal-backdrop);
  animation: fadeIn var(--transition-fast);
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  background-color: var(--bg-elevated);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  overflow: hidden;
  animation: slideIn var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

/* Toast Component */
.toast {
  position: fixed;
  top: var(--space-lg);
  right: var(--space-lg);
  min-width: 300px;
  padding: var(--space-md);
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  animation: slideInRight var(--transition-normal);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--brand-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --space-xs: 0.125rem;
    --space-sm: 0.25rem;
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-2xl: 2rem;
  }
  
  .modal {
    width: 95%;
    max-width: none;
  }
  
  .toast {
    left: var(--space-md);
    right: var(--space-md);
    min-width: auto;
  }
}

/* Print Styles */
@media print {
  .modal-backdrop,
  .toast,
  .btn,
  [data-print-hide] {
    display: none !important;
  }
  
  .card {
    border: 1px solid #000;
    box-shadow: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
  }
  
  .dark {
    --border-primary: #FFFFFF;
    --border-secondary: #FFFFFF;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced Restructured POS Styles */

/* Improved Focus Management */
.focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Enhanced Grid Layouts */
.pos-grid-responsive {
  display: grid;
  gap: var(--space-md);
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

@media (max-width: 640px) {
  .pos-grid-responsive {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--space-sm);
  }
}

/* Optimized Product Cards */
.product-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.product-card:active {
  transform: translateY(0);
}

.product-card-unavailable {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card-unavailable:hover {
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* Enhanced Order Panel */
.order-panel {
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.order-item {
  transition: all var(--transition-fast);
}

.order-item:hover {
  background-color: var(--bg-tertiary);
}

/* Improved Navigation */
.nav-item {
  position: relative;
  transition: all var(--transition-fast);
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background-color: var(--brand-primary);
  transition: height var(--transition-fast);
}

.nav-item.active::before {
  height: 70%;
}

.nav-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  white-space: nowrap;
  z-index: var(--z-tooltip);
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
}

.nav-item:hover .nav-tooltip {
  opacity: 1;
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-online {
  background-color: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.status-offline {
  background-color: rgba(239, 68, 68, 0.1);
  color: #DC2626;
}

.status-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: #D97706;
}

/* Improved Form Elements */
.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: var(--space-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Enhanced Notification System */
.notification-container {
  position: fixed;
  top: var(--space-lg);
  right: var(--space-lg);
  z-index: var(--z-toast);
  max-width: 400px;
  width: 100%;
}

.notification {
  margin-bottom: var(--space-sm);
  padding: var(--space-md);
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  animation: slideInRight var(--transition-normal) ease-out;
}

.notification-success {
  border-left: 4px solid var(--success);
}

.notification-error {
  border-left: 4px solid var(--error);
}

.notification-warning {
  border-left: 4px solid var(--warning);
}

.notification-info {
  border-left: 4px solid var(--info);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Improved Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .sidebar-desktop {
    display: none;
  }

  .sidebar-mobile {
    display: block;
  }
}

@media (max-width: 768px) {
  :root {
    --space-xs: 0.125rem;
    --space-sm: 0.25rem;
    --space-md: 0.75rem;
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-2xl: 2rem;
  }

  .order-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50vh;
    z-index: var(--z-modal);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }

  .product-grid {
    padding-bottom: 50vh;
  }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Print Optimizations */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .page-break {
    page-break-before: always;
  }

  .avoid-break {
    page-break-inside: avoid;
  }
}
