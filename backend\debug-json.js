const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function debugJsonIssue() {
  try {
    // Create a test order
    const testOrder = {
      id: uuidv4(),
      items: JSON.stringify([
        { id: '1', name: 'Burger', price: 12.99, quantity: 1 },
        { id: '2', name: 'Fries', price: 4.99, quantity: 1 }
      ]),
      timestamp: new Date().toISOString(),
      status: 'pending',
      total: 17.98,
      subtotal: 16.65,
      tax: 1.33,
      payment_method: 'cash',
      tip: 0,
      tab_name: 'Table 5',
      employee_id: null
    };

    console.log('1. Original items (before saving):', testOrder.items);
    console.log('1. Type:', typeof testOrder.items);

    // Save to orders table
    const saveResult = await pool.query(
      `INSERT INTO orders (
        id, items, timestamp, status, total, subtotal, tax,
        payment_method, tip, tab_name, employee_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) 
      RETURNING *`,
      [
        testOrder.id, testOrder.items, testOrder.timestamp, testOrder.status, 
        testOrder.total, testOrder.subtotal, testOrder.tax,
        testOrder.payment_method, testOrder.tip, testOrder.tab_name, testOrder.employee_id
      ]
    );

    console.log('2. Saved order items:', saveResult.rows[0].items);
    console.log('2. Type:', typeof saveResult.rows[0].items);

    // Retrieve from orders table
    const retrieveResult = await pool.query(
      'SELECT * FROM orders WHERE id = $1',
      [testOrder.id]
    );

    const retrievedOrder = retrieveResult.rows[0];
    console.log('3. Retrieved order items:', retrievedOrder.items);
    console.log('3. Type:', typeof retrievedOrder.items);

    // Test what happens when we try to insert into kitchen_orders
    console.log('4. Testing kitchen_orders insertion...');
    
    try {
      const kitchenResult = await pool.query(`
        INSERT INTO kitchen_orders (
          id, order_id, order_number, table_number, items, status, 
          timestamp, priority, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        uuidv4(),
        testOrder.id,
        1,
        5,
        retrievedOrder.items,
        'new',
        Date.now(),
        'normal',
        'Test order'
      ]);
      
      console.log('✅ Kitchen order created successfully:', kitchenResult.rows[0].id);
    } catch (error) {
      console.log('❌ Kitchen order creation failed:', error.message);
      
      // Try with explicit JSON conversion
      console.log('5. Trying with explicit JSON handling...');
      const itemsForKitchen = typeof retrievedOrder.items === 'object' 
        ? JSON.stringify(retrievedOrder.items) 
        : retrievedOrder.items;
      
      console.log('5. Items for kitchen:', itemsForKitchen);
      console.log('5. Type:', typeof itemsForKitchen);
      
      const kitchenResult2 = await pool.query(`
        INSERT INTO kitchen_orders (
          id, order_id, order_number, table_number, items, status, 
          timestamp, priority, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        uuidv4(),
        testOrder.id,
        2,
        6,
        itemsForKitchen,
        'new',
        Date.now(),
        'normal',
        'Test order 2'
      ]);
      
      console.log('✅ Kitchen order created with explicit handling:', kitchenResult2.rows[0].id);
    }

    // Clean up
    await pool.query('DELETE FROM kitchen_orders WHERE order_id = $1', [testOrder.id]);
    await pool.query('DELETE FROM orders WHERE id = $1', [testOrder.id]);
    
  } catch (error) {
    console.error('Debug failed:', error);
  } finally {
    await pool.end();
  }
}

debugJsonIssue();
