import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  Banknote, 
  Gift,
  Loader2,
  CheckCircle,
  XCircle,
  Calculator,
  Percent,
  DollarSign,
  Users,
  Clock,
  Receipt,
  Mail,
  MessageSquare,
  Printer,
  ArrowLeft,
  AlertTriangle
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface PaymentMethod {
  id: string;
  name: string;
  display_name: string;
  icon: string;
  processing_fee_percentage: number;
  processing_fee_fixed: number;
  requires_authorization: boolean;
  supports_tips: boolean;
  supports_split_payment: boolean;
}

interface PaymentProcessorProps {
  orderData: {
    id?: string;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
      total: number;
    }>;
    subtotal: number;
    tax: number;
    total: number;
    tableId?: string;
    tableNumber?: number;
    guestCount?: number;
    serverName?: string;
  };
  onPaymentComplete: (result: any) => void;
  onCancel: () => void;
}

const EnhancedPaymentProcessor: React.FC<PaymentProcessorProps> = ({
  orderData,
  onPaymentComplete,
  onCancel
}) => {
  const { apiCall } = useEnhancedAppContext();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [tipAmount, setTipAmount] = useState(0);
  const [tipPercentage, setTipPercentage] = useState(0);
  const [customTipAmount, setCustomTipAmount] = useState('');
  const [splitPayments, setSplitPayments] = useState<Array<{
    method: PaymentMethod;
    amount: number;
    percentage: number;
  }>>([]);
  const [isSplitPayment, setIsSplitPayment] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'method' | 'tip' | 'confirmation' | 'processing' | 'success' | 'error'>('method');
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [customerInfo, setCustomerInfo] = useState({
    email: '',
    phone: '',
    name: ''
  });
  const [receiptOptions, setReceiptOptions] = useState({
    print: true,
    email: false,
    sms: false
  });

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      const response = await apiCall('/api/payments/methods');
      if (response.ok) {
        const methods = await response.json();
        setPaymentMethods(methods);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    }
  };

  const calculateProcessingFee = (method: PaymentMethod, amount: number) => {
    return (amount * method.processing_fee_percentage) + method.processing_fee_fixed;
  };

  const calculateFinalTotal = () => {
    if (isSplitPayment) {
      return splitPayments.reduce((total, payment) => total + payment.amount, 0) + tipAmount;
    }
    
    if (!selectedPaymentMethod) return orderData.total + tipAmount;
    
    const processingFee = calculateProcessingFee(selectedPaymentMethod, orderData.total + tipAmount);
    return orderData.total + tipAmount + processingFee;
  };

  const handleTipSelection = (percentage: number) => {
    setTipPercentage(percentage);
    const amount = Math.round(orderData.subtotal * (percentage / 100) * 100) / 100;
    setTipAmount(amount);
    setCustomTipAmount('');
  };

  const handleCustomTip = (value: string) => {
    setCustomTipAmount(value);
    const amount = parseFloat(value) || 0;
    setTipAmount(amount);
    setTipPercentage(0);
  };

  const addSplitPayment = () => {
    if (!selectedPaymentMethod) return;
    
    const remainingAmount = orderData.total - splitPayments.reduce((sum, p) => sum + p.amount, 0);
    if (remainingAmount <= 0) return;

    setSplitPayments([...splitPayments, {
      method: selectedPaymentMethod,
      amount: remainingAmount,
      percentage: (remainingAmount / orderData.total) * 100
    }]);
    setSelectedPaymentMethod(null);
  };

  const removeSplitPayment = (index: number) => {
    setSplitPayments(splitPayments.filter((_, i) => i !== index));
  };

  const processPayment = async () => {
    setIsProcessing(true);
    setPaymentStep('processing');

    try {
      const paymentData = {
        order_id: orderData.id || `order_${Date.now()}`,
        order_data: orderData,
        payment_method: selectedPaymentMethod?.name,
        payment_method_id: selectedPaymentMethod?.id,
        amount: orderData.total,
        tip_amount: tipAmount,
        total_amount: calculateFinalTotal(),
        customer_info: customerInfo,
        receipt_options: receiptOptions,
        split_payments: isSplitPayment ? splitPayments : null,
        metadata: {
          table_id: orderData.tableId,
          table_number: orderData.tableNumber,
          guest_count: orderData.guestCount,
          server_name: orderData.serverName
        }
      };

      const response = await apiCall('/api/payments/process', {
        method: 'POST',
        body: JSON.stringify(paymentData)
      });

      if (response.ok) {
        const result = await response.json();
        setPaymentResult(result);
        setPaymentStep('success');
        
        // Auto-complete after showing success
        setTimeout(() => {
          onPaymentComplete(result);
        }, 3000);
      } else {
        const error = await response.json();
        setErrorMessage(error.message || 'Payment processing failed');
        setPaymentStep('error');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setErrorMessage('Network error occurred. Please try again.');
      setPaymentStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const getPaymentMethodIcon = (iconName: string) => {
    switch (iconName) {
      case 'credit-card': return <CreditCard className="h-6 w-6" />;
      case 'smartphone': return <Smartphone className="h-6 w-6" />;
      case 'banknote': return <Banknote className="h-6 w-6" />;
      case 'gift': return <Gift className="h-6 w-6" />;
      default: return <CreditCard className="h-6 w-6" />;
    }
  };

  const renderPaymentMethodSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Select Payment Method</h2>
        <p className="text-gray-600">Choose how you'd like to pay for this order</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {paymentMethods.map((method) => (
          <button
            key={method.id}
            onClick={() => setSelectedPaymentMethod(method)}
            className={`p-6 rounded-lg border-2 transition-all ${
              selectedPaymentMethod?.id === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-4">
              <div className={`p-3 rounded-full ${
                selectedPaymentMethod?.id === method.id ? 'bg-blue-100' : 'bg-gray-100'
              }`}>
                {getPaymentMethodIcon(method.icon)}
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-gray-900">{method.display_name}</h3>
                {method.processing_fee_percentage > 0 && (
                  <p className="text-sm text-gray-500">
                    {(method.processing_fee_percentage * 100).toFixed(1)}% + ${method.processing_fee_fixed.toFixed(2)} fee
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>

      {selectedPaymentMethod && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-600">Subtotal:</span>
            <span className="font-medium">${orderData.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-600">Tax:</span>
            <span className="font-medium">${orderData.tax.toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-600">Processing Fee:</span>
            <span className="font-medium">
              ${calculateProcessingFee(selectedPaymentMethod, orderData.total).toFixed(2)}
            </span>
          </div>
          <div className="border-t pt-2">
            <div className="flex items-center justify-between">
              <span className="font-semibold text-gray-900">Total:</span>
              <span className="font-bold text-lg">${orderData.total.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}

      <div className="flex space-x-4">
        <button
          onClick={onCancel}
          className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={() => setPaymentStep('tip')}
          disabled={!selectedPaymentMethod}
          className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </div>
  );

  const renderTipSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Add Tip</h2>
        <p className="text-gray-600">Show your appreciation for great service</p>
      </div>

      <div className="grid grid-cols-4 gap-3">
        {[15, 18, 20, 25].map((percentage) => (
          <button
            key={percentage}
            onClick={() => handleTipSelection(percentage)}
            className={`p-4 rounded-lg border-2 transition-all ${
              tipPercentage === percentage
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">{percentage}%</div>
              <div className="text-sm text-gray-500">
                ${Math.round(orderData.subtotal * (percentage / 100) * 100) / 100}
              </div>
            </div>
          </button>
        ))}
      </div>

      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">Custom Tip Amount</label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="number"
            value={customTipAmount}
            onChange={(e) => handleCustomTip(e.target.value)}
            placeholder="0.00"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-600">Order Total:</span>
          <span className="font-medium">${orderData.total.toFixed(2)}</span>
        </div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-600">Tip:</span>
          <span className="font-medium">${tipAmount.toFixed(2)}</span>
        </div>
        <div className="border-t pt-2">
          <div className="flex items-center justify-between">
            <span className="font-semibold text-gray-900">Final Total:</span>
            <span className="font-bold text-lg">${calculateFinalTotal().toFixed(2)}</span>
          </div>
        </div>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={() => setPaymentStep('method')}
          className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2 inline" />
          Back
        </button>
        <button
          onClick={() => setPaymentStep('confirmation')}
          className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Continue
        </button>
      </div>
    </div>
  );

  const renderConfirmation = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Confirm Payment</h2>
        <p className="text-gray-600">Review your payment details before processing</p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="font-semibold text-gray-900 mb-4">Payment Summary</h3>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Payment Method:</span>
            <span className="font-medium">{selectedPaymentMethod?.display_name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Order Total:</span>
            <span className="font-medium">${orderData.total.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Tip:</span>
            <span className="font-medium">${tipAmount.toFixed(2)}</span>
          </div>
          {selectedPaymentMethod && calculateProcessingFee(selectedPaymentMethod, orderData.total + tipAmount) > 0 && (
            <div className="flex justify-between">
              <span className="text-gray-600">Processing Fee:</span>
              <span className="font-medium">
                ${calculateProcessingFee(selectedPaymentMethod, orderData.total + tipAmount).toFixed(2)}
              </span>
            </div>
          )}
          <div className="border-t pt-3">
            <div className="flex justify-between">
              <span className="font-bold text-gray-900">Final Total:</span>
              <span className="font-bold text-xl text-green-600">${calculateFinalTotal().toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Receipt Options</label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={receiptOptions.print}
                onChange={(e) => setReceiptOptions({...receiptOptions, print: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Print receipt</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={receiptOptions.email}
                onChange={(e) => setReceiptOptions({...receiptOptions, email: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Email receipt</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={receiptOptions.sms}
                onChange={(e) => setReceiptOptions({...receiptOptions, sms: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">SMS receipt</span>
            </label>
          </div>
        </div>

        {(receiptOptions.email || receiptOptions.sms) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {receiptOptions.email && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <input
                  type="email"
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
            )}
            {receiptOptions.sms && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                <input
                  type="tel"
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="(*************"
                />
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex space-x-4">
        <button
          onClick={() => setPaymentStep('tip')}
          className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          <ArrowLeft className="h-4 w-4 mr-2 inline" />
          Back
        </button>
        <button
          onClick={processPayment}
          disabled={isProcessing}
          className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 inline animate-spin" />
              Processing...
            </>
          ) : (
            'Process Payment'
          )}
        </button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Processing Payment</h2>
        <p className="text-gray-600">Please wait while we process your payment...</p>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <p className="text-sm text-gray-500">
          Do not close this window or navigate away from this page.
        </p>
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle className="h-8 w-8 text-green-600" />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h2>
        <p className="text-gray-600">Your payment has been processed successfully</p>
      </div>
      
      {paymentResult && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600">Transaction ID:</span>
              <span className="font-mono text-sm">{paymentResult.transaction_id}</span>
            </div>
            {paymentResult.authorization_code && (
              <div className="flex justify-between">
                <span className="text-gray-600">Auth Code:</span>
                <span className="font-mono text-sm">{paymentResult.authorization_code}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="font-semibold">${paymentResult.total_amount?.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}

      <p className="text-sm text-gray-500">
        Receipt will be generated automatically. Redirecting to order completion...
      </p>
    </div>
  );

  const renderError = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
        <XCircle className="h-8 w-8 text-red-600" />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Failed</h2>
        <p className="text-gray-600">There was an issue processing your payment</p>
      </div>
      
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
          <div className="text-left">
            <h3 className="font-medium text-red-800">Error Details</h3>
            <p className="text-sm text-red-700 mt-1">{errorMessage}</p>
          </div>
        </div>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={() => setPaymentStep('confirmation')}
          className="flex-1 px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          Try Again
        </button>
        <button
          onClick={onCancel}
          className="flex-1 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {paymentStep === 'method' && renderPaymentMethodSelection()}
          {paymentStep === 'tip' && renderTipSelection()}
          {paymentStep === 'confirmation' && renderConfirmation()}
          {paymentStep === 'processing' && renderProcessing()}
          {paymentStep === 'success' && renderSuccess()}
          {paymentStep === 'error' && renderError()}
        </div>
      </div>
    </div>
  );
};

export default EnhancedPaymentProcessor;
