const axios = require('axios');

const baseURL = 'http://localhost:4000';

async function testFrontendLogin() {
  console.log('🔍 Testing Frontend Login Flow...');
  console.log('=' .repeat(50));
  
  try {
    // Test the exact same request the frontend makes
    console.log('Testing Enhanced Interface login request...');
    
    const response = await axios.post(`${baseURL}/api/auth/login`, {
      pin: '123456',
      tenant_slug: 'demo-restaurant'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    console.log('Response Status:', response.status);
    console.log('Response Headers:', response.headers);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log('✅ Frontend login simulation successful!');
      console.log('Token received:', response.data.token ? 'Yes' : 'No');
      console.log('Employee:', response.data.employee?.name);
      console.log('Tenant:', response.data.tenant?.name);
      console.log('Tenant Slug:', response.data.tenant?.slug);
    } else {
      console.log('❌ Frontend login simulation failed');
      console.log('Error details:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Response Status:', error.response.status);
      console.log('Response Data:', error.response.data);
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  
  try {
    // Test without tenant_slug (should still work)
    console.log('Testing login without tenant_slug...');
    
    const response2 = await axios.post(`${baseURL}/api/auth/login`, {
      pin: '123456'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    console.log('Response Status:', response2.status);
    console.log('Response Data:', JSON.stringify(response2.data, null, 2));
    
    if (response2.status === 200) {
      console.log('✅ Login without tenant_slug successful!');
    } else {
      console.log('❌ Login without tenant_slug failed');
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
  
  console.log('\n' + '=' .repeat(50));
  
  try {
    // Test CORS headers
    console.log('Testing CORS headers...');
    
    const response3 = await axios.options(`${baseURL}/api/auth/login`, {
      headers: {
        'Origin': 'http://localhost:5175',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      timeout: 5000
    });
    
    console.log('CORS Response Status:', response3.status);
    console.log('CORS Headers:', response3.headers);
    
  } catch (error) {
    console.log('CORS test failed:', error.message);
  }
}

testFrontendLogin().catch(console.error);
