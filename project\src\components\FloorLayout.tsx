 import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { Table } from '../types';
import { Users, Clock, CheckCircle, AlertCircle, Plus, Edit3 } from 'lucide-react';

const FloorLayout: React.FC = () => {
  const { state, dispatch, updateTableStatus } = useAppContext();
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [showTableEditor, setShowTableEditor] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });

  // Default tables if no floor layout exists
  const defaultTables: Table[] = [
    { id: '1', number: 1, seats: 4, x: 50, y: 50, width: 80, height: 80, shape: 'rectangle', status: 'available' },
    { id: '2', number: 2, seats: 2, x: 200, y: 50, width: 60, height: 60, shape: 'circle', status: 'available' },
    { id: '3', number: 3, seats: 6, x: 350, y: 50, width: 100, height: 80, shape: 'rectangle', status: 'available' },
    { id: '4', number: 4, seats: 4, x: 50, y: 200, width: 80, height: 80, shape: 'rectangle', status: 'available' },
    { id: '5', number: 5, seats: 2, x: 200, y: 200, width: 60, height: 60, shape: 'circle', status: 'available' },
    { id: '6', number: 6, seats: 8, x: 350, y: 200, width: 120, height: 80, shape: 'rectangle', status: 'available' },
    { id: '7', number: 7, seats: 4, x: 50, y: 350, width: 80, height: 80, shape: 'rectangle', status: 'available' },
    { id: '8', number: 8, seats: 2, x: 200, y: 350, width: 60, height: 60, shape: 'circle', status: 'available' },
    { id: '9', number: 9, seats: 6, x: 350, y: 350, width: 100, height: 80, shape: 'rectangle', status: 'available' },
    { id: '10', number: 10, seats: 4, x: 500, y: 50, width: 80, height: 80, shape: 'rectangle', status: 'available' },
    { id: '11', number: 11, seats: 2, x: 500, y: 200, width: 60, height: 60, shape: 'circle', status: 'available' },
    { id: '12', number: 12, seats: 6, x: 500, y: 350, width: 100, height: 80, shape: 'rectangle', status: 'available' }
  ];

  const tables = state.tables.length > 0 ? state.tables : defaultTables;

  useEffect(() => {
    if (state.tables.length === 0) {
      dispatch({ type: 'SET_TABLES', payload: defaultTables });
    }
  }, []);

  const getTableStatusColor = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-500 hover:bg-green-600';
      case 'occupied':
        return 'bg-red-500 hover:bg-red-600';
      case 'reserved':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'needs-cleaning':
        return 'bg-orange-500 hover:bg-orange-600';
      default:
        return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  const getTableStatusIcon = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        return <Users className="h-4 w-4" />;
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleTableClick = (table: Table) => {
    if (isEditMode) {
      setSelectedTable(table);
      setShowTableEditor(true);
      return;
    }

    // Select table for order taking
    dispatch({ type: 'SELECT_TABLE', payload: table });
    
    // If table is available, mark as occupied and start new order
    if (table.status === 'available') {
      updateTableStatus(table.id, 'occupied');
    }
  };

  const handleStatusChange = async (tableId: string, newStatus: Table['status']) => {
    try {
      await updateTableStatus(tableId, newStatus);
    } catch (error) {
      console.error('Failed to update table status:', error);
    }
  };

  const TableComponent: React.FC<{ table: Table }> = ({ table }) => {
    const isSelected = state.selectedTable?.id === table.id;
    
    return (
      <div
        key={table.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          table.shape === 'circle' ? 'rounded-full' : 'rounded-lg'
        } ${getTableStatusColor(table.status)} ${
          isSelected ? 'ring-4 ring-purple-400 ring-opacity-75' : ''
        } flex flex-col items-center justify-center text-white font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`}
        style={{
          left: `${table.x}px`,
          top: `${table.y}px`,
          width: `${table.width}px`,
          height: `${table.height}px`,
        }}
        onClick={() => handleTableClick(table)}
      >
        <div className="flex items-center space-x-1">
          {getTableStatusIcon(table.status)}
          <span className="text-sm">{table.number}</span>
        </div>
        <div className="text-xs opacity-75">
          {table.seats} seats
        </div>
        {table.status === 'reserved' && table.reservationTime && (
          <div className="text-xs opacity-75">
            {new Date(table.reservationTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        )}
      </div>
    );
  };

  const TableEditor: React.FC = () => {
    if (!selectedTable) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700 shadow-xl">
          <h3 className="text-xl font-semibold text-white mb-4">
            Edit Table {selectedTable.number}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Status
              </label>
              <select
                value={selectedTable.status}
                onChange={(e) => handleStatusChange(selectedTable.id, e.target.value as Table['status'])}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="available">Available</option>
                <option value="occupied">Occupied</option>
                <option value="reserved">Reserved</option>
                <option value="needs-cleaning">Needs Cleaning</option>
              </select>
            </div>

            {selectedTable.status === 'reserved' && (
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Reservation Time
                </label>
                <input
                  type="datetime-local"
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  defaultValue={
                    selectedTable.reservationTime
                      ? new Date(selectedTable.reservationTime).toISOString().slice(0, 16)
                      : ''
                  }
                />
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => {
                setShowTableEditor(false);
                setSelectedTable(null);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setShowTableEditor(false);
                setSelectedTable(null);
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    );
  };

  const handleZoomIn = () => setZoomLevel(prev => Math.min(prev + 0.2, 2));
  const handleZoomOut = () => setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  const handleResetZoom = () => setZoomLevel(1);

  const handleDragStart = (e: React.MouseEvent) => {
    if (!isEditMode) return;
    setIsDragging(true);
    setDragPosition({ x: e.clientX, y: e.clientY });
  };

  const handleDragMove = (e: React.MouseEvent) => {
    if (!isDragging || !isEditMode) return;
    const deltaX = e.clientX - dragPosition.x;
    const deltaY = e.clientY - dragPosition.y;
    setDragPosition({ x: e.clientX, y: e.clientY });
    
    const container = document.getElementById('floor-plan-container');
    if (container) {
      container.scrollLeft -= deltaX;
      container.scrollTop -= deltaY;
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 bg-gray-800 rounded-lg mb-4 gap-4">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <span className="mr-2">Restaurant Floor Layout</span>
          <span className="text-sm text-gray-400">({Math.round(zoomLevel * 100)}%)</span>
        </h2>
        <div className="flex flex-wrap items-center gap-3">
          {/* Zoom controls */}
          <div className="flex items-center bg-gray-700 rounded-lg">
            <button
              onClick={handleZoomOut}
              className="px-3 py-2 text-gray-300 hover:text-white transition-colors"
              title="Zoom Out"
            >
              -
            </button>
            <button
              onClick={handleResetZoom}
              className="px-3 py-2 text-gray-300 hover:text-white transition-colors border-l border-r border-gray-600"
              title="Reset Zoom"
            >
              100%
            </button>
            <button
              onClick={handleZoomIn}
              className="px-3 py-2 text-gray-300 hover:text-white transition-colors"
              title="Zoom In"
            >
              +
            </button>
          </div>
          <button
            onClick={() => setIsEditMode(!isEditMode)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              isEditMode
                ? 'bg-purple-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <Edit3 className="h-4 w-4" />
            <span>{isEditMode ? 'Exit Edit' : 'Edit Layout'}</span>
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-4 p-4 bg-gray-800 rounded-lg mb-4">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-green-500 rounded"></div>
          <span className="text-gray-300 text-sm">Available</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-red-500 rounded"></div>
          <span className="text-gray-300 text-sm">Occupied</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-yellow-500 rounded"></div>
          <span className="text-gray-300 text-sm">Reserved</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-orange-500 rounded"></div>
          <span className="text-gray-300 text-sm">Needs Cleaning</span>
        </div>
      </div>

      {/* Floor Plan with improved interaction */}
      <div 
        id="floor-plan-container"
        className="flex-grow bg-gray-800 rounded-lg p-4 relative overflow-auto"
        style={{
          cursor: isDragging && isEditMode ? 'grabbing' : isEditMode ? 'grab' : 'default'
        }}
        onMouseDown={handleDragStart}
        onMouseMove={handleDragMove}
        onMouseUp={handleDragEnd}
        onMouseLeave={handleDragEnd}
      >
        <div 
          className="relative w-full h-full min-h-[500px] transition-transform duration-200"
          style={{ 
            minWidth: '700px',
            transform: `scale(${zoomLevel})`,
            transformOrigin: '0 0'
          }}
        >
          {/* Background grid */}
          <div 
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: 'radial-gradient(circle, #ffffff 1px, transparent 1px)',
              backgroundSize: '20px 20px'
            }}
          />
          
          {/* Grid overlay */}
          <div 
            className="absolute inset-0 opacity-10 pointer-events-none"
            style={{
              backgroundImage: 'radial-gradient(circle, #ffffff 1px, transparent 1px)',
              backgroundSize: '20px 20px'
            }}
          />
          
          {/* Tables with improved visibility */}
          <div className="relative">
            {tables.map((table) => (
              <TableComponent key={table.id} table={table} />
            ))}
          </div>

          {/* Selected table info */}
          {state.selectedTable && (
            <div className="absolute top-4 right-4 bg-purple-600 text-white p-3 rounded-lg shadow-lg">
              <h4 className="font-semibold">Table {state.selectedTable.number}</h4>
              <p className="text-sm opacity-90">{state.selectedTable.seats} seats</p>
              <p className="text-sm opacity-90 capitalize">{state.selectedTable.status}</p>
            </div>
          )}
        </div>
      </div>

      {/* Table Editor Modal */}
      {showTableEditor && <TableEditor />}

      {/* Instructions with better visibility */}
      <div className="mt-4 p-4 bg-gray-800 rounded-lg shadow-lg border border-gray-700">
        <p className="text-gray-300 text-sm">
          {isEditMode
            ? 'Click on tables to edit their properties and status.'
            : 'Click on a table to select it for order taking. Available tables will automatically be marked as occupied.'}
        </p>
      </div>
    </div>
  );
};

export default FloorLayout;
