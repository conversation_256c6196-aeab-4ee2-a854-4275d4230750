// Test script for Phase 2B endpoints
// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:4000';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MiwidXNlcm5hbWUiOiJFbmhhbmNlZCBBZG1pbiIsInJvbGUiOiJzdXBlcl9hZG1pbiIsInRlbmFudF9pZCI6MSwiaWF0IjoxNzMzNTEzMDMxLCJleHAiOjE3MzM1OTk0MzF9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testEndpoint(endpoint, method = 'GET', body = null) {
  try {
    console.log(`\n🧪 Testing ${method} ${endpoint}...`);
    
    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${TOKEN}`,
        'Content-Type': 'application/json'
      }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ SUCCESS: ${response.status}`);
      console.log(`📊 Data keys: ${Object.keys(data).join(', ')}`);
      
      // Show sample data structure
      if (data.overview) {
        console.log(`📈 Overview: ${JSON.stringify(data.overview, null, 2)}`);
      }
      
      return data;
    } else {
      console.log(`❌ FAILED: ${response.status} - ${data.error || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    console.log(`💥 ERROR: ${error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Phase 2B Endpoint Tests...\n');
  
  // Test Security Audit System
  console.log('🔒 TESTING SECURITY AUDIT SYSTEM');
  console.log('='.repeat(50));
  
  await testEndpoint('/api/admin/security-audit?range=7d');
  await testEndpoint('/api/admin/security-audit?range=30d');
  
  // Test security event logging
  await testEndpoint('/api/admin/security-events', 'POST', {
    event_type: 'test_event',
    action: 'endpoint_test',
    details: 'Testing security event logging from test script',
    ip_address: '127.0.0.1',
    user_agent: 'Test Script'
  });
  
  // Test Backup Management System
  console.log('\n💾 TESTING BACKUP MANAGEMENT SYSTEM');
  console.log('='.repeat(50));
  
  await testEndpoint('/api/admin/backup-management');
  
  // Test manual backup creation
  await testEndpoint('/api/admin/backup/create', 'POST', {
    type: 'incremental',
    description: 'Test backup created from endpoint test'
  });
  
  // Test API Management System
  console.log('\n🔌 TESTING API MANAGEMENT SYSTEM');
  console.log('='.repeat(50));
  
  await testEndpoint('/api/admin/api-management?range=24h');
  await testEndpoint('/api/admin/api-management?range=7d');
  
  // Test API key creation
  await testEndpoint('/api/admin/api-keys', 'POST', {
    name: 'Test API Key',
    tenant_id: 1,
    permissions: ['orders:read', 'products:read'],
    rate_limit: 1000
  });
  
  // Test rate limit updates
  await testEndpoint('/api/admin/rate-limits', 'PUT', {
    endpoint: '/api/test',
    limits: {
      requests_per_minute: 100,
      requests_per_hour: 5000,
      burst_limit: 20
    }
  });
  
  console.log('\n🎉 Phase 2B Endpoint Testing Complete!');
}

// Run the tests
runTests().catch(console.error);
