const XLSX = require('xlsx');

// Create sample data
const data = [
  {
    name: "Test Beer",
    price: 5.99,
    category: "beer",
    description: "Test product",
    inStock: true
  },
  {
    name: "Test Wine",
    price: 12.99,
    category: "wine",
    description: "Test wine product",
    inStock: true
  }
];

// Create workbook and worksheet
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.json_to_sheet(data);
XLSX.utils.book_append_sheet(workbook, worksheet, "Products");

// Write to file
XLSX.writeFile(workbook, "test-products.xlsx");
