// PostgreSQL Database Connection Test
// Database: BARPOS, Host: localhost, User: BARPOS, Password: Chaand@0319, Port: 5432

const { Pool } = require('pg');

// Database configuration
const dbConfig = {
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

async function testDatabaseConnection() {
  console.log('🔍 POSTGRESQL DATABASE CONNECTION TEST');
  console.log('=====================================');
  console.log(`📊 Database: ${dbConfig.database}`);
  console.log(`🏠 Host: ${dbConfig.host}:${dbConfig.port}`);
  console.log(`👤 User: ${dbConfig.user}`);
  console.log('');

  const pool = new Pool(dbConfig);

  try {
    console.log('🔄 Testing database connection...');
    
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Test database version
    const versionResult = await client.query('SELECT version()');
    console.log(`✅ PostgreSQL Version: ${versionResult.rows[0].version.split(' ')[0]} ${versionResult.rows[0].version.split(' ')[1]}`);
    
    // Test current timestamp
    const timeResult = await client.query('SELECT NOW() as current_time');
    console.log(`✅ Database Time: ${timeResult.rows[0].current_time}`);
    
    // Test database name
    const dbResult = await client.query('SELECT current_database()');
    console.log(`✅ Current Database: ${dbResult.rows[0].current_database}`);
    
    // Test user permissions
    const userResult = await client.query('SELECT current_user, session_user');
    console.log(`✅ Current User: ${userResult.rows[0].current_user}`);
    console.log(`✅ Session User: ${userResult.rows[0].session_user}`);
    
    // Test if BARPOS database exists and has tables
    console.log('\n🔍 Checking database schema...');
    
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    if (tablesResult.rows.length > 0) {
      console.log(`✅ Found ${tablesResult.rows.length} tables in database:`);
      tablesResult.rows.forEach(row => {
        console.log(`   📋 ${row.table_name}`);
      });
    } else {
      console.log('⚠️  No tables found in database - database may need initialization');
    }
    
    // Test specific tables for Super Admin Dashboard
    const requiredTables = ['tenants', 'users', 'transactions', 'security_audits', 'system_activity'];
    console.log('\n🔍 Checking required tables for Super Admin Dashboard...');
    
    for (const tableName of requiredTables) {
      try {
        const tableCheck = await client.query(`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_name = $1
        `, [tableName]);
        
        if (tableCheck.rows[0].count > 0) {
          // Check row count
          const rowCount = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
          console.log(`✅ Table '${tableName}' exists with ${rowCount.rows[0].count} rows`);
        } else {
          console.log(`❌ Table '${tableName}' missing - needs to be created`);
        }
      } catch (error) {
        console.log(`❌ Error checking table '${tableName}': ${error.message}`);
      }
    }
    
    // Test sample data query
    console.log('\n🔍 Testing sample data queries...');
    
    try {
      const tenantQuery = await client.query('SELECT COUNT(*) as count FROM tenants');
      console.log(`✅ Tenants table query successful: ${tenantQuery.rows[0].count} tenants`);
    } catch (error) {
      console.log(`❌ Tenants table query failed: ${error.message}`);
    }
    
    try {
      const userQuery = await client.query('SELECT COUNT(*) as count FROM users');
      console.log(`✅ Users table query successful: ${userQuery.rows[0].count} users`);
    } catch (error) {
      console.log(`❌ Users table query failed: ${error.message}`);
    }
    
    client.release();
    
    console.log('\n🎉 DATABASE CONNECTION TEST COMPLETED SUCCESSFULLY!');
    console.log('✅ PostgreSQL server is running and accessible');
    console.log('✅ Database credentials are correct');
    console.log('✅ BARPOS database is accessible');
    console.log('✅ User permissions are working');
    
  } catch (error) {
    console.error('\n💥 DATABASE CONNECTION TEST FAILED!');
    console.error('❌ Error details:', error.message);
    console.error('❌ Error code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔧 Solution: PostgreSQL server is not running or not accepting connections');
      console.error('   - Check if PostgreSQL service is started');
      console.error('   - Verify port 5432 is not blocked by firewall');
    } else if (error.code === '28P01') {
      console.error('🔧 Solution: Authentication failed');
      console.error('   - Check username and password');
      console.error('   - Verify user exists in PostgreSQL');
    } else if (error.code === '3D000') {
      console.error('🔧 Solution: Database does not exist');
      console.error('   - Create BARPOS database');
      console.error('   - Run database initialization script');
    } else {
      console.error('🔧 Solution: Check PostgreSQL configuration and network connectivity');
    }
    
    return false;
  } finally {
    await pool.end();
  }
  
  return true;
}

// Run the test
testDatabaseConnection()
  .then(success => {
    if (success) {
      console.log('\n🚀 Ready to start Super Admin Dashboard with PostgreSQL!');
      process.exit(0);
    } else {
      console.log('\n🛠️  Database needs to be configured before starting Super Admin Dashboard');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
