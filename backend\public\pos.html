<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BARPOS - Point of Sale System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .table-seat {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .table-seat:hover {
            transform: scale(1.05);
        }
        .table-occupied {
            background-color: #ef4444;
        }
        .table-available {
            background-color: #10b981;
        }
        .table-reserved {
            background-color: #f59e0b;
        }
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #374151;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 3px;
        }
        .modal-backdrop {
            backdrop-filter: blur(4px);
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Top Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700 sticky top-0 z-40">
        <div class="max-w-full mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold text-white">BARPOS</h1>
                    <div class="hidden md:flex space-x-1">
                        <button onclick="showView('floor')" id="floorBtn" class="nav-btn bg-purple-600 text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="layout-grid" class="h-4 w-4 mr-2"></i>Floor
                        </button>
                        <button onclick="showView('orders')" id="ordersBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="clipboard-list" class="h-4 w-4 mr-2"></i>Orders
                        </button>
                        <button onclick="showView('inventory')" id="inventoryBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="package" class="h-4 w-4 mr-2"></i>Inventory
                        </button>
                        <button onclick="showView('sales')" id="salesBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="trending-up" class="h-4 w-4 mr-2"></i>Sales
                        </button>
                        <button onclick="showView('scheduling')" id="schedulingBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="calendar" class="h-4 w-4 mr-2"></i>Schedule
                        </button>
                        <button onclick="showView('loyalty')" id="loyaltyBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="heart" class="h-4 w-4 mr-2"></i>Loyalty
                        </button>
                        <button onclick="showView('settings')" id="settingsBtn" class="nav-btn text-gray-300 hover:text-white px-4 py-2 rounded-md text-sm">
                            <i data-lucide="settings" class="h-4 w-4 mr-2"></i>Settings
                        </button>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-300">
                        <span id="currentUser">John Doe</span> | <span id="currentTime"></span>
                    </div>
                    <button onclick="logout()" class="text-gray-300 hover:text-white">
                        <i data-lucide="log-out" class="h-5 w-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Floor Layout View -->
    <div id="floorView" class="view-content">
        <div class="flex h-screen">
            <!-- Restaurant Floor Layout -->
            <div class="flex-1 p-6 bg-gray-900">
                <div class="mb-4 flex justify-between items-center">
                    <h2 class="text-2xl font-bold">Restaurant Floor</h2>
                    <div class="flex space-x-2">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-green-500 rounded"></div>
                            <span class="text-sm">Available</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-red-500 rounded"></div>
                            <span class="text-sm">Occupied</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                            <span class="text-sm">Reserved</span>
                        </div>
                    </div>
                </div>
                
                <!-- Floor Grid -->
                <div class="grid grid-cols-8 gap-4 h-full max-h-[calc(100vh-200px)]" id="floorGrid">
                    <!-- Tables will be dynamically generated -->
                </div>
            </div>

            <!-- Quick Stats Sidebar -->
            <div class="w-80 bg-gray-800 p-6 border-l border-gray-700">
                <h3 class="text-lg font-semibold mb-4">Today's Overview</h3>
                <div class="space-y-4">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Total Sales</span>
                            <span class="text-2xl font-bold text-green-400" id="todaySales">$0.00</span>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Orders</span>
                            <span class="text-2xl font-bold text-blue-400" id="todayOrders">0</span>
                        </div>
                    </div>
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-300">Tables Occupied</span>
                            <span class="text-2xl font-bold text-yellow-400" id="occupiedTables">0/20</span>
                        </div>
                    </div>
                </div>

                <h3 class="text-lg font-semibold mt-8 mb-4">Active Orders</h3>
                <div class="space-y-2 max-h-64 overflow-y-auto scrollbar-thin" id="activeOrdersList">
                    <!-- Active orders will be listed here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Order Panel Modal -->
    <div id="orderModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-gray-800 rounded-lg w-full max-w-6xl h-[90vh] flex">
            <!-- Menu Categories & Items -->
            <div class="flex-1 p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold" id="orderModalTitle">Table 1 - New Order</h3>
                    <button onclick="closeOrderModal()" class="text-gray-400 hover:text-white">
                        <i data-lucide="x" class="h-6 w-6"></i>
                    </button>
                </div>

                <!-- Category Tabs -->
                <div class="flex space-x-2 mb-4 overflow-x-auto" id="categoryTabs">
                    <!-- Categories will be loaded here -->
                </div>

                <!-- Menu Items Grid -->
                <div class="grid grid-cols-3 gap-4 h-[calc(100%-120px)] overflow-y-auto scrollbar-thin" id="menuItemsGrid">
                    <!-- Menu items will be loaded here -->
                </div>
            </div>

            <!-- Order Summary -->
            <div class="w-96 bg-gray-700 p-6 border-l border-gray-600">
                <h4 class="text-lg font-semibold mb-4">Current Order</h4>
                
                <!-- Order Items -->
                <div class="space-y-2 max-h-64 overflow-y-auto scrollbar-thin mb-4" id="orderItems">
                    <!-- Order items will be listed here -->
                </div>

                <!-- Order Totals -->
                <div class="border-t border-gray-600 pt-4 space-y-2">
                    <div class="flex justify-between">
                        <span>Subtotal:</span>
                        <span id="orderSubtotal">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Tax:</span>
                        <span id="orderTax">$0.00</span>
                    </div>
                    <div class="flex justify-between text-lg font-bold">
                        <span>Total:</span>
                        <span id="orderTotal">$0.00</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 space-y-2">
                    <button onclick="sendToKitchen()" class="w-full bg-blue-600 hover:bg-blue-500 text-white py-3 rounded-md font-medium">
                        Send to Kitchen
                    </button>
                    <button onclick="showPaymentModal()" class="w-full bg-green-600 hover:bg-green-500 text-white py-3 rounded-md font-medium">
                        Process Payment
                    </button>
                    <button onclick="holdOrder()" class="w-full bg-yellow-600 hover:bg-yellow-500 text-white py-3 rounded-md font-medium">
                        Hold Order
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 modal-backdrop flex items-center justify-center p-4 z-60 hidden">
        <div class="bg-gray-800 rounded-lg w-full max-w-2xl p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold">Process Payment</h3>
                <button onclick="closePaymentModal()" class="text-gray-400 hover:text-white">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>

            <div class="grid grid-cols-2 gap-6">
                <!-- Order Summary -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Order Summary</h4>
                    <div class="space-y-2 max-h-48 overflow-y-auto scrollbar-thin" id="paymentOrderItems">
                        <!-- Order items for payment -->
                    </div>
                    <div class="border-t border-gray-600 pt-4 mt-4">
                        <div class="flex justify-between">
                            <span>Subtotal:</span>
                            <span id="paymentSubtotal">$0.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Tax:</span>
                            <span id="paymentTax">$0.00</span>
                        </div>
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total:</span>
                            <span id="paymentTotal">$0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Options -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Payment Method</h4>
                    <div class="space-y-3">
                        <button onclick="selectPaymentMethod('cash')" class="payment-method w-full bg-gray-700 hover:bg-gray-600 p-4 rounded-lg text-left">
                            <div class="flex items-center">
                                <i data-lucide="dollar-sign" class="h-6 w-6 mr-3"></i>
                                <span>Cash</span>
                            </div>
                        </button>
                        <button onclick="selectPaymentMethod('card')" class="payment-method w-full bg-gray-700 hover:bg-gray-600 p-4 rounded-lg text-left">
                            <div class="flex items-center">
                                <i data-lucide="credit-card" class="h-6 w-6 mr-3"></i>
                                <span>Credit/Debit Card</span>
                            </div>
                        </button>
                        <button onclick="selectPaymentMethod('mobile')" class="payment-method w-full bg-gray-700 hover:bg-gray-600 p-4 rounded-lg text-left">
                            <div class="flex items-center">
                                <i data-lucide="smartphone" class="h-6 w-6 mr-3"></i>
                                <span>Mobile Wallet</span>
                            </div>
                        </button>
                    </div>

                    <!-- Tip Entry -->
                    <div class="mt-6">
                        <label class="block text-sm font-medium mb-2">Tip Amount</label>
                        <div class="flex space-x-2">
                            <button onclick="setTipPercent(15)" class="tip-btn flex-1 bg-gray-700 hover:bg-gray-600 py-2 rounded">15%</button>
                            <button onclick="setTipPercent(18)" class="tip-btn flex-1 bg-gray-700 hover:bg-gray-600 py-2 rounded">18%</button>
                            <button onclick="setTipPercent(20)" class="tip-btn flex-1 bg-gray-700 hover:bg-gray-600 py-2 rounded">20%</button>
                        </div>
                        <input type="number" id="customTip" placeholder="Custom tip amount" class="w-full mt-2 bg-gray-700 text-white px-3 py-2 rounded-md">
                    </div>

                    <button onclick="processPayment()" class="w-full mt-6 bg-green-600 hover:bg-green-500 text-white py-3 rounded-md font-medium">
                        Complete Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Other Views (Initially Hidden) -->
    <div id="ordersView" class="view-content hidden">
        <div class="p-6">
            <h2 class="text-2xl font-bold mb-6">Order Management</h2>
            <!-- Orders content will be added -->
        </div>
    </div>

    <div id="inventoryView" class="view-content hidden">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Inventory Dashboard</h2>
                <div class="flex space-x-3">
                    <button onclick="showAddInventoryModal()" class="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md">
                        <i data-lucide="plus" class="h-4 w-4 mr-2"></i>Add Item
                    </button>
                    <button onclick="adjustInventory()" class="bg-yellow-600 hover:bg-yellow-500 text-white px-4 py-2 rounded-md">
                        <i data-lucide="edit" class="h-4 w-4 mr-2"></i>Bulk Adjust
                    </button>
                    <button onclick="generateInventoryReport()" class="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded-md">
                        <i data-lucide="download" class="h-4 w-4 mr-2"></i>Export Report
                    </button>
                </div>
            </div>

            <!-- Inventory Stats -->
            <div class="grid grid-cols-4 gap-6 mb-6">
                <div class="bg-gray-800 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Total Items</p>
                            <p class="text-2xl font-bold text-white" id="totalInventoryItems">0</p>
                        </div>
                        <i data-lucide="package" class="h-8 w-8 text-blue-400"></i>
                    </div>
                </div>
                <div class="bg-gray-800 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Low Stock Alerts</p>
                            <p class="text-2xl font-bold text-red-400" id="lowStockCount">0</p>
                        </div>
                        <i data-lucide="alert-triangle" class="h-8 w-8 text-red-400"></i>
                    </div>
                </div>
                <div class="bg-gray-800 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Out of Stock</p>
                            <p class="text-2xl font-bold text-orange-400" id="outOfStockCount">0</p>
                        </div>
                        <i data-lucide="x-circle" class="h-8 w-8 text-orange-400"></i>
                    </div>
                </div>
                <div class="bg-gray-800 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-400 text-sm">Total Value</p>
                            <p class="text-2xl font-bold text-green-400" id="totalInventoryValue">$0</p>
                        </div>
                        <i data-lucide="dollar-sign" class="h-8 w-8 text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Inventory Table -->
            <div class="bg-gray-800 rounded-lg overflow-hidden">
                <div class="p-4 border-b border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold">Inventory Items</h3>
                        <div class="flex space-x-2">
                            <input type="text" id="inventorySearch" placeholder="Search items..." class="bg-gray-700 text-white px-3 py-2 rounded-md text-sm">
                            <select id="inventoryFilter" class="bg-gray-700 text-white px-3 py-2 rounded-md text-sm">
                                <option value="all">All Items</option>
                                <option value="low">Low Stock</option>
                                <option value="out">Out of Stock</option>
                                <option value="beverages">Beverages</option>
                                <option value="food">Food</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Item</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Category</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Current Stock</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Min. Threshold</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Unit Cost</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Total Value</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Status</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody" class="divide-y divide-gray-700">
                            <!-- Inventory items will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="salesView" class="view-content hidden">
        <div class="p-6">
            <h2 class="text-2xl font-bold mb-6">Sales Analytics</h2>
            <!-- Sales content will be added -->
        </div>
    </div>

    <div id="schedulingView" class="view-content hidden">
        <div class="p-6">
            <h2 class="text-2xl font-bold mb-6">Staff Scheduling</h2>
            <!-- Scheduling content will be added -->
        </div>
    </div>

    <div id="loyaltyView" class="view-content hidden">
        <div class="p-6">
            <h2 class="text-2xl font-bold mb-6">Loyalty Program</h2>
            <!-- Loyalty content will be added -->
        </div>
    </div>

    <div id="settingsView" class="view-content hidden">
        <div class="p-6">
            <h2 class="text-2xl font-bold mb-6">System Settings</h2>
            <!-- Settings content will be added -->
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- Toast notifications will appear here -->
    </div>

    <script>
        const API_URL = 'http://localhost:4000';
        
        // Global state
        let currentOrder = {
            tableId: null,
            items: [],
            subtotal: 0,
            tax: 0,
            total: 0
        };
        
        let floorLayout = [];
        let menuCategories = [];
        let menuItems = [];
        let selectedPaymentMethod = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            initializeFloorLayout();
            loadMenuData();
            loadTodayStats();
        });

        // Time display
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString();
        }

        // Navigation
        function showView(viewName) {
            // Hide all views
            document.querySelectorAll('.view-content').forEach(view => {
                view.classList.add('hidden');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('bg-purple-600', 'text-white');
                btn.classList.add('text-gray-300');
            });
            
            // Show selected view
            document.getElementById(viewName + 'View').classList.remove('hidden');
            
            // Activate nav button
            const activeBtn = document.getElementById(viewName + 'Btn');
            activeBtn.classList.add('bg-purple-600', 'text-white');
            activeBtn.classList.remove('text-gray-300');
        }

        // Floor Layout
        function initializeFloorLayout() {
            const floorGrid = document.getElementById('floorGrid');
            floorLayout = [];
            
            // Generate tables and bar seats
            for (let i = 1; i <= 20; i++) {
                const table = {
                    id: i,
                    type: i <= 15 ? 'table' : 'bar',
                    status: 'available', // available, occupied, reserved
                    capacity: i <= 15 ? (i % 3 === 0 ? 6 : 4) : 1,
                    currentOrder: null
                };
                floorLayout.push(table);
                
                const tableElement = document.createElement('div');
                tableElement.className = `table-seat table-available bg-green-500 rounded-lg p-4 text-center cursor-pointer hover:scale-105 transition-all`;
                tableElement.onclick = () => openTable(table.id);
                
                tableElement.innerHTML = `
                    <div class="text-white font-bold">
                        ${table.type === 'table' ? 'Table' : 'Bar'} ${i}
                    </div>
                    <div class="text-xs text-white opacity-75">
                        ${table.capacity} ${table.capacity === 1 ? 'seat' : 'seats'}
                    </div>
                `;
                
                floorGrid.appendChild(tableElement);
            }
        }

        // Open table for ordering
        function openTable(tableId) {
            const table = floorLayout.find(t => t.id === tableId);
            if (!table) return;
            
            currentOrder.tableId = tableId;
            document.getElementById('orderModalTitle').textContent = 
                `${table.type === 'table' ? 'Table' : 'Bar'} ${tableId} - New Order`;
            
            document.getElementById('orderModal').classList.remove('hidden');
            loadMenuCategories();
        }

        // Close order modal
        function closeOrderModal() {
            document.getElementById('orderModal').classList.add('hidden');
            currentOrder = { tableId: null, items: [], subtotal: 0, tax: 0, total: 0 };
            updateOrderSummary();
        }

        // Load menu data
        async function loadMenuData() {
            try {
                const [categoriesResponse, itemsResponse] = await Promise.all([
                    fetch(`${API_URL}/categories`),
                    fetch(`${API_URL}/products`)
                ]);
                
                menuCategories = await categoriesResponse.json();
                menuItems = await itemsResponse.json();
            } catch (error) {
                console.error('Error loading menu data:', error);
                showToast('Error loading menu data', 'error');
            }
        }

        // Load menu categories
        function loadMenuCategories() {
            const categoryTabs = document.getElementById('categoryTabs');
            categoryTabs.innerHTML = '';
            
            menuCategories.forEach((category, index) => {
                const tab = document.createElement('button');
                tab.className = `px-4 py-2 rounded-md text-sm font-medium ${index === 0 ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`;
                tab.textContent = category.name;
                tab.onclick = () => loadMenuItems(category.id, tab);
                categoryTabs.appendChild(tab);
            });
            
            // Load first category by default
            if (menuCategories.length > 0) {
                loadMenuItems(menuCategories[0].id);
            }
        }

        // Load menu items for category
        function loadMenuItems(categoryId, activeTab = null) {
            // Update active tab
            if (activeTab) {
                document.querySelectorAll('#categoryTabs button').forEach(btn => {
                    btn.className = 'px-4 py-2 rounded-md text-sm font-medium bg-gray-700 text-gray-300 hover:bg-gray-600';
                });
                activeTab.className = 'px-4 py-2 rounded-md text-sm font-medium bg-purple-600 text-white';
            }
            
            const menuItemsGrid = document.getElementById('menuItemsGrid');
            const categoryItems = menuItems.filter(item => item.category_id === categoryId);
            
            menuItemsGrid.innerHTML = categoryItems.map(item => `
                <div class="bg-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-600 transition-colors" onclick="addToOrder(${item.id})">
                    <h4 class="font-medium text-white mb-2">${item.name}</h4>
                    <p class="text-gray-400 text-sm mb-2">${item.description || ''}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-green-400 font-bold">$${parseFloat(item.price).toFixed(2)}</span>
                        <span class="text-xs ${item.in_stock ? 'text-green-400' : 'text-red-400'}">
                            ${item.in_stock ? 'In Stock' : 'Out of Stock'}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // Add item to order
        function addToOrder(itemId) {
            const item = menuItems.find(i => i.id === itemId);
            if (!item || !item.in_stock) return;
            
            const existingItem = currentOrder.items.find(i => i.id === itemId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                currentOrder.items.push({
                    id: itemId,
                    name: item.name,
                    price: parseFloat(item.price),
                    quantity: 1
                });
            }
            
            updateOrderSummary();
            showToast(`${item.name} added to order`, 'success');
        }

        // Update order summary
        function updateOrderSummary() {
            const orderItems = document.getElementById('orderItems');
            const paymentOrderItems = document.getElementById('paymentOrderItems');
            
            if (currentOrder.items.length === 0) {
                orderItems.innerHTML = '<p class="text-gray-400 text-center py-4">No items added</p>';
                if (paymentOrderItems) paymentOrderItems.innerHTML = orderItems.innerHTML;
            } else {
                const itemsHtml = currentOrder.items.map(item => `
                    <div class="flex justify-between items-center bg-gray-600 p-3 rounded">
                        <div>
                            <div class="font-medium">${item.name}</div>
                            <div class="text-sm text-gray-400">$${item.price.toFixed(2)} each</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="updateQuantity(${item.id}, -1)" class="bg-red-600 hover:bg-red-500 w-6 h-6 rounded text-xs">-</button>
                            <span class="w-8 text-center">${item.quantity}</span>
                            <button onclick="updateQuantity(${item.id}, 1)" class="bg-green-600 hover:bg-green-500 w-6 h-6 rounded text-xs">+</button>
                        </div>
                    </div>
                `).join('');
                
                orderItems.innerHTML = itemsHtml;
                if (paymentOrderItems) paymentOrderItems.innerHTML = itemsHtml;
            }
            
            // Calculate totals
            currentOrder.subtotal = currentOrder.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            currentOrder.tax = currentOrder.subtotal * 0.0825; // 8.25% tax rate
            currentOrder.total = currentOrder.subtotal + currentOrder.tax;
            
            // Update display
            document.getElementById('orderSubtotal').textContent = `$${currentOrder.subtotal.toFixed(2)}`;
            document.getElementById('orderTax').textContent = `$${currentOrder.tax.toFixed(2)}`;
            document.getElementById('orderTotal').textContent = `$${currentOrder.total.toFixed(2)}`;
            
            // Update payment modal if open
            if (document.getElementById('paymentSubtotal')) {
                document.getElementById('paymentSubtotal').textContent = `$${currentOrder.subtotal.toFixed(2)}`;
                document.getElementById('paymentTax').textContent = `$${currentOrder.tax.toFixed(2)}`;
                document.getElementById('paymentTotal').textContent = `$${currentOrder.total.toFixed(2)}`;
            }
        }

        // Update item quantity
        function updateQuantity(itemId, change) {
            const item = currentOrder.items.find(i => i.id === itemId);
            if (!item) return;
            
            item.quantity += change;
            if (item.quantity <= 0) {
                currentOrder.items = currentOrder.items.filter(i => i.id !== itemId);
            }
            
            updateOrderSummary();
        }

        // Order actions
        function sendToKitchen() {
            if (currentOrder.items.length === 0) {
                showToast('No items in order', 'error');
                return;
            }
            
            // Here you would send the order to kitchen system
            showToast('Order sent to kitchen', 'success');
            
            // Update table status
            const tableElement = document.querySelector(`[onclick="openTable(${currentOrder.tableId})"]`);
            if (tableElement) {
                tableElement.className = tableElement.className.replace('table-available bg-green-500', 'table-occupied bg-red-500');
            }
        }

        function holdOrder() {
            if (currentOrder.items.length === 0) {
                showToast('No items in order', 'error');
                return;
            }
            
            // Save order for later
            showToast('Order held', 'success');
            closeOrderModal();
        }

        // Payment functions
        function showPaymentModal() {
            if (currentOrder.items.length === 0) {
                showToast('No items in order', 'error');
                return;
            }
            
            updateOrderSummary(); // Ensure payment modal has latest data
            document.getElementById('paymentModal').classList.remove('hidden');
        }

        function closePaymentModal() {
            document.getElementById('paymentModal').classList.add('hidden');
            selectedPaymentMethod = null;
        }

        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;
            document.querySelectorAll('.payment-method').forEach(btn => {
                btn.classList.remove('bg-purple-600');
                btn.classList.add('bg-gray-700');
            });
            event.target.closest('.payment-method').classList.remove('bg-gray-700');
            event.target.closest('.payment-method').classList.add('bg-purple-600');
        }

        function setTipPercent(percent) {
            const tipAmount = (currentOrder.total * percent / 100);
            document.getElementById('customTip').value = tipAmount.toFixed(2);
            
            document.querySelectorAll('.tip-btn').forEach(btn => {
                btn.classList.remove('bg-purple-600');
                btn.classList.add('bg-gray-700');
            });
            event.target.classList.remove('bg-gray-700');
            event.target.classList.add('bg-purple-600');
        }

        function processPayment() {
            if (!selectedPaymentMethod) {
                showToast('Please select a payment method', 'error');
                return;
            }
            
            const tipAmount = parseFloat(document.getElementById('customTip').value) || 0;
            const finalTotal = currentOrder.total + tipAmount;
            
            // Process payment (integrate with payment processor)
            showToast(`Payment of $${finalTotal.toFixed(2)} processed successfully`, 'success');
            
            // Clear table
            const tableElement = document.querySelector(`[onclick="openTable(${currentOrder.tableId})"]`);
            if (tableElement) {
                tableElement.className = tableElement.className.replace('table-occupied bg-red-500', 'table-available bg-green-500');
            }
            
            closePaymentModal();
            closeOrderModal();
            
            // Update stats
            loadTodayStats();
        }

        // Load today's statistics
        async function loadTodayStats() {
            try {
                // Mock data for now - replace with actual API calls
                document.getElementById('todaySales').textContent = '$1,247.50';
                document.getElementById('todayOrders').textContent = '23';
                document.getElementById('occupiedTables').textContent = '3/20';
                
                // Load active orders
                const activeOrdersList = document.getElementById('activeOrdersList');
                activeOrdersList.innerHTML = `
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="flex justify-between">
                            <span class="font-medium">Table 5</span>
                            <span class="text-sm text-gray-400">12:30 PM</span>
                        </div>
                        <div class="text-sm text-gray-400">2 items - $24.50</div>
                    </div>
                    <div class="bg-gray-700 p-3 rounded">
                        <div class="flex justify-between">
                            <span class="font-medium">Bar 3</span>
                            <span class="text-sm text-gray-400">1:15 PM</span>
                        </div>
                        <div class="text-sm text-gray-400">1 item - $12.00</div>
                    </div>
                `;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Toast notifications
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            
            const bgColor = type === 'success' ? 'bg-green-600' : 
                           type === 'error' ? 'bg-red-600' : 'bg-blue-600';
            
            toast.className = `${bgColor} text-white px-4 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i data-lucide="x" class="h-4 w-4"></i>
                    </button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            lucide.createIcons();
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // Inventory Management Functions
        let inventoryData = [];

        // Load inventory data when inventory view is shown
        function showView(viewName) {
            // Hide all views
            document.querySelectorAll('.view-content').forEach(view => {
                view.classList.add('hidden');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('bg-purple-600', 'text-white');
                btn.classList.add('text-gray-300');
            });
            
            // Show selected view
            document.getElementById(viewName + 'View').classList.remove('hidden');
            
            // Activate nav button
            const activeBtn = document.getElementById(viewName + 'Btn');
            activeBtn.classList.add('bg-purple-600', 'text-white');
            activeBtn.classList.remove('text-gray-300');

            // Load data for specific views
            if (viewName === 'inventory') {
                loadInventoryData();
            }
        }

        async function loadInventoryData() {
            try {
                // Mock inventory data - replace with actual API call
                inventoryData = [
                    { id: 1, name: 'Beer - IPA', category: 'Beverages', currentStock: 24, minThreshold: 12, unitCost: 3.50, status: 'good' },
                    { id: 2, name: 'Vodka - Premium', category: 'Beverages', currentStock: 8, minThreshold: 10, unitCost: 25.00, status: 'low' },
                    { id: 3, name: 'Chicken Wings', category: 'Food', currentStock: 0, minThreshold: 5, unitCost: 8.00, status: 'out' },
                    { id: 4, name: 'French Fries', category: 'Food', currentStock: 15, minThreshold: 8, unitCost: 2.50, status: 'good' },
                    { id: 5, name: 'Wine - Red', category: 'Beverages', currentStock: 6, minThreshold: 8, unitCost: 18.00, status: 'low' },
                    { id: 6, name: 'Burger Patties', category: 'Food', currentStock: 20, minThreshold: 10, unitCost: 4.00, status: 'good' },
                ];

                updateInventoryStats();
                displayInventoryTable();
                setupInventoryFilters();
            } catch (error) {
                console.error('Error loading inventory:', error);
                showToast('Error loading inventory data', 'error');
            }
        }

        function updateInventoryStats() {
            const totalItems = inventoryData.length;
            const lowStockItems = inventoryData.filter(item => item.status === 'low').length;
            const outOfStockItems = inventoryData.filter(item => item.status === 'out').length;
            const totalValue = inventoryData.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0);

            document.getElementById('totalInventoryItems').textContent = totalItems;
            document.getElementById('lowStockCount').textContent = lowStockItems;
            document.getElementById('outOfStockCount').textContent = outOfStockItems;
            document.getElementById('totalInventoryValue').textContent = `$${totalValue.toFixed(2)}`;
        }

        function displayInventoryTable(filteredData = null) {
            const data = filteredData || inventoryData;
            const tbody = document.getElementById('inventoryTableBody');
            
            tbody.innerHTML = data.map(item => {
                const statusColor = item.status === 'good' ? 'text-green-400' : 
                                  item.status === 'low' ? 'text-yellow-400' : 'text-red-400';
                const statusText = item.status === 'good' ? 'In Stock' : 
                                 item.status === 'low' ? 'Low Stock' : 'Out of Stock';
                
                return `
                    <tr class="hover:bg-gray-700">
                        <td class="px-4 py-3 text-white">${item.name}</td>
                        <td class="px-4 py-3 text-gray-300">${item.category}</td>
                        <td class="px-4 py-3 text-white">${item.currentStock}</td>
                        <td class="px-4 py-3 text-gray-300">${item.minThreshold}</td>
                        <td class="px-4 py-3 text-white">$${item.unitCost.toFixed(2)}</td>
                        <td class="px-4 py-3 text-white">$${(item.currentStock * item.unitCost).toFixed(2)}</td>
                        <td class="px-4 py-3 ${statusColor}">${statusText}</td>
                        <td class="px-4 py-3">
                            <div class="flex space-x-2">
                                <button onclick="editInventoryItem(${item.id})" class="text-blue-400 hover:text-blue-300">
                                    <i data-lucide="edit" class="h-4 w-4"></i>
                                </button>
                                <button onclick="adjustStock(${item.id})" class="text-yellow-400 hover:text-yellow-300">
                                    <i data-lucide="plus-minus" class="h-4 w-4"></i>
                                </button>
                                <button onclick="deleteInventoryItem(${item.id})" class="text-red-400 hover:text-red-300">
                                    <i data-lucide="trash-2" class="h-4 w-4"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
            
            lucide.createIcons();
        }

        function setupInventoryFilters() {
            const searchInput = document.getElementById('inventorySearch');
            const filterSelect = document.getElementById('inventoryFilter');

            searchInput.addEventListener('input', filterInventory);
            filterSelect.addEventListener('change', filterInventory);
        }

        function filterInventory() {
            const searchTerm = document.getElementById('inventorySearch').value.toLowerCase();
            const filterValue = document.getElementById('inventoryFilter').value;

            let filteredData = inventoryData;

            // Apply search filter
            if (searchTerm) {
                filteredData = filteredData.filter(item => 
                    item.name.toLowerCase().includes(searchTerm) ||
                    item.category.toLowerCase().includes(searchTerm)
                );
            }

            // Apply category/status filter
            if (filterValue !== 'all') {
                if (filterValue === 'low') {
                    filteredData = filteredData.filter(item => item.status === 'low');
                } else if (filterValue === 'out') {
                    filteredData = filteredData.filter(item => item.status === 'out');
                } else {
                    filteredData = filteredData.filter(item => 
                        item.category.toLowerCase().includes(filterValue.toLowerCase())
                    );
                }
            }

            displayInventoryTable(filteredData);
        }

        function showAddInventoryModal() {
            showToast('Add inventory item functionality would be implemented here', 'info');
        }

        function editInventoryItem(itemId) {
            const item = inventoryData.find(i => i.id === itemId);
            if (item) {
                showToast(`Edit ${item.name} functionality would be implemented here`, 'info');
            }
        }

        function adjustStock(itemId) {
            const item = inventoryData.find(i => i.id === itemId);
            if (item) {
                const adjustment = prompt(`Current stock: ${item.currentStock}\nEnter adjustment (+/- number):`);
                if (adjustment !== null && !isNaN(adjustment)) {
                    const newStock = Math.max(0, item.currentStock + parseInt(adjustment));
                    item.currentStock = newStock;
                    
                    // Update status based on new stock level
                    if (newStock === 0) {
                        item.status = 'out';
                    } else if (newStock <= item.minThreshold) {
                        item.status = 'low';
                    } else {
                        item.status = 'good';
                    }
                    
                    updateInventoryStats();
                    displayInventoryTable();
                    showToast(`${item.name} stock updated to ${newStock}`, 'success');
                }
            }
        }

        function deleteInventoryItem(itemId) {
            const item = inventoryData.find(i => i.id === itemId);
            if (item && confirm(`Are you sure you want to delete ${item.name}?`)) {
                inventoryData = inventoryData.filter(i => i.id !== itemId);
                updateInventoryStats();
                displayInventoryTable();
                showToast(`${item.name} deleted from inventory`, 'success');
            }
        }

        function adjustInventory() {
            showToast('Bulk inventory adjustment functionality would be implemented here', 'info');
        }

        function generateInventoryReport() {
            // Generate CSV report
            const headers = ['Item', 'Category', 'Current Stock', 'Min Threshold', 'Unit Cost', 'Total Value', 'Status'];
            const csvContent = [
                headers.join(','),
                ...inventoryData.map(item => [
                    item.name,
                    item.category,
                    item.currentStock,
                    item.minThreshold,
                    item.unitCost,
                    (item.currentStock * item.unitCost).toFixed(2),
                    item.status === 'good' ? 'In Stock' : item.status === 'low' ? 'Low Stock' : 'Out of Stock'
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showToast('Inventory report exported successfully', 'success');
        }

        // Logout function
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/';
            }
        }
