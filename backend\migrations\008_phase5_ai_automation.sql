-- Phase 5: AI & Automation Migration
-- Database: BARPOS, PostgreSQL
-- This migration adds comprehensive AI and automation capabilities

-- =====================================================
-- AI FRAUD DETECTION MODELS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_fraud_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(20) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL, -- 'neural_network', 'random_forest', 'svm', 'ensemble'
    training_data_size INTEGER DEFAULT 0,
    accuracy_score DECIMAL(5,4) DEFAULT 0,
    precision_score DECIMAL(5,4) DEFAULT 0,
    recall_score DECIMAL(5,4) DEFAULT 0,
    f1_score DECIMAL(5,4) DEFAULT 0,
    false_positive_rate DECIMAL(5,4) DEFAULT 0,
    false_negative_rate DECIMAL(5,4) DEFAULT 0,
    last_trained TIMESTAMP,
    training_duration_minutes INTEGER,
    is_active BOOLEAN DEFAULT true,
    model_parameters JSONB DEFAULT '{}',
    feature_importance JSONB DEFAULT '{}',
    validation_results JSONB DEFAULT '{}',
    deployment_status VARCHAR(20) DEFAULT 'training', -- 'training', 'testing', 'deployed', 'retired'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, model_name, model_version)
);

-- =====================================================
-- TRANSACTION RISK ASSESSMENTS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_transaction_risks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID REFERENCES payment_transactions(id) ON DELETE CASCADE,
    risk_score DECIMAL(5,4) NOT NULL CHECK (risk_score >= 0 AND risk_score <= 1),
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    fraud_indicators JSONB DEFAULT '{}',
    risk_factors JSONB DEFAULT '{}',
    ai_model_id UUID REFERENCES ai_fraud_models(id),
    processing_time_ms INTEGER,
    confidence_score DECIMAL(5,4),
    action_taken VARCHAR(50) DEFAULT 'approved', -- 'approved', 'flagged', 'blocked', 'manual_review'
    action_reason TEXT,
    reviewed_by UUID,
    review_outcome VARCHAR(20), -- 'confirmed_fraud', 'false_positive', 'pending'
    review_notes TEXT,
    escalated BOOLEAN DEFAULT false,
    escalation_level INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP
);

-- =====================================================
-- CUSTOMER BEHAVIOR PROFILES
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_customer_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    customer_identifier VARCHAR(255) NOT NULL, -- email, phone, or customer_id
    customer_type VARCHAR(20) DEFAULT 'regular', -- 'new', 'regular', 'vip', 'at_risk'
    behavior_score DECIMAL(5,4) DEFAULT 0.5,
    spending_pattern JSONB DEFAULT '{}',
    visit_frequency JSONB DEFAULT '{}',
    preferred_payment_methods TEXT[] DEFAULT ARRAY[]::TEXT[],
    preferred_order_times JSONB DEFAULT '{}',
    favorite_items JSONB DEFAULT '{}',
    risk_factors JSONB DEFAULT '{}',
    loyalty_score DECIMAL(5,4) DEFAULT 0,
    churn_probability DECIMAL(5,4) DEFAULT 0,
    lifetime_value DECIMAL(12,2) DEFAULT 0,
    acquisition_cost DECIMAL(10,2) DEFAULT 0,
    last_order_date DATE,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0,
    avg_order_value DECIMAL(10,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, customer_identifier)
);

-- =====================================================
-- PREDICTIVE MODELS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_prediction_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    model_type VARCHAR(50) NOT NULL, -- 'sales_forecast', 'demand_prediction', 'inventory_optimization', 'staffing_forecast'
    model_name VARCHAR(100) NOT NULL,
    prediction_horizon VARCHAR(20) NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
    algorithm_type VARCHAR(50) NOT NULL,
    accuracy_metrics JSONB DEFAULT '{}',
    feature_importance JSONB DEFAULT '{}',
    hyperparameters JSONB DEFAULT '{}',
    training_period_start DATE,
    training_period_end DATE,
    validation_score DECIMAL(5,4),
    mean_absolute_error DECIMAL(10,4),
    root_mean_square_error DECIMAL(10,4),
    last_prediction TIMESTAMP,
    next_training_scheduled TIMESTAMP,
    training_frequency VARCHAR(20) DEFAULT 'weekly', -- 'daily', 'weekly', 'monthly'
    is_active BOOLEAN DEFAULT true,
    model_status VARCHAR(20) DEFAULT 'training',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, model_type, model_name)
);

-- =====================================================
-- PREDICTION RESULTS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_predictions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID REFERENCES ai_prediction_models(id) ON DELETE CASCADE,
    prediction_date DATE NOT NULL,
    prediction_hour INTEGER CHECK (prediction_hour >= 0 AND prediction_hour <= 23),
    prediction_category VARCHAR(50), -- 'total_sales', 'item_demand', 'customer_count', 'staff_needed'
    target_identifier VARCHAR(255), -- product_id, location_id, etc.
    predicted_value DECIMAL(12,2) NOT NULL,
    confidence_interval JSONB DEFAULT '{}', -- {lower: number, upper: number, confidence_level: number}
    prediction_factors JSONB DEFAULT '{}',
    actual_value DECIMAL(12,2),
    accuracy_score DECIMAL(5,4),
    prediction_error DECIMAL(10,4),
    prediction_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- AI RECOMMENDATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL, -- 'upsell', 'cross_sell', 'payment_method', 'menu_optimization', 'pricing'
    target_entity_type VARCHAR(50) NOT NULL, -- 'customer', 'order', 'menu_item', 'employee', 'location'
    target_entity_id VARCHAR(255) NOT NULL,
    recommendation_title VARCHAR(200),
    recommendation_description TEXT,
    recommendation_data JSONB DEFAULT '{}',
    confidence_score DECIMAL(5,4) NOT NULL,
    expected_impact JSONB DEFAULT '{}', -- {revenue_increase: number, conversion_rate: number, cost_savings: number}
    priority_level VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected', 'expired', 'implemented'
    implementation_notes TEXT,
    actual_impact JSONB DEFAULT '{}',
    expires_at TIMESTAMP,
    accepted_at TIMESTAMP,
    implemented_at TIMESTAMP,
    created_by_model_id UUID REFERENCES ai_prediction_models(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- AUTOMATION WORKFLOWS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_automation_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    workflow_name VARCHAR(100) NOT NULL,
    workflow_description TEXT,
    workflow_type VARCHAR(50) NOT NULL, -- 'inventory_reorder', 'dynamic_pricing', 'staff_scheduling', 'maintenance_alert', 'customer_retention'
    trigger_conditions JSONB NOT NULL DEFAULT '{}',
    trigger_schedule VARCHAR(50), -- 'real_time', 'hourly', 'daily', 'weekly', 'monthly', 'on_demand'
    actions JSONB NOT NULL DEFAULT '{}',
    approval_required BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,4) DEFAULT 0,
    avg_execution_time_ms INTEGER DEFAULT 0,
    last_executed TIMESTAMP,
    last_success TIMESTAMP,
    last_failure TIMESTAMP,
    next_scheduled_execution TIMESTAMP,
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, workflow_name)
);

-- =====================================================
-- WORKFLOW EXECUTION LOGS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID REFERENCES ai_automation_workflows(id) ON DELETE CASCADE,
    execution_trigger VARCHAR(50) NOT NULL, -- 'scheduled', 'manual', 'event_driven', 'api_call'
    trigger_data JSONB DEFAULT '{}',
    execution_status VARCHAR(20) NOT NULL DEFAULT 'started', -- 'started', 'running', 'completed', 'failed', 'cancelled', 'timeout'
    actions_planned JSONB DEFAULT '{}',
    actions_performed JSONB DEFAULT '{}',
    actions_failed JSONB DEFAULT '{}',
    execution_time_ms INTEGER,
    memory_usage_mb INTEGER,
    cpu_usage_percent DECIMAL(5,2),
    error_message TEXT,
    error_stack TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- DYNAMIC PRICING RULES
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_dynamic_pricing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL,
    location_id UUID,
    base_price DECIMAL(10,2) NOT NULL,
    current_price DECIMAL(10,2) NOT NULL,
    min_price DECIMAL(10,2),
    max_price DECIMAL(10,2),
    pricing_strategy VARCHAR(50) DEFAULT 'demand_based', -- 'demand_based', 'time_based', 'inventory_based', 'competitor_based'
    pricing_factors JSONB DEFAULT '{}', -- {demand: number, inventory: number, time_of_day: number, weather: number, competition: number}
    price_elasticity DECIMAL(5,4),
    demand_sensitivity DECIMAL(5,4),
    last_price_change TIMESTAMP,
    price_change_frequency INTEGER DEFAULT 0, -- changes per day
    price_change_reason TEXT,
    revenue_impact DECIMAL(12,2) DEFAULT 0,
    volume_impact INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, product_id, location_id)
);

-- =====================================================
-- CUSTOMER SEGMENTATION
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_customer_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    segment_name VARCHAR(100) NOT NULL,
    segment_description TEXT,
    segment_criteria JSONB NOT NULL DEFAULT '{}',
    segmentation_algorithm VARCHAR(50) DEFAULT 'k_means', -- 'k_means', 'hierarchical', 'dbscan', 'rule_based'
    customer_count INTEGER DEFAULT 0,
    avg_order_value DECIMAL(10,2) DEFAULT 0,
    avg_visit_frequency DECIMAL(5,2) DEFAULT 0,
    avg_lifetime_value DECIMAL(12,2) DEFAULT 0,
    churn_risk DECIMAL(5,4) DEFAULT 0,
    profitability_score DECIMAL(5,4) DEFAULT 0,
    growth_potential DECIMAL(5,4) DEFAULT 0,
    recommended_actions JSONB DEFAULT '{}',
    marketing_strategies JSONB DEFAULT '{}',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, segment_name)
);

-- =====================================================
-- MENU OPTIMIZATION INSIGHTS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_menu_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL,
    location_id UUID,
    analysis_period_start DATE NOT NULL,
    analysis_period_end DATE NOT NULL,
    performance_score DECIMAL(5,4) DEFAULT 0,
    popularity_rank INTEGER,
    popularity_trend VARCHAR(20) DEFAULT 'stable', -- 'increasing', 'stable', 'decreasing', 'seasonal'
    profit_margin DECIMAL(5,4),
    profit_margin_rank INTEGER,
    velocity_score DECIMAL(5,4), -- how quickly item sells
    cross_sell_score DECIMAL(5,4),
    upsell_potential DECIMAL(5,4),
    cannibalization_risk DECIMAL(5,4),
    seasonal_patterns JSONB DEFAULT '{}',
    customer_sentiment DECIMAL(5,4),
    optimization_recommendations JSONB DEFAULT '{}',
    pricing_recommendations JSONB DEFAULT '{}',
    menu_placement_score DECIMAL(5,4),
    last_analyzed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, product_id, location_id, analysis_period_start, analysis_period_end)
);

-- =====================================================
-- AI SYSTEM PERFORMANCE METRICS
-- =====================================================

CREATE TABLE IF NOT EXISTS ai_system_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    metric_hour INTEGER CHECK (metric_hour >= 0 AND metric_hour <= 23),
    
    -- Fraud detection metrics
    fraud_checks_performed INTEGER DEFAULT 0,
    fraud_detected INTEGER DEFAULT 0,
    false_positives INTEGER DEFAULT 0,
    false_negatives INTEGER DEFAULT 0,
    fraud_detection_accuracy DECIMAL(5,4) DEFAULT 0,
    
    -- Prediction metrics
    predictions_generated INTEGER DEFAULT 0,
    prediction_accuracy DECIMAL(5,4) DEFAULT 0,
    forecast_errors DECIMAL(10,4) DEFAULT 0,
    
    -- Recommendation metrics
    recommendations_generated INTEGER DEFAULT 0,
    recommendations_accepted INTEGER DEFAULT 0,
    recommendation_conversion_rate DECIMAL(5,4) DEFAULT 0,
    revenue_from_recommendations DECIMAL(12,2) DEFAULT 0,
    
    -- Automation metrics
    workflows_executed INTEGER DEFAULT 0,
    workflows_successful INTEGER DEFAULT 0,
    automation_success_rate DECIMAL(5,4) DEFAULT 0,
    time_saved_minutes INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time_ms INTEGER DEFAULT 0,
    cpu_usage_percent DECIMAL(5,2) DEFAULT 0,
    memory_usage_mb INTEGER DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, metric_date, metric_hour)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_ai_transaction_risks_transaction ON ai_transaction_risks(transaction_id);
CREATE INDEX IF NOT EXISTS idx_ai_transaction_risks_risk_level ON ai_transaction_risks(risk_level);
CREATE INDEX IF NOT EXISTS idx_ai_transaction_risks_created ON ai_transaction_risks(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_customer_profiles_tenant ON ai_customer_profiles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_ai_customer_profiles_identifier ON ai_customer_profiles(customer_identifier);
CREATE INDEX IF NOT EXISTS idx_ai_customer_profiles_score ON ai_customer_profiles(behavior_score);

CREATE INDEX IF NOT EXISTS idx_ai_predictions_model ON ai_predictions(model_id);
CREATE INDEX IF NOT EXISTS idx_ai_predictions_date ON ai_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_ai_predictions_category ON ai_predictions(prediction_category);

CREATE INDEX IF NOT EXISTS idx_ai_recommendations_tenant ON ai_recommendations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);

CREATE INDEX IF NOT EXISTS idx_ai_workflows_tenant ON ai_automation_workflows(tenant_id);
CREATE INDEX IF NOT EXISTS idx_ai_workflows_type ON ai_automation_workflows(workflow_type);
CREATE INDEX IF NOT EXISTS idx_ai_workflows_active ON ai_automation_workflows(is_active);

CREATE INDEX IF NOT EXISTS idx_ai_workflow_executions_workflow ON ai_workflow_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_ai_workflow_executions_status ON ai_workflow_executions(execution_status);
CREATE INDEX IF NOT EXISTS idx_ai_workflow_executions_started ON ai_workflow_executions(started_at);

CREATE INDEX IF NOT EXISTS idx_ai_dynamic_pricing_tenant_product ON ai_dynamic_pricing(tenant_id, product_id);
CREATE INDEX IF NOT EXISTS idx_ai_dynamic_pricing_active ON ai_dynamic_pricing(is_active);

CREATE INDEX IF NOT EXISTS idx_ai_menu_insights_tenant_product ON ai_menu_insights(tenant_id, product_id);
CREATE INDEX IF NOT EXISTS idx_ai_menu_insights_period ON ai_menu_insights(analysis_period_start, analysis_period_end);

CREATE INDEX IF NOT EXISTS idx_ai_system_metrics_tenant_date ON ai_system_metrics(tenant_id, metric_date);

-- =====================================================
-- SAMPLE DATA FOR DEMONSTRATION
-- =====================================================

-- Insert sample AI fraud model for each tenant
INSERT INTO ai_fraud_models (tenant_id, model_name, model_version, algorithm_type, accuracy_score, precision_score, recall_score, f1_score, false_positive_rate, is_active, last_trained)
SELECT 
    id as tenant_id,
    'Fraud Detection Model v1' as model_name,
    '1.0.0' as model_version,
    'ensemble' as algorithm_type,
    0.9650 as accuracy_score,
    0.9420 as precision_score,
    0.9580 as recall_score,
    0.9500 as f1_score,
    0.0080 as false_positive_rate,
    true as is_active,
    NOW() - INTERVAL '1 day' as last_trained
FROM tenants
ON CONFLICT (tenant_id, model_name, model_version) DO NOTHING;

-- Insert sample prediction model for each tenant
INSERT INTO ai_prediction_models (tenant_id, model_type, model_name, prediction_horizon, algorithm_type, accuracy_metrics, validation_score, is_active)
SELECT 
    id as tenant_id,
    'sales_forecast' as model_type,
    'Daily Sales Predictor' as model_name,
    'daily' as prediction_horizon,
    'neural_network' as algorithm_type,
    '{"mae": 45.2, "rmse": 67.8, "mape": 8.5}' as accuracy_metrics,
    0.8750 as validation_score,
    true as is_active
FROM tenants
ON CONFLICT (tenant_id, model_type, model_name) DO NOTHING;

-- Insert sample automation workflow for each tenant
INSERT INTO ai_automation_workflows (tenant_id, workflow_name, workflow_description, workflow_type, trigger_conditions, actions, is_active)
SELECT 
    id as tenant_id,
    'Low Inventory Alert' as workflow_name,
    'Automatically alert managers when inventory levels are low' as workflow_description,
    'inventory_reorder' as workflow_type,
    '{"inventory_threshold": 10, "check_frequency": "hourly"}' as trigger_conditions,
    '{"send_alert": true, "suggest_reorder": true, "auto_reorder": false}' as actions,
    true as is_active
FROM tenants
ON CONFLICT (tenant_id, workflow_name) DO NOTHING;

-- Grant permissions to BARPOS user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "BARPOS";

-- Migration completed successfully
-- Phase 5: AI & Automation database schema is now ready
