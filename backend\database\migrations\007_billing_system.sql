-- 💰 RestroFlow Billing System Database Schema
-- Phase 7B: Customer Systems & Billing
-- Comprehensive subscription and billing management

-- ================================
-- BILLING CUSTOMERS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_customers (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    stripe_customer_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_subscription_id VARCHAR(255) UNIQUE,
    plan_id VARCHAR(50) NOT NULL DEFAULT 'starter',
    status VARCHAR(50) NOT NULL DEFAULT 'trialing',
    trial_start TIMESTAMP,
    trial_end TIMESTAMP,
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    cancelled_at TIMESTAMP,
    cancellation_reason TEXT,
    last_payment_date TIMESTAMP,
    next_payment_date TIMESTAMP,
    payment_method_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Indexes for performance
    CONSTRAINT unique_tenant_billing UNIQUE(tenant_id)
);

-- ================================
-- BILLING INVOICES TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_invoices (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    stripe_invoice_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    amount_due INTEGER NOT NULL, -- in cents
    amount_paid INTEGER DEFAULT 0, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    status VARCHAR(50) NOT NULL,
    invoice_number VARCHAR(100),
    invoice_url TEXT,
    hosted_invoice_url TEXT,
    invoice_pdf TEXT,
    due_date TIMESTAMP,
    paid_at TIMESTAMP,
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- BILLING TRANSACTIONS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_transactions (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    stripe_payment_intent_id VARCHAR(255),
    stripe_charge_id VARCHAR(255),
    invoice_id INTEGER REFERENCES billing_invoices(id),
    amount INTEGER NOT NULL, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    status VARCHAR(50) NOT NULL,
    payment_method_type VARCHAR(50),
    payment_method_last4 VARCHAR(4),
    payment_method_brand VARCHAR(50),
    failure_code VARCHAR(100),
    failure_message TEXT,
    receipt_url TEXT,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- SUBSCRIPTION PLANS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS subscription_plans (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price INTEGER NOT NULL, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    interval VARCHAR(20) DEFAULT 'month',
    interval_count INTEGER DEFAULT 1,
    trial_period_days INTEGER DEFAULT 30,
    features JSONB DEFAULT '[]',
    limits JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- USAGE TRACKING TABLE
-- ================================
CREATE TABLE IF NOT EXISTS usage_tracking (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_value INTEGER NOT NULL DEFAULT 0,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    billing_period VARCHAR(20) DEFAULT 'month',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    -- Unique constraint for tenant + metric + period
    CONSTRAINT unique_usage_period UNIQUE(tenant_id, metric_name, period_start, period_end)
);

-- ================================
-- BILLING EVENTS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_events (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_source VARCHAR(50) DEFAULT 'stripe',
    stripe_event_id VARCHAR(255),
    event_data JSONB NOT NULL DEFAULT '{}',
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- BILLING DISCOUNTS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_discounts (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    code VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'percentage' or 'fixed'
    value INTEGER NOT NULL, -- percentage (0-100) or fixed amount in cents
    currency VARCHAR(3) DEFAULT 'usd',
    description TEXT,
    max_uses INTEGER,
    current_uses INTEGER DEFAULT 0,
    valid_from TIMESTAMP DEFAULT NOW(),
    valid_until TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES employees(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT unique_discount_code UNIQUE(code)
);

-- ================================
-- BILLING NOTIFICATIONS TABLE
-- ================================
CREATE TABLE IF NOT EXISTS billing_notifications (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    notification_type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    scheduled_for TIMESTAMP DEFAULT NOW(),
    sent_at TIMESTAMP,
    email_sent BOOLEAN DEFAULT FALSE,
    sms_sent BOOLEAN DEFAULT FALSE,
    in_app_sent BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- INDEXES FOR PERFORMANCE
-- ================================

-- Billing customers indexes
CREATE INDEX IF NOT EXISTS idx_billing_customers_tenant ON billing_customers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_billing_customers_stripe_customer ON billing_customers(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_billing_customers_status ON billing_customers(status);
CREATE INDEX IF NOT EXISTS idx_billing_customers_trial_end ON billing_customers(trial_end);

-- Billing invoices indexes
CREATE INDEX IF NOT EXISTS idx_billing_invoices_tenant ON billing_invoices(tenant_id);
CREATE INDEX IF NOT EXISTS idx_billing_invoices_status ON billing_invoices(status);
CREATE INDEX IF NOT EXISTS idx_billing_invoices_due_date ON billing_invoices(due_date);
CREATE INDEX IF NOT EXISTS idx_billing_invoices_created ON billing_invoices(created_at DESC);

-- Billing transactions indexes
CREATE INDEX IF NOT EXISTS idx_billing_transactions_tenant ON billing_transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_status ON billing_transactions(status);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_created ON billing_transactions(created_at DESC);

-- Usage tracking indexes
CREATE INDEX IF NOT EXISTS idx_usage_tracking_tenant_metric ON usage_tracking(tenant_id, metric_name);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_period ON usage_tracking(period_start, period_end);

-- Billing events indexes
CREATE INDEX IF NOT EXISTS idx_billing_events_processed ON billing_events(processed, created_at);
CREATE INDEX IF NOT EXISTS idx_billing_events_tenant ON billing_events(tenant_id);

-- ================================
-- INSERT DEFAULT SUBSCRIPTION PLANS
-- ================================
INSERT INTO subscription_plans (id, name, description, price, features, limits) VALUES
('starter', 'Starter Plan', 'Perfect for small restaurants getting started', 9900, 
 '["Single location", "Up to 5 employees", "Basic POS features", "Email support", "Basic analytics"]',
 '{"locations": 1, "employees": 5, "transactions": 1000, "storage_gb": 1}'),

('professional', 'Professional Plan', 'Ideal for growing restaurant businesses', 29900,
 '["Up to 3 locations", "Up to 25 employees", "Advanced analytics", "Phone support", "Kitchen display system", "Loyalty programs", "Inventory management"]',
 '{"locations": 3, "employees": 25, "transactions": 10000, "storage_gb": 10}'),

('enterprise', 'Enterprise Plan', 'Complete solution for restaurant chains', 79900,
 '["Unlimited locations", "Unlimited employees", "AI features", "Priority support", "Custom integrations", "Advanced reporting", "Multi-tenant management", "API access"]',
 '{"locations": -1, "employees": -1, "transactions": -1, "storage_gb": 100}'),

('enterprise_plus', 'Enterprise Plus', 'White-label and custom solutions', 0,
 '["Everything in Enterprise", "White-label options", "Custom development", "On-premise deployment", "24/7 dedicated support", "SLA guarantees", "Custom training"]',
 '{"locations": -1, "employees": -1, "transactions": -1, "storage_gb": -1}')

ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price = EXCLUDED.price,
    features = EXCLUDED.features,
    limits = EXCLUDED.limits,
    updated_at = NOW();

-- ================================
-- BILLING FUNCTIONS
-- ================================

-- Function to calculate monthly usage
CREATE OR REPLACE FUNCTION calculate_monthly_usage(tenant_id_param INTEGER, metric_name_param VARCHAR)
RETURNS INTEGER AS $$
DECLARE
    usage_count INTEGER;
BEGIN
    CASE metric_name_param
        WHEN 'transactions' THEN
            SELECT COUNT(*) INTO usage_count
            FROM orders 
            WHERE tenant_id = tenant_id_param 
            AND created_at >= date_trunc('month', CURRENT_DATE);
            
        WHEN 'employees' THEN
            SELECT COUNT(*) INTO usage_count
            FROM employees 
            WHERE tenant_id = tenant_id_param 
            AND is_active = true;
            
        WHEN 'locations' THEN
            SELECT COUNT(*) INTO usage_count
            FROM locations 
            WHERE tenant_id = tenant_id_param 
            AND is_active = true;
            
        ELSE
            usage_count := 0;
    END CASE;
    
    RETURN COALESCE(usage_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to check plan limits
CREATE OR REPLACE FUNCTION check_plan_limits(tenant_id_param INTEGER, metric_name_param VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    current_usage INTEGER;
    plan_limit INTEGER;
    tenant_plan VARCHAR;
BEGIN
    -- Get tenant's current plan
    SELECT plan_id INTO tenant_plan
    FROM billing_customers 
    WHERE tenant_id = tenant_id_param;
    
    -- Get current usage
    current_usage := calculate_monthly_usage(tenant_id_param, metric_name_param);
    
    -- Get plan limit
    SELECT (limits->>metric_name_param)::INTEGER INTO plan_limit
    FROM subscription_plans 
    WHERE id = tenant_plan;
    
    -- If limit is -1, it means unlimited
    IF plan_limit = -1 THEN
        RETURN TRUE;
    END IF;
    
    -- Check if usage is within limit
    RETURN current_usage < plan_limit;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- TRIGGERS FOR USAGE TRACKING
-- ================================

-- Trigger to update usage when orders are created
CREATE OR REPLACE FUNCTION update_transaction_usage()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO usage_tracking (tenant_id, metric_name, metric_value, period_start, period_end)
    VALUES (
        NEW.tenant_id,
        'transactions',
        1,
        date_trunc('month', NEW.created_at),
        date_trunc('month', NEW.created_at) + INTERVAL '1 month' - INTERVAL '1 day'
    )
    ON CONFLICT (tenant_id, metric_name, period_start, period_end)
    DO UPDATE SET 
        metric_value = usage_tracking.metric_value + 1,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_transaction_usage
    AFTER INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_usage();

-- ================================
-- VIEWS FOR REPORTING
-- ================================

-- Billing summary view
CREATE OR REPLACE VIEW billing_summary AS
SELECT 
    bc.tenant_id,
    t.name as tenant_name,
    t.email as tenant_email,
    bc.plan_id,
    sp.name as plan_name,
    sp.price as plan_price,
    bc.status,
    bc.trial_end,
    bc.current_period_end,
    bc.last_payment_date,
    bc.created_at as customer_since,
    CASE 
        WHEN bc.trial_end > NOW() THEN 'trial'
        WHEN bc.status = 'active' THEN 'paying'
        ELSE bc.status
    END as billing_status
FROM billing_customers bc
JOIN tenants t ON bc.tenant_id = t.id
JOIN subscription_plans sp ON bc.plan_id = sp.id;

-- Monthly revenue view
CREATE OR REPLACE VIEW monthly_revenue AS
SELECT 
    DATE_TRUNC('month', bi.created_at) as month,
    COUNT(*) as invoice_count,
    SUM(bi.amount_paid) as total_revenue,
    AVG(bi.amount_paid) as average_invoice,
    COUNT(DISTINCT bi.tenant_id) as paying_customers
FROM billing_invoices bi
WHERE bi.status = 'paid'
GROUP BY DATE_TRUNC('month', bi.created_at)
ORDER BY month DESC;

-- Usage analytics view
CREATE OR REPLACE VIEW usage_analytics AS
SELECT 
    ut.tenant_id,
    t.name as tenant_name,
    bc.plan_id,
    ut.metric_name,
    ut.metric_value as current_usage,
    (sp.limits->>ut.metric_name)::INTEGER as plan_limit,
    CASE 
        WHEN (sp.limits->>ut.metric_name)::INTEGER = -1 THEN 0
        ELSE (ut.metric_value::FLOAT / (sp.limits->>ut.metric_name)::INTEGER * 100)
    END as usage_percentage
FROM usage_tracking ut
JOIN tenants t ON ut.tenant_id = t.id
JOIN billing_customers bc ON ut.tenant_id = bc.tenant_id
JOIN subscription_plans sp ON bc.plan_id = sp.id
WHERE ut.period_start = date_trunc('month', CURRENT_DATE);

-- ================================
-- CUSTOMER ONBOARDING TABLES
-- ================================

-- Customer onboarding tracking
CREATE TABLE IF NOT EXISTS customer_onboarding (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    current_step INTEGER DEFAULT 1,
    total_steps INTEGER DEFAULT 7,
    progress_percentage INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'in_progress',
    started_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    assigned_specialist_id INTEGER,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    CONSTRAINT unique_tenant_onboarding UNIQUE(tenant_id)
);

-- Individual onboarding steps
CREATE TABLE IF NOT EXISTS onboarding_steps (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    step_id INTEGER NOT NULL,
    step_name VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    CONSTRAINT unique_tenant_step UNIQUE(tenant_id, step_name)
);

-- Onboarding resources and materials
CREATE TABLE IF NOT EXISTS onboarding_resources (
    id SERIAL PRIMARY KEY,
    step_name VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL, -- 'video', 'document', 'checklist', 'tutorial'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    url TEXT,
    file_path TEXT,
    duration_minutes INTEGER,
    is_required BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Customer support tickets
CREATE TABLE IF NOT EXISTS support_tickets (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium',
    status VARCHAR(50) DEFAULT 'open',
    category VARCHAR(100),
    assigned_to INTEGER,
    created_by INTEGER REFERENCES employees(id),
    resolved_at TIMESTAMP,
    resolution_notes TEXT,
    satisfaction_rating INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Support ticket messages
CREATE TABLE IF NOT EXISTS support_messages (
    id SERIAL PRIMARY KEY,
    ticket_id INTEGER NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL, -- 'customer', 'support'
    sender_id INTEGER,
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Knowledge base articles
CREATE TABLE IF NOT EXISTS knowledge_base (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(100),
    tags TEXT[],
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    is_published BOOLEAN DEFAULT TRUE,
    author_id INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ================================
-- ONBOARDING INDEXES
-- ================================
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_tenant ON customer_onboarding(tenant_id);
CREATE INDEX IF NOT EXISTS idx_customer_onboarding_status ON customer_onboarding(status);
CREATE INDEX IF NOT EXISTS idx_onboarding_steps_tenant ON onboarding_steps(tenant_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_steps_status ON onboarding_steps(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_tenant ON support_tickets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_category ON knowledge_base(category);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_published ON knowledge_base(is_published);

-- ================================
-- INSERT DEFAULT ONBOARDING RESOURCES
-- ================================
INSERT INTO onboarding_resources (step_name, resource_type, title, description, url, is_required, sort_order) VALUES
('account_setup', 'video', 'Getting Started with BARPOS', 'Introduction to your new POS system', '/videos/getting-started.mp4', true, 1),
('account_setup', 'document', 'Quick Start Guide', 'Step-by-step setup instructions', '/docs/quick-start.pdf', true, 2),
('restaurant_info', 'tutorial', 'Restaurant Configuration', 'How to set up your restaurant information', '/tutorials/restaurant-config', true, 1),
('menu_setup', 'video', 'Menu Management', 'Creating and organizing your menu', '/videos/menu-setup.mp4', true, 1),
('menu_setup', 'checklist', 'Menu Setup Checklist', 'Ensure you have everything configured', '/checklists/menu-setup', false, 2),
('staff_setup', 'video', 'Staff Management', 'Adding employees and setting permissions', '/videos/staff-management.mp4', true, 1),
('payment_setup', 'document', 'Payment Configuration Guide', 'Setting up payment processing', '/docs/payment-setup.pdf', true, 1),
('testing', 'tutorial', 'System Testing', 'How to test your POS system', '/tutorials/system-testing', true, 1),
('go_live', 'checklist', 'Go-Live Checklist', 'Final steps before going live', '/checklists/go-live', true, 1)

ON CONFLICT (step_name, title) DO NOTHING;

-- ================================
-- INSERT KNOWLEDGE BASE ARTICLES
-- ================================
INSERT INTO knowledge_base (title, slug, content, excerpt, category, tags) VALUES
('How to Add Menu Items', 'how-to-add-menu-items',
 'Step-by-step guide to adding menu items to your BARPOS system...',
 'Learn how to quickly add and organize menu items',
 'Menu Management',
 ARRAY['menu', 'items', 'setup']),

('Setting Up Payment Processing', 'payment-processing-setup',
 'Complete guide to configuring payment methods in BARPOS...',
 'Configure credit cards, cash, and other payment methods',
 'Payment Setup',
 ARRAY['payments', 'setup', 'credit-cards']),

('Managing Staff and Permissions', 'staff-permissions',
 'How to add staff members and configure their access levels...',
 'Control what each staff member can access in the system',
 'Staff Management',
 ARRAY['staff', 'permissions', 'security']),

('Understanding Reports and Analytics', 'reports-analytics',
 'Guide to using BARPOS reporting and analytics features...',
 'Get insights into your restaurant performance',
 'Reports',
 ARRAY['reports', 'analytics', 'insights']),

('Troubleshooting Common Issues', 'troubleshooting',
 'Solutions to common problems and error messages...',
 'Quick fixes for the most common issues',
 'Troubleshooting',
 ARRAY['troubleshooting', 'errors', 'support'])

ON CONFLICT (slug) DO NOTHING;

COMMENT ON TABLE billing_customers IS 'Customer billing and subscription information';
COMMENT ON TABLE billing_invoices IS 'Invoice records and payment history';
COMMENT ON TABLE billing_transactions IS 'Individual payment transactions';
COMMENT ON TABLE subscription_plans IS 'Available subscription plans and pricing';
COMMENT ON TABLE usage_tracking IS 'Usage metrics for billing and limits';
COMMENT ON TABLE billing_events IS 'Webhook events and billing system events';
COMMENT ON TABLE billing_discounts IS 'Discount codes and promotions';
COMMENT ON TABLE billing_notifications IS 'Billing-related notifications and alerts';
-- ================================
-- MARKETING & LEAD GENERATION TABLES
-- ================================

-- Marketing leads
CREATE TABLE IF NOT EXISTS marketing_leads (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    company VARCHAR(255),
    source VARCHAR(100), -- 'website', 'google_ads', 'facebook', 'referral', etc.
    campaign VARCHAR(100),
    message TEXT,
    interests TEXT[], -- array of interests
    utm_params JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'new', -- 'new', 'contacted', 'qualified', 'converted', 'lost'
    lead_score INTEGER DEFAULT 0,
    assigned_to INTEGER,
    notes TEXT,
    last_contact TIMESTAMP,
    converted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    CONSTRAINT unique_marketing_email UNIQUE(email)
);

-- Marketing activities and interactions
CREATE TABLE IF NOT EXISTS marketing_activities (
    id SERIAL PRIMARY KEY,
    lead_id INTEGER REFERENCES marketing_leads(id) ON DELETE CASCADE,
    activity_type VARCHAR(100) NOT NULL, -- 'email_sent', 'call_made', 'demo_scheduled', etc.
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Marketing events tracking
CREATE TABLE IF NOT EXISTS marketing_events (
    id SERIAL PRIMARY KEY,
    event_name VARCHAR(100) NOT NULL,
    properties JSONB DEFAULT '{}',
    url TEXT,
    user_agent TEXT,
    session_id VARCHAR(255),
    ip_address INET,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Demo requests
CREATE TABLE IF NOT EXISTS demo_requests (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    company VARCHAR(255),
    preferred_time VARCHAR(255),
    message TEXT,
    utm_params JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'scheduled', 'completed', 'cancelled'
    scheduled_at TIMESTAMP,
    completed_at TIMESTAMP,
    demo_notes TEXT,
    assigned_to INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Marketing campaigns
CREATE TABLE IF NOT EXISTS marketing_campaigns (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL, -- 'email', 'social', 'ppc', 'content', etc.
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'active', 'paused', 'completed'
    budget DECIMAL(10,2),
    start_date DATE,
    end_date DATE,
    target_audience JSONB DEFAULT '{}',
    goals JSONB DEFAULT '{}',
    metrics JSONB DEFAULT '{}',
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Email templates
CREATE TABLE IF NOT EXISTS email_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    template_type VARCHAR(100), -- 'welcome', 'follow_up', 'demo_confirmation', etc.
    variables JSONB DEFAULT '{}', -- template variables
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- A/B test experiments
CREATE TABLE IF NOT EXISTS ab_experiments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'running', 'completed', 'paused'
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    variants JSONB NOT NULL, -- variant configurations
    traffic_split JSONB DEFAULT '{}', -- traffic allocation
    success_metric VARCHAR(100),
    results JSONB DEFAULT '{}',
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- A/B test assignments
CREATE TABLE IF NOT EXISTS ab_assignments (
    id SERIAL PRIMARY KEY,
    experiment_id INTEGER NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
    session_id VARCHAR(255) NOT NULL,
    variant VARCHAR(100) NOT NULL,
    converted BOOLEAN DEFAULT FALSE,
    conversion_value DECIMAL(10,2),
    assigned_at TIMESTAMP DEFAULT NOW(),
    converted_at TIMESTAMP,

    CONSTRAINT unique_session_experiment UNIQUE(session_id, experiment_id)
);

-- Beta program applications
CREATE TABLE IF NOT EXISTS beta_applications (
    id SERIAL PRIMARY KEY,
    restaurant_name VARCHAR(255) NOT NULL,
    restaurant_type VARCHAR(100),
    locations VARCHAR(10),
    monthly_revenue VARCHAR(50),
    contact_name VARCHAR(255) NOT NULL,
    role VARCHAR(100),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    current_pos VARCHAR(255),
    current_cost VARCHAR(100),
    goals TEXT NOT NULL,
    motivation TEXT NOT NULL,
    source VARCHAR(100) DEFAULT 'beta_program',
    campaign VARCHAR(100) DEFAULT 'beta_launch',
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'reviewing', 'accepted', 'rejected', 'onboarding', 'active'
    notes TEXT,
    reviewer VARCHAR(255),
    reviewed_at TIMESTAMP,
    onboarded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),

    CONSTRAINT unique_beta_email UNIQUE(email)
);

-- ================================
-- MARKETING INDEXES
-- ================================
CREATE INDEX IF NOT EXISTS idx_marketing_leads_email ON marketing_leads(email);
CREATE INDEX IF NOT EXISTS idx_marketing_leads_status ON marketing_leads(status);
CREATE INDEX IF NOT EXISTS idx_marketing_leads_source ON marketing_leads(source);
CREATE INDEX IF NOT EXISTS idx_marketing_leads_created ON marketing_leads(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_marketing_activities_lead ON marketing_activities(lead_id);
CREATE INDEX IF NOT EXISTS idx_marketing_activities_type ON marketing_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_marketing_events_name ON marketing_events(event_name);
CREATE INDEX IF NOT EXISTS idx_marketing_events_session ON marketing_events(session_id);
CREATE INDEX IF NOT EXISTS idx_marketing_events_created ON marketing_events(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_demo_requests_status ON demo_requests(status);
CREATE INDEX IF NOT EXISTS idx_demo_requests_email ON demo_requests(email);
CREATE INDEX IF NOT EXISTS idx_ab_assignments_experiment ON ab_assignments(experiment_id);
CREATE INDEX IF NOT EXISTS idx_ab_assignments_session ON ab_assignments(session_id);
CREATE INDEX IF NOT EXISTS idx_beta_applications_status ON beta_applications(status);
CREATE INDEX IF NOT EXISTS idx_beta_applications_email ON beta_applications(email);
CREATE INDEX IF NOT EXISTS idx_beta_applications_created ON beta_applications(created_at DESC);

-- ================================
-- INSERT DEFAULT EMAIL TEMPLATES
-- ================================
INSERT INTO email_templates (name, subject, content, template_type, variables) VALUES
('welcome_lead', 'Welcome to BARPOS - Transform Your Restaurant!',
 '<h2>Welcome to BARPOS! 👋</h2><p>Hi {{name}},</p><p>Thank you for your interest in BARPOS!</p>',
 'welcome', '{"name": "Lead Name"}'),

('demo_confirmation', 'Your BARPOS Demo is Confirmed!',
 '<h2>Demo Request Confirmed! 🎉</h2><p>Hi {{name}},</p><p>Demo ID: {{demo_id}}</p>',
 'demo_confirmation', '{"name": "Lead Name", "demo_id": "Demo ID"}'),

('follow_up_trial', 'How is your BARPOS trial going?',
 '<h2>How are you finding BARPOS?</h2><p>Hi {{name}},</p><p>We wanted to check in on your trial...</p>',
 'follow_up', '{"name": "Customer Name"}'),

('trial_ending', 'Your BARPOS trial ends soon',
 '<h2>Don\'t let your trial end!</h2><p>Hi {{name}},</p><p>Your trial ends in {{days_left}} days...</p>',
 'trial_ending', '{"name": "Customer Name", "days_left": "Days Remaining"}')

ON CONFLICT (name) DO UPDATE SET
    subject = EXCLUDED.subject,
    content = EXCLUDED.content,
    updated_at = NOW();

-- ================================
-- MARKETING VIEWS
-- ================================

-- Lead conversion funnel
CREATE OR REPLACE VIEW marketing_funnel AS
SELECT
    DATE_TRUNC('week', created_at) as week,
    COUNT(*) as total_leads,
    COUNT(*) FILTER (WHERE status = 'contacted') as contacted,
    COUNT(*) FILTER (WHERE status = 'qualified') as qualified,
    COUNT(*) FILTER (WHERE status = 'converted') as converted,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'converted')::DECIMAL /
        NULLIF(COUNT(*), 0) * 100, 2
    ) as conversion_rate
FROM marketing_leads
GROUP BY DATE_TRUNC('week', created_at)
ORDER BY week DESC;

-- Source performance
CREATE OR REPLACE VIEW source_performance AS
SELECT
    source,
    COUNT(*) as total_leads,
    COUNT(*) FILTER (WHERE status = 'converted') as conversions,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'converted')::DECIMAL /
        NULLIF(COUNT(*), 0) * 100, 2
    ) as conversion_rate,
    AVG(lead_score) as avg_lead_score
FROM marketing_leads
WHERE source IS NOT NULL
GROUP BY source
ORDER BY conversion_rate DESC;

-- Campaign performance
CREATE OR REPLACE VIEW campaign_performance AS
SELECT
    campaign,
    COUNT(*) as total_leads,
    COUNT(*) FILTER (WHERE status = 'converted') as conversions,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'converted')::DECIMAL /
        NULLIF(COUNT(*), 0) * 100, 2
    ) as conversion_rate,
    COUNT(DISTINCT source) as sources_used
FROM marketing_leads
WHERE campaign IS NOT NULL
GROUP BY campaign
ORDER BY total_leads DESC;

COMMENT ON TABLE customer_onboarding IS 'Customer onboarding progress tracking';
COMMENT ON TABLE onboarding_steps IS 'Individual onboarding step completion';
COMMENT ON TABLE support_tickets IS 'Customer support ticket system';
COMMENT ON TABLE knowledge_base IS 'Self-service knowledge base articles';
COMMENT ON TABLE marketing_leads IS 'Marketing lead capture and management';
COMMENT ON TABLE marketing_activities IS 'Lead interaction and activity tracking';
COMMENT ON TABLE marketing_events IS 'Website and marketing event analytics';
COMMENT ON TABLE demo_requests IS 'Demo scheduling and management';
COMMENT ON TABLE marketing_campaigns IS 'Marketing campaign management';
COMMENT ON TABLE email_templates IS 'Email marketing template library';
COMMENT ON TABLE ab_experiments IS 'A/B testing experiment configuration';
COMMENT ON TABLE ab_assignments IS 'A/B test variant assignments and results';
