import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  Download, 
  Calendar, 
  Filter, 
  FileText, 
  Table, 
  Image,
  Mail,
  Clock,
  RefreshCw,
  Settings,
  Share2,
  Eye,
  Trash2,
  Plus,
  Edit3,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  DollarSign,
  Package,
  Target
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface ExportConfig {
  id: string;
  name: string;
  description: string;
  type: 'sales' | 'inventory' | 'users' | 'financial' | 'custom';
  format: 'csv' | 'excel' | 'pdf' | 'json';
  schedule?: 'none' | 'daily' | 'weekly' | 'monthly';
  filters: Record<string, any>;
  columns: string[];
  created_at: string;
  last_run?: string;
  status: 'active' | 'inactive' | 'error';
}

interface AnalyticsAction {
  id: string;
  type: 'export' | 'schedule' | 'share' | 'preview' | 'edit' | 'delete';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresConfirmation?: boolean;
}

export const EnhancedAnalyticsExporter: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [exportConfigs, setExportConfigs] = useState<ExportConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState<string | null>(null);
  const [showPreviewModal, setShowPreviewModal] = useState<string | null>(null);
  const [showScheduleModal, setShowScheduleModal] = useState<string | null>(null);
  const [selectedConfigs, setSelectedConfigs] = useState<string[]>([]);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Analytics actions configuration
  const analyticsActions: AnalyticsAction[] = [
    {
      id: 'export_now',
      type: 'export',
      label: 'Export Now',
      icon: Download,
      color: 'blue'
    },
    {
      id: 'preview_data',
      type: 'preview',
      label: 'Preview',
      icon: Eye,
      color: 'gray'
    },
    {
      id: 'schedule_export',
      type: 'schedule',
      label: 'Schedule',
      icon: Clock,
      color: 'purple'
    },
    {
      id: 'share_report',
      type: 'share',
      label: 'Share',
      icon: Share2,
      color: 'green'
    },
    {
      id: 'edit_config',
      type: 'edit',
      label: 'Edit',
      icon: Edit3,
      color: 'orange'
    },
    {
      id: 'delete_config',
      type: 'delete',
      label: 'Delete',
      icon: Trash2,
      color: 'red',
      requiresConfirmation: true
    }
  ];

  const exportTypes = [
    { id: 'sales', name: 'Sales Analytics', icon: TrendingUp, color: 'green' },
    { id: 'inventory', name: 'Inventory Reports', icon: Package, color: 'blue' },
    { id: 'users', name: 'User Analytics', icon: Users, color: 'purple' },
    { id: 'financial', name: 'Financial Reports', icon: DollarSign, color: 'yellow' },
    { id: 'custom', name: 'Custom Reports', icon: Target, color: 'gray' }
  ];

  const exportFormats = [
    { id: 'csv', name: 'CSV', description: 'Comma-separated values' },
    { id: 'excel', name: 'Excel', description: 'Microsoft Excel spreadsheet' },
    { id: 'pdf', name: 'PDF', description: 'Portable document format' },
    { id: 'json', name: 'JSON', description: 'JavaScript object notation' }
  ];

  // Load export configurations
  useEffect(() => {
    loadExportConfigs();
  }, []);

  const loadExportConfigs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiCall('/api/analytics/export-configs');
      if (response.ok) {
        const data = await response.json();
        setExportConfigs(data);
      } else {
        throw new Error('Failed to load export configurations');
      }
    } catch (error) {
      console.error('Error loading export configs:', error);
      setError('Failed to load export configurations');
      
      // Mock data fallback
      setExportConfigs([
        {
          id: 'config_001',
          name: 'Daily Sales Report',
          description: 'Daily sales summary with top products and revenue',
          type: 'sales',
          format: 'excel',
          schedule: 'daily',
          filters: {
            date_range: 'today',
            include_products: true,
            include_revenue: true
          },
          columns: ['date', 'total_sales', 'order_count', 'top_products', 'revenue'],
          created_at: new Date(Date.now() - 86400000 * 7).toISOString(),
          last_run: new Date(Date.now() - 3600000).toISOString(),
          status: 'active'
        },
        {
          id: 'config_002',
          name: 'Weekly Inventory Report',
          description: 'Weekly inventory levels and low stock alerts',
          type: 'inventory',
          format: 'csv',
          schedule: 'weekly',
          filters: {
            include_low_stock: true,
            include_movements: true
          },
          columns: ['item_name', 'current_stock', 'minimum_stock', 'status'],
          created_at: new Date(Date.now() - 86400000 * 14).toISOString(),
          last_run: new Date(Date.now() - 86400000 * 2).toISOString(),
          status: 'active'
        },
        {
          id: 'config_003',
          name: 'Monthly Financial Summary',
          description: 'Monthly financial performance and trends',
          type: 'financial',
          format: 'pdf',
          schedule: 'monthly',
          filters: {
            include_expenses: true,
            include_profit_margin: true,
            include_trends: true
          },
          columns: ['revenue', 'expenses', 'profit', 'margin', 'growth'],
          created_at: new Date(Date.now() - 86400000 * 30).toISOString(),
          status: 'inactive'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyticsAction = async (action: AnalyticsAction, configId: string) => {
    if (action.requiresConfirmation) {
      if (!confirm(`Are you sure you want to ${action.label.toLowerCase()}?`)) {
        return;
      }
    }

    setActionLoading(`${action.id}_${configId}`);
    setError(null);

    try {
      switch (action.type) {
        case 'export':
          await handleExportNow(configId);
          break;

        case 'preview':
          setShowPreviewModal(configId);
          break;

        case 'schedule':
          setShowScheduleModal(configId);
          break;

        case 'share':
          await handleShareReport(configId);
          break;

        case 'edit':
          setShowEditModal(configId);
          break;

        case 'delete':
          await handleDeleteConfig(configId);
          break;

        default:
          console.warn('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      setError(`Failed to ${action.label.toLowerCase()}`);
    } finally {
      setActionLoading(null);
    }
  };

  const handleExportNow = async (configId: string) => {
    const config = exportConfigs.find(c => c.id === configId);
    if (!config) return;

    const response = await apiCall('/api/analytics/export', {
      method: 'POST',
      body: JSON.stringify({
        config_id: configId,
        format: config.format,
        filters: config.filters
      })
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${config.name.replace(/\s+/g, '_')}-${new Date().toISOString().split('T')[0]}.${config.format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Update last run time
      setExportConfigs(prev => prev.map(c => 
        c.id === configId ? { ...c, last_run: new Date().toISOString() } : c
      ));
    } else {
      throw new Error('Failed to export data');
    }
  };

  const handleShareReport = async (configId: string) => {
    const config = exportConfigs.find(c => c.id === configId);
    if (!config) return;

    const response = await apiCall('/api/analytics/share', {
      method: 'POST',
      body: JSON.stringify({
        config_id: configId,
        recipients: ['<EMAIL>'], // This would come from a modal
        message: `Sharing ${config.name} report`
      })
    });

    if (response.ok) {
      alert('Report shared successfully!');
    } else {
      throw new Error('Failed to share report');
    }
  };

  const handleDeleteConfig = async (configId: string) => {
    const response = await apiCall(`/api/analytics/export-configs/${configId}`, {
      method: 'DELETE'
    });

    if (response.ok) {
      setExportConfigs(prev => prev.filter(c => c.id !== configId));
    } else {
      throw new Error('Failed to delete configuration');
    }
  };

  const handleBulkExport = async () => {
    if (selectedConfigs.length === 0) {
      setError('Please select configurations to export');
      return;
    }

    setActionLoading('bulk_export');
    try {
      const response = await apiCall('/api/analytics/bulk-export', {
        method: 'POST',
        body: JSON.stringify({
          config_ids: selectedConfigs
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bulk-export-${new Date().toISOString().split('T')[0]}.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        setSelectedConfigs([]);
      } else {
        throw new Error('Failed to perform bulk export');
      }
    } catch (error) {
      console.error('Error performing bulk export:', error);
      setError('Failed to perform bulk export. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const getTypeColor = (type: string) => {
    const typeConfig = exportTypes.find(t => t.id === type);
    switch (typeConfig?.color) {
      case 'green': return 'bg-green-100 text-green-800';
      case 'blue': return 'bg-blue-100 text-blue-800';
      case 'purple': return 'bg-purple-100 text-purple-800';
      case 'yellow': return 'bg-yellow-100 text-yellow-800';
      case 'gray': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionButtonClass = (action: AnalyticsAction) => {
    const baseClass = "flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
    
    switch (action.color) {
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'gray': return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'red': return `${baseClass} bg-red-600 hover:bg-red-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  const filteredConfigs = exportConfigs.filter(config => {
    const matchesType = filterType === 'all' || config.type === filterType;
    const matchesStatus = filterStatus === 'all' || config.status === filterStatus;
    return matchesType && matchesStatus;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading analytics configurations...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics & Export Manager</h2>
          <p className="text-gray-600">Create, schedule, and manage data exports and reports</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            <span>New Export</span>
          </button>
          <button
            onClick={loadExportConfigs}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Quick Export Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        {exportTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => console.log(`Quick export: ${type.id}`)}
            className={`p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-all group`}
          >
            <div className="flex flex-col items-center space-y-2">
              <type.icon className="h-6 w-6 text-gray-400 group-hover:text-gray-600" />
              <span className="text-sm font-medium text-gray-600 group-hover:text-gray-800">{type.name}</span>
            </div>
          </button>
        ))}
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Types</option>
          {exportTypes.map(type => (
            <option key={type.id} value={type.id}>{type.name}</option>
          ))}
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="error">Error</option>
        </select>
      </div>

      {/* Bulk Actions */}
      {selectedConfigs.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-blue-800 font-medium">
              {selectedConfigs.length} configuration(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={handleBulkExport}
                disabled={actionLoading === 'bulk_export'}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                {actionLoading === 'bulk_export' ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Bulk Export'
                )}
              </button>
              <button
                onClick={() => setSelectedConfigs([])}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 font-medium text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Export Configurations Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-200">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-200 px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedConfigs.length === filteredConfigs.length && filteredConfigs.length > 0}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedConfigs(filteredConfigs.map(config => config.id));
                    } else {
                      setSelectedConfigs([]);
                    }
                  }}
                  className="rounded"
                />
              </th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Configuration</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Type</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Format</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Schedule</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Status</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Last Run</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredConfigs.map((config) => (
              <tr key={config.id} className="hover:bg-gray-50">
                <td className="border border-gray-200 px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedConfigs.includes(config.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedConfigs(prev => [...prev, config.id]);
                      } else {
                        setSelectedConfigs(prev => prev.filter(id => id !== config.id));
                      }
                    }}
                    className="rounded"
                  />
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div>
                    <div className="font-medium text-gray-900">{config.name}</div>
                    <div className="text-sm text-gray-500">{config.description}</div>
                  </div>
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(config.type)}`}>
                    {exportTypes.find(t => t.id === config.type)?.name || config.type}
                  </span>
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900 uppercase">
                  {config.format}
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                  {config.schedule === 'none' ? 'Manual' : config.schedule}
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(config.status)}`}>
                    {config.status.toUpperCase()}
                  </span>
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                  {config.last_run ? new Date(config.last_run).toLocaleDateString() : 'Never'}
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="flex flex-wrap gap-1">
                    {analyticsActions.map((action) => (
                      <button
                        key={action.id}
                        onClick={() => handleAnalyticsAction(action, config.id)}
                        disabled={actionLoading === `${action.id}_${config.id}`}
                        className={getActionButtonClass(action)}
                        title={action.label}
                      >
                        {actionLoading === `${action.id}_${config.id}` ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <action.icon className="h-3 w-3" />
                        )}
                        <span className="hidden sm:inline">{action.label}</span>
                      </button>
                    ))}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredConfigs.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <BarChart3 className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p>No export configurations found</p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="mt-3 text-blue-600 hover:text-blue-800 font-medium"
          >
            Create your first export configuration
          </button>
        </div>
      )}
    </div>
  );
};
