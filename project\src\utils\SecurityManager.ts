// Advanced Security Manager for Authentication System
// Handles rate limiting, brute force protection, IP monitoring, and security analytics

interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'login_success' | 'login_failure' | 'suspicious_activity' | 'account_locked';
  userId?: number;
  tenantId?: number;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  details: Record<string, any>;
  riskScore: number;
  blocked: boolean;
}

interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDurationMs: number;
}

interface SecurityMetrics {
  totalAttempts: number;
  successfulLogins: number;
  failedAttempts: number;
  blockedAttempts: number;
  uniqueIPs: number;
  suspiciousActivity: number;
  averageRiskScore: number;
}

class SecurityManager {
  private static instance: SecurityManager;
  private attemptHistory: Map<string, number[]> = new Map();
  private blockedIPs: Map<string, number> = new Map();
  private securityEvents: SecurityEvent[] = [];
  private whitelistedIPs: Set<string> = new Set();
  private suspiciousPatterns: RegExp[] = [];

  // Rate limiting configurations
  private rateLimits: Record<string, RateLimitConfig> = {
    login: { windowMs: 15 * 60 * 1000, maxAttempts: 5, blockDurationMs: 30 * 60 * 1000 }, // 5 attempts per 15 min, block for 30 min
    admin: { windowMs: 10 * 60 * 1000, maxAttempts: 3, blockDurationMs: 60 * 60 * 1000 }, // 3 attempts per 10 min, block for 1 hour
    api: { windowMs: 60 * 1000, maxAttempts: 100, blockDurationMs: 5 * 60 * 1000 }        // 100 requests per minute, block for 5 min
  };

  private constructor() {
    this.initializeSecurity();
    this.startSecurityMonitoring();
  }

  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  private initializeSecurity(): void {
    // Initialize whitelisted IPs (localhost, private networks)
    this.whitelistedIPs.add('127.0.0.1');
    this.whitelistedIPs.add('::1');
    this.whitelistedIPs.add('localhost');

    // Initialize suspicious patterns
    this.suspiciousPatterns = [
      /bot|crawler|spider|scraper/i,
      /curl|wget|python|java/i,
      /sqlmap|nikto|nmap/i
    ];

    console.log('🔒 Security Manager initialized');
  }

  private startSecurityMonitoring(): void {
    // Clean up old attempts every 5 minutes
    setInterval(() => {
      this.cleanupOldAttempts();
      this.cleanupBlockedIPs();
    }, 5 * 60 * 1000);

    // Security analytics every hour
    setInterval(() => {
      this.generateSecurityReport();
    }, 60 * 60 * 1000);
  }

  // Check if IP is rate limited
  public checkRateLimit(ipAddress: string, type: 'login' | 'admin' | 'api' = 'login'): boolean {
    // Skip rate limiting for whitelisted IPs
    if (this.whitelistedIPs.has(ipAddress)) {
      return true;
    }

    // Check if IP is currently blocked
    const blockExpiry = this.blockedIPs.get(ipAddress);
    if (blockExpiry && Date.now() < blockExpiry) {
      this.logSecurityEvent({
        type: 'suspicious_activity',
        ipAddress,
        userAgent: navigator.userAgent,
        details: { reason: 'rate_limit_exceeded', type },
        riskScore: 80,
        blocked: true
      });
      return false;
    }

    const config = this.rateLimits[type];
    const key = `${ipAddress}:${type}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Get attempts within the window
    let attempts = this.attemptHistory.get(key) || [];
    attempts = attempts.filter(timestamp => timestamp > windowStart);

    // Check if limit exceeded
    if (attempts.length >= config.maxAttempts) {
      // Block the IP
      this.blockedIPs.set(ipAddress, now + config.blockDurationMs);
      
      this.logSecurityEvent({
        type: 'suspicious_activity',
        ipAddress,
        userAgent: navigator.userAgent,
        details: { 
          reason: 'rate_limit_exceeded', 
          type, 
          attempts: attempts.length,
          windowMs: config.windowMs
        },
        riskScore: 90,
        blocked: true
      });

      console.warn(`🚫 IP ${ipAddress} blocked for ${type} rate limit exceeded`);
      return false;
    }

    // Record this attempt
    attempts.push(now);
    this.attemptHistory.set(key, attempts);

    return true;
  }

  // Analyze login attempt for suspicious activity
  public analyzeLoginAttempt(data: {
    ipAddress: string;
    userAgent: string;
    pin?: string;
    tenantSlug?: string;
    success: boolean;
    userId?: number;
    tenantId?: number;
  }): { allowed: boolean; riskScore: number; reasons: string[] } {
    let riskScore = 0;
    const reasons: string[] = [];

    // Check rate limiting first
    if (!this.checkRateLimit(data.ipAddress, 'login')) {
      return { allowed: false, riskScore: 100, reasons: ['rate_limit_exceeded'] };
    }

    // Analyze user agent for suspicious patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(data.userAgent)) {
        riskScore += 30;
        reasons.push('suspicious_user_agent');
        break;
      }
    }

    // Check for rapid successive attempts from same IP
    const recentAttempts = this.getRecentAttempts(data.ipAddress, 60000); // Last minute
    if (recentAttempts > 3) {
      riskScore += 25;
      reasons.push('rapid_attempts');
    }

    // Check for attempts from multiple IPs for same user
    if (data.userId) {
      const userAttempts = this.getRecentUserAttempts(data.userId, 300000); // Last 5 minutes
      if (userAttempts.length > 2) {
        riskScore += 20;
        reasons.push('multiple_ip_attempts');
      }
    }

    // Check for common attack patterns in PIN
    if (data.pin) {
      if (/^(123456|000000|111111|password|admin)$/i.test(data.pin)) {
        riskScore += 40;
        reasons.push('common_password_attempt');
      }
    }

    // Check for non-existent tenant attempts
    if (data.tenantSlug && !data.success) {
      riskScore += 15;
      reasons.push('invalid_tenant_attempt');
    }

    // Geographic anomaly detection (simplified)
    const isUnusualLocation = this.checkGeographicAnomaly(data.ipAddress);
    if (isUnusualLocation) {
      riskScore += 20;
      reasons.push('unusual_location');
    }

    // Time-based anomaly (login outside business hours)
    const isUnusualTime = this.checkTimeAnomaly();
    if (isUnusualTime) {
      riskScore += 10;
      reasons.push('unusual_time');
    }

    const allowed = riskScore < 70; // Block if risk score >= 70

    // Log the security event
    this.logSecurityEvent({
      type: data.success ? 'login_success' : 'login_failure',
      userId: data.userId,
      tenantId: data.tenantId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      details: { pin_length: data.pin?.length, tenant_slug: data.tenantSlug, reasons },
      riskScore,
      blocked: !allowed
    });

    if (!allowed) {
      console.warn(`🚫 Login attempt blocked - Risk Score: ${riskScore}, Reasons: ${reasons.join(', ')}`);
    }

    return { allowed, riskScore, reasons };
  }

  // Log security events
  private logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      id: `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ...event
    };

    this.securityEvents.push(securityEvent);

    // Keep only last 1000 events in memory
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // Send to backend for persistent storage
    this.sendSecurityEventToBackend(securityEvent);
  }

  // Send security event to backend
  private async sendSecurityEventToBackend(event: SecurityEvent): Promise<void> {
    try {
      const token = localStorage.getItem('authToken');
      await fetch('http://localhost:4000/api/security/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Failed to send security event to backend:', error);
    }
  }

  // Get recent attempts from IP
  private getRecentAttempts(ipAddress: string, windowMs: number): number {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    return this.securityEvents.filter(event => 
      event.ipAddress === ipAddress && 
      new Date(event.timestamp).getTime() > windowStart
    ).length;
  }

  // Get recent attempts for user
  private getRecentUserAttempts(userId: number, windowMs: number): SecurityEvent[] {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    return this.securityEvents.filter(event => 
      event.userId === userId && 
      new Date(event.timestamp).getTime() > windowStart
    );
  }

  // Check for geographic anomalies (simplified)
  private checkGeographicAnomaly(ipAddress: string): boolean {
    // In production, this would use IP geolocation services
    // For now, just check if it's a known local IP
    return !this.whitelistedIPs.has(ipAddress) && !ipAddress.startsWith('192.168.');
  }

  // Check for time-based anomalies
  private checkTimeAnomaly(): boolean {
    const now = new Date();
    const hour = now.getHours();
    
    // Consider logins between 11 PM and 5 AM as unusual
    return hour >= 23 || hour <= 5;
  }

  // Clean up old attempts
  private cleanupOldAttempts(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    for (const [key, attempts] of this.attemptHistory.entries()) {
      const validAttempts = attempts.filter(timestamp => timestamp > cutoff);
      if (validAttempts.length === 0) {
        this.attemptHistory.delete(key);
      } else {
        this.attemptHistory.set(key, validAttempts);
      }
    }
  }

  // Clean up expired IP blocks
  private cleanupBlockedIPs(): void {
    const now = Date.now();
    
    for (const [ip, expiry] of this.blockedIPs.entries()) {
      if (now >= expiry) {
        this.blockedIPs.delete(ip);
        console.log(`🔓 IP ${ip} unblocked`);
      }
    }
  }

  // Generate security metrics
  public getSecurityMetrics(windowMs: number = 24 * 60 * 60 * 1000): SecurityMetrics {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    const recentEvents = this.securityEvents.filter(event => 
      new Date(event.timestamp).getTime() > windowStart
    );

    const uniqueIPs = new Set(recentEvents.map(event => event.ipAddress)).size;
    const totalAttempts = recentEvents.filter(event => 
      event.type === 'login_attempt' || event.type === 'login_success' || event.type === 'login_failure'
    ).length;
    
    const successfulLogins = recentEvents.filter(event => event.type === 'login_success').length;
    const failedAttempts = recentEvents.filter(event => event.type === 'login_failure').length;
    const blockedAttempts = recentEvents.filter(event => event.blocked).length;
    const suspiciousActivity = recentEvents.filter(event => event.type === 'suspicious_activity').length;
    
    const averageRiskScore = recentEvents.length > 0 
      ? recentEvents.reduce((sum, event) => sum + event.riskScore, 0) / recentEvents.length 
      : 0;

    return {
      totalAttempts,
      successfulLogins,
      failedAttempts,
      blockedAttempts,
      uniqueIPs,
      suspiciousActivity,
      averageRiskScore: Math.round(averageRiskScore * 100) / 100
    };
  }

  // Generate security report
  private generateSecurityReport(): void {
    const metrics = this.getSecurityMetrics();
    
    console.log('📊 Security Report (Last 24 hours):');
    console.log(`   Total Attempts: ${metrics.totalAttempts}`);
    console.log(`   Successful Logins: ${metrics.successfulLogins}`);
    console.log(`   Failed Attempts: ${metrics.failedAttempts}`);
    console.log(`   Blocked Attempts: ${metrics.blockedAttempts}`);
    console.log(`   Unique IPs: ${metrics.uniqueIPs}`);
    console.log(`   Suspicious Activity: ${metrics.suspiciousActivity}`);
    console.log(`   Average Risk Score: ${metrics.averageRiskScore}`);
    
    // Alert if high risk activity detected
    if (metrics.averageRiskScore > 50 || metrics.suspiciousActivity > 10) {
      console.warn('⚠️ High risk security activity detected!');
      this.sendSecurityAlert(metrics);
    }
  }

  // Send security alert
  private async sendSecurityAlert(metrics: SecurityMetrics): Promise<void> {
    try {
      const token = localStorage.getItem('authToken');
      await fetch('http://localhost:4000/api/security/alert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify({
          type: 'high_risk_activity',
          metrics,
          timestamp: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to send security alert:', error);
    }
  }

  // Get recent security events
  public getRecentEvents(limit: number = 50): SecurityEvent[] {
    return this.securityEvents
      .slice(-limit)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  // Check if IP is currently blocked
  public isIPBlocked(ipAddress: string): boolean {
    const blockExpiry = this.blockedIPs.get(ipAddress);
    return blockExpiry ? Date.now() < blockExpiry : false;
  }

  // Manually block/unblock IP
  public blockIP(ipAddress: string, durationMs: number = 60 * 60 * 1000): void {
    this.blockedIPs.set(ipAddress, Date.now() + durationMs);
    console.log(`🚫 IP ${ipAddress} manually blocked for ${durationMs / 1000} seconds`);
  }

  public unblockIP(ipAddress: string): void {
    this.blockedIPs.delete(ipAddress);
    console.log(`🔓 IP ${ipAddress} manually unblocked`);
  }

  // Add/remove whitelisted IPs
  public addWhitelistedIP(ipAddress: string): void {
    this.whitelistedIPs.add(ipAddress);
    console.log(`✅ IP ${ipAddress} added to whitelist`);
  }

  public removeWhitelistedIP(ipAddress: string): void {
    this.whitelistedIPs.delete(ipAddress);
    console.log(`❌ IP ${ipAddress} removed from whitelist`);
  }
}

// Export singleton instance
export const securityManager = SecurityManager.getInstance();

// Export types
export type { SecurityEvent, SecurityMetrics, RateLimitConfig };

// Utility functions
export const checkRateLimit = (ip: string, type: 'login' | 'admin' | 'api' = 'login') => 
  securityManager.checkRateLimit(ip, type);

export const analyzeLoginAttempt = (data: any) => 
  securityManager.analyzeLoginAttempt(data);

export const getSecurityMetrics = (windowMs?: number) => 
  securityManager.getSecurityMetrics(windowMs);

export const isIPBlocked = (ip: string) => 
  securityManager.isIPBlocked(ip);
