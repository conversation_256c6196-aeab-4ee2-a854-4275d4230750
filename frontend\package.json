{"name": "barpos-frontend", "version": "7.0.0", "description": "BARPOS - AI-Powered Global Restaurant POS System Frontend", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^5.6.3", "web-vitals": "^2.1.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "lucide-react": "^0.344.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^27.5.2"}, "proxy": "http://localhost:4000"}