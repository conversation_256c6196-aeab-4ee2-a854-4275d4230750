-- RestroFlow Complete Database Setup Script
-- This script creates all necessary tables and sample data

-- Connect to the database
\c RESTROFLOW;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS ai_system_metrics CASCADE;
DROP TABLE IF EXISTS ai_menu_insights CASCADE;
DROP TABLE IF EXISTS ai_customer_segments CASCADE;
DROP TABLE IF EXISTS ai_dynamic_pricing CASCADE;
DROP TABLE IF EXISTS ai_workflow_executions CASCADE;
DROP TABLE IF EXISTS ai_automation_workflows CASCADE;
DROP TABLE IF EXISTS ai_recommendations CASCADE;
DROP TABLE IF EXISTS ai_predictions CASCADE;
DROP TABLE IF EXISTS ai_prediction_models CASCADE;
DROP TABLE IF EXISTS ai_customer_profiles CASCADE;
DROP TABLE IF EXISTS ai_transaction_risks CASCADE;
DROP TABLE IF EXISTS ai_fraud_models CASCADE;
DROP TABLE IF EXISTS tenant_audit_logs CASCADE;
DROP TABLE IF EXISTS tenant_settings CASCADE;
DROP TABLE IF EXISTS order_items CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS employees CASCADE;
DROP TABLE IF EXISTS locations CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Create core tables
CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    logo_url VARCHAR(500),
    theme VARCHAR(20) DEFAULT 'light',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE employees (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    pin VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'employee',
    permissions TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50) DEFAULT 'folder',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(name, tenant_id)
);

CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2) DEFAULT 0,
    sku VARCHAR(100),
    barcode VARCHAR(100),
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL,
    employee_id INTEGER REFERENCES employees(id) ON DELETE SET NULL,
    order_number VARCHAR(50) NOT NULL,
    table_number VARCHAR(20),
    customer_name VARCHAR(255),
    customer_phone VARCHAR(50),
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending',
    order_status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tenant_settings (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE UNIQUE,
    business_name VARCHAR(255),
    business_type VARCHAR(100),
    tax_rate DECIMAL(5,4) DEFAULT 0.08,
    currency VARCHAR(3) DEFAULT 'USD',
    timezone VARCHAR(50) DEFAULT 'UTC',
    features JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE tenant_audit_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES employees(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_employees_tenant_pin ON employees(tenant_id, pin);
CREATE INDEX idx_products_tenant_active ON products(tenant_id, is_active);
CREATE INDEX idx_orders_tenant_status ON orders(tenant_id, order_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_categories_tenant_active ON categories(tenant_id, is_active);

-- Insert sample data
INSERT INTO tenants (name, slug, email, phone, address) VALUES
('Demo Restaurant', 'demo-restaurant', '<EMAIL>', '******-0123', '123 Main St, City, State 12345'),
('RestroFlow System', 'restroflow-system', '<EMAIL>', '******-0456', '456 Tech Ave, Innovation City, TC 67890');

INSERT INTO locations (tenant_id, name, address, phone) VALUES
(1, 'Main Location', '123 Main St, City, State 12345', '******-0123'),
(2, 'RestroFlow HQ', '456 Tech Ave, Innovation City, TC 67890', '******-0456');

-- Insert employees with bcrypt hashed PINs
INSERT INTO employees (tenant_id, location_id, name, email, pin, role, permissions) VALUES
(1, 1, 'Super Admin', '<EMAIL>', '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO', 'super_admin', ARRAY['all']),
(1, 1, 'Manager', '<EMAIL>', '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO', 'manager', ARRAY['pos', 'inventory', 'reports']),
(1, 1, 'Employee', '<EMAIL>', '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO', 'employee', ARRAY['pos']),
(2, 2, 'RestroFlow Super Admin', '<EMAIL>', '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO', 'super_admin', ARRAY['all']),
(2, 2, 'RestroFlow Tenant Admin', '<EMAIL>', '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO', 'tenant_admin', ARRAY['pos', 'inventory', 'reports', 'tenant_management', 'floor_layout']);

INSERT INTO categories (tenant_id, name, description, color, icon) VALUES
(1, 'Beverages', 'Hot and cold drinks', '#3B82F6', 'coffee'),
(1, 'Food', 'Main dishes and meals', '#10B981', 'utensils'),
(1, 'Desserts', 'Sweet treats and desserts', '#F59E0B', 'cake'),
(2, 'Beverages', 'Premium drinks and cocktails', '#8B5CF6', 'wine-glass'),
(2, 'Food', 'Gourmet dishes', '#EF4444', 'chef-hat'),
(2, 'Appetizers', 'Starters and small plates', '#06B6D4', 'plate');

INSERT INTO products (tenant_id, category_id, name, description, price, cost, sku) VALUES
(1, 1, 'Coffee', 'Fresh brewed coffee', 3.50, 0.75, 'BEV001'),
(1, 1, 'Espresso', 'Strong espresso shot', 2.75, 0.50, 'BEV002'),
(1, 1, 'Tea', 'Premium tea selection', 2.50, 0.40, 'BEV003'),
(1, 2, 'Sandwich', 'Gourmet sandwich', 8.99, 3.50, 'FOOD001'),
(1, 2, 'Salad', 'Fresh garden salad', 7.50, 2.75, 'FOOD002'),
(1, 2, 'Burger', 'Classic beef burger', 12.99, 5.25, 'FOOD003'),
(2, 4, 'Craft Beer', 'Local craft beer', 6.50, 2.25, 'BEV101'),
(2, 4, 'Wine Glass', 'Premium wine selection', 8.99, 3.50, 'BEV102'),
(2, 4, 'Cocktail', 'Signature cocktail', 12.00, 4.50, 'BEV103'),
(2, 5, 'Steak', 'Premium ribeye steak', 24.99, 12.50, 'FOOD101'),
(2, 5, 'Fish & Chips', 'Beer battered fish', 16.50, 7.25, 'FOOD102'),
(2, 6, 'Appetizer Platter', 'Mixed appetizer selection', 14.99, 6.75, 'APP101');

INSERT INTO tenant_settings (tenant_id, business_name, business_type, tax_rate, currency, features) VALUES
(1, 'Demo Restaurant & Bar', 'restaurant', 0.08, 'USD', '{"pos": true, "inventory": true, "analytics": true}'),
(2, 'RestroFlow Restaurant Management System', 'technology', 0.08, 'USD', '{"pos": true, "inventory": true, "analytics": true, "floor_layout": true, "tenant_admin": true}');

-- Create update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at column
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_settings_updated_at BEFORE UPDATE ON tenant_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO BARPOS;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO BARPOS;

-- Display setup completion message
SELECT 'RestroFlow database setup completed successfully!' as message;
