# 🚀 BARPOS Production Environment Configuration
# Phase 7: Production Deployment & Market Launch
# Copy to production.env and fill in actual values

# ================================
# APPLICATION SETTINGS
# ================================
NODE_ENV=production
PORT=4000
APP_NAME=BARPOS Production
VERSION=1.0.0
DEBUG=false

# ================================
# DATABASE CONFIGURATION
# ================================
DATABASE_URL=***********************************************************/BARPOS_PROD
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=BARPOS_PROD
POSTGRES_USER=barpos_prod
POSTGRES_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_URL=redis://:CHANGE_THIS_PASSWORD@redis:6379
REDIS_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD
REDIS_TTL=3600

# ================================
# JWT & SECURITY
# ================================
JWT_SECRET=CHANGE_THIS_TO_SECURE_SECRET_KEY_32_CHARS_MIN
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
ENCRYPTION_KEY=CHANGE_THIS_TO_32_CHAR_SECRET_KEY

# ================================
# PAYMENT GATEWAYS
# ================================
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal Configuration
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...
PAYPAL_MODE=live

# Moneris Configuration (Canadian)
MONERIS_STORE_ID=...
MONERIS_API_TOKEN=...
MONERIS_ENVIRONMENT=prod

# ================================
# EMAIL CONFIGURATION
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=BARPOS <<EMAIL>>

# ================================
# FILE STORAGE (AWS S3)
# ================================
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_REGION=us-east-1
AWS_S3_BUCKET=barpos-production-files
AWS_S3_RECEIPTS_BUCKET=barpos-production-receipts

# ================================
# MONITORING & LOGGING
# ================================
SENTRY_DSN=https://...
NEW_RELIC_LICENSE_KEY=...
LOG_LEVEL=info
LOG_FILE=./logs/production.log

# ================================
# SECURITY & CORS
# ================================
CORS_ORIGIN=https://yourapp.com,https://www.yourapp.com
CORS_CREDENTIALS=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
HELMET_ENABLED=true

# ================================
# FEATURE FLAGS
# ================================
ENABLE_AI_FEATURES=true
ENABLE_GLOBAL_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_FRAUD_DETECTION=true
ENABLE_PREDICTIVE_ANALYTICS=true
ENABLE_AUTOMATION=true

# ================================
# FRONTEND CONFIGURATION
# ================================
REACT_APP_API_URL=https://api.yourapp.com
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_...
REACT_APP_PAYPAL_CLIENT_ID=...
REACT_APP_SENTRY_DSN=https://...
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0

# ================================
# MONITORING PASSWORDS
# ================================
GRAFANA_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD
PROMETHEUS_PASSWORD=CHANGE_THIS_TO_SECURE_PASSWORD

# ================================
# BACKUP CONFIGURATION
# ================================
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=barpos-production-backups

# ================================
# SSL CONFIGURATION
# ================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSL_CHAIN_PATH=/etc/nginx/ssl/chain.pem

# ================================
# PERFORMANCE SETTINGS
# ================================
MAX_CONNECTIONS=200
CONNECTION_TIMEOUT=30000
QUERY_TIMEOUT=10000
CACHE_TTL=300

# ================================
# COMPLIANCE SETTINGS
# ================================
GDPR_ENABLED=true
CCPA_ENABLED=true
PCI_DSS_ENABLED=true
DATA_RETENTION_DAYS=2555  # 7 years
AUDIT_LOG_ENABLED=true

# ================================
# NOTIFICATION SETTINGS
# ================================
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
ALERT_EMAIL=<EMAIL>

# ================================
# BUSINESS SETTINGS
# ================================
COMPANY_NAME=Your Company Name
COMPANY_EMAIL=<EMAIL>
COMPANY_PHONE=******-123-4567
SUPPORT_EMAIL=<EMAIL>
BILLING_EMAIL=<EMAIL>

# ================================
# SUBSCRIPTION & BILLING
# ================================
STRIPE_WEBHOOK_ENDPOINT_SECRET=whsec_...
BILLING_CYCLE_DAY=1
TRIAL_PERIOD_DAYS=30
GRACE_PERIOD_DAYS=3

# ================================
# ANALYTICS & TRACKING
# ================================
GOOGLE_ANALYTICS_ID=GA-...
MIXPANEL_TOKEN=...
HOTJAR_ID=...

# ================================
# THIRD-PARTY INTEGRATIONS
# ================================
TWILIO_ACCOUNT_SID=...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+1...

# QuickBooks Integration
QUICKBOOKS_CLIENT_ID=...
QUICKBOOKS_CLIENT_SECRET=...

# Xero Integration
XERO_CLIENT_ID=...
XERO_CLIENT_SECRET=...

# ================================
# DEVELOPMENT OVERRIDES
# ================================
# Leave empty for production
DEV_BYPASS_AUTH=false
DEV_MOCK_PAYMENTS=false
DEV_SKIP_EMAIL=false
