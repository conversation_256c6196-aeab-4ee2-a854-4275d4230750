import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Shield, 
  Users, 
  AlertTriangle, 
  TrendingUp, 
  Clock,
  Database,
  Server,
  Wifi,
  Lock,
  Eye,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  RefreshCw,
  Download,
  <PERSON><PERSON>s,
  Bell,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface DashboardMetrics {
  security: {
    totalEvents: number;
    blockedAttempts: number;
    riskScore: number;
    activeThreats: number;
  };
  authentication: {
    totalLogins: number;
    successRate: number;
    averageTime: number;
    activeUsers: number;
  };
  system: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    responseTime: number;
  };
  audit: {
    eventsToday: number;
    criticalEvents: number;
    complianceScore: number;
    lastAudit: string;
  };
}

interface AlertItem {
  id: string;
  type: 'security' | 'performance' | 'system' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

const MonitoringDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadDashboardData();
    
    if (autoRefresh) {
      const interval = setInterval(loadDashboardData, refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load metrics
      const metricsResponse = await fetch('http://localhost:4000/api/monitoring/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData);
      }

      // Load alerts
      const alertsResponse = await fetch('http://localhost:4000/api/monitoring/alerts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setAlerts(alertsData);
      }

      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      await fetch(`http://localhost:4000/api/monitoring/alerts/${alertId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      setAlerts(alerts.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  };

  const exportReport = async (type: 'security' | 'performance' | 'audit') => {
    try {
      const response = await fetch(`http://localhost:4000/api/monitoring/export/${type}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}-report-${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const getMetricColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-600 dark:text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-orange-600" />;
      case 'medium':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default:
        return <CheckCircle className="w-5 h-5 text-blue-600" />;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (isLoading && !metrics) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading monitoring dashboard...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen p-6 ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-3xl font-bold ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              Security Monitoring Dashboard
            </h1>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Real-time security and performance monitoring
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Last updated: {lastUpdate.toLocaleTimeString()}
              </span>
            </div>
            
            <button
              onClick={loadDashboardData}
              disabled={isLoading}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' 
                  : 'bg-white hover:bg-gray-50 text-gray-600'
              } shadow-sm`}
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                autoRefresh
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : isDarkMode
                  ? 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
            </button>
          </div>
        </div>
      </div>

      {/* Alerts Section */}
      {alerts.filter(alert => !alert.acknowledged).length > 0 && (
        <div className="mb-8">
          <h2 className={`text-xl font-semibold mb-4 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Active Alerts
          </h2>
          <div className="grid gap-4">
            {alerts.filter(alert => !alert.acknowledged).slice(0, 5).map((alert) => (
              <div
                key={alert.id}
                className={`p-4 rounded-lg border-l-4 ${
                  alert.severity === 'critical' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                  alert.severity === 'high' ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' :
                  alert.severity === 'medium' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                } ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getAlertIcon(alert.severity)}
                    <div>
                      <p className={`font-medium ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {alert.message}
                      </p>
                      <p className={`text-sm ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {new Date(alert.timestamp).toLocaleString()} • {alert.type}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => acknowledgeAlert(alert.id)}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    Acknowledge
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Metrics Grid */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          
          {/* Security Metrics */}
          <div className={`p-6 rounded-lg ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          } shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Security
              </h3>
              <Shield className="w-6 h-6 text-red-600" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Risk Score
                </span>
                <span className={`font-medium ${
                  getMetricColor(100 - metrics.security.riskScore, { good: 80, warning: 60 })
                }`}>
                  {metrics.security.riskScore}/100
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Blocked Attempts
                </span>
                <span className={`font-medium ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {metrics.security.blockedAttempts}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Active Threats
                </span>
                <span className={`font-medium ${
                  metrics.security.activeThreats > 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {metrics.security.activeThreats}
                </span>
              </div>
            </div>
          </div>

          {/* Authentication Metrics */}
          <div className={`p-6 rounded-lg ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          } shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Authentication
              </h3>
              <Lock className="w-6 h-6 text-blue-600" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Success Rate
                </span>
                <span className={`font-medium ${
                  getMetricColor(metrics.authentication.successRate, { good: 95, warning: 85 })
                }`}>
                  {metrics.authentication.successRate.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Avg Time
                </span>
                <span className={`font-medium ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {metrics.authentication.averageTime}ms
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Active Users
                </span>
                <span className={`font-medium ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {metrics.authentication.activeUsers}
                </span>
              </div>
            </div>
          </div>

          {/* System Metrics */}
          <div className={`p-6 rounded-lg ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          } shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                System
              </h3>
              <Server className="w-6 h-6 text-green-600" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Uptime
                </span>
                <span className={`font-medium ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {formatUptime(metrics.system.uptime)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  CPU Usage
                </span>
                <span className={`font-medium ${
                  getMetricColor(100 - metrics.system.cpuUsage, { good: 70, warning: 50 })
                }`}>
                  {metrics.system.cpuUsage.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Memory
                </span>
                <span className={`font-medium ${
                  getMetricColor(100 - metrics.system.memoryUsage, { good: 70, warning: 50 })
                }`}>
                  {metrics.system.memoryUsage.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          {/* Audit Metrics */}
          <div className={`p-6 rounded-lg ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          } shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Audit & Compliance
              </h3>
              <Eye className="w-6 h-6 text-purple-600" />
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Events Today
                </span>
                <span className={`font-medium ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {metrics.audit.eventsToday}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Critical Events
                </span>
                <span className={`font-medium ${
                  metrics.audit.criticalEvents > 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {metrics.audit.criticalEvents}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Compliance
                </span>
                <span className={`font-medium ${
                  getMetricColor(metrics.audit.complianceScore, { good: 95, warning: 85 })
                }`}>
                  {metrics.audit.complianceScore}%
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 mb-8">
        <button
          onClick={() => exportReport('security')}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          <span>Security Report</span>
        </button>
        
        <button
          onClick={() => exportReport('performance')}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          <span>Performance Report</span>
        </button>
        
        <button
          onClick={() => exportReport('audit')}
          className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          <Download className="w-4 h-4" />
          <span>Audit Report</span>
        </button>
        
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
            isDarkMode 
              ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <Settings className="w-4 h-4" />
          <span>Configure Alerts</span>
        </button>
      </div>

      {/* Footer */}
      <div className={`text-center text-sm ${
        isDarkMode ? 'text-gray-500' : 'text-gray-400'
      }`}>
        <p>RestroFlow Security Monitoring Dashboard • Real-time data updates every {refreshInterval} seconds</p>
      </div>
    </div>
  );
};

export default MonitoringDashboard;
