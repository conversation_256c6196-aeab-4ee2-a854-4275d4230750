// RestroFlow Production Deployment Configuration
// =============================================

const config = {
  // Environment Configuration
  environment: 'production',
  version: '1.0.0',
  buildDate: new Date().toISOString(),

  // Server Configuration
  server: {
    port: process.env.PORT || 4000,
    host: process.env.HOST || '0.0.0.0',
    cors: {
      origin: process.env.CORS_ORIGIN || ['https://restroflow.com', 'https://app.restroflow.com'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP, please try again later.'
    },
    compression: true,
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
          scriptSrc: ["'self'", "https://cdn.tailwindcss.com"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "https:"],
          fontSrc: ["'self'", "https:", "data:"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }
  },

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'RESTROFLOW',
    user: process.env.DB_USER || 'BARPOS',
    password: process.env.DB_PASSWORD || 'Chaand@0319',
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    pool: {
      min: 5,
      max: 20,
      idle: 10000,
      acquire: 60000,
      evict: 1000
    },
    logging: process.env.NODE_ENV === 'production' ? false : console.log
  },

  // Authentication Configuration
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    jwtExpiration: process.env.JWT_EXPIRATION || '24h',
    refreshTokenExpiration: process.env.REFRESH_TOKEN_EXPIRATION || '7d',
    bcryptRounds: 12,
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000, // 15 minutes
    twoFactorAuth: {
      enabled: true,
      issuer: 'RestroFlow',
      window: 2,
      codeLength: 6
    }
  },

  // WebSocket Configuration
  websocket: {
    enabled: true,
    port: process.env.WS_PORT || 4001,
    pingInterval: 30000,
    pingTimeout: 5000,
    maxConnections: 1000,
    compression: true,
    cors: {
      origin: process.env.CORS_ORIGIN || ['https://restroflow.com', 'https://app.restroflow.com'],
      credentials: true
    }
  },

  // Redis Configuration (for session storage and caching)
  redis: {
    enabled: process.env.REDIS_ENABLED === 'true',
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: process.env.REDIS_DB || 0,
    ttl: 3600, // 1 hour default TTL
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: 'combined',
    directory: './logs',
    filename: 'restroflow-%DATE%.log',
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '14d',
    auditFile: './logs/audit.json',
    errorLog: './logs/error.log',
    combinedLog: './logs/combined.log'
  },

  // Email Configuration
  email: {
    service: process.env.EMAIL_SERVICE || 'gmail',
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '',
      pass: process.env.EMAIL_PASS || ''
    },
    from: process.env.EMAIL_FROM || '<EMAIL>',
    templates: {
      welcome: './templates/welcome.html',
      resetPassword: './templates/reset-password.html',
      twoFactorAuth: './templates/two-factor-auth.html'
    }
  },

  // File Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    destination: process.env.UPLOAD_PATH || './uploads',
    cloudStorage: {
      enabled: process.env.CLOUD_STORAGE_ENABLED === 'true',
      provider: process.env.CLOUD_PROVIDER || 'aws', // aws, gcp, azure
      bucket: process.env.CLOUD_BUCKET || 'restroflow-uploads',
      region: process.env.CLOUD_REGION || 'us-east-1'
    }
  },

  // Payment Gateway Configuration
  payments: {
    stripe: {
      enabled: process.env.STRIPE_ENABLED === 'true',
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      secretKey: process.env.STRIPE_SECRET_KEY || '',
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || ''
    },
    moneris: {
      enabled: process.env.MONERIS_ENABLED === 'true',
      storeId: process.env.MONERIS_STORE_ID || '',
      apiToken: process.env.MONERIS_API_TOKEN || '',
      environment: process.env.MONERIS_ENV || 'sandbox' // sandbox or production
    }
  },

  // Monitoring and Analytics
  monitoring: {
    enabled: true,
    metricsInterval: 30000, // 30 seconds
    healthCheck: {
      enabled: true,
      interval: 60000, // 1 minute
      timeout: 5000,
      endpoints: [
        '/health',
        '/api/health',
        '/api/admin/health',
        '/api/tenant/health'
      ]
    },
    performance: {
      enabled: true,
      sampleRate: 0.1, // 10% sampling
      slowQueryThreshold: 1000, // 1 second
      memoryThreshold: 0.8 // 80% memory usage
    }
  },

  // Security Configuration
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32,
      ivLength: 16
    },
    dataProtection: {
      enabled: true,
      anonymizeAfter: 365, // days
      deleteAfter: 2555, // days (7 years)
      backupRetention: 90 // days
    },
    audit: {
      enabled: true,
      logLevel: 'info',
      includeRequestBody: false,
      includeResponseBody: false,
      sensitiveFields: ['password', 'token', 'secret', 'key']
    }
  },

  // Feature Flags
  features: {
    multiTenant: true,
    realTimeUpdates: true,
    advancedAnalytics: true,
    mobileApp: false,
    aiRecommendations: false,
    voiceOrdering: false,
    blockchainPayments: false
  },

  // API Configuration
  api: {
    version: 'v1',
    prefix: '/api',
    documentation: {
      enabled: process.env.NODE_ENV !== 'production',
      path: '/api-docs',
      title: 'RestroFlow API',
      description: 'Comprehensive Restaurant Management System API'
    },
    pagination: {
      defaultLimit: 20,
      maxLimit: 100
    },
    cache: {
      enabled: true,
      ttl: 300, // 5 minutes
      maxSize: 1000
    }
  },

  // Backup Configuration
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // Daily at 2 AM
    retention: 30, // days
    compression: true,
    encryption: true,
    destinations: [
      {
        type: 'local',
        path: './backups'
      },
      {
        type: 'cloud',
        provider: 'aws',
        bucket: 'restroflow-backups'
      }
    ]
  }
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'development') {
  config.database.logging = console.log;
  config.auth.jwtSecret = 'dev-secret-key';
  config.server.cors.origin = ['http://localhost:3000', 'http://localhost:5173'];
  config.logging.level = 'debug';
}

if (process.env.NODE_ENV === 'test') {
  config.database.database = 'RESTROFLOW_TEST';
  config.auth.jwtSecret = 'test-secret-key';
  config.logging.level = 'error';
  config.websocket.enabled = false;
}

module.exports = config;
