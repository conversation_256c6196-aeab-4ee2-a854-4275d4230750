<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RestroFlow Enterprise Command Center - Global Multi-Tenant Management</title>
    <meta name="description" content="Ultimate Enterprise Super Administrator Command Center with AI-Powered Analytics, Global Deployment Management, Advanced Security Controls, and Real-time Multi-Tenant Monitoring" />
    <meta name="keywords" content="enterprise command center, global deployment, multi-tenant management, AI analytics, security monitoring, restaurant pos, super admin dashboard" />

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Favicon and app icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Performance and SEO optimizations -->
    <meta name="theme-color" content="#dc2626" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Progressive Web App manifest -->
    <link rel="manifest" href="/manifest-admin.json" />

    <!-- Security headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' ws: wss:;" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main-super-admin.tsx" as="script" />
    <link rel="preload" href="/api/admin/dashboard/critical" as="fetch" crossorigin="anonymous" />

    <!-- Critical CSS for loading state -->
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%);
        min-height: 100vh;
        overflow-x: hidden;
        line-height: 1.5;
        position: relative;
      }

      /* Enhanced security indicator */
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ef4444, #f97316, #eab308, #22c55e, #3b82f6, #8b5cf6, #ef4444);
        background-size: 200% 100%;
        animation: securityGradient 3s ease-in-out infinite;
        z-index: 10000;
      }

      @keyframes securityGradient {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }

      /* Enterprise Security Center Styles */
      .security-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        z-index: 9999;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }

      .security-indicator.low {
        background: rgba(34, 197, 94, 0.9);
        color: white;
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
      }

      .security-indicator.medium {
        background: rgba(251, 191, 36, 0.9);
        color: white;
        box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
      }

      .security-indicator.high {
        background: rgba(239, 68, 68, 0.9);
        color: white;
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        animation: pulse 2s infinite;
      }

      .security-indicator.critical {
        background: rgba(153, 27, 27, 0.9);
        color: white;
        box-shadow: 0 0 30px rgba(153, 27, 27, 0.5);
        animation: criticalPulse 1s infinite;
      }

      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }

      @keyframes criticalPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.8; }
      }

      /* Enhanced loading features styling */
      .loading-feature {
        font-size: 14px;
        opacity: 0.9;
        margin: 8px 0;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        animation: featureSlide 0.5s ease-out forwards;
        transform: translateX(-20px);
        opacity: 0;
        border-left: 3px solid #3b82f6;
      }

      .loading-feature:nth-child(1) {
        animation-delay: 0.2s;
        border-left-color: #ef4444;
      }
      .loading-feature:nth-child(2) {
        animation-delay: 0.4s;
        border-left-color: #10b981;
      }
      .loading-feature:nth-child(3) {
        animation-delay: 0.6s;
        border-left-color: #8b5cf6;
      }
      .loading-feature:nth-child(4) {
        animation-delay: 0.8s;
        border-left-color: #f59e0b;
      }

      #root {
        min-height: 100vh;
        position: relative;
      }

      /* Enhanced Loading Screen */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-content {
        text-align: center;
        color: white;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.2);
        border-top: 4px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 24px;
      }

      .loading-title {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
      }

      .loading-subtitle {
        font-size: 16px;
        font-weight: 400;
        opacity: 0.8;
        margin-bottom: 24px;
      }

      .loading-features {
        margin: 24px 0;
        text-align: left;
        max-width: 300px;
      }

      .loading-feature {
        font-size: 14px;
        opacity: 0.9;
        margin: 8px 0;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        animation: featureSlide 0.5s ease-out forwards;
        transform: translateX(-20px);
        opacity: 0;
      }

      .loading-feature:nth-child(1) { animation-delay: 0.2s; }
      .loading-feature:nth-child(2) { animation-delay: 0.4s; }
      .loading-feature:nth-child(3) { animation-delay: 0.6s; }
      .loading-feature:nth-child(4) { animation-delay: 0.8s; }

      .loading-status {
        font-size: 12px;
        opacity: 0.7;
        margin-top: 16px;
        font-style: italic;
      }

      @keyframes featureSlide {
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .loading-progress {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin: 0 auto;
      }

      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #ffffff, #fecaca);
        border-radius: 2px;
        animation: progress 2s ease-in-out infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @keyframes progress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
      }



      /* Hide loading when app loads */
      .app-loaded .loading-container {
        display: none;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .loading-title {
          font-size: 20px;
        }

        .loading-subtitle {
          font-size: 14px;
        }


      }
    </style>
  </head>
  <body>
    <!-- Enterprise Security Indicator -->
    <div class="security-indicator low">Security: SECURE</div>

    <!-- Enhanced Security Loading Screen -->
    <div class="loading-container" id="securityLoadingScreen">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-title">� RestroFlow Security Center</div>
        <div class="loading-subtitle">Performing Security Verification...</div>
        <div class="loading-features">
          <div class="loading-feature">🔐 Verifying Access Credentials</div>
          <div class="loading-feature">🛡️ Checking Security Protocols</div>
          <div class="loading-feature">🔍 Scanning for Threats</div>
          <div class="loading-feature">✅ Establishing Secure Connection</div>
        </div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
        <div class="loading-status" id="securityStatus">Initializing security systems...</div>
      </div>
    </div>

    <!-- Security Warning Screen (Hidden by default) -->
    <div id="securityWarning" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #dc2626, #991b1b); z-index: 10000; color: white; font-family: 'Inter', sans-serif;">
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 20px;">
        <div style="text-align: center; max-width: 600px;">
          <div style="font-size: 4rem; margin-bottom: 20px;">🚨</div>
          <h1 style="font-size: 2.5rem; font-weight: bold; margin-bottom: 20px;">SECURITY ALERT</h1>
          <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">
            Unauthorized access attempt detected. This system is restricted to authorized Super Administrators only.
          </p>
          <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <p style="font-size: 0.9rem; margin-bottom: 10px;">🔒 Access Requirements:</p>
            <ul style="text-align: left; font-size: 0.9rem; opacity: 0.8;">
              <li>• Valid Super Administrator credentials</li>
              <li>• Authorized network access</li>
              <li>• Security clearance verification</li>
            </ul>
          </div>
          <button onclick="window.location.reload()" style="background: #ffffff; color: #dc2626; padding: 12px 30px; border: none; border-radius: 8px; font-weight: bold; cursor: pointer; font-size: 1rem;">
            🔄 Retry Access
          </button>
        </div>
      </div>
    </div>

    <!-- Main app container -->
    <div id="root"></div>

    <!-- Main application script -->
    <script type="module" src="/src/main-super-admin.tsx"></script>

    <!-- Enhanced Security Center Integration -->
    <script>
      // Global security monitoring
      window.securityCenter = {
        threatLevel: 'high',
        activeThreats: 0,
        blockedAttempts: 0,
        complianceScore: 98.5,
        lastScan: new Date().toISOString(),
        accessAttempts: 0,
        maxAccessAttempts: 3
      };

      // Enhanced Security Check - Immediate Protection
      function performSecurityCheck() {
        console.log('🔒 Performing Super Admin Security Check...');

        // Check for suspicious access patterns
        const accessCount = parseInt(sessionStorage.getItem('adminAccessAttempts') || '0');
        sessionStorage.setItem('adminAccessAttempts', (accessCount + 1).toString());

        if (accessCount > 5) {
          console.warn('⚠️ Multiple access attempts detected');
          window.securityCenter.threatLevel = 'critical';
          updateSecurityIndicators();
        }

        // Log access attempt
        const accessLog = {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          ip: 'client-side', // Would be filled by backend
          referrer: document.referrer || 'direct'
        };

        console.log('📊 Access Log:', accessLog);

        // Store access attempt for audit
        const attempts = JSON.parse(localStorage.getItem('adminAccessLog') || '[]');
        attempts.push(accessLog);
        // Keep only last 10 attempts
        if (attempts.length > 10) attempts.shift();
        localStorage.setItem('adminAccessLog', JSON.stringify(attempts));
      }

      // Real-time security updates
      function updateSecurityStatus() {
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('🔒 No auth token found - maintaining high security level');
          window.securityCenter.threatLevel = 'high';
          updateSecurityIndicators();
          return;
        }

        fetch('/api/admin/security/status', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          window.securityCenter = { ...window.securityCenter, ...data };
          updateSecurityIndicators();
        })
        .catch(error => {
          console.error('Security status update failed:', error);
          window.securityCenter.threatLevel = 'high';
          updateSecurityIndicators();
        });
      }

      function updateSecurityIndicators() {
        const indicator = document.querySelector('.security-indicator');
        if (indicator) {
          const level = window.securityCenter.threatLevel;
          indicator.className = `security-indicator ${level}`;

          const statusText = {
            'low': 'SECURE',
            'medium': 'ELEVATED',
            'high': 'HIGH ALERT',
            'critical': 'CRITICAL'
          };

          indicator.textContent = `Security: ${statusText[level] || 'UNKNOWN'}`;
        }
      }

      // Enhanced access control
      function checkAuthorizedAccess() {
        // Check if accessing from authorized domain/port
        const allowedHosts = ['localhost:5173', 'localhost:5174', '127.0.0.1:5173', '127.0.0.1:5174'];
        const currentHost = window.location.host;

        if (!allowedHosts.includes(currentHost)) {
          console.error('🚨 Unauthorized host access detected:', currentHost);
          document.body.innerHTML = `
            <div style="background: #dc2626; color: white; padding: 20px; text-align: center; font-family: Arial;">
              <h1>🚨 UNAUTHORIZED ACCESS DETECTED</h1>
              <p>This system is restricted to authorized personnel only.</p>
              <p>Access attempt has been logged.</p>
            </div>
          `;
          return false;
        }

        return true;
      }

      // Initialize security on page load
      document.addEventListener('DOMContentLoaded', function() {
        if (!checkAuthorizedAccess()) return;

        performSecurityCheck();
        updateSecurityStatus();

        // Start security monitoring
        setInterval(updateSecurityStatus, 30000); // Update every 30 seconds
      });

      // Perform immediate security check
      if (!checkAuthorizedAccess()) {
        // Stop execution if unauthorized
      } else {
        performSecurityCheck();
      }
    </script>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
  </body>
</html>
