export interface Tenant {
  id: string;
  name: string;
  slug: string;
  subscription_plan: SubscriptionPlan;
  status: TenantStatus;
  created_at: string;
  updated_at: string;
  features: TenantFeatures;
  settings: TenantSettings;
}

export type SubscriptionPlan = 'basic' | 'professional' | 'enterprise';

export type TenantStatus = 'active' | 'suspended' | 'pending' | 'cancelled';

export interface TenantFeatures {
  inventory_management: boolean;
  staff_scheduling: boolean;
  analytics_dashboard: boolean;
  loyalty_program: boolean;
  kitchen_display: boolean;
  online_ordering: boolean;
  equipment_management: boolean;
  multi_location: boolean;
  advanced_reporting: boolean;
}

export interface TenantSettings {
  business_hours: BusinessHours;
  currency: string;
  timezone: string;
  tax_rate: number;
  notification_preferences: NotificationPreferences;
  branding: BrandingSettings;
}

export interface BusinessHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  open: string;
  close: string;
  is_closed: boolean;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  low_stock_alerts: boolean;
  order_notifications: boolean;
  staff_updates: boolean;
}

export interface BrandingSettings {
  logo_url: string;
  primary_color: string;
  secondary_color: string;
  font_family: string;
}

export interface CreateTenantRequest {
  name: string;
  slug: string;
  subscription_plan: SubscriptionPlan;
  settings: Partial<TenantSettings>;
}

export interface UpdateTenantRequest {
  name?: string;
  subscription_plan?: SubscriptionPlan;
  status?: TenantStatus;
  features?: Partial<TenantFeatures>;
  settings?: Partial<TenantSettings>;
}
