<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow Beta Program Management</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-reviewing { background: #dbeafe; color: #1e40af; }
        .status-accepted { background: #d1fae5; color: #065f46; }
        .status-rejected { background: #fee2e2; color: #991b1b; }
        .status-active { background: #dcfce7; color: #166534; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-purple-600">RestroFlow</h1>
                    <span class="ml-4 text-gray-600">Beta Program Management</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600">
                        <span class="font-semibold" id="totalApplications">0</span> Total Applications
                    </div>
                    <div class="text-sm text-gray-600">
                        <span class="font-semibold" id="pendingApplications">0</span> Pending Review
                    </div>
                    <button onclick="refreshApplications()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Stats Dashboard -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-gray-900" id="statPending">0</div>
                    <div class="text-sm text-gray-600">Pending</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-blue-600" id="statReviewing">0</div>
                    <div class="text-sm text-gray-600">Reviewing</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-green-600" id="statAccepted">0</div>
                    <div class="text-sm text-gray-600">Accepted</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-red-600" id="statRejected">0</div>
                    <div class="text-sm text-gray-600">Rejected</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-purple-600" id="statActive">0</div>
                    <div class="text-sm text-gray-600">Active Beta</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters -->
    <section class="pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="statusFilter" onchange="filterApplications()" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="reviewing">Reviewing</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                            <option value="active">Active Beta</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Restaurant Type</label>
                        <select id="typeFilter" onchange="filterApplications()" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">All Types</option>
                            <option value="fast_casual">Fast Casual</option>
                            <option value="full_service">Full Service</option>
                            <option value="quick_service">Quick Service</option>
                            <option value="fine_dining">Fine Dining</option>
                            <option value="cafe">Café</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Revenue Range</label>
                        <select id="revenueFilter" onchange="filterApplications()" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">All Ranges</option>
                            <option value="50k-100k">$50K - $100K</option>
                            <option value="100k-250k">$100K - $250K</option>
                            <option value="250k-500k">$250K - $500K</option>
                            <option value="500k+">$500K+</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" id="searchFilter" onkeyup="filterApplications()" placeholder="Search restaurant name, contact..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Applications Table -->
    <section class="pb-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Beta Applications</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Restaurant</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="applicationsTable" class="bg-white divide-y divide-gray-200">
                            <!-- Applications will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Detail Modal -->
    <div id="applicationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-2xl font-bold text-gray-900">Beta Application Details</h3>
                        <button onclick="closeApplicationModal()" class="text-gray-400 hover:text-gray-600">
                            <span class="text-2xl">&times;</span>
                        </button>
                    </div>
                    
                    <div id="applicationDetails">
                        <!-- Application details will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allApplications = [];
        let filteredApplications = [];

        // Load applications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadApplications();
        });

        // Load beta applications
        async function loadApplications() {
            try {
                const response = await fetch('/api/marketing/beta-applications');
                const result = await response.json();
                
                if (result.success) {
                    allApplications = result.applications;
                    filteredApplications = [...allApplications];
                    updateStats();
                    renderApplications();
                } else {
                    console.error('Failed to load applications:', result.message);
                }
            } catch (error) {
                console.error('Error loading applications:', error);
            }
        }

        // Update statistics
        function updateStats() {
            const stats = {
                pending: 0,
                reviewing: 0,
                accepted: 0,
                rejected: 0,
                active: 0
            };

            allApplications.forEach(app => {
                if (stats.hasOwnProperty(app.status)) {
                    stats[app.status]++;
                }
            });

            document.getElementById('totalApplications').textContent = allApplications.length;
            document.getElementById('pendingApplications').textContent = stats.pending;
            document.getElementById('statPending').textContent = stats.pending;
            document.getElementById('statReviewing').textContent = stats.reviewing;
            document.getElementById('statAccepted').textContent = stats.accepted;
            document.getElementById('statRejected').textContent = stats.rejected;
            document.getElementById('statActive').textContent = stats.active;
        }

        // Filter applications
        function filterApplications() {
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const revenueFilter = document.getElementById('revenueFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

            filteredApplications = allApplications.filter(app => {
                const matchesStatus = !statusFilter || app.status === statusFilter;
                const matchesType = !typeFilter || app.restaurant_type === typeFilter;
                const matchesRevenue = !revenueFilter || app.monthly_revenue === revenueFilter;
                const matchesSearch = !searchFilter || 
                    app.restaurant_name.toLowerCase().includes(searchFilter) ||
                    app.contact_name.toLowerCase().includes(searchFilter) ||
                    app.email.toLowerCase().includes(searchFilter);

                return matchesStatus && matchesType && matchesRevenue && matchesSearch;
            });

            renderApplications();
        }

        // Render applications table
        function renderApplications() {
            const tbody = document.getElementById('applicationsTable');
            
            if (filteredApplications.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            No applications found matching your filters.
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredApplications.map(app => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${app.restaurant_name}</div>
                        <div class="text-sm text-gray-500">${app.restaurant_type || 'Not specified'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${app.contact_name}</div>
                        <div class="text-sm text-gray-500">${app.email}</div>
                        <div class="text-sm text-gray-500">${app.phone || 'No phone'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${app.locations} location(s)</div>
                        <div class="text-sm text-gray-500">${app.monthly_revenue || 'Not specified'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full status-${app.status}">
                            ${app.status.charAt(0).toUpperCase() + app.status.slice(1)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(app.created_at).toLocaleDateString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewApplication(${app.id})" class="text-purple-600 hover:text-purple-900 mr-3">
                            View
                        </button>
                        ${app.status === 'pending' ? `
                            <button onclick="updateStatus(${app.id}, 'accepted')" class="text-green-600 hover:text-green-900 mr-3">
                                Accept
                            </button>
                            <button onclick="updateStatus(${app.id}, 'rejected')" class="text-red-600 hover:text-red-900">
                                Reject
                            </button>
                        ` : ''}
                    </td>
                </tr>
            `).join('');
        }

        // View application details
        function viewApplication(applicationId) {
            const app = allApplications.find(a => a.id === applicationId);
            if (!app) return;

            const detailsHtml = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Restaurant Information</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Restaurant Name</label>
                                <div class="text-sm text-gray-900">${app.restaurant_name}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Type</label>
                                <div class="text-sm text-gray-900">${app.restaurant_type || 'Not specified'}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Locations</label>
                                <div class="text-sm text-gray-900">${app.locations}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Monthly Revenue</label>
                                <div class="text-sm text-gray-900">${app.monthly_revenue || 'Not specified'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Contact Name</label>
                                <div class="text-sm text-gray-900">${app.contact_name}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Role</label>
                                <div class="text-sm text-gray-900">${app.role || 'Not specified'}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Email</label>
                                <div class="text-sm text-gray-900">${app.email}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Phone</label>
                                <div class="text-sm text-gray-900">${app.phone || 'Not provided'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="md:col-span-2">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Current System</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Current POS</label>
                                <div class="text-sm text-gray-900">${app.current_pos || 'Not specified'}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Current Cost</label>
                                <div class="text-sm text-gray-900">${app.current_cost || 'Not specified'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="md:col-span-2">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Goals & Motivation</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Goals</label>
                                <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded">${app.goals}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Motivation</label>
                                <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded">${app.motivation}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="md:col-span-2">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Application Status</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Status</label>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full status-${app.status}">
                                    ${app.status.charAt(0).toUpperCase() + app.status.slice(1)}
                                </span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Applied</label>
                                <div class="text-sm text-gray-900">${new Date(app.created_at).toLocaleString()}</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Reviewer</label>
                                <div class="text-sm text-gray-900">${app.reviewer || 'Not assigned'}</div>
                            </div>
                        </div>
                        ${app.notes ? `
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700">Notes</label>
                                <div class="text-sm text-gray-900 bg-gray-50 p-3 rounded">${app.notes}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                ${app.status === 'pending' ? `
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Actions</h4>
                        <div class="flex gap-4">
                            <button onclick="updateStatus(${app.id}, 'accepted')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                                Accept Application
                            </button>
                            <button onclick="updateStatus(${app.id}, 'rejected')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                                Reject Application
                            </button>
                            <button onclick="updateStatus(${app.id}, 'reviewing')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                Mark as Reviewing
                            </button>
                        </div>
                    </div>
                ` : ''}
            `;

            document.getElementById('applicationDetails').innerHTML = detailsHtml;
            document.getElementById('applicationModal').classList.remove('hidden');
        }

        // Close application modal
        function closeApplicationModal() {
            document.getElementById('applicationModal').classList.add('hidden');
        }

        // Update application status
        async function updateStatus(applicationId, newStatus) {
            const notes = newStatus === 'rejected' ? prompt('Please provide a reason for rejection:') : '';
            
            if (newStatus === 'rejected' && !notes) {
                return; // User cancelled
            }

            try {
                const response = await fetch(`/api/marketing/beta-applications/${applicationId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: newStatus,
                        notes: notes,
                        reviewer: 'Sales Team' // In real app, get from auth
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    // Refresh applications
                    await loadApplications();
                    closeApplicationModal();
                    
                    // Show success message
                    alert(`Application ${newStatus} successfully!`);
                } else {
                    alert('Failed to update application: ' + result.message);
                }
            } catch (error) {
                console.error('Error updating application:', error);
                alert('Error updating application');
            }
        }

        // Refresh applications
        function refreshApplications() {
            loadApplications();
        }
    </script>
</body>
</html>
