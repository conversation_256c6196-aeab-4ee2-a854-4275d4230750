import React, { useState } from 'react';

const SimpleCompliance: React.FC = () => {
  const [activeRegion, setActiveRegion] = useState('global');

  const complianceData = {
    global: {
      score: 99.2,
      regulations: [
        { name: 'GDPR', status: 'compliant', region: 'EU', description: 'General Data Protection Regulation' },
        { name: 'CCPA', status: 'compliant', region: 'US-CA', description: 'California Consumer Privacy Act' },
        { name: 'PIPEDA', status: 'compliant', region: 'CA', description: 'Personal Information Protection Act' },
        { name: 'PCI-DSS', status: 'compliant', region: 'Global', description: 'Payment Card Industry Data Security' }
      ]
    }
  };

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-red-50 to-orange-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            🛡️ Compliance Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Global regulatory compliance monitoring and management
          </p>
        </div>
        <div className="flex gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✅ 99.2% Compliant
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🌍 Global Coverage
          </span>
        </div>
      </div>

      {/* Compliance Score */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Overall Compliance Score</h3>
        </div>
        <div className="px-6 py-4">
          <div className="text-center">
            <div className="text-6xl font-bold text-green-600 mb-2">
              {complianceData.global.score}%
            </div>
            <p className="text-gray-600">Global Compliance Rating</p>
            <div className="w-full bg-gray-200 rounded-full h-4 mt-4">
              <div 
                className="bg-green-600 h-4 rounded-full transition-all duration-500" 
                style={{ width: `${complianceData.global.score}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Regulations Status */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Regulatory Compliance Status</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {complianceData.global.regulations.map((regulation, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900">{regulation.name}</h4>
                    <p className="text-sm text-gray-600">{regulation.description}</p>
                  </div>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ✅ Compliant
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Region: {regulation.region}</span>
                  <span className="text-xs text-green-600 font-medium">Active</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Data Protection Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">🔒 Data Encryption</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Data at Rest</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">AES-256</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Data in Transit</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">TLS 1.3</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Database</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Encrypted</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">👥 Access Control</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Multi-Factor Auth</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Enabled</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Role-Based Access</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Session Management</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Secure</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">📋 Audit Trail</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Activity Logging</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Data Access Logs</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Tracked</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Compliance Reports</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Generated</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Rights Management */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Privacy Rights Management</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg text-center">
              <div className="text-2xl mb-2">📄</div>
              <div className="font-medium text-gray-900">Data Access</div>
              <div className="text-sm text-gray-600">Right to access personal data</div>
              <button className="mt-2 text-xs bg-blue-600 text-white px-3 py-1 rounded">
                Process Request
              </button>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg text-center">
              <div className="text-2xl mb-2">✏️</div>
              <div className="font-medium text-gray-900">Data Correction</div>
              <div className="text-sm text-gray-600">Right to rectify data</div>
              <button className="mt-2 text-xs bg-green-600 text-white px-3 py-1 rounded">
                Process Request
              </button>
            </div>
            
            <div className="p-4 bg-red-50 rounded-lg text-center">
              <div className="text-2xl mb-2">🗑️</div>
              <div className="font-medium text-gray-900">Data Deletion</div>
              <div className="text-sm text-gray-600">Right to be forgotten</div>
              <button className="mt-2 text-xs bg-red-600 text-white px-3 py-1 rounded">
                Process Request
              </button>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg text-center">
              <div className="text-2xl mb-2">📦</div>
              <div className="font-medium text-gray-900">Data Portability</div>
              <div className="text-sm text-gray-600">Right to data portability</div>
              <button className="mt-2 text-xs bg-purple-600 text-white px-3 py-1 rounded">
                Process Request
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Compliance Activities */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Compliance Activities</h3>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-3">
            {[
              { time: '2 hours ago', action: 'GDPR compliance audit completed', status: 'success' },
              { time: '1 day ago', action: 'PCI-DSS security scan passed', status: 'success' },
              { time: '3 days ago', action: 'Data retention policy updated', status: 'info' },
              { time: '1 week ago', action: 'Privacy policy reviewed and approved', status: 'success' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'info' ? 'bg-blue-500' : 'bg-yellow-500'
                  }`}></div>
                  <span className="text-sm text-gray-900">{activity.action}</span>
                </div>
                <span className="text-xs text-gray-500">{activity.time}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleCompliance;
