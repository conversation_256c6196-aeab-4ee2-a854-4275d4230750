const express = require('express');

const app = express();
const PORT = 4000;

// Simple health check
app.get('/api/admin/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Start server and test
const server = app.listen(PORT, async () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  
  // Test the server
  try {
    const response = await fetch(`http://localhost:${PORT}/api/admin/health`);
    const data = await response.json();
    console.log('✅ Server test successful:', data);
    
    // Close server after test
    server.close(() => {
      console.log('✅ Test completed - server closed');
      process.exit(0);
    });
  } catch (error) {
    console.error('❌ Server test failed:', error.message);
    server.close(() => {
      process.exit(1);
    });
  }
});
