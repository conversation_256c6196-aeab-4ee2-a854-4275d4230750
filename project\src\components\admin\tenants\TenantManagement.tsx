import React, { useState } from 'react';
import { TenantManagementDashboard } from './TenantManagementDashboard';
import { TenantFormModal } from './TenantFormModal';
import { TenantDetailsModal } from './TenantDetailsModal';
import { DeleteTenantModal } from './DeleteTenantModal';

interface Tenant {
  id: number;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  status: 'active' | 'suspended' | 'inactive';
  employeeCount: number;
  activeEmployees: number;
  locationCount: number;
  totalRevenue: number;
  orderCount: number;
  ordersToday: number;
  ordersWeek: number;
  lastActivity: string;
  planType: string;
  subscriptionStatus: string;
  createdAt: string;
  updatedAt: string;
}

export const TenantManagement: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Create tenant
  const handleCreateTenant = async (tenantData: Omit<Tenant, 'id'>) => {
    try {
      const response = await fetch('/api/admin/tenants', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tenantData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create tenant');
      }

      const newTenant = await response.json();
      console.log('✅ Tenant created successfully:', newTenant);
      
      // Refresh the tenant list
      setRefreshKey(prev => prev + 1);
      
      // Show success message (you can implement a toast notification here)
      alert(`Tenant "${newTenant.name}" created successfully!\nDefault admin PIN: ${newTenant.defaultPin}`);
      
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  };

  // Update tenant
  const handleUpdateTenant = async (tenantData: Tenant) => {
    try {
      const response = await fetch(`/api/admin/tenants/${tenantData.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tenantData)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update tenant');
      }

      const updatedTenant = await response.json();
      console.log('✅ Tenant updated successfully:', updatedTenant);
      
      // Refresh the tenant list
      setRefreshKey(prev => prev + 1);
      
      // Show success message
      alert(`Tenant "${updatedTenant.name}" updated successfully!`);
      
    } catch (error) {
      console.error('Error updating tenant:', error);
      throw error;
    }
  };

  // Delete tenant
  const handleDeleteTenant = async (tenant: Tenant) => {
    try {
      const response = await fetch(`/api/admin/tenants/${tenant.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ confirmDelete: true })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete tenant');
      }

      const result = await response.json();
      console.log('✅ Tenant deleted successfully:', result);
      
      // Refresh the tenant list
      setRefreshKey(prev => prev + 1);
      
      // Show success message
      alert(`Tenant "${tenant.name}" deleted successfully!`);
      
    } catch (error) {
      console.error('Error deleting tenant:', error);
      throw error;
    }
  };

  // Modal handlers
  const handleCreateClick = () => {
    setSelectedTenant(null);
    setShowCreateModal(true);
  };

  const handleEditClick = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setShowEditModal(true);
  };

  const handleViewClick = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setShowDetailsModal(true);
  };

  const handleDeleteClick = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setShowDeleteModal(true);
  };

  const handleEditFromDetails = (tenant: Tenant) => {
    setShowDetailsModal(false);
    setSelectedTenant(tenant);
    setShowEditModal(true);
  };

  const handleDeleteFromDetails = (tenant: Tenant) => {
    setShowDetailsModal(false);
    setSelectedTenant(tenant);
    setShowDeleteModal(true);
  };

  const closeAllModals = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDetailsModal(false);
    setShowDeleteModal(false);
    setSelectedTenant(null);
  };

  return (
    <div className="space-y-6">
      {/* Main Dashboard */}
      <TenantManagementDashboard
        key={refreshKey}
        onCreateTenant={handleCreateClick}
        onEditTenant={handleEditClick}
        onViewTenant={handleViewClick}
        onDeleteTenant={handleDeleteClick}
      />

      {/* Create Tenant Modal */}
      <TenantFormModal
        isOpen={showCreateModal}
        onClose={closeAllModals}
        onSubmit={handleCreateTenant}
        mode="create"
      />

      {/* Edit Tenant Modal */}
      <TenantFormModal
        isOpen={showEditModal}
        onClose={closeAllModals}
        onSubmit={handleUpdateTenant}
        tenant={selectedTenant}
        mode="edit"
      />

      {/* Tenant Details Modal */}
      <TenantDetailsModal
        isOpen={showDetailsModal}
        onClose={closeAllModals}
        tenantId={selectedTenant?.id || null}
        onEdit={handleEditFromDetails}
        onDelete={handleDeleteFromDetails}
      />

      {/* Delete Confirmation Modal */}
      <DeleteTenantModal
        isOpen={showDeleteModal}
        onClose={closeAllModals}
        onConfirm={handleDeleteTenant}
        tenant={selectedTenant}
      />
    </div>
  );
};
