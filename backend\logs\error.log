{"level":"error","message":"Unhandled error: permissions.some is not a function","service":"pos-backend","stack":"TypeError: permissions.some is not a function\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\middleware\\auth.js:72:39\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at ensureTenantIsolation (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\middleware\\auth.js:89:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\middleware\\auth.js:54:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-05-29T21:39:49.970Z"}
{"code":"22P02","detail":"Expected \":\", but found \"}\".","file":"jsonfuncs.c","length":239,"level":"error","line":"661","message":"Error saving order: invalid input syntax for type json","name":"error","routine":"json_errsave_error","service":"pos-backend","severity":"ERROR","stack":"error: invalid input syntax for type json\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:485:20","timestamp":"2025-05-29T21:42:21.554Z","where":"JSON data, line 1: ...st Burger\\\",\\\"price\\\":\\\"12.99\\\",\\\"quantity\\\":2}\"}\nunnamed portal parameter $2 = '...'"}
{"code":"23502","column":"timestamp","detail":"Failing row contains (bb65d031-09be-404b-afb5-b5422d4ae26a, [{\"name\": \"Test Burger\", \"price\": \"12.99\", \"quantity\": 2, \"produ..., null, null, 25.98, 25.98, 2.08, null, 0.00, null, 17413b0b-0743-4170-a01a-3adaa13597ad, 2025-05-29 19:01:41.805133, bcca581c-f64d-46f3-8e8b-fd9860271699, afa5bc22-0c36-4d3f-bfbb-8e8813d07904, null, null, null, null, null).","file":"execMain.c","length":525,"level":"error","line":"1988","message":"Error saving order: null value in column \"timestamp\" of relation \"orders\" violates not-null constraint","name":"error","routine":"ExecConstraints","schema":"public","service":"pos-backend","severity":"ERROR","stack":"error: null value in column \"timestamp\" of relation \"orders\" violates not-null constraint\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:485:20","table":"orders","timestamp":"2025-05-29T23:01:41.809Z"}
{"code":"22008","file":"datetime.c","hint":"Perhaps you need a different \"datestyle\" setting.","length":205,"level":"error","line":"4110","message":"Error saving order: date/time field value out of range: \"1748559746347\"","name":"error","routine":"DateTimeParseError","service":"pos-backend","severity":"ERROR","stack":"error: date/time field value out of range: \"1748559746347\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:485:20","timestamp":"2025-05-29T23:02:26.364Z","where":"unnamed portal parameter $3 = '...'"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend-phase3","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-30T03:54:16.552Z","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend-phase3","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-30T03:54:53.462Z","type":"entity.parse.failed"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Tenant creation error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-phase3","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-phase3.js:898:20","timestamp":"2025-05-30T03:55:09.366Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend-phase3","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-30T04:29:11.458Z","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend-phase3","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-30T04:33:05.120Z","type":"entity.parse.failed"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:36:51.564Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:06.152Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:06.394Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:06.701Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:06.909Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:07.223Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:07.432Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:07.636Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:07.845Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Login error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:268:20","timestamp":"2025-05-31T01:38:08.069Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Tenants fetch error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:04:25.230Z"}
{"code":"28P01","file":"auth.c","length":104,"level":"error","line":"324","message":"Tenants fetch error: password authentication failed for user \"postgres\"","name":"error","routine":"auth_failed","service":"pos-backend-combined","severity":"FATAL","stack":"error: password authentication failed for user \"postgres\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:04:25.334Z"}
{"code":"42703","file":"parse_relation.c","length":118,"level":"error","line":"3721","message":"Tenants fetch error: column ts.subscription_plan does not exist","name":"error","position":"51","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column ts.subscription_plan does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:05:59.654Z"}
{"code":"42703","file":"parse_relation.c","length":118,"level":"error","line":"3721","message":"Tenants fetch error: column ts.subscription_plan does not exist","name":"error","position":"51","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column ts.subscription_plan does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:05:59.867Z"}
{"code":"42703","file":"parse_relation.c","length":118,"level":"error","line":"3721","message":"Tenants fetch error: column ts.subscription_plan does not exist","name":"error","position":"51","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column ts.subscription_plan does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:06:14.413Z"}
{"code":"42703","file":"parse_relation.c","length":118,"level":"error","line":"3721","message":"Tenants fetch error: column ts.subscription_plan does not exist","name":"error","position":"51","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column ts.subscription_plan does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1010:20","timestamp":"2025-05-31T02:06:14.572Z"}
{"code":"23502","column":"email","detail":"Failing row contains (46ea2046-64b0-4ffd-bb8a-ab02683742df, bar-pos, bar-pos, null, null, null, active, null, 2025-05-30 22:11:10.117374, 2025-05-30 22:11:10.117374).","file":"execMain.c","length":334,"level":"error","line":"1988","message":"Tenant creation error: null value in column \"email\" of relation \"tenants\" violates not-null constraint","name":"error","routine":"ExecConstraints","schema":"public","service":"pos-backend-combined","severity":"ERROR","stack":"error: null value in column \"email\" of relation \"tenants\" violates not-null constraint\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1048:28","table":"tenants","timestamp":"2025-05-31T02:11:10.124Z"}
{"code":"23502","column":"email","detail":"Failing row contains (8ace381b-5785-42c9-ad63-d2a493da4655, bar-pos, bar-pos, null, null, null, active, null, 2025-05-30 22:14:16.564629, 2025-05-30 22:14:16.564629).","file":"execMain.c","length":334,"level":"error","line":"1988","message":"Tenant creation error: null value in column \"email\" of relation \"tenants\" violates not-null constraint","name":"error","routine":"ExecConstraints","schema":"public","service":"pos-backend-combined","severity":"ERROR","stack":"error: null value in column \"email\" of relation \"tenants\" violates not-null constraint\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-combined.js:1048:28","table":"tenants","timestamp":"2025-05-31T02:14:16.565Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:16:19.860Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:16:19.917Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:33:51.010Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:33:51.055Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:42:08.759Z"}
{"code":"42703","file":"parse_relation.c","hint":"Perhaps you meant to reference the column \"p.category_id\".","length":168,"level":"error","line":"3732","message":"Inventory fetch error: column p.category does not exist","name":"error","position":"70","routine":"errorMissingColumn","service":"pos-backend-combined","severity":"ERROR","stack":"error: column p.category does not exist\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server.js:528:20","timestamp":"2025-06-02T02:42:08.920Z"}
