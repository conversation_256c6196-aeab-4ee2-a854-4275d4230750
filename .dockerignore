# 1. Version control
.git/
.gitignore
.svn/
.hg/

# 2. Node.js dependencies
node_modules/
**/node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
pnpm-debug.log

# 3. Build outputs
build/
dist/
out/
**/build/
**/dist/
**/out/

# 4. Test and coverage artifacts
coverage/
**/coverage/
test/
tests/
__tests__/
**/__tests__/
cypress/
**/cypress/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# 5. IDE/editor files
.idea/
.vscode/
*.swp
*.swo

# 6. Debug and log files
logs/
**/logs/
*.log
*.trace

# 7. Environment and secrets
.env
.env.*
*.env
*.pem
*.key
*.crt
config.local.*
*.local.yml
.local/
**/.local/

# 8. Documentation and markdown
README*
*.md
docs/
**/docs/

# 9. Temporary and backup files
tmp/
temp/
*.tmp
*.bak
*.backup
*~

# 10. Project-specific and misc
public/
**/public/
.DS_Store
Thumbs.db
*.xlsx

# 11. Docker and compose files
Dockerfile*
docker-compose*

# 12. Miscellaneous
.blackboxrules

# 13. Exclude lock files from ignore (as per instructions)
# (Do not add *.lock here)

# 14. Exclude Java/Go/Python/Java-specific patterns (not needed for this project)
# (No __pycache__, *.pyc, *.pyo, .pytest_cache, .coverage, /vendor/, *.test, .go-cache, target/, *.class, .gradle/)
