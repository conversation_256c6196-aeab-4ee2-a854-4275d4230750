<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Restructured POS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 20px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #2563EB;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 600;
        }
        .success { background: rgba(34, 197, 94, 0.2); border: 2px solid #22C55E; }
        .error { background: rgba(239, 68, 68, 0.2); border: 2px solid #EF4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border: 2px solid #F59E0B; }
        .info { background: rgba(59, 130, 246, 0.2); border: 2px solid #3B82F6; }
        
        .step {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: left;
        }
        .step h3 {
            margin-top: 0;
            color: #60A5FA;
        }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🚀 Test Restructured POS Interface</h1>
            <p>This page will help you enable and test the restructured POS interface</p>
            
            <div id="status-display">
                <!-- Status will be populated by JavaScript -->
            </div>
            
            <div>
                <button onclick="enableAndTest()">🚀 Enable & Test Restructured Mode</button>
                <button onclick="openPOSDirectly()">📱 Open POS System</button>
                <button onclick="clearAndReset()">🗑️ Clear All Settings</button>
            </div>
        </div>
        
        <div class="card">
            <h2>📋 Step-by-Step Instructions</h2>
            
            <div class="step">
                <h3>Step 1: Enable Restructured Mode</h3>
                <p>Click the "Enable & Test Restructured Mode" button above. This will:</p>
                <ul>
                    <li>Set <code>localStorage.useRestructuredPOS = 'true'</code></li>
                    <li>Open the POS system with the correct parameters</li>
                    <li>Force the restructured interface to load</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Step 2: Login to POS System</h3>
                <p>When the POS system opens, login with:</p>
                <ul>
                    <li><strong>PIN:</strong> <code>123456</code> (Super Admin)</li>
                    <li><strong>Alternative:</strong> <code>567890</code> (Manager/Employee)</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Step 3: Look for Restructured Indicators</h3>
                <p>Once logged in, you should see:</p>
                <ul>
                    <li>🚀 "RESTRUCTURED" badge in the header</li>
                    <li>Optimized sidebar with categorized navigation</li>
                    <li>Enhanced product grid and order panel</li>
                    <li>Modern design system components</li>
                </ul>
            </div>
            
            <div class="step">
                <h3>Step 4: Test Different Tabs</h3>
                <p>Navigate through the sidebar to test:</p>
                <ul>
                    <li><strong>Point of Sale:</strong> Enhanced product grid and order panel</li>
                    <li><strong>Floor Layout:</strong> Restructured floor management</li>
                    <li><strong>Order Queue:</strong> Improved order tracking</li>
                    <li><strong>Inventory:</strong> Enhanced inventory management</li>
                    <li><strong>Analytics:</strong> Advanced analytics dashboard</li>
                </ul>
            </div>
        </div>
        
        <div class="card">
            <h2>🔧 Debug Information</h2>
            <div id="debug-info">
                <!-- Debug info will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        function updateStatus() {
            const statusDiv = document.getElementById('status-display');
            const debugDiv = document.getElementById('debug-info');
            
            const isEnabled = localStorage.getItem('useRestructuredPOS') === 'true';
            const currentUrl = window.location.href;
            
            // Status display
            let statusHTML = '';
            if (isEnabled) {
                statusHTML = '<div class="status success">✅ Restructured Mode: ENABLED</div>';
            } else {
                statusHTML = '<div class="status error">❌ Restructured Mode: DISABLED</div>';
            }
            statusDiv.innerHTML = statusHTML;
            
            // Debug info
            const debugInfo = {
                'Restructured Flag': localStorage.getItem('useRestructuredPOS') || 'not set',
                'Current URL': currentUrl,
                'Target POS URL': 'http://localhost:5173/?industry=true&restructured=true',
                'Browser': navigator.userAgent.split(' ').pop(),
                'Timestamp': new Date().toLocaleString()
            };
            
            let debugHTML = '<table style="width: 100%; text-align: left;">';
            for (const [key, value] of Object.entries(debugInfo)) {
                debugHTML += `
                    <tr style="border-bottom: 1px solid rgba(255,255,255,0.2);">
                        <td style="padding: 8px; font-weight: 600;">${key}:</td>
                        <td style="padding: 8px; font-family: monospace; word-break: break-all;">${value}</td>
                    </tr>
                `;
            }
            debugHTML += '</table>';
            debugDiv.innerHTML = debugHTML;
        }
        
        function enableAndTest() {
            console.log('🚀 Enabling restructured mode...');

            // Set both localStorage flags
            localStorage.setItem('useRestructuredPOS', 'true');
            localStorage.setItem('useIndustryStandardPOS', 'true');

            // Update status
            updateStatus();

            // Show success message
            alert('✅ Restructured mode enabled! Opening POS system...');

            // Open POS system with all necessary parameters
            const posUrl = 'http://localhost:5173/?industry=true&restructured=true';
            console.log('Opening:', posUrl);
            window.open(posUrl, '_blank');
        }
        
        function openPOSDirectly() {
            const posUrl = 'http://localhost:5173/?industry=true&restructured=true';
            console.log('Opening POS directly:', posUrl);
            window.open(posUrl, '_blank');
        }
        
        function clearAndReset() {
            localStorage.clear();
            sessionStorage.clear();
            updateStatus();
            alert('🗑️ All settings cleared! Restructured mode is now disabled.');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            console.log('🔧 Test page loaded');
            console.log('localStorage.useRestructuredPOS:', localStorage.getItem('useRestructuredPOS'));
        });
        
        // Update status every 5 seconds
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
