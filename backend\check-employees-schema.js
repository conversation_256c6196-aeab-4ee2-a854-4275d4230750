const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function checkEmployeesSchema() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Checking employees table schema...');
    
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Employees table columns:');
    result.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // Check if location_id column exists
    const hasLocationId = result.rows.some(row => row.column_name === 'location_id');
    
    if (!hasLocationId) {
      console.log('\n⚠️ location_id column missing, adding it...');
      await client.query(`
        ALTER TABLE employees ADD COLUMN location_id VARCHAR(255)
      `);
      console.log('✅ Added location_id column to employees table');
    }
    
    // Check tenant_settings table to see if it needs integer tenant_id
    console.log('\n🔍 Checking tenant_settings table...');
    const tsResult = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'tenant_settings' AND column_name = 'tenant_id'
    `);
    
    console.log('📋 tenant_settings.tenant_id type:', tsResult.rows[0]?.data_type);
    
    // Check locations table
    console.log('\n🔍 Checking locations table...');
    const locResult = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'locations' AND column_name = 'tenant_id'
    `);
    
    console.log('📋 locations.tenant_id type:', locResult.rows[0]?.data_type);
    
  } catch (error) {
    console.error('💥 Schema check failed:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

checkEmployeesSchema();
