const { Pool } = require('pg');

// Create a single pool instance that can be shared across the application
const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  // Add pool specific configuration
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// The pool will emit an error on behalf of any idle clients
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
});

// Export query helper functions
module.exports = {
  /**
   * Execute a query using a pooled connection
   * @param {string} text - The SQL query text
   * @param {Array} params - The query parameters
   */
  async query(text, params) {
    const start = Date.now();
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text, duration, rows: res.rowCount });
    return res;
  },

  /**
   * Get a client from the pool for transactions
   */
  async getClient() {
    const client = await pool.connect();
    const query = client.query;
    const release = client.release;

    // Set a timeout of 5 seconds, after which we will log this client's last query
    const timeout = setTimeout(() => {
      console.error('A client has been checked out for more than 5 seconds!');
      console.error(`The last executed query on this client was: ${client.lastQuery}`);
    }, 5000);

    // Monkey patch the query method to keep track of the last query executed
    client.query = (...args) => {
      client.lastQuery = args;
      return query.apply(client, args);
    };

    client.release = () => {
      clearTimeout(timeout);
      client.query = query;
      client.release = release;
      return release.apply(client);
    };

    return client;
  },

  /**
   * Prepare a statement with given name and text
   * @param {string} name - The name of the prepared statement
   * @param {string} text - The SQL query text
   * @param {Array} params - The parameter types array
   */
  async prepare(name, text, params) {
    await pool.query(`PREPARE ${name} AS ${text}`, params);
  },

  /**
   * Close the pool
   */
  async end() {
    await pool.end();
  }
};
