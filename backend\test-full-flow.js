const http = require('http');

// Test all data saving operations
async function testFullFlow() {
  // 1. Add a product
  const productData = JSON.stringify({
    name: "Flow Test Beer",
    price: 6.99,
    category: "beer",
    description: "Test product for full flow",
    inStock: true
  });

  // Add product request
  const addProduct = () => new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/products',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': productData.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n1. Add Product Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', JSON.parse(data));
        resolve(JSON.parse(data));
      });
    });

    req.on('error', error => {
      console.error('Error:', error);
      reject(error);
    });

    req.write(productData);
    req.end();
  });

  // 2. Add an employee
  const employeeData = JSON.stringify({
    name: "Flow Test Employee",
    pin: "9999",
    role: "server"
  });

  const addEmployee = () => new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/employees',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': employeeData.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n2. Add Employee Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', JSON.parse(data));
        resolve(JSON.parse(data));
      });
    });

    req.on('error', error => {
      console.error('Error:', error);
      reject(error);
    });

    req.write(employeeData);
    req.end();
  });

  // 3. Create an order with the new product and employee
  const createOrder = (product, employee) => new Promise((resolve, reject) => {
    const orderData = JSON.stringify({
      items: JSON.stringify([{
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 2
      }]),
      timestamp: new Date().toISOString(),
      status: 'completed',
      total: product.price * 2,
      subtotal: product.price * 2,
      tax: 0,
      payment_method: 'cash',
      employee_id: employee.id
    });

    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': orderData.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n3. Create Order Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', JSON.parse(data));
        resolve(JSON.parse(data));
      });
    });

    req.on('error', error => {
      console.error('Error:', error);
      reject(error);
    });

    req.write(orderData);
    req.end();
  });

  // Run the full flow
  try {
    const product = await addProduct();
    const employee = await addEmployee();
    await createOrder(product, employee);
    console.log('\nFull flow test completed successfully!');
  } catch (error) {
    console.error('\nFull flow test failed:', error);
  }
}

// Run the tests
testFullFlow();
