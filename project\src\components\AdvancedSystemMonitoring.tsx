import React, { useState, useEffect, useRef } from 'react';
import { 
  Activity, 
  Server, 
  Database, 
  Wifi, 
  Cpu, 
  HardDrive,
  Memory,
  Network,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Globe,
  Users,
  Clock,
  BarChart3,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Settings,
  RefreshCw,
  Download,
  Bell,
  Eye,
  Filter,
  Search
} from 'lucide-react';

interface SystemMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
    frequency: number;
  };
  memory: {
    used: number;
    total: number;
    available: number;
    cached: number;
  };
  disk: {
    used: number;
    total: number;
    available: number;
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    latency: number;
  };
  database: {
    connections: number;
    maxConnections: number;
    queryTime: number;
    cacheHitRate: number;
    replicationLag: number;
  };
  application: {
    activeUsers: number;
    requestsPerSecond: number;
    responseTime: number;
    errorRate: number;
    uptime: number;
  };
}

interface SystemAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  category: 'performance' | 'security' | 'capacity' | 'availability';
  title: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
  source: string;
  severity: number;
}

interface PredictiveInsight {
  id: string;
  type: 'capacity_planning' | 'performance_degradation' | 'failure_prediction' | 'optimization';
  title: string;
  description: string;
  confidence: number;
  timeframe: string;
  impact: 'high' | 'medium' | 'low';
  recommendations: string[];
  metrics: Record<string, number>;
}

const AdvancedSystemMonitoring: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [insights, setInsights] = useState<PredictiveInsight[]>([]);
  const [historicalData, setHistoricalData] = useState<SystemMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');
  const [selectedMetric, setSelectedMetric] = useState('cpu');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const chartRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadSystemData();
    
    if (autoRefresh) {
      const interval = setInterval(loadSystemData, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedTimeRange]);

  const loadSystemData = async () => {
    try {
      setIsLoading(true);
      
      // Load current metrics
      const metricsResponse = await fetch('http://localhost:4000/api/admin/monitoring/metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics(metricsData);
      }

      // Load alerts
      const alertsResponse = await fetch('http://localhost:4000/api/admin/monitoring/alerts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setAlerts(alertsData);
      }

      // Load predictive insights
      const insightsResponse = await fetch('http://localhost:4000/api/admin/monitoring/insights', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (insightsResponse.ok) {
        const insightsData = await insightsResponse.json();
        setInsights(insightsData);
      }

      // Load historical data
      const historyResponse = await fetch(`http://localhost:4000/api/admin/monitoring/history?range=${selectedTimeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setHistoricalData(historyData);
      }

    } catch (error) {
      console.error('Error loading system data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      await fetch(`http://localhost:4000/api/admin/monitoring/alerts/${alertId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      setAlerts(alerts.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      console.error('Error acknowledging alert:', error);
    }
  };

  const exportMetrics = async (format: 'csv' | 'json' | 'pdf') => {
    try {
      const response = await fetch(`http://localhost:4000/api/admin/monitoring/export?format=${format}&range=${selectedTimeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-metrics-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting metrics:', error);
    }
  };

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return 'text-red-600 dark:text-red-400';
    if (value >= thresholds.warning) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getStatusIcon = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return <XCircle className="w-5 h-5 text-red-600" />;
    if (value >= thresholds.warning) return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
    return <CheckCircle className="w-5 h-5 text-green-600" />;
  };

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (isLoading && !metrics) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Activity className="w-8 h-8 animate-pulse mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading system monitoring data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      
      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Advanced System Monitoring
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Real-time performance analytics and predictive maintenance
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="5m">Last 5 minutes</option>
                <option value="1h">Last hour</option>
                <option value="24h">Last 24 hours</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
              </select>
              
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  autoRefresh
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
              </button>
              
              <button
                onClick={loadSystemData}
                disabled={isLoading}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
                }`}
              >
                <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              
              <button
                onClick={() => exportMetrics('pdf')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Critical Alerts */}
        {alerts.filter(alert => alert.type === 'critical' && !alert.acknowledged).length > 0 && (
          <div className="mb-8">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                  Critical System Alerts
                </h3>
              </div>
              <div className="space-y-2">
                {alerts.filter(alert => alert.type === 'critical' && !alert.acknowledged).map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded border">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{alert.title}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{alert.message}</p>
                    </div>
                    <button
                      onClick={() => acknowledgeAlert(alert.id)}
                      className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                    >
                      Acknowledge
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* System Overview Cards */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            {/* CPU Usage */}
            <div className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className={`text-lg font-semibold ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  CPU Usage
                </h3>
                <Cpu className="w-6 h-6 text-blue-600" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-2xl font-bold ${
                    getStatusColor(metrics.cpu.usage, { warning: 70, critical: 90 })
                  }`}>
                    {metrics.cpu.usage.toFixed(1)}%
                  </span>
                  {getStatusIcon(metrics.cpu.usage, { warning: 70, critical: 90 })}
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                  <div 
                    className={`h-2 rounded-full ${
                      metrics.cpu.usage >= 90 ? 'bg-red-600' :
                      metrics.cpu.usage >= 70 ? 'bg-yellow-600' : 'bg-green-600'
                    }`}
                    style={{ width: `${metrics.cpu.usage}%` }}
                  ></div>
                </div>
                
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>{metrics.cpu.cores} cores • {metrics.cpu.frequency}GHz</p>
                  <p>Temperature: {metrics.cpu.temperature}°C</p>
                </div>
              </div>
            </div>

            {/* Memory Usage */}
            <div className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className={`text-lg font-semibold ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Memory Usage
                </h3>
                <Memory className="w-6 h-6 text-green-600" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-2xl font-bold ${
                    getStatusColor((metrics.memory.used / metrics.memory.total) * 100, { warning: 80, critical: 95 })
                  }`}>
                    {((metrics.memory.used / metrics.memory.total) * 100).toFixed(1)}%
                  </span>
                  {getStatusIcon((metrics.memory.used / metrics.memory.total) * 100, { warning: 80, critical: 95 })}
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                  <div 
                    className={`h-2 rounded-full ${
                      (metrics.memory.used / metrics.memory.total) * 100 >= 95 ? 'bg-red-600' :
                      (metrics.memory.used / metrics.memory.total) * 100 >= 80 ? 'bg-yellow-600' : 'bg-green-600'
                    }`}
                    style={{ width: `${(metrics.memory.used / metrics.memory.total) * 100}%` }}
                  ></div>
                </div>
                
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>Used: {formatBytes(metrics.memory.used)}</p>
                  <p>Total: {formatBytes(metrics.memory.total)}</p>
                </div>
              </div>
            </div>

            {/* Disk Usage */}
            <div className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className={`text-lg font-semibold ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Disk Usage
                </h3>
                <HardDrive className="w-6 h-6 text-purple-600" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-2xl font-bold ${
                    getStatusColor((metrics.disk.used / metrics.disk.total) * 100, { warning: 85, critical: 95 })
                  }`}>
                    {((metrics.disk.used / metrics.disk.total) * 100).toFixed(1)}%
                  </span>
                  {getStatusIcon((metrics.disk.used / metrics.disk.total) * 100, { warning: 85, critical: 95 })}
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                  <div 
                    className={`h-2 rounded-full ${
                      (metrics.disk.used / metrics.disk.total) * 100 >= 95 ? 'bg-red-600' :
                      (metrics.disk.used / metrics.disk.total) * 100 >= 85 ? 'bg-yellow-600' : 'bg-green-600'
                    }`}
                    style={{ width: `${(metrics.disk.used / metrics.disk.total) * 100}%` }}
                  ></div>
                </div>
                
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>Used: {formatBytes(metrics.disk.used)}</p>
                  <p>IOPS: {metrics.disk.iops.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Network Activity */}
            <div className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className={`text-lg font-semibold ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Network
                </h3>
                <Network className="w-6 h-6 text-orange-600" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className={`text-2xl font-bold ${
                    getStatusColor(metrics.network.latency, { warning: 100, critical: 200 })
                  }`}>
                    {metrics.network.latency}ms
                  </span>
                  {getStatusIcon(metrics.network.latency, { warning: 100, critical: 200 })}
                </div>
                
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>In: {formatBytes(metrics.network.bytesIn)}/s</p>
                  <p>Out: {formatBytes(metrics.network.bytesOut)}/s</p>
                  <p>Packets: {metrics.network.packetsIn.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Predictive Insights */}
        {insights.length > 0 && (
          <div className="mb-8">
            <h2 className={`text-2xl font-bold mb-6 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              AI-Powered Predictive Insights
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {insights.slice(0, 4).map((insight) => (
                <div key={insight.id} className={`p-6 rounded-lg ${
                  isDarkMode ? 'bg-gray-800' : 'bg-white'
                } shadow-lg border-l-4 ${
                  insight.impact === 'high' ? 'border-red-500' :
                  insight.impact === 'medium' ? 'border-yellow-500' : 'border-green-500'
                }`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`text-lg font-semibold ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {insight.title}
                    </h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      insight.impact === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      {insight.impact} impact
                    </span>
                  </div>
                  
                  <p className={`text-sm mb-4 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-600'
                  }`}>
                    {insight.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>
                      Confidence: {insight.confidence}% • {insight.timeframe}
                    </span>
                    <button className="text-blue-600 hover:text-blue-700 font-medium">
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedSystemMonitoring;
