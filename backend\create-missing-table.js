// Create missing ai_fraud_analysis table

const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost', 
  database: 'BARPOS',
  password: '<PERSON><PERSON>@0319',
  port: 5432
});

async function createMissingTable() {
  try {
    const client = await pool.connect();
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS ai_fraud_analysis (
        id SERIAL PRIMARY KEY,
        tenant_id INTEGER NOT NULL,
        transaction_id VARCHAR(255) NOT NULL,
        risk_score DECIMAL(5,4) NOT NULL,
        risk_level VARCHAR(20) NOT NULL,
        analysis_data JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ ai_fraud_analysis table created');
    client.release();
    await pool.end();
  } catch (error) {
    console.error('❌ Error creating table:', error);
  }
}

createMissingTable();
