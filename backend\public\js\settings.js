// State management
let state = {
    employees: [],
    products: [],
    categories: [],
    systemConfig: {
        tax_rate: 0.0825,
        receipt_header: 'Thank you for visiting!',
        receipt_footer: 'Please come again',
        business_name: 'Bar POS',
        business_address: '',
        business_phone: '',
        theme_primary_color: '#4f46e5',
        theme_secondary_color: '#10b981'
    }
};

// API functions
async function fetchEmployees() {
    try {
        const response = await fetch('http://localhost:4000/employees');
        if (!response.ok) throw new Error('Failed to fetch employees');
        const employees = await response.json();
        state.employees = employees;
        renderEmployees();
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to fetch employees');
    }
}

async function fetchProducts() {
    try {
        const response = await fetch('http://localhost:4000/products');
        if (!response.ok) throw new Error('Failed to fetch products');
        const products = await response.json();
        state.products = products;
        document.getElementById('totalMenuItems').textContent = products.length;
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to fetch products');
    }
}

async function fetchCategories() {
    try {
        const response = await fetch('http://localhost:4000/categories');
        if (!response.ok) throw new Error('Failed to fetch categories');
        const categories = await response.json();
        state.categories = categories;
        document.getElementById('totalCategories').textContent = categories.length;
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to fetch categories');
    }
}

async function fetchSettings() {
    try {
        const response = await fetch('http://localhost:4000/settings');
        if (!response.ok) throw new Error('Failed to fetch settings');
        const settings = await response.json();
        state.systemConfig = settings;
        updateSettingsDisplay();
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to fetch settings');
    }
}

async function updateSettings(settings) {
    try {
        const response = await fetch('http://localhost:4000/settings', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(settings)
        });
        if (!response.ok) throw new Error('Failed to update settings');
        const updatedSettings = await response.json();
        state.systemConfig = updatedSettings;
        updateSettingsDisplay();
        return updatedSettings;
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to update settings');
        throw error;
    }
}

// Render functions
function renderEmployees() {
    const tbody = document.querySelector('#employeesTable tbody');
    tbody.innerHTML = state.employees.map(employee => `
        <tr class="border-b border-gray-700 last:border-0">
            <td class="py-2 text-white">${employee.name}</td>
            <td class="py-2 text-gray-300">
                <span class="capitalize">${employee.role}</span>
            </td>
            <td class="py-2 text-gray-400 text-sm">
                ${employee.created_at ? new Date(employee.created_at).toLocaleDateString() : ''}
            </td>
            <td class="py-2">
                <div class="flex space-x-2">
                    <button
                        onclick="handleEditEmployee('${employee.id}')"
                        class="text-blue-400 hover:text-blue-300 transition-colors p-1"
                        aria-label="Edit ${employee.name}"
                    >
                        <i data-lucide="edit-2" class="h-4 w-4"></i>
                    </button>
                    <button
                        onclick="handleDeleteEmployee('${employee.id}')"
                        class="text-red-400 hover:text-red-300 transition-colors p-1"
                        aria-label="Delete ${employee.name}"
                    >
                        <i data-lucide="trash" class="h-4 w-4"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    lucide.createIcons();
}

function updateSettingsDisplay() {
    document.getElementById('currentTaxRate').value = `${(state.systemConfig.tax_rate * 100).toFixed(2)}%`;
}

// Modal handling
function showModal(content) {
    const modalContainer = document.getElementById('modalContainer');
    modalContainer.innerHTML = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
                ${content}
            </div>
        </div>
    `;
    lucide.createIcons();
}

// Event handlers
async function handleEditEmployee(employeeId) {
    const employee = state.employees.find(e => e.id === employeeId);
    if (!employee) return;

    showModal(`
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">Edit Employee</h3>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white transition-colors">
                <i data-lucide="x" class="h-6 w-6"></i>
            </button>
        </div>
        <form onsubmit="handleSaveEmployee(event, '${employeeId}')" class="space-y-4">
            <div>
                <label class="block text-gray-300 mb-1">Name</label>
                <input type="text" id="employeeName" value="${employee.name}" required
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">PIN</label>
                <input type="password" id="employeePin" placeholder="Leave blank to keep current"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Role</label>
                <select id="employeeRole" required
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
                    <option value="admin" ${employee.role === 'admin' ? 'selected' : ''}>Admin</option>
                    <option value="manager" ${employee.role === 'manager' ? 'selected' : ''}>Manager</option>
                    <option value="bartender" ${employee.role === 'bartender' ? 'selected' : ''}>Bartender</option>
                    <option value="server" ${employee.role === 'server' ? 'selected' : ''}>Server</option>
                </select>
            </div>
            <div class="flex justify-end space-x-2 mt-6">
                <button type="button" onclick="closeModal()"
                    class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors">
                    Cancel
                </button>
                <button type="submit"
                    class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors">
                    Save Changes
                </button>
            </div>
        </form>
    `);
}

async function handleSaveEmployee(event, employeeId) {
    event.preventDefault();
    const formData = {
        name: document.getElementById('employeeName').value,
        pin: document.getElementById('employeePin').value,
        role: document.getElementById('employeeRole').value
    };

    try {
        const response = await fetch(`http://localhost:4000/employees/${employeeId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
        });
        if (!response.ok) throw new Error('Failed to update employee');
        await fetchEmployees();
        closeModal();
        alert('Employee updated successfully');
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to update employee');
    }
}

async function handleDeleteEmployee(employeeId) {
    if (!confirm('Are you sure you want to delete this employee?')) return;

    try {
        const response = await fetch(`http://localhost:4000/employees/${employeeId}`, {
            method: 'DELETE'
        });
        if (!response.ok) throw new Error('Failed to delete employee');
        await fetchEmployees();
        alert('Employee deleted successfully');
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to delete employee');
    }
}

function showConfigModal() {
    showModal(`
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-white">System Configuration</h3>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white transition-colors">
                <i data-lucide="x" class="h-6 w-6"></i>
            </button>
        </div>
        <form onsubmit="handleSaveConfig(event)" class="space-y-4">
            <div>
                <label class="block text-gray-300 mb-1">Tax Rate (%)</label>
                <input type="number" id="configTaxRate" step="0.01" min="0" max="100"
                    value="${(state.systemConfig.tax_rate * 100).toFixed(2)}" required
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Business Name</label>
                <input type="text" id="configBusinessName" value="${state.systemConfig.business_name || ''}"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Business Address</label>
                <input type="text" id="configBusinessAddress" value="${state.systemConfig.business_address || ''}"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Business Phone</label>
                <input type="text" id="configBusinessPhone" value="${state.systemConfig.business_phone || ''}"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Receipt Header</label>
                <textarea id="configReceiptHeader" rows="2"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">${state.systemConfig.receipt_header || ''}</textarea>
            </div>
            <div>
                <label class="block text-gray-300 mb-1">Receipt Footer</label>
                <textarea id="configReceiptFooter" rows="2"
                    class="w-full bg-gray-700 text-white px-3 py-2 rounded-md">${state.systemConfig.receipt_footer || ''}</textarea>
            </div>
            <div class="flex justify-end space-x-2 mt-6">
                <button type="button" onclick="closeModal()"
                    class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors">
                    Cancel
                </button>
                <button type="submit"
                    class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors">
                    Save Changes
                </button>
            </div>
        </form>
    `);
}

async function handleSaveConfig(event) {
    event.preventDefault();
    const formData = {
        tax_rate: parseFloat(document.getElementById('configTaxRate').value) / 100,
        business_name: document.getElementById('configBusinessName').value,
        business_address: document.getElementById('configBusinessAddress').value,
        business_phone: document.getElementById('configBusinessPhone').value,
        receipt_header: document.getElementById('configReceiptHeader').value,
        receipt_footer: document.getElementById('configReceiptFooter').value
    };

    try {
        await updateSettings(formData);
        closeModal();
        alert('Settings updated successfully');
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to update settings');
    }
}

function closeModal() {
    document.getElementById('modalContainer').innerHTML = '';
}

// Event listeners
document.getElementById('editConfigBtn').addEventListener('click', showConfigModal);
document.getElementById('configureReceiptBtn').addEventListener('click', showConfigModal);

document.getElementById('downloadTemplateBtn').addEventListener('click', async () => {
    try {
        const response = await fetch('http://localhost:4000/products/template');
        if (!response.ok) throw new Error('Failed to download template');
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'product_template.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to download template');
    }
});

document.getElementById('exportExcelBtn').addEventListener('click', async () => {
    try {
        const response = await fetch('http://localhost:4000/products/export');
        if (!response.ok) throw new Error('Failed to export products');
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'products.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to export products');
    }
});

document.getElementById('importExcelBtn').addEventListener('click', () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx';
    input.onchange = async (e) => {
        const file = e.target.files?.[0];
        if (file) {
            const formData = new FormData();
            formData.append('file', file);
            try {
                const response = await fetch('http://localhost:4000/products/bulk', {
                    method: 'POST',
                    body: formData,
                });
                if (!response.ok) throw new Error('Failed to import products');
                const result = await response.json();
                alert(`Successfully imported ${result.success.length} products. ${result.errors.length} errors.`);
                await fetchProducts();
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to import products');
            }
        }
    };
    input.click();
});

// Initialize
async function init() {
    try {
        await Promise.all([
            fetchEmployees(),
            fetchProducts(),
            fetchCategories(),
            fetchSettings()
        ]);
    } catch (error) {
        console.error('Error initializing:', error);
        alert('Failed to initialize settings page');
    }
}

init();
