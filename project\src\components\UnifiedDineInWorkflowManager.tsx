import React, { useState, useEffect, useCallback } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Table, Employee, Order } from '../types';
import { 
  Users, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  MapPin, 
  User,
  ShoppingCart,
  CreditCard,
  ArrowRight,
  Lock,
  Unlock
} from 'lucide-react';
import TableSelectionModal from './TableSelectionModal';
import EmployeeVerificationModal from './EmployeeVerificationModal';
import UnifiedProductGrid from './UnifiedProductGrid';
import UnifiedOrderPanel from './UnifiedOrderPanel';

interface WorkflowState {
  currentStep: 'table_selection' | 'employee_confirmation' | 'ordering' | 'payment' | 'completed';
  selectedTable: Table | null;
  assignedEmployee: Employee | null;
  currentOrder: Order | null;
  sessionId: string;
  isLocked: boolean;
  lockExpiry: Date | null;
}

interface UnifiedDineInWorkflowManagerProps {
  onWorkflowComplete: (orderData: any) => void;
  onCancel: () => void;
}

const UnifiedDineInWorkflowManager: React.FC<UnifiedDineInWorkflowManagerProps> = ({
  onWorkflowComplete,
  onCancel
}) => {
  const { state, apiCall } = useEnhancedAppContext();
  const [workflowState, setWorkflowState] = useState<WorkflowState>({
    currentStep: 'table_selection',
    selectedTable: null,
    assignedEmployee: state.currentEmployee,
    currentOrder: null,
    sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    isLocked: false,
    lockExpiry: null
  });

  const [availableTables, setAvailableTables] = useState<Table[]>([]);
  const [showTableSelection, setShowTableSelection] = useState(true);
  const [showEmployeeVerification, setShowEmployeeVerification] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available tables on component mount
  useEffect(() => {
    fetchAvailableTables();
  }, []);

  // WebSocket listeners for real-time updates
  useEffect(() => {
    if (state.socket) {
      // Listen for table status changes
      state.socket.on('table:status:changed', handleTableStatusChange);
      state.socket.on('table:assignment:created', handleTableAssignmentCreated);
      state.socket.on('table:concurrent:blocked', handleConcurrentBlocked);

      return () => {
        state.socket.off('table:status:changed');
        state.socket.off('table:assignment:created');
        state.socket.off('table:concurrent:blocked');
      };
    }
  }, [state.socket]);

  const fetchAvailableTables = async () => {
    try {
      setLoading(true);
      const response = await apiCall('/api/floor/tables?status=available');
      if (response.ok) {
        const tables = await response.json();
        setAvailableTables(tables);
      } else {
        throw new Error('Failed to fetch available tables');
      }
    } catch (error) {
      console.error('Error fetching available tables:', error);
      setError('Failed to load available tables. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleTableStatusChange = useCallback((data: { tableId: string; status: string; employeeId: string }) => {
    // Update available tables list when table status changes
    setAvailableTables(prev => 
      prev.map(table => 
        table.id === data.tableId 
          ? { ...table, status: data.status as any }
          : table
      ).filter(table => table.status === 'available')
    );
  }, []);

  const handleTableAssignmentCreated = useCallback((data: any) => {
    // Remove assigned table from available list
    setAvailableTables(prev => 
      prev.filter(table => table.id !== data.tableId)
    );
  }, []);

  const handleConcurrentBlocked = useCallback((data: { tableId: string; blockingEmployee: string }) => {
    if (workflowState.selectedTable?.id === data.tableId) {
      setError(`Table is currently being assigned by ${data.blockingEmployee}. Please select another table.`);
      setWorkflowState(prev => ({ ...prev, selectedTable: null }));
    }
  }, [workflowState.selectedTable]);

  const handleTableSelect = async (table: Table) => {
    try {
      setLoading(true);
      setError(null);

      // Check for concurrent access
      const concurrencyCheck = await apiCall(`/api/floor/tables/${table.id}/check-availability`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId: workflowState.sessionId
        })
      });

      if (!concurrencyCheck.ok) {
        const errorData = await concurrencyCheck.json();
        throw new Error(errorData.message || 'Table is not available');
      }

      // Create temporary lock on table
      const lockResponse = await apiCall(`/api/floor/tables/${table.id}/lock`, {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          sessionId: workflowState.sessionId,
          duration: 30000 // 30 seconds
        })
      });

      if (lockResponse.ok) {
        const lockData = await lockResponse.json();
        setWorkflowState(prev => ({
          ...prev,
          selectedTable: table,
          isLocked: true,
          lockExpiry: new Date(lockData.expiry)
        }));
        setShowTableSelection(false);
        setShowEmployeeVerification(true);
      }
    } catch (error) {
      console.error('Error selecting table:', error);
      setError(error instanceof Error ? error.message : 'Failed to select table');
    } finally {
      setLoading(false);
    }
  };

  const handleEmployeeConfirmation = async (confirmationPin: string) => {
    try {
      setLoading(true);
      setError(null);

      // Verify employee PIN
      const verificationResponse = await apiCall('/api/employees/verify-pin', {
        method: 'POST',
        body: JSON.stringify({
          employeeId: state.currentEmployee?.id,
          pin: confirmationPin
        })
      });

      if (!verificationResponse.ok) {
        throw new Error('Invalid PIN. Please try again.');
      }

      // Create table assignment
      const assignmentResponse = await apiCall('/api/floor/table-assignments', {
        method: 'POST',
        body: JSON.stringify({
          tableId: workflowState.selectedTable?.id,
          employeeId: state.currentEmployee?.id,
          sessionId: workflowState.sessionId,
          assignmentType: 'dine_in',
          employeeConfirmationPin: confirmationPin
        })
      });

      if (assignmentResponse.ok) {
        const assignmentData = await assignmentResponse.json();
        
        // Broadcast table assignment to other terminals
        if (state.socket) {
          state.socket.emit('table:assignment:created', {
            tableId: workflowState.selectedTable?.id,
            employeeId: state.currentEmployee?.id,
            employeeName: state.currentEmployee?.name,
            assignmentId: assignmentData.id,
            sessionId: workflowState.sessionId
          });
        }

        setWorkflowState(prev => ({
          ...prev,
          currentStep: 'ordering',
          isLocked: false,
          lockExpiry: null
        }));
        setShowEmployeeVerification(false);
      }
    } catch (error) {
      console.error('Error confirming employee:', error);
      setError(error instanceof Error ? error.message : 'Failed to confirm employee');
    } finally {
      setLoading(false);
    }
  };

  const handleOrderComplete = async (orderData: any) => {
    try {
      setLoading(true);

      // Update table status to occupied
      await apiCall(`/api/floor/tables/${workflowState.selectedTable?.id}/status`, {
        method: 'PUT',
        body: JSON.stringify({
          status: 'occupied',
          substatus: 'eating',
          currentOrderId: orderData.id,
          orderTotal: orderData.total,
          orderItems: orderData.items?.length || 0
        })
      });

      // Broadcast order completion
      if (state.socket) {
        state.socket.emit('order:created', {
          orderId: orderData.id,
          tableId: workflowState.selectedTable?.id,
          employeeId: state.currentEmployee?.id,
          orderData
        });
      }

      setWorkflowState(prev => ({
        ...prev,
        currentStep: 'completed',
        currentOrder: orderData
      }));

      // Call completion callback
      onWorkflowComplete(orderData);
    } catch (error) {
      console.error('Error completing order:', error);
      setError('Failed to complete order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    try {
      // Release table lock if exists
      if (workflowState.isLocked && workflowState.selectedTable) {
        await apiCall(`/api/floor/tables/${workflowState.selectedTable.id}/unlock`, {
          method: 'POST',
          body: JSON.stringify({
            sessionId: workflowState.sessionId
          })
        });
      }

      onCancel();
    } catch (error) {
      console.error('Error canceling workflow:', error);
      onCancel(); // Still cancel even if unlock fails
    }
  };

  const renderWorkflowProgress = () => {
    const steps = [
      { key: 'table_selection', label: 'Select Table', icon: MapPin },
      { key: 'employee_confirmation', label: 'Confirm Employee', icon: User },
      { key: 'ordering', label: 'Take Order', icon: ShoppingCart },
      { key: 'payment', label: 'Process Payment', icon: CreditCard },
      { key: 'completed', label: 'Complete', icon: CheckCircle }
    ];

    const currentStepIndex = steps.findIndex(step => step.key === workflowState.currentStep);

    return (
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStepIndex;
            const isCompleted = index < currentStepIndex;
            const isUpcoming = index > currentStepIndex;

            return (
              <React.Fragment key={step.key}>
                <div className={`flex items-center space-x-2 ${
                  isActive ? 'text-blue-600' : 
                  isCompleted ? 'text-green-600' : 
                  'text-gray-400'
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    isActive ? 'bg-blue-100 border-2 border-blue-600' :
                    isCompleted ? 'bg-green-100 border-2 border-green-600' :
                    'bg-gray-100 border-2 border-gray-300'
                  }`}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <span className={`text-sm font-medium ${
                    isActive ? 'text-blue-600' :
                    isCompleted ? 'text-green-600' :
                    'text-gray-500'
                  }`}>
                    {step.label}
                  </span>
                </div>
                {index < steps.length - 1 && (
                  <ArrowRight className={`w-4 h-4 ${
                    index < currentStepIndex ? 'text-green-600' : 'text-gray-300'
                  }`} />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </div>
    );
  };

  const renderCurrentStep = () => {
    switch (workflowState.currentStep) {
      case 'table_selection':
        return (
          <TableSelectionModal
            availableTables={availableTables}
            currentEmployee={state.currentEmployee!}
            onTableSelect={handleTableSelect}
            onCancel={handleCancel}
            loading={loading}
            error={error}
          />
        );

      case 'employee_confirmation':
        return (
          <EmployeeVerificationModal
            selectedTable={workflowState.selectedTable!}
            currentEmployee={state.currentEmployee!}
            onConfirm={handleEmployeeConfirmation}
            onCancel={handleCancel}
            loading={loading}
            error={error}
            lockExpiry={workflowState.lockExpiry}
          />
        );

      case 'ordering':
        return (
          <div className="flex h-full">
            <div className="flex-1">
              <UnifiedProductGrid />
            </div>
            <div className="w-96 border-l border-gray-200">
              <UnifiedOrderPanel 
                selectedTable={workflowState.selectedTable}
                onOrderComplete={handleOrderComplete}
              />
            </div>
          </div>
        );

      case 'completed':
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center p-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Order Completed Successfully!</h3>
              <p className="text-gray-600 mb-4">
                Table {workflowState.selectedTable?.number} order has been processed.
              </p>
              <button
                onClick={onCancel}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Return to POS
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {renderWorkflowProgress()}
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-4 mt-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex-1 overflow-hidden">
        {renderCurrentStep()}
      </div>

      {workflowState.isLocked && workflowState.lockExpiry && (
        <div className="bg-yellow-50 border-t border-yellow-200 p-3">
          <div className="flex items-center justify-center space-x-2 text-yellow-800">
            <Lock className="w-4 h-4" />
            <span className="text-sm font-medium">
              Table {workflowState.selectedTable?.number} is temporarily locked for your selection
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedDineInWorkflowManager;
