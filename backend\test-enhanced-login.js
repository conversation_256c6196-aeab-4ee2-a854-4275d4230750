const axios = require('axios');
const db = require('./db/pool');
const queries = require('./db/queries');

const baseURL = 'http://localhost:4000';

async function testEnhancedLogin() {
  console.log('🔍 Testing Enhanced Interface Login...');
  console.log('='.repeat(50));
  
  try {
    // Test 1: Login with 6-digit PIN
    console.log('Testing 6-digit PIN login...');
    const response = await axios.post(`${baseURL}/api/auth/login`, {
      pin: '123456',
      tenant_slug: 'demo-restaurant'
    }, {
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500; // Accept all status codes below 500
      }
    });
    
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log('✅ Login successful!');
      console.log('Token:', response.data.token ? 'Present' : 'Missing');
      console.log('Employee:', response.data.employee ? response.data.employee.name : 'Missing');
      console.log('Tenant:', response.data.tenant ? response.data.tenant.name : 'Missing');
    } else {
      console.log('❌ Login failed with status:', response.status);
      console.log('Error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Response Status:', error.response.status);
      console.log('Response Data:', error.response.data);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  try {
    // Test 2: Login without tenant_slug
    console.log('Testing login without tenant_slug...');
    const response2 = await axios.post(`${baseURL}/api/auth/login`, {
      pin: '123456'
    }, {
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    console.log('Response Status:', response2.status);
    console.log('Response Data:', JSON.stringify(response2.data, null, 2));
    
    if (response2.status === 200) {
      console.log('✅ Login without tenant_slug successful!');
    } else {
      console.log('❌ Login without tenant_slug failed');
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Response Status:', error.response.status);
      console.log('Response Data:', error.response.data);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  
  try {
    // Test 3: Check if user exists in database using prepared statement
    console.log('Testing database query...');
    
    const employee = await queries.getEmployeeByPin('123456', 'demo-restaurant');
    
    if (employee) {
      console.log('Database query result:', employee);
      
      // Test caching by querying again
      console.log('\nTesting cache (second query should be faster)...');
      console.time('First query');
      await queries.getEmployeeByPin('123456', 'demo-restaurant');
      console.timeEnd('First query');
      
      console.time('Second query (cached)');
      await queries.getEmployeeByPin('123456', 'demo-restaurant');
      console.timeEnd('Second query (cached)');
    } else {
      console.log('No employee found with the given PIN and tenant');
    }
    
  } catch (error) {
    console.error('❌ Database query failed:', error.message);
  }
}

testEnhancedLogin().catch(console.error);
