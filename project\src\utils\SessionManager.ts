// Session Management Utility for Enhanced Login System
// Handles token validation, session persistence, and role-based access control

interface UserSession {
  token: string;
  employee: {
    id: number;
    name: string;
    role: string;
    email?: string;
    permissions?: string[];
  };
  tenant: {
    id: number;
    name: string;
    slug: string;
    business_name?: string;
  };
  location?: {
    id: string;
    name: string;
  };
  expiresAt: number;
  adminAccess?: boolean;
}

class SessionManager {
  private static instance: SessionManager;
  private session: UserSession | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.loadSessionFromStorage();
    this.startSessionMonitoring();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // Load session from localStorage
  private loadSessionFromStorage(): void {
    try {
      const token = localStorage.getItem('authToken');
      const employee = localStorage.getItem('currentEmployee');
      const tenant = localStorage.getItem('currentTenant');
      const location = localStorage.getItem('currentLocation');
      const adminAccess = localStorage.getItem('adminAccess') === 'true';

      if (token && employee && tenant) {
        this.session = {
          token,
          employee: JSON.parse(employee),
          tenant: JSON.parse(tenant),
          location: location ? JSON.parse(location) : undefined,
          expiresAt: this.getTokenExpiration(token),
          adminAccess
        };

        console.log('✅ Session loaded from storage:', {
          user: this.session.employee.name,
          role: this.session.employee.role,
          tenant: this.session.tenant.business_name || this.session.tenant.name,
          adminAccess: this.session.adminAccess
        });
      }
    } catch (error) {
      console.error('❌ Error loading session from storage:', error);
      this.clearSession();
    }
  }

  // Save session to localStorage
  private saveSessionToStorage(): void {
    if (!this.session) return;

    try {
      localStorage.setItem('authToken', this.session.token);
      localStorage.setItem('currentEmployee', JSON.stringify(this.session.employee));
      localStorage.setItem('currentTenant', JSON.stringify(this.session.tenant));
      
      if (this.session.location) {
        localStorage.setItem('currentLocation', JSON.stringify(this.session.location));
      }
      
      if (this.session.adminAccess) {
        localStorage.setItem('adminAccess', 'true');
      } else {
        localStorage.removeItem('adminAccess');
      }

      console.log('✅ Session saved to storage');
    } catch (error) {
      console.error('❌ Error saving session to storage:', error);
    }
  }

  // Extract token expiration from JWT
  private getTokenExpiration(token: string): number {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000; // Convert to milliseconds
    } catch (error) {
      console.error('❌ Error parsing token expiration:', error);
      return Date.now() + (24 * 60 * 60 * 1000); // Default to 24 hours
    }
  }

  // Start session monitoring for auto-refresh and expiration
  private startSessionMonitoring(): void {
    // Check session every 5 minutes
    this.refreshTimer = setInterval(() => {
      this.checkSessionValidity();
    }, 5 * 60 * 1000);
  }

  // Check if session is still valid
  private async checkSessionValidity(): Promise<boolean> {
    if (!this.session) return false;

    // Check if token is expired
    if (Date.now() >= this.session.expiresAt) {
      console.log('⏰ Session expired');
      this.clearSession();
      return false;
    }

    // Verify token with backend
    try {
      const response = await fetch('http://localhost:4000/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${this.session.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log('✅ Session verified with backend');
        return true;
      } else {
        console.log('❌ Session verification failed');
        this.clearSession();
        return false;
      }
    } catch (error) {
      console.error('❌ Session verification error:', error);
      return false; // Don't clear session on network errors
    }
  }

  // Create new session
  public createSession(sessionData: {
    token: string;
    employee: any;
    tenant: any;
    location?: any;
    adminAccess?: boolean;
  }): void {
    this.session = {
      token: sessionData.token,
      employee: sessionData.employee,
      tenant: sessionData.tenant,
      location: sessionData.location,
      expiresAt: this.getTokenExpiration(sessionData.token),
      adminAccess: sessionData.adminAccess || false
    };

    this.saveSessionToStorage();
    console.log('✅ New session created:', {
      user: this.session.employee.name,
      role: this.session.employee.role,
      tenant: this.session.tenant.business_name || this.session.tenant.name
    });
  }

  // Get current session
  public getSession(): UserSession | null {
    return this.session;
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return this.session !== null && Date.now() < this.session.expiresAt;
  }

  // Check if user has specific role
  public hasRole(role: string): boolean {
    return this.session?.employee.role === role;
  }

  // Check if user has any of the specified roles
  public hasAnyRole(roles: string[]): boolean {
    return this.session ? roles.includes(this.session.employee.role) : false;
  }

  // Check if user has admin access
  public hasAdminAccess(): boolean {
    return this.session?.adminAccess === true || this.hasRole('super_admin');
  }

  // Check if user has permission
  public hasPermission(permission: string): boolean {
    return this.session?.employee.permissions?.includes(permission) || false;
  }

  // Get current tenant
  public getCurrentTenant(): UserSession['tenant'] | null {
    return this.session?.tenant || null;
  }

  // Get current employee
  public getCurrentEmployee(): UserSession['employee'] | null {
    return this.session?.employee || null;
  }

  // Get auth token
  public getAuthToken(): string | null {
    return this.session?.token || null;
  }

  // Clear session
  public clearSession(): void {
    this.session = null;
    
    // Clear localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentEmployee');
    localStorage.removeItem('currentTenant');
    localStorage.removeItem('currentLocation');
    localStorage.removeItem('adminAccess');
    localStorage.removeItem('tenantSlug');

    console.log('✅ Session cleared');
  }

  // Logout
  public logout(): void {
    this.clearSession();
    
    // Clear refresh timer
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }

    console.log('✅ User logged out');
  }

  // Refresh session (verify with backend)
  public async refreshSession(): Promise<boolean> {
    return await this.checkSessionValidity();
  }

  // Check tenant isolation (ensure user can only access their tenant data)
  public validateTenantAccess(tenantId: number | string): boolean {
    if (!this.session) return false;
    
    // Super admins can access any tenant
    if (this.hasRole('super_admin')) return true;
    
    // Regular users can only access their own tenant
    return this.session.tenant.id.toString() === tenantId.toString();
  }

  // Get authorization headers for API calls
  public getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.session?.token) {
      headers['Authorization'] = `Bearer ${this.session.token}`;
    }

    return headers;
  }

  // Auto-login check (for page refresh)
  public async autoLogin(): Promise<boolean> {
    if (this.isAuthenticated()) {
      const isValid = await this.checkSessionValidity();
      if (isValid) {
        console.log('✅ Auto-login successful');
        return true;
      }
    }
    
    console.log('❌ Auto-login failed');
    return false;
  }
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance();

// Export types
export type { UserSession };

// Utility functions
export const isAuthenticated = () => sessionManager.isAuthenticated();
export const hasRole = (role: string) => sessionManager.hasRole(role);
export const hasAnyRole = (roles: string[]) => sessionManager.hasAnyRole(roles);
export const hasAdminAccess = () => sessionManager.hasAdminAccess();
export const getCurrentTenant = () => sessionManager.getCurrentTenant();
export const getCurrentEmployee = () => sessionManager.getCurrentEmployee();
export const getAuthToken = () => sessionManager.getAuthToken();
export const getAuthHeaders = () => sessionManager.getAuthHeaders();
export const logout = () => sessionManager.logout();
export const validateTenantAccess = (tenantId: number | string) => sessionManager.validateTenantAccess(tenantId);
