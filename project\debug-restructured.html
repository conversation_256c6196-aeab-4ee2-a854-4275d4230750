<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Restructured POS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f8fafc;
        }
        .debug-panel {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }
        .success { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        .error { background: #fef2f2; color: #991b1b; border: 1px solid #fecaca; }
        .warning { background: #fefce8; color: #a16207; border: 1px solid #fef3c7; }
        .info { background: #eff6ff; color: #1e40af; border: 1px solid #dbeafe; }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        button:hover { background: #2563eb; }
        
        code {
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        
        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h1>🔧 Debug Restructured POS System</h1>
        <p>This page helps debug why the restructured interface might not be showing.</p>
        
        <div id="status-container">
            <!-- Status will be populated by JavaScript -->
        </div>
        
        <div>
            <button onclick="enableRestructured()">Enable Restructured Mode</button>
            <button onclick="testPOS()">Test POS System</button>
            <button onclick="clearStorage()">Clear All Storage</button>
            <button onclick="refreshDebug()">Refresh Debug Info</button>
        </div>
    </div>
    
    <div class="debug-panel">
        <h2>📊 System Information</h2>
        <div id="system-info">
            <!-- Will be populated by JavaScript -->
        </div>
    </div>
    
    <div class="debug-panel">
        <h2>📝 Debug Log</h2>
        <div id="debug-log" class="log">
            Initializing debug session...\n
        </div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let logContainer;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            
            if (!logContainer) {
                logContainer = document.getElementById('debug-log');
            }
            
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Also log to browser console
            console.log(`[DEBUG] ${message}`);
        }
        
        function updateStatus() {
            const container = document.getElementById('status-container');
            const isRestructuredEnabled = localStorage.getItem('useRestructuredPOS') === 'true';
            const hasIndustryParam = window.location.search.includes('industry=true');
            const hasRestructuredParam = window.location.search.includes('restructured=true');
            
            let statusHTML = '';
            
            // Check localStorage flag
            if (isRestructuredEnabled) {
                statusHTML += '<div class="status success">✅ localStorage flag: ENABLED</div>';
                log('localStorage flag is enabled');
            } else {
                statusHTML += '<div class="status error">❌ localStorage flag: DISABLED</div>';
                log('localStorage flag is disabled');
            }
            
            // Check URL parameters
            if (hasIndustryParam) {
                statusHTML += '<div class="status success">✅ Industry parameter: PRESENT</div>';
                log('Industry parameter found in URL');
            } else {
                statusHTML += '<div class="status warning">⚠️ Industry parameter: MISSING</div>';
                log('Industry parameter missing from URL');
            }
            
            if (hasRestructuredParam) {
                statusHTML += '<div class="status success">✅ Restructured parameter: PRESENT</div>';
                log('Restructured parameter found in URL');
            } else {
                statusHTML += '<div class="status info">ℹ️ Restructured parameter: MISSING (optional)</div>';
                log('Restructured parameter missing from URL (this is optional)');
            }
            
            // Overall status
            const shouldShowRestructured = isRestructuredEnabled || hasRestructuredParam;
            if (shouldShowRestructured) {
                statusHTML += '<div class="status success">🚀 RESTRUCTURED MODE SHOULD BE ACTIVE</div>';
                log('Restructured mode should be active based on flags');
            } else {
                statusHTML += '<div class="status error">❌ RESTRUCTURED MODE SHOULD BE INACTIVE</div>';
                log('Restructured mode should be inactive');
            }
            
            container.innerHTML = statusHTML;
        }
        
        function updateSystemInfo() {
            const container = document.getElementById('system-info');
            const info = {
                'Current URL': window.location.href,
                'URL Search': window.location.search || 'none',
                'localStorage useRestructuredPOS': localStorage.getItem('useRestructuredPOS') || 'not set',
                'localStorage useIndustryStandardPOS': localStorage.getItem('useIndustryStandardPOS') || 'not set',
                'User Agent': navigator.userAgent,
                'Viewport': `${window.innerWidth}x${window.innerHeight}`,
                'Local Storage Keys': Object.keys(localStorage).join(', ') || 'none'
            };
            
            let infoHTML = '<table style="width: 100%; border-collapse: collapse;">';
            for (const [key, value] of Object.entries(info)) {
                infoHTML += `
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                        <td style="padding: 8px; font-weight: 600; width: 200px;">${key}:</td>
                        <td style="padding: 8px; font-family: monospace; word-break: break-all;">${value}</td>
                    </tr>
                `;
            }
            infoHTML += '</table>';
            
            container.innerHTML = infoHTML;
            log('System information updated');
        }
        
        function enableRestructured() {
            localStorage.setItem('useRestructuredPOS', 'true');
            log('Enabled restructured mode via localStorage');
            updateStatus();
            updateSystemInfo();
            alert('✅ Restructured mode enabled! Now try opening the POS system.');
        }
        
        function testPOS() {
            log('Opening POS system in new tab...');
            const url = 'http://localhost:5173/?industry=true&restructured=true';
            window.open(url, '_blank');
            log(`Opened: ${url}`);
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            log('Cleared all localStorage and sessionStorage');
            updateStatus();
            updateSystemInfo();
            alert('🗑️ All storage cleared!');
        }
        
        function clearLog() {
            document.getElementById('debug-log').textContent = 'Log cleared...\n';
            log('Debug log cleared');
        }
        
        function refreshDebug() {
            log('Refreshing debug information...');
            updateStatus();
            updateSystemInfo();
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('debug-log');
            log('Debug page loaded');
            updateStatus();
            updateSystemInfo();
            
            // Check if we're on the POS page
            if (window.location.pathname.includes('industry') || window.location.search.includes('industry')) {
                log('Detected POS page - checking for restructured interface...');
                
                // Check if restructured interface is actually loaded
                setTimeout(() => {
                    const restructuredElements = document.querySelectorAll('[data-restructured], .restructured-pos, #restructured-indicator');
                    if (restructuredElements.length > 0) {
                        log('✅ Restructured interface elements detected!');
                    } else {
                        log('❌ No restructured interface elements found');
                        log('This might indicate the restructured components are not loading properly');
                    }
                }, 2000);
            }
        });
        
        // Monitor localStorage changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'useRestructuredPOS') {
                log(`localStorage changed: ${e.key} = ${e.newValue}`);
                updateStatus();
            }
        });
        
        // Monitor URL changes
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                log(`URL changed to: ${url}`);
                updateStatus();
                updateSystemInfo();
            }
        }).observe(document, {subtree: true, childList: true});
        
        log('Debug system initialized');
    </script>
</body>
</html>
