describe('Localization and Internationalization Tests', () => {
  before(() => {
    cy.visit('/');
    cy.get('input[type="text"]', { timeout: 10000 }).should('be.visible').type('1234'); // Login PIN
    cy.get('button', { timeout: 10000 }).contains('Login').click();
    cy.contains('POS', { timeout: 10000 }).should('be.visible');
  });

  it('should display UI text in selected language', () => {
    // Assuming language selector exists
    cy.get('select#language-selector').select('es'); // Spanish
    cy.contains(/carrito|productos/i).should('be.visible');

    cy.get('select#language-selector').select('en'); // English
    cy.contains(/cart|products/i).should('be.visible');
  });

  it('should display prices in selected currency format', () => {
    // Assuming currency selector exists
    cy.get('select#currency-selector').select('EUR');
    cy.get('.product-price').first().should('contain.text', '€');

    cy.get('select#currency-selector').select('USD');
    cy.get('.product-price').first().should('contain.text', '$');
  });
});
