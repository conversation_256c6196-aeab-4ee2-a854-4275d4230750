const fetch = require('node-fetch');

async function testLogin(pin, tenantSlug = null) {
  try {
    console.log(`🔐 Testing login with PIN: ${pin}, Tenant: ${tenantSlug || 'auto-detect'}`);
    
    const response = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: pin,
        tenant_slug: tenantSlug
      })
    });

    console.log(`📡 Response status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Login successful!`);
      console.log(`👤 Employee: ${result.employee.name} (${result.employee.role})`);
      console.log(`🏢 Tenant: ${result.tenant.name}`);
      console.log(`📍 Location: ${result.location.name}`);
      console.log(`🔑 Token: ${result.token ? 'Received' : 'Missing'}`);
      return result;
    } else {
      const errorText = await response.text();
      console.log(`❌ Login failed: ${errorText}`);
      return null;
    }
  } catch (error) {
    console.error(`💥 Login error: ${error.message}`);
    return null;
  }
}

async function testTenantListing(token) {
  try {
    console.log(`📋 Testing tenant listing with token...`);
    
    const response = await fetch('http://localhost:4000/api/tenants', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`📡 Tenants response: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const tenants = await response.json();
      console.log(`✅ Fetched ${tenants.length} tenants`);
      tenants.forEach((tenant, index) => {
        console.log(`  ${index + 1}. ${tenant.business_name || tenant.name} (${tenant.email}) - ${tenant.status}`);
      });
      return tenants;
    } else {
      const errorText = await response.text();
      console.log(`❌ Failed to fetch tenants: ${errorText}`);
      return [];
    }
  } catch (error) {
    console.error(`💥 Tenant listing error: ${error.message}`);
    return [];
  }
}

async function runLoginTests() {
  console.log('🚀 Starting comprehensive login tests...');
  console.log('='.repeat(60));
  
  // Test all known PINs
  const testPins = [
    { pin: '123456', description: 'Super Admin PIN (should work for multiple employees)' },
    { pin: '888888', description: 'Main Admin PIN' },
    { pin: '999999', description: 'Enhanced Admin PIN' },
    { pin: '234567', description: 'Main Employee PIN' },
    { pin: '567890', description: 'Manager/Employee PIN (mock fallback)' }
  ];
  
  for (const test of testPins) {
    console.log(`\n📋 Test: ${test.description}`);
    console.log('-'.repeat(40));
    
    // Test with demo-restaurant tenant
    const result = await testLogin(test.pin, 'demo-restaurant');
    
    if (result && result.employee.role === 'super_admin') {
      console.log('\n🔑 Testing Super Admin tenant listing...');
      await testTenantListing(result.token);
    }
    
    console.log('');
  }
  
  // Test auto-detect (no tenant specified)
  console.log(`\n📋 Test: Auto-detect tenant`);
  console.log('-'.repeat(40));
  const autoResult = await testLogin('123456');
  
  if (autoResult && autoResult.employee.role === 'super_admin') {
    console.log('\n🔑 Testing Super Admin tenant listing (auto-detect)...');
    await testTenantListing(autoResult.token);
  }
  
  console.log('\n🏁 Login tests completed!');
  console.log('='.repeat(60));
  
  console.log('\n📋 SUMMARY:');
  console.log('✅ Working PINs for demo-restaurant tenant:');
  console.log('  - 123456: Super Admin, Enhanced Employee, Test Admin');
  console.log('  - 888888: Main Admin (super_admin)');
  console.log('  - 999999: Enhanced Admin (super_admin)');
  console.log('  - 234567: Main Employee (employee)');
  console.log('\n🎯 For POS System Access: Use PIN 123456, 888888, 999999, or 234567');
  console.log('🎯 For Super Admin Dashboard: Use PIN 123456, 888888, or 999999');
}

runLoginTests();
