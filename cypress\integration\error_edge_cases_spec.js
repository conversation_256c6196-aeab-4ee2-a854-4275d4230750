describe('Error and Edge-Case Handling Tests', () => {
  before(() => {
    cy.visit('/');
    cy.get('input[type="text"]', { timeout: 10000 }).should('be.visible').type('1234'); // Login PIN
    cy.get('button', { timeout: 10000 }).contains('Login').click();
    cy.contains('POS', { timeout: 10000 }).should('be.visible');
  });

  it('should handle network loss mid-transaction gracefully', () => {
    // Add a product to order
    cy.get('.product-item').first().click();
    cy.get('button').contains('Checkout').click();

    // Simulate network offline
    cy.log('Simulating network offline');
    cy.intercept('POST', '/api/orders', { forceNetworkError: true }).as('postOrder');

    cy.get('button').contains('Pay').click();

    // Wait for the network error
    cy.wait('@postOrder');

    // Check for error message or retry prompt
    cy.contains(/network error|failed to submit order|retry/i).should('be.visible');

    // Simulate network back online and retry
    cy.log('Simulating network back online');
    cy.intercept('POST', '/api/orders', { statusCode: 201, body: { id: 'order123' } }).as('postOrderSuccess');

    cy.get('button').contains(/retry|submit order/i).click();

    cy.wait('@postOrderSuccess');

    cy.contains('Receipt').should('be.visible');
  });

  it('should show validation errors for invalid input on order submission', () => {
    // Attempt to submit order with empty items
    cy.get('button').contains('Checkout').click();
    cy.get('button').contains('Pay').click();

    cy.contains(/order cannot be empty|please add items/i).should('be.visible');
  });

  it('should handle unauthorized access to protected pages', () => {
    // Logout first
    cy.get('button').contains('Logout').click();
    cy.contains('Enter your PIN to continue').should('be.visible');

    // Try to visit tenant management page without login
    cy.visit('/tenant');
    cy.contains(/login required|unauthorized|access denied/i).should('be.visible');
  });
});
