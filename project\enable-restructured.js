// Enable Restructured POS Mode
console.log('🚀 Enabling Restructured POS Mode...');

// Set localStorage flag
localStorage.setItem('useRestructuredPOS', 'true');
console.log('✅ localStorage flag set:', localStorage.getItem('useRestructuredPOS'));

// Check current URL
console.log('📍 Current URL:', window.location.href);

// Check if we need to reload or redirect
const currentUrl = new URL(window.location.href);
const hasIndustryParam = currentUrl.searchParams.has('industry');
const hasRestructuredParam = currentUrl.searchParams.has('restructured');

if (!hasIndustryParam) {
    console.log('⚠️ Missing industry parameter, adding it...');
    currentUrl.searchParams.set('industry', 'true');
}

if (!hasRestructuredParam) {
    console.log('⚠️ Missing restructured parameter, adding it...');
    currentUrl.searchParams.set('restructured', 'true');
}

// If we added parameters, redirect
if (!hasIndustryParam || !hasRestructuredParam) {
    console.log('🔄 Redirecting to:', currentUrl.toString());
    window.location.href = currentUrl.toString();
} else {
    console.log('✅ All parameters present, reloading page...');
    window.location.reload();
}
