-- Phase 3: Database Schema Fixes and Optimizations
-- Addresses column mismatches, precision issues, and missing extensions

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Fix 1: Standardize amount/total_amount columns across tables
-- Update orders table to use consistent column naming
DO $$
BEGIN
    -- Check if 'amount' column exists and 'total_amount' doesn't
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'orders' AND column_name = 'amount')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'orders' AND column_name = 'total_amount') THEN
        ALTER TABLE orders RENAME COLUMN amount TO total_amount;
    END IF;
    
    -- Check if 'total' column exists and 'total_amount' doesn't
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'orders' AND column_name = 'total')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'orders' AND column_name = 'total_amount') THEN
        ALTER TABLE orders RENAME COLUMN total TO total_amount;
    END IF;
END $$;

-- Fix 2: Update transactions table column naming
DO $$
BEGIN
    -- Standardize transactions table
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'transactions' AND column_name = 'amount')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'transactions' AND column_name = 'total_amount') THEN
        ALTER TABLE transactions RENAME COLUMN amount TO total_amount;
    END IF;
END $$;

-- Fix 3: Improve exchange rate precision to handle large values
-- Update global_exchange_rates table precision
DO $$
BEGIN
    -- Check if table exists before altering
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_name = 'global_exchange_rates') THEN
        
        -- Update exchange_rate column precision
        ALTER TABLE global_exchange_rates 
        ALTER COLUMN exchange_rate TYPE DECIMAL(20,10);
        
        -- Update bid_rate and ask_rate precision
        ALTER TABLE global_exchange_rates 
        ALTER COLUMN bid_rate TYPE DECIMAL(20,10);
        
        ALTER TABLE global_exchange_rates 
        ALTER COLUMN ask_rate TYPE DECIMAL(20,10);
        
        -- Update volatility precision
        ALTER TABLE global_exchange_rates 
        ALTER COLUMN volatility TYPE DECIMAL(8,6);
    END IF;
END $$;

-- Fix 4: Create missing tables if they don't exist
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID,
    table_id UUID,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'served', 'cancelled')),
    order_type VARCHAR(20) DEFAULT 'dine_in' CHECK (order_type IN ('dine_in', 'takeout', 'delivery', 'online')),
    items JSONB DEFAULT '[]',
    subtotal DECIMAL(12,4) DEFAULT 0,
    tax_amount DECIMAL(12,4) DEFAULT 0,
    discount_amount DECIMAL(12,4) DEFAULT 0,
    total_amount DECIMAL(12,4) NOT NULL CHECK (total_amount >= 0),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'refunded', 'failed')),
    payment_method VARCHAR(50),
    notes TEXT,
    special_instructions TEXT,
    estimated_ready_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) DEFAULT 'sale' CHECK (transaction_type IN ('sale', 'refund', 'void', 'adjustment')),
    payment_method VARCHAR(50) NOT NULL,
    total_amount DECIMAL(12,4) NOT NULL CHECK (total_amount >= 0),
    currency_code VARCHAR(3) DEFAULT 'CAD',
    exchange_rate DECIMAL(20,10) DEFAULT 1.0,
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')),
    processing_fee DECIMAL(10,4) DEFAULT 0,
    net_amount DECIMAL(12,4),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fix 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_tenant_status ON orders(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_total_amount ON orders(total_amount);
CREATE INDEX IF NOT EXISTS idx_transactions_tenant_status ON transactions(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_payment_method ON transactions(payment_method);
CREATE INDEX IF NOT EXISTS idx_global_exchange_rates_currencies ON global_exchange_rates(base_currency, target_currency);
CREATE INDEX IF NOT EXISTS idx_global_exchange_rates_updated ON global_exchange_rates(last_updated);

-- Fix 6: Update views to use correct column names
DROP VIEW IF EXISTS tenant_summary;
CREATE OR REPLACE VIEW tenant_summary AS
SELECT 
    t.id,
    t.name,
    t.slug,
    t.status,
    t.plan,
    t.locations,
    COUNT(DISTINCT u.id) as user_count,
    COALESCE(SUM(tr.total_amount), 0) as total_revenue,
    COUNT(DISTINCT tr.id) as transaction_count,
    COUNT(DISTINCT o.id) as order_count,
    AVG(o.total_amount) as avg_order_value,
    t.created_at,
    t.last_login
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id
LEFT JOIN transactions tr ON t.id = tr.tenant_id AND tr.status = 'completed'
LEFT JOIN orders o ON t.id = o.tenant_id AND o.status IN ('served', 'completed')
GROUP BY t.id, t.name, t.slug, t.status, t.plan, t.locations, t.created_at, t.last_login;

DROP VIEW IF EXISTS system_health;
CREATE OR REPLACE VIEW system_health AS
SELECT 
    COUNT(DISTINCT t.id) as total_tenants,
    COUNT(DISTINCT t.id) FILTER (WHERE t.status = 'active') as active_tenants,
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT u.id) FILTER (WHERE u.status = 'active') as active_users,
    COUNT(DISTINCT tr.id) as total_transactions,
    COALESCE(SUM(tr.total_amount), 0) as total_revenue,
    COUNT(DISTINCT o.id) as total_orders,
    AVG(o.total_amount) as avg_order_value,
    COUNT(DISTINCT sa.id) FILTER (WHERE sa.severity IN ('high', 'critical')) as critical_security_events
FROM tenants t
CROSS JOIN users u
CROSS JOIN transactions tr
CROSS JOIN orders o
LEFT JOIN security_audits sa ON true;

-- Fix 7: Create function to update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply timestamp triggers to relevant tables
DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fix 8: Grant proper permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO "BARPOS";
GRANT USAGE ON SCHEMA public TO "BARPOS";

-- Fix 9: Insert sample data if tables are empty
INSERT INTO tenants (name, slug, status, plan) 
VALUES 
    ('Demo Restaurant', 'demo-restaurant', 'active', 'pro'),
    ('Test Cafe', 'test-cafe', 'active', 'basic')
ON CONFLICT (slug) DO NOTHING;

-- Migration completed
SELECT 'Phase 3 Database Schema Fixes Applied Successfully' as status;
