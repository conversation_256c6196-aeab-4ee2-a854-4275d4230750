import React, { useState, useEffect } from 'react';
import {
  Users,
  DollarSign,
  Activity,
  AlertTriangle,
  TrendingUp,
  Server,
  Database,
  Settings,
  Search,
  Bell,
  Plus,
  Eye,
  ToggleLeft,
  ToggleRight,
  UserCog
} from 'lucide-react';
// import ComprehensiveUserManagement from './admin/ComprehensiveUserManagement';

interface DashboardData {
  tenant_overview: {
    total_tenants: number;
    active_tenants: number;
    trial_tenants: number;
    inactive_tenants: number;
  };
  system_metrics: {
    active_users_now: number;
    avg_request_latency: number;
    failed_syncs: number;
    api_errors: number;
    uptime_percentage: number;
  };
  revenue_summary: {
    total_collected: number;
    monthly_recurring_revenue: number;
    cancelled_subscriptions: number;
    growth_rate: number;
  };
  tenant_types: Array<{
    industry: string;
    count: number;
    percentage: number;
  }>;
}

interface Tenant {
  id: string;
  name: string;
  business_name: string;
  status: string;
  plan_type: string;
  features: Record<string, boolean>;
}

interface SuperAdminLandingPageProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const SuperAdminLandingPage: React.FC<SuperAdminLandingPageProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [dbConnectionStatus, setDbConnectionStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');

  useEffect(() => {
    checkDatabaseConnection();
    fetchDashboardData();
    fetchTenants();

    // Set up auto-refresh every 30 seconds for real-time data
    const refreshInterval = setInterval(() => {
      checkDatabaseConnection();
      fetchDashboardData();
      fetchTenants();
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, []);

  const checkDatabaseConnection = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        setDbConnectionStatus('disconnected');
        return;
      }

      const response = await fetch('/api/admin/health/database', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setDbConnectionStatus('connected');
        console.log('✅ Database connection healthy');
      } else {
        setDbConnectionStatus('disconnected');
        console.log('❌ Database connection failed');
      }
    } catch (error) {
      setDbConnectionStatus('disconnected');
      console.error('❌ Database health check failed:', error);
    }
  };

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch('/api/admin/metrics/system', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform the data to match the expected format
      const transformedData = {
        tenant_overview: {
          total_tenants: data.totalTenants || 0,
          active_tenants: data.activeTenants || 0,
          trial_tenants: 0,
          inactive_tenants: (data.totalTenants || 0) - (data.activeTenants || 0)
        },
        system_metrics: {
          active_users_now: data.activeUsers || 0,
          avg_request_latency: data.averageResponseTime || 0,
          failed_syncs: 0,
          api_errors: Math.floor(data.errorRate || 0),
          uptime_percentage: data.systemUptime || 0
        },
        revenue_summary: {
          total_collected: data.totalRevenue || 0,
          monthly_recurring_revenue: data.totalRevenue || 0,
          cancelled_subscriptions: 0,
          growth_rate: 0
        },
        tenant_types: []
      };

      setDashboardData(transformedData);
      console.log('✅ Dashboard data fetched successfully from database');
    } catch (error) {
      console.error('❌ Error fetching dashboard data:', error);
      // Don't set any fallback data - let the UI show loading or error state
    }
  };

  const fetchTenants = async () => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('No authentication token found');
        setLoading(false);
        return;
      }

      const response = await fetch('/api/admin/tenants', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setTenants(Array.isArray(data) ? data : []);
      setLoading(false);
      console.log('✅ Tenants data fetched successfully from database');
    } catch (error) {
      console.error('❌ Error fetching tenants:', error);
      setTenants([]); // Set empty array instead of keeping old data
      setLoading(false);
    }
  };

  const toggleTenantFeature = async (tenantId: string, feature: string, enabled: boolean) => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      const response = await fetch(`http://localhost:4000/api/admin/tenants/${tenantId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          features: { [feature]: enabled }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log(`✅ Updated tenant ${tenantId} feature ${feature} to ${enabled}`);
      fetchTenants(); // Refresh data from database
    } catch (error) {
      console.error('❌ Error updating tenant feature:', error);
    }
  };

  const StatCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode; color: string }> =
    ({ title, value, icon, color }) => (
    <div className={`rounded-lg shadow p-6 transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    }`}>
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
        <div className="ml-4">
          <p className={`text-sm font-medium transition-colors duration-300 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            {title}
          </p>
          <p className={`text-2xl font-semibold transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            {value}
          </p>
        </div>
      </div>
    </div>
  );

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Tenants"
          value={dashboardData?.tenant_overview.total_tenants || 0}
          icon={<Users className="h-6 w-6 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Active Users"
          value={dashboardData?.system_metrics.active_users_now || 0}
          icon={<Activity className="h-6 w-6 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title="Monthly Revenue"
          value={`$${dashboardData?.revenue_summary.monthly_recurring_revenue?.toLocaleString() || 0}`}
          icon={<DollarSign className="h-6 w-6 text-white" />}
          color="bg-yellow-500"
        />
        <StatCard
          title="System Uptime"
          value={`${dashboardData?.system_metrics.uptime_percentage || 0}%`}
          icon={<Server className="h-6 w-6 text-white" />}
          color="bg-purple-500"
        />
      </div>

      {/* System Health */}
      <div className={`rounded-lg shadow p-6 transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          System Health
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {dashboardData?.system_metrics.avg_request_latency || 0}ms
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Avg Response Time
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {dashboardData?.system_metrics.api_errors || 0}
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              API Errors
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {dashboardData?.system_metrics.failed_syncs || 0}
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Failed Syncs
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Summary */}
      <div className={`rounded-lg shadow p-6 transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Revenue Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-2xl font-bold text-green-600">
              ${dashboardData?.revenue_summary.total_collected?.toLocaleString() || 0}
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Total Collected
            </div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {dashboardData?.revenue_summary.growth_rate || 0}%
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Growth Rate
            </div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">
              {dashboardData?.revenue_summary.cancelled_subscriptions || 0}
            </div>
            <div className={`text-sm transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Cancelled Subscriptions
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTenants = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tenant Management</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="h-4 w-4" />
          Create Tenant
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="relative">
            <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search tenants..."
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Business
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Features
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {tenants.filter(tenant => 
                tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                tenant.business_name.toLowerCase().includes(searchTerm.toLowerCase())
              ).map((tenant) => (
                <tr key={tenant.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{tenant.business_name}</div>
                      <div className="text-sm text-gray-500">{tenant.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      tenant.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {tenant.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {tenant.plan_type || 'starter'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      {Object.entries(tenant.features || {}).map(([feature, enabled]) => (
                        <button
                          key={feature}
                          onClick={() => toggleTenantFeature(tenant.id, feature, !enabled)}
                          className="flex items-center space-x-1"
                        >
                          {enabled ? (
                            <ToggleRight className="h-4 w-4 text-green-500" />
                          ) : (
                            <ToggleLeft className="h-4 w-4 text-gray-400" />
                          )}
                          <span className="text-xs">{feature}</span>
                        </button>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Settings className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
          <p className={`mt-4 transition-colors duration-300 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Loading Super Admin Dashboard...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      {/* Header */}
      <header className={`shadow transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Super Admin Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {/* Database Status Indicator */}
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm ${
                dbConnectionStatus === 'connected'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : dbConnectionStatus === 'disconnected'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  dbConnectionStatus === 'connected'
                    ? 'bg-green-500 animate-pulse'
                    : dbConnectionStatus === 'disconnected'
                    ? 'bg-red-500'
                    : 'bg-yellow-500 animate-pulse'
                }`}></div>
                <span className="font-medium">
                  {dbConnectionStatus === 'connected' ? 'DB Connected' :
                   dbConnectionStatus === 'disconnected' ? 'DB Offline' : 'Checking...'}
                </span>
              </div>

              {/* Theme Toggle */}
              {onThemeToggle && (
                <button
                  onClick={onThemeToggle}
                  className={`p-2 rounded-lg transition-colors duration-300 ${
                    isDarkMode
                      ? 'text-yellow-400 hover:bg-gray-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {isDarkMode ? '☀️' : '🌙'}
                </button>
              )}

              <button className={`p-2 transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-400 hover:text-gray-600'
              }`}>
                <Bell className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">SA</span>
                </div>
                <span className={`text-sm font-medium transition-colors duration-300 ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-700'
                }`}>
                  Super Admin
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className={`border-b transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: Activity },
              { id: 'tenants', label: 'Tenants', icon: Users },
              { id: 'users', label: 'User Management', icon: UserCog },
              { id: 'analytics', label: 'Analytics', icon: TrendingUp },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-300 ${
                    activeTab === tab.id
                      ? isDarkMode
                        ? 'border-red-400 text-red-400'
                        : 'border-red-500 text-red-600'
                      : isDarkMode
                        ? 'border-transparent text-gray-400 hover:text-gray-200 hover:border-gray-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'tenants' && renderTenants()}
          {activeTab === 'users' && (
            <div className="text-center py-12">
              <UserCog className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className={`text-lg font-medium transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                User Management Coming Soon
              </h3>
              <p className={`transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                Comprehensive user management will be available here.
              </p>
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="text-center py-12">
              <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className={`text-lg font-medium transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Analytics Coming Soon
              </h3>
              <p className={`transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                Advanced analytics dashboard will be available here.
              </p>
            </div>
          )}
          {activeTab === 'settings' && (
            <div className="text-center py-12">
              <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className={`text-lg font-medium transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Settings Coming Soon
              </h3>
              <p className={`transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                System settings and configuration will be available here.
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default SuperAdminLandingPage;
