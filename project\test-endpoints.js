// Test script for the new landing page endpoints
// Run with: node test-endpoints.js

import axios from 'axios';

const BASE_URL = 'http://localhost:4000';

// Test credentials
const SUPER_ADMIN_PIN = '123456';
const MANAGER_PIN = '567890';

let superAdminToken = '';
let managerToken = '';

async function login(pin, role) {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      pin: pin,
      tenant_slug: 'demo-restaurant'
    });
    
    console.log(`✅ ${role} login successful`);
    return response.data.token;
  } catch (error) {
    console.error(`❌ ${role} login failed:`, error.response?.data?.error || error.message);
    return null;
  }
}

async function testSuperAdminEndpoints() {
  console.log('\n🔥 Testing Super Admin Endpoints...');
  
  const headers = { Authorization: `Bearer ${superAdminToken}` };
  
  try {
    // Test dashboard
    const dashboard = await axios.get(`${BASE_URL}/api/superadmin/dashboard`, { headers });
    console.log('✅ Super Admin Dashboard:', dashboard.data.tenant_overview);
    
    // Test analytics
    const analytics = await axios.get(`${BASE_URL}/api/superadmin/analytics`, { headers });
    console.log('✅ Super Admin Analytics:', analytics.data.usage_stats);
    
    // Test tenant creation
    const newTenant = await axios.post(`${BASE_URL}/api/superadmin/tenants`, {
      name: 'Test Restaurant',
      business_name: 'Test Restaurant & Bar',
      email: '<EMAIL>',
      phone: '555-0123',
      plan_type: 'pro',
      features: { pos: true, inventory: true, analytics: true }
    }, { headers });
    console.log('✅ Tenant Created:', newTenant.data.name);
    
    // Test feature toggle
    await axios.put(`${BASE_URL}/api/superadmin/tenants/${newTenant.data.id}/features`, {
      features: { kitchen_display: true }
    }, { headers });
    console.log('✅ Tenant Features Updated');
    
  } catch (error) {
    console.error('❌ Super Admin endpoint error:', error.response?.data?.error || error.message);
  }
}

async function testTenantAdminEndpoints() {
  console.log('\n🏢 Testing Tenant Admin Endpoints...');
  
  const headers = { Authorization: `Bearer ${managerToken}` };
  
  try {
    // Test dashboard
    const dashboard = await axios.get(`${BASE_URL}/api/tenant/dashboard`, { headers });
    console.log('✅ Tenant Dashboard:', dashboard.data.business_overview);
    
    // Test menu
    const menu = await axios.get(`${BASE_URL}/api/tenant/menu`, { headers });
    console.log('✅ Menu Items:', menu.data.items.length, 'items');
    
    // Test staff
    const staff = await axios.get(`${BASE_URL}/api/tenant/staff`, { headers });
    console.log('✅ Staff Members:', staff.data.length, 'members');
    
    // Test devices
    const devices = await axios.get(`${BASE_URL}/api/tenant/devices`, { headers });
    console.log('✅ Devices:', devices.data.length, 'devices');
    
    // Test menu item creation
    const newItem = await axios.post(`${BASE_URL}/api/tenant/menu`, {
      name: 'Test Burger',
      price: 15.99,
      category: 'Food',
      description: 'Delicious test burger'
    }, { headers });
    console.log('✅ Menu Item Created:', newItem.data.name);
    
  } catch (error) {
    console.error('❌ Tenant Admin endpoint error:', error.response?.data?.error || error.message);
  }
}

async function testHealthCheck() {
  try {
    const health = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ Server Health:', health.data.status);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Landing Page API Tests...\n');
  
  // Check server health
  const serverHealthy = await testHealthCheck();
  if (!serverHealthy) {
    console.log('❌ Server is not running. Please start the backend server first.');
    return;
  }
  
  // Login as Super Admin
  superAdminToken = await login(SUPER_ADMIN_PIN, 'Super Admin');
  if (!superAdminToken) return;
  
  // Login as Manager
  managerToken = await login(MANAGER_PIN, 'Manager');
  if (!managerToken) return;
  
  // Test Super Admin endpoints
  await testSuperAdminEndpoints();
  
  // Test Tenant Admin endpoints
  await testTenantAdminEndpoints();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n📱 Frontend URLs:');
  console.log('   Landing Page: http://localhost:5173/landing.html');
  console.log('   Unified POS:  http://localhost:5173/unified-pos.html');
  console.log('   Super Admin:  http://localhost:5173/super-admin.html');
}

// Run the tests
runTests().catch(console.error);
