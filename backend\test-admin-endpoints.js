const fetch = require('node-fetch');

const API_BASE = 'http://localhost:4000/api';

// Test credentials - using the Enhanced Admin super admin
const TEST_CREDENTIALS = {
  pin: '888888',
  tenant: 'demo-restaurant'
};

async function testAdminEndpoints() {
  console.log('🧪 Testing Super Admin Dashboard API Endpoints');
  console.log('================================================');

  try {
    // Step 1: Login to get auth token
    console.log('🔐 Step 1: Authenticating as super admin...');
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(TEST_CREDENTIALS)
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', loginData.user.name, `(${loginData.user.role})`);
    
    const authToken = loginData.token;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`
    };

    // Step 2: Test /api/admin/metrics
    console.log('\n📊 Step 2: Testing /api/admin/metrics...');
    const metricsResponse = await fetch(`${API_BASE}/admin/metrics`, { headers });
    
    if (metricsResponse.ok) {
      const metrics = await metricsResponse.json();
      console.log('✅ Metrics endpoint working:');
      console.log('   - Total Tenants:', metrics.totalTenants);
      console.log('   - Active Tenants:', metrics.activeTenants);
      console.log('   - Total Revenue:', `$${metrics.totalRevenue.toLocaleString()}`);
      console.log('   - System Uptime:', `${metrics.systemUptime}%`);
      console.log('   - Active Users:', metrics.activeUsers);
      console.log('   - Transactions Today:', metrics.transactionsToday);
    } else {
      console.log('❌ Metrics endpoint failed:', metricsResponse.status, metricsResponse.statusText);
    }

    // Step 3: Test /api/admin/tenants
    console.log('\n🏢 Step 3: Testing /api/admin/tenants...');
    const tenantsResponse = await fetch(`${API_BASE}/admin/tenants`, { headers });
    
    if (tenantsResponse.ok) {
      const tenants = await tenantsResponse.json();
      console.log('✅ Tenants endpoint working:');
      console.log(`   - Found ${tenants.length} tenants`);
      tenants.forEach((tenant, index) => {
        console.log(`   ${index + 1}. ${tenant.name} (${tenant.status})`);
        console.log(`      - Employees: ${tenant.employeeCount}`);
        console.log(`      - Locations: ${tenant.locationCount}`);
        console.log(`      - Revenue: $${tenant.totalRevenue.toLocaleString()}`);
      });
    } else {
      console.log('❌ Tenants endpoint failed:', tenantsResponse.status, tenantsResponse.statusText);
    }

    // Step 4: Test /api/admin/analytics
    console.log('\n📈 Step 4: Testing /api/admin/analytics...');
    const analyticsResponse = await fetch(`${API_BASE}/admin/analytics`, { headers });
    
    if (analyticsResponse.ok) {
      const analytics = await analyticsResponse.json();
      console.log('✅ Analytics endpoint working:');
      console.log(`   - Revenue trends: ${analytics.revenueTrends.length} data points`);
      console.log(`   - Top tenants: ${analytics.topTenants.length} entries`);
      console.log(`   - Performance metrics: ${analytics.performanceMetrics.length} entries`);
    } else {
      console.log('❌ Analytics endpoint failed:', analyticsResponse.status, analyticsResponse.statusText);
    }

    // Step 5: Test /api/admin/activity
    console.log('\n📋 Step 5: Testing /api/admin/activity...');
    const activityResponse = await fetch(`${API_BASE}/admin/activity`, { headers });
    
    if (activityResponse.ok) {
      const activities = await activityResponse.json();
      console.log('✅ Activity endpoint working:');
      console.log(`   - Found ${activities.length} recent activities`);
      activities.slice(0, 3).forEach((activity, index) => {
        console.log(`   ${index + 1}. ${activity.action} - ${activity.tenant} (${activity.time})`);
      });
    } else {
      console.log('❌ Activity endpoint failed:', activityResponse.status, activityResponse.statusText);
    }

    console.log('\n🎉 All admin endpoints tested successfully!');
    console.log('✅ The Super Admin Dashboard should now display real data from the database.');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testAdminEndpoints();
