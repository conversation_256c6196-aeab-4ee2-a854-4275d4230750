import React, { useState, useEffect } from 'react';
import { Table, Employee } from '../types';
import { 
  User, 
  Lock, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  X,
  Shield,
  MapPin,
  Users,
  Timer
} from 'lucide-react';

interface EmployeeVerificationModalProps {
  selectedTable: Table;
  currentEmployee: Employee;
  onConfirm: (pin: string) => void;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
  lockExpiry?: Date | null;
}

const EmployeeVerificationModal: React.FC<EmployeeVerificationModalProps> = ({
  selectedTable,
  currentEmployee,
  onConfirm,
  onCancel,
  loading = false,
  error = null,
  lockExpiry = null
}) => {
  const [pin, setPin] = useState('');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [showPinInput, setShowPinInput] = useState(false);

  // Calculate time remaining for table lock
  useEffect(() => {
    if (lockExpiry) {
      const updateTimer = () => {
        const now = new Date();
        const remaining = Math.max(0, Math.floor((lockExpiry.getTime() - now.getTime()) / 1000));
        setTimeRemaining(remaining);
        
        if (remaining === 0) {
          // Lock expired, cancel the process
          onCancel();
        }
      };

      updateTimer();
      const interval = setInterval(updateTimer, 1000);
      return () => clearInterval(interval);
    }
  }, [lockExpiry, onCancel]);

  const handlePinChange = (value: string) => {
    // Only allow numeric input and limit to 6 digits
    if (/^\d*$/.test(value) && value.length <= 6) {
      setPin(value);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (pin.length >= 4) {
      onConfirm(pin);
    }
  };

  const handleKeypadClick = (digit: string) => {
    if (digit === 'clear') {
      setPin('');
    } else if (digit === 'backspace') {
      setPin(prev => prev.slice(0, -1));
    } else if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTableIcon = (table: Table) => {
    switch (table.shape) {
      case 'circle':
        return '⭕';
      case 'oval':
        return '🔵';
      default:
        return '⬜';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6 rounded-t-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-8 h-8" />
              <div>
                <h2 className="text-xl font-bold">Employee Verification</h2>
                <p className="text-green-100 text-sm">Confirm your identity to proceed</p>
              </div>
            </div>
            <button
              onClick={onCancel}
              className="text-white hover:text-gray-200 transition-colors"
              disabled={loading}
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Table Information */}
        <div className="p-6 border-b border-gray-200">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">{getTableIcon(selectedTable)}</div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-lg">Table {selectedTable.number}</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {selectedTable.seats} seats
                  </div>
                  {selectedTable.section && (
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {selectedTable.section}
                    </div>
                  )}
                  {selectedTable.tableType && (
                    <div className="text-xs text-gray-500 capitalize">
                      {selectedTable.tableType.replace('_', ' ')}
                    </div>
                  )}
                </div>
              </div>
              {lockExpiry && timeRemaining > 0 && (
                <div className="text-center">
                  <div className="flex items-center text-orange-600 mb-1">
                    <Timer className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">Lock Expires</span>
                  </div>
                  <div className="text-lg font-bold text-orange-700">
                    {formatTime(timeRemaining)}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Employee Information */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <User className="w-5 h-5 text-gray-500" />
            <div>
              <div className="font-medium text-gray-900">{currentEmployee.name}</div>
              <div className="text-sm text-gray-600 capitalize">
                {currentEmployee.role?.replace('_', ' ')}
              </div>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Security Verification Required</p>
                <p>Please enter your PIN to confirm table assignment and proceed with the order.</p>
              </div>
            </div>
          </div>
        </div>

        {/* PIN Input Section */}
        <div className="p-6">
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Your PIN
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="password"
                  value={pin}
                  onChange={(e) => handlePinChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center text-lg font-mono tracking-widest"
                  placeholder="••••••"
                  maxLength={6}
                  autoFocus
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Enter your 4-6 digit PIN to verify your identity
              </p>
            </div>

            {/* Virtual Keypad */}
            <div className="grid grid-cols-3 gap-2 mb-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                <button
                  key={num}
                  type="button"
                  onClick={() => handleKeypadClick(num.toString())}
                  className="h-12 bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold text-gray-700 transition-colors"
                  disabled={loading}
                >
                  {num}
                </button>
              ))}
              <button
                type="button"
                onClick={() => handleKeypadClick('clear')}
                className="h-12 bg-red-100 hover:bg-red-200 rounded-lg font-medium text-red-700 transition-colors text-sm"
                disabled={loading}
              >
                Clear
              </button>
              <button
                type="button"
                onClick={() => handleKeypadClick('0')}
                className="h-12 bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold text-gray-700 transition-colors"
                disabled={loading}
              >
                0
              </button>
              <button
                type="button"
                onClick={() => handleKeypadClick('backspace')}
                className="h-12 bg-yellow-100 hover:bg-yellow-200 rounded-lg font-medium text-yellow-700 transition-colors text-sm"
                disabled={loading}
              >
                ⌫
              </button>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={pin.length < 4 || loading}
                className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center space-x-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Verifying...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    <span>Confirm</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Security Notice */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-xl border-t border-gray-200">
          <div className="flex items-center space-x-2 text-xs text-gray-600">
            <Shield className="w-4 h-4" />
            <span>Your PIN is encrypted and secure. This verification ensures proper table assignment tracking.</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeVerificationModal;
