const axios = require('axios');

async function testRateLimit() {
  console.log('Testing rate limiting on auth endpoint...');
  
  for (let i = 1; i <= 12; i++) {
    try {
      const response = await axios.post('http://localhost:4000/api/auth/login', {
        pin: '1234'
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log(`Request ${i}: Success`);
    } catch (error) {
      if (error.response && error.response.status === 429) {
        console.log(`Request ${i}: Rate limited (429)`);
      } else {
        console.log(`Request ${i}: ${error.response ? error.response.status : 'Error'}`);
      }
    }
    
    // Small delay to avoid overwhelming
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

testRateLimit();
