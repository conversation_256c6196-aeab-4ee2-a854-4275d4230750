import React, { useState, useEffect } from 'react';
import { ModernCard, StatCard, ActionCard, InfoCard } from '../components/ui/ModernCard';
import { ThemeToggle, ThemeToggleAdvanced } from '../components/ui/ThemeToggle';
import { useTheme, getThemeClasses, animations } from '../contexts/ThemeContext';
import { useTranslation } from '../hooks/useTranslation';
import LanguageSelector from '../components/LanguageSelector';
import { AddTenantModal } from '../components/modals/AddTenantModal';
import { GenerateReportModal } from '../components/modals/GenerateReportModal';
import AdvancedVoiceRecognition from '../components/AdvancedVoiceRecognition';
import VoiceAnalyticsDashboard from '../components/VoiceAnalyticsDashboard';
import CulturalIntelligenceEngine from '../components/CulturalIntelligenceEngine';
import CulturalAnalyticsDashboard from '../components/CulturalAnalyticsDashboard';
import GlobalComplianceDashboard from '../components/GlobalComplianceDashboard';
import AdvancedSecurityDashboard from '../components/AdvancedSecurityDashboard';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import {
  Users,
  UserPlus,
  Building2,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart,
  LineChart,
  Settings,
  Shield,
  Database,
  Server,
  Wifi,
  WifiOff,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Bell,
  Mail,
  Phone,
  Globe,
  Calendar,
  MapPin,
  CreditCard,
  Zap,
  Target,
  Award,
  Star,
  Heart,
  Bookmark,
  Share2,
  ExternalLink,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Sparkles,
  Layers,
  Monitor,
  Brain,
  Cpu,
  HardDrive,
  Network,
  CloudIcon as Cloud,
  Mic as MicrophoneIcon
} from 'lucide-react';

interface DashboardMetrics {
  totalTenants: number;
  activeUsers: number;
  monthlyRevenue: number;
  systemHealth: number;
  newSignups: number;
  churnRate: number;
  avgResponseTime: number;
  uptime: number;
}

interface TenantData {
  id: string;
  name: string;
  plan: string;
  status: 'active' | 'inactive' | 'suspended';
  users: number;
  revenue: number;
  lastActive: Date;
  location: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
}

export function ModernSuperAdminDashboard() {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const { t, formatCurrency, formatDate } = useTranslation();
  
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [tenants, setTenants] = useState<TenantData[]>([]);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('7d');
  const [showVoicePanel, setShowVoicePanel] = useState(false);
  const [showVoiceAnalytics, setShowVoiceAnalytics] = useState(false);
  const [showCulturalIntelligence, setShowCulturalIntelligence] = useState(false);
  const [showCulturalAnalytics, setShowCulturalAnalytics] = useState(false);
  const [showGlobalCompliance, setShowGlobalCompliance] = useState(false);
  const [showAdvancedSecurity, setShowAdvancedSecurity] = useState(false);
  const [showAddTenantModal, setShowAddTenantModal] = useState(false);
  const [showBackupModal, setShowBackupModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Handler functions for action buttons
  const handleAddNewTenant = async () => {
    setActionLoading('add-tenant');
    try {
      // Show modal for tenant creation
      setShowAddTenantModal(true);
    } catch (error) {
      console.error('Error opening tenant creation modal:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleSystemBackup = async () => {
    setActionLoading('backup');
    try {
      const response = await fetch('/api/admin/system/backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Backup initiated successfully. Backup ID: ${result.backupId}`);
      } else {
        throw new Error('Backup failed');
      }
    } catch (error) {
      console.error('Error starting backup:', error);
      alert('Failed to start system backup. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleGenerateReport = async () => {
    setActionLoading('report');
    try {
      setShowReportModal(true);
    } catch (error) {
      console.error('Error opening report modal:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefreshData = async () => {
    setLoading(true);
    try {
      // Refresh all dashboard data
      await fetchData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  // Fetch real data from API
  const fetchData = async () => {
    setLoading(true);

    try {
      // Fetch real metrics from API
      const metricsResponse = await fetch('/api/admin/metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics({
          totalTenants: metricsData.totalTenants || 0,
          activeUsers: metricsData.activeUsers || 0,
          monthlyRevenue: metricsData.totalRevenue || 0,
          systemHealth: metricsData.systemUptime || 0,
          newSignups: metricsData.transactionsToday || 0,
          churnRate: metricsData.errorRate || 0,
          avgResponseTime: metricsData.responseTime || 0,
          uptime: metricsData.systemUptime || 0
        });
      }

      // Fetch real tenants from API
      const tenantsResponse = await fetch('/api/admin/tenants', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (tenantsResponse.ok) {
        const tenantsData = await tenantsResponse.json();
        const formattedTenants = tenantsData.slice(0, 5).map((tenant: any) => ({
          id: tenant.id.toString(),
          name: tenant.name || 'Unknown Restaurant',
          plan: tenant.planType || 'Starter',
          status: tenant.status || 'active',
          users: tenant.employeeCount || 0,
          revenue: tenant.totalRevenue || 0,
          lastActive: tenant.lastActivity ? new Date(tenant.lastActivity) : new Date(),
          location: tenant.address || 'Unknown Location'
        }));
        setTenants(formattedTenants);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Set default values on error
      setMetrics({
        totalTenants: 0,
        activeUsers: 0,
        monthlyRevenue: 0,
        systemHealth: 0,
        newSignups: 0,
        churnRate: 0,
        avgResponseTime: 0,
        uptime: 0
      });
      setTenants([]);
    }

      // Fetch real alerts from API if available
      try {
        const alertsResponse = await fetch('/api/admin/alerts', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          }
        });

        if (alertsResponse.ok) {
          const alertsData = await alertsResponse.json();
          setAlerts(alertsData);
        } else {
          // Set empty alerts if API not available
          setAlerts([]);
        }
      } catch (error) {
        console.log('Alerts API not available, setting empty alerts');
        setAlerts([]);
      }

      setLoading(false);
    };

    fetchData();
  }, [selectedTimeRange]);

  // Voice command handlers
  const handleVoiceCommand = (command: any) => {
    console.log('🎤 Voice command received:', command);

    switch (command.intent.name) {
      case 'report':
        console.log('📊 Generating report via voice command');
        break;
      case 'help':
        setShowVoicePanel(true);
        break;
      case 'navigation':
        if (command.entities.some((e: any) => e.value === 'analytics')) {
          setShowVoiceAnalytics(true);
        }
        if (command.entities.some((e: any) => e.value === 'cultural')) {
          setShowCulturalIntelligence(true);
        }
        if (command.entities.some((e: any) => e.value === 'compliance')) {
          setShowGlobalCompliance(true);
        }
        if (command.entities.some((e: any) => e.value === 'security')) {
          setShowAdvancedSecurity(true);
        }
        break;
      default:
        console.log('🤖 Processing voice command:', command.intent.name);
    }
  };

  const handleVoiceAuthentication = (authResult: any) => {
    console.log('🔐 Voice authentication result:', authResult);
    if (authResult.authenticated) {
      console.log('✅ Voice authentication successful');
    } else {
      console.log('❌ Voice authentication failed');
    }
  };

  // Cultural intelligence handlers
  const handleCulturalInsight = (insight: any) => {
    console.log('🧠 Cultural insight received:', insight);
  };

  const handleEmotionDetected = (emotion: any) => {
    console.log('💭 Emotion detected:', emotion);
  };

  const handleAdaptationRecommended = (adaptation: any) => {
    console.log('🎨 Cultural adaptation recommended:', adaptation);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'inactive': return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
      case 'suspended': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error': return 'border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'warning': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'info': return 'border-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'success': return 'border-green-500 bg-green-50 dark:bg-green-900/20';
      default: return 'border-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'info': return <Bell className="h-5 w-5 text-blue-500" />;
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      default: return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${themeClasses.bg.secondary}`}>
      {/* Header */}
      <header className={`${themeClasses.bg.primary} ${themeClasses.border.primary} border-b sticky top-0 z-40 backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className={`text-xl font-bold ${themeClasses.text.primary}`}>
                    {t('nav.dashboard', 'Super Admin Dashboard')}
                  </h1>
                  <p className={`text-sm ${themeClasses.text.secondary}`}>
                    {t('dashboard.subtitle', 'System Management & Analytics')}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Time Range Selector */}
              <div className="hidden sm:flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                {(['24h', '7d', '30d', '90d'] as const).map((range) => (
                  <button
                    key={range}
                    onClick={() => setSelectedTimeRange(range)}
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${
                      selectedTimeRange === range
                        ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>

              {/* Voice Recognition Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowVoicePanel(!showVoicePanel)}
                className={`relative ${showVoicePanel ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''}`}
              >
                <MicrophoneIcon className="h-5 w-5" />
                {showVoicePanel && (
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full animate-pulse"></span>
                )}
              </Button>

              {/* Cultural Intelligence Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCulturalIntelligence(!showCulturalIntelligence)}
                className={`relative ${showCulturalIntelligence ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400' : ''}`}
              >
                <Brain className="h-5 w-5" />
                {showCulturalIntelligence && (
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-purple-500 rounded-full animate-pulse"></span>
                )}
              </Button>

              {/* Global Compliance Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowGlobalCompliance(!showGlobalCompliance)}
                className={`relative ${showGlobalCompliance ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' : ''}`}
              >
                <Scale className="h-5 w-5" />
                {showGlobalCompliance && (
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full animate-pulse"></span>
                )}
              </Button>

              {/* Advanced Security Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvancedSecurity(!showAdvancedSecurity)}
                className={`relative ${showAdvancedSecurity ? 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400' : ''}`}
              >
                <Shield className="h-5 w-5" />
                {showAdvancedSecurity && (
                  <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse"></span>
                )}
              </Button>

              {/* Language Selector */}
              <LanguageSelector compact={true} position="bottom-right" />

              {/* Theme Toggle */}
              <ThemeToggleAdvanced />

              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {alerts.filter(a => !a.resolved).length}
                </span>
              </Button>

              {/* Settings */}
              <Button variant="ghost" size="sm">
                <Settings className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Key Metrics */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className={`text-2xl font-bold ${themeClasses.text.primary}`}>
                {t('dashboard.platform_overview', 'Platform Overview')}
              </h2>
              <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                {t('dashboard.metrics_description', 'Real-time system metrics and performance indicators')}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              onClick={handleRefreshData}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Tenants"
              value={metrics?.totalTenants.toLocaleString() || '0'}
              change={{ value: 12.5, type: 'increase' }}
              icon={Building2}
              color="blue"
              loading={loading}
            />
            <StatCard
              title="Active Users"
              value={metrics?.activeUsers.toLocaleString() || '0'}
              change={{ value: 8.3, type: 'increase' }}
              icon={Users}
              color="green"
              loading={loading}
            />
            <StatCard
              title="Monthly Revenue"
              value={`$${(metrics?.monthlyRevenue || 0).toLocaleString()}`}
              change={{ value: 15.7, type: 'increase' }}
              icon={DollarSign}
              color="purple"
              loading={loading}
            />
            <StatCard
              title="System Health"
              value={`${metrics?.systemHealth || 0}%`}
              change={{ value: 0.2, type: 'increase' }}
              icon={Activity}
              color="indigo"
              loading={loading}
            />
          </div>
        </div>

        {/* Secondary Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="New Signups"
            value={metrics?.newSignups.toLocaleString() || '0'}
            description="This month"
            icon={UserPlus}
            color="green"
            loading={loading}
          />
          <StatCard
            title="Churn Rate"
            value={`${metrics?.churnRate || 0}%`}
            description="Monthly average"
            icon={TrendingDown}
            color="red"
            loading={loading}
          />
          <StatCard
            title="Response Time"
            value={`${metrics?.avgResponseTime || 0}ms`}
            description="Average API response"
            icon={Zap}
            color="yellow"
            loading={loading}
          />
          <StatCard
            title="Uptime"
            value={`${metrics?.uptime || 0}%`}
            description="Last 30 days"
            icon={Server}
            color="blue"
            loading={loading}
          />
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-4`}>
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <ActionCard
              title="Add New Tenant"
              description="Onboard a new restaurant to the platform"
              icon={Plus}
              action="Create Tenant"
              onClick={handleAddNewTenant}
              color="blue"
            />
            <ActionCard
              title="System Backup"
              description="Create a full system backup"
              icon={Database}
              action="Start Backup"
              onClick={handleSystemBackup}
              color="green"
            />
            <ActionCard
              title="Generate Report"
              description="Create comprehensive analytics report"
              icon={BarChart3}
              action="Generate"
              onClick={handleGenerateReport}
              color="purple"
            />
          </div>
        </div>

        {/* System Alerts */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${themeClasses.text.primary}`}>
              System Alerts
            </h3>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </div>

          <div className="space-y-3">
            {alerts.slice(0, 3).map((alert) => (
              <div
                key={alert.id}
                className={`p-4 rounded-lg border-l-4 ${getAlertColor(alert.type)} ${animations.fadeIn}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getAlertIcon(alert.type)}
                    <div>
                      <h4 className={`font-medium ${themeClasses.text.primary}`}>
                        {alert.title}
                      </h4>
                      <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                        {alert.message}
                      </p>
                      <p className={`text-xs ${themeClasses.text.tertiary} mt-2`}>
                        {alert.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {!alert.resolved && (
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Tenants */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${themeClasses.text.primary}`}>
              Recent Tenants
            </h3>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>

          <ModernCard padding="none">
            <div className="overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className={`${themeClasses.bg.tertiary}`}>
                    <tr>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Tenant
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Plan
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Status
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Users
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Revenue
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Last Active
                      </th>
                      <th className={`px-6 py-3 text-left text-xs font-medium ${themeClasses.text.secondary} uppercase tracking-wider`}>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`${themeClasses.bg.card} divide-y ${themeClasses.border.primary}`}>
                    {tenants.map((tenant, index) => (
                      <tr key={tenant.id} className={`${animations.fadeIn} hover:${themeClasses.bg.hover} transition-colors duration-150`} style={{ animationDelay: `${index * 100}ms` }}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                <Building2 className="h-5 w-5 text-white" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className={`text-sm font-medium ${themeClasses.text.primary}`}>
                                {tenant.name}
                              </div>
                              <div className={`text-sm ${themeClasses.text.secondary} flex items-center`}>
                                <MapPin className="h-3 w-3 mr-1" />
                                {tenant.location}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge className={`${
                            tenant.plan === 'Enterprise' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300' :
                            tenant.plan === 'Professional' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                            'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300'
                          }`}>
                            {tenant.plan}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Badge className={getStatusColor(tenant.status)}>
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                tenant.status === 'active' ? 'bg-green-500' :
                                tenant.status === 'inactive' ? 'bg-gray-500' :
                                'bg-red-500'
                              }`} />
                              {tenant.status}
                            </div>
                          </Badge>
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.text.primary}`}>
                          {tenant.users}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.text.primary}`}>
                          ${tenant.revenue.toLocaleString()}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.text.secondary}`}>
                          {tenant.lastActive.toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </ModernCard>
        </div>

        {/* System Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <InfoCard
            title="System Performance"
            icon={Monitor}
            badge={{ text: 'Real-time', color: 'green' }}
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4" />
                  <span className="text-sm">CPU Usage</span>
                </div>
                <span className="text-sm font-medium">67%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '67%' }}></div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <HardDrive className="h-4 w-4" />
                  <span className="text-sm">Memory Usage</span>
                </div>
                <span className="text-sm font-medium">45%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '45%' }}></div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Network className="h-4 w-4" />
                  <span className="text-sm">Network I/O</span>
                </div>
                <span className="text-sm font-medium">23%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '23%' }}></div>
              </div>
            </div>
          </InfoCard>

          <InfoCard
            title="Recent Activity"
            icon={Activity}
            badge={{ text: 'Live', color: 'blue' }}
          >
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm">New tenant registered: Ocean Breeze Cafe</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm">System backup completed successfully</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">15 minutes ago</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm">High CPU usage detected on server-03</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">1 hour ago</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-sm">Monthly report generated</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">3 hours ago</p>
                </div>
              </div>
            </div>
          </InfoCard>
        </div>

        {/* Voice Recognition Panel */}
        {showVoicePanel && (
          <div className="mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('voice.control_panel', 'Voice Control Panel')}
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowVoiceAnalytics(!showVoiceAnalytics)}
                  >
                    {t('voice.analytics', 'Analytics')}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowVoicePanel(false)}
                  >
                    ✕
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <AdvancedVoiceRecognition
                  mode="conversation"
                  onCommand={handleVoiceCommand}
                  onAuthentication={handleVoiceAuthentication}
                  enableNLP={true}
                  showAnalytics={true}
                />
                <AdvancedVoiceRecognition
                  mode="authentication"
                  onCommand={handleVoiceCommand}
                  onAuthentication={handleVoiceAuthentication}
                  enableNLP={false}
                  showAnalytics={false}
                />
              </div>
            </div>
          </div>
        )}

        {/* Voice Analytics Dashboard */}
        {showVoiceAnalytics && (
          <div className="mb-8">
            <VoiceAnalyticsDashboard />
          </div>
        )}

        {/* Cultural Intelligence Engine */}
        {showCulturalIntelligence && (
          <div className="mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('cultural.intelligence_engine', 'Cultural Intelligence Engine')}
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCulturalAnalytics(!showCulturalAnalytics)}
                  >
                    {t('cultural.analytics', 'Analytics')}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowCulturalIntelligence(false)}
                  >
                    ✕
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <CulturalIntelligenceEngine
                  customerData={{
                    region: 'north-america',
                    preferences: ['fast-service', 'customization'],
                    communicationStyle: 'direct',
                    previousInteractions: []
                  }}
                  region="north-america"
                  onCulturalInsight={handleCulturalInsight}
                  onEmotionDetected={handleEmotionDetected}
                  onAdaptationRecommended={handleAdaptationRecommended}
                  enableEmotionRecognition={true}
                  enableMarketIntelligence={true}
                />
                <CulturalIntelligenceEngine
                  customerData={{
                    region: 'east-asia',
                    preferences: ['traditional', 'family-style'],
                    communicationStyle: 'indirect',
                    previousInteractions: []
                  }}
                  region="east-asia"
                  onCulturalInsight={handleCulturalInsight}
                  onEmotionDetected={handleEmotionDetected}
                  onAdaptationRecommended={handleAdaptationRecommended}
                  enableEmotionRecognition={true}
                  enableMarketIntelligence={false}
                />
              </div>
            </div>
          </div>
        )}

        {/* Cultural Analytics Dashboard */}
        {showCulturalAnalytics && (
          <div className="mb-8">
            <CulturalAnalyticsDashboard />
          </div>
        )}

        {/* Global Compliance Dashboard */}
        {showGlobalCompliance && (
          <div className="mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('compliance.global_compliance', 'Global Compliance & Regulatory Framework')}
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAdvancedSecurity(!showAdvancedSecurity)}
                  >
                    {t('security.advanced_security', 'Security')}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowGlobalCompliance(false)}
                  >
                    ✕
                  </Button>
                </div>
              </div>

              <GlobalComplianceDashboard />
            </div>
          </div>
        )}

        {/* Advanced Security Dashboard */}
        {showAdvancedSecurity && (
          <div className="mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t('security.advanced_security_dashboard', 'Advanced Security & Threat Detection')}
                </h3>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowGlobalCompliance(!showGlobalCompliance)}
                  >
                    {t('compliance.compliance', 'Compliance')}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAdvancedSecurity(false)}
                  >
                    ✕
                  </Button>
                </div>
              </div>

              <AdvancedSecurityDashboard />
            </div>
          </div>
        )}
      </main>

      {/* Modals */}
      <AddTenantModal
        isOpen={showAddTenantModal}
        onClose={() => setShowAddTenantModal(false)}
        onSubmit={(tenantData) => {
          console.log('New tenant created:', tenantData);
          // Refresh data after tenant creation
          handleRefreshData();
        }}
      />

      <GenerateReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        onGenerate={(reportConfig) => {
          console.log('Report generated:', reportConfig);
        }}
      />
    </div>
  );
}

// Helper component for user plus icon
function UserPlus({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
    </svg>
  );
}
