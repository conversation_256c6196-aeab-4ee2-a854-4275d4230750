import React, { useState, useEffect, useCallback } from 'react';
import { AdminLayout } from '../components/layout/AdminLayout';
import { EnhancedCard } from '../components/ui/enhanced-card';
import { EnhancedUserManager } from '../components/EnhancedUserManager';
import { MultiLocationManager } from '../components/admin/tenants/MultiLocationManager';
import { AdvancedAnalyticsDashboard } from '../components/admin/analytics/AdvancedAnalyticsDashboard';
import { SystemHealthDashboard } from '../components/admin/monitoring/SystemHealthDashboard';
import { SecurityAuditDashboard } from '../components/admin/security/SecurityAuditDashboard';
import { BackupManagement } from '../components/admin/backup/BackupManagement';
import { ApiManagementDashboard } from '../components/admin/api/ApiManagementDashboard';
import { BillingManagement } from '../components/admin/billing/BillingManagement';
import { NotificationCenter } from '../components/admin/notifications/NotificationCenter';
import { TenantManagement } from '../components/admin/tenants/TenantManagement';
import { Phase2CPerformanceMonitor } from '../components/admin/monitoring/Phase2CPerformanceMonitor';
import { Phase2COptimizationDashboard } from '../components/admin/optimization/Phase2COptimizationDashboard';
import { Phase2DEnterpriseIntegrations } from '../components/admin/integrations/Phase2DEnterpriseIntegrations';
import { Phase2DAdvancedAutomation } from '../components/admin/automation/Phase2DAdvancedAutomation';
import { Phase2DMobileAPIManagement } from '../components/admin/mobile/Phase2DMobileAPIManagement';
import { Phase2DOmnichannelExperience } from '../components/admin/omnichannel/Phase2DOmnichannelExperience';
import { Phase2DDeploymentReadiness } from '../components/admin/deployment/Phase2DDeploymentReadiness';
import { Phase3AAIAnalyticsDashboard } from '../components/admin/ai/Phase3AAIAnalyticsDashboard';
import { Phase3BPredictiveSalesForecasting } from '../components/admin/ai/Phase3BPredictiveSalesForecasting';
import { Phase3CIntelligentInventoryOptimization } from '../components/admin/ai/Phase3CIntelligentInventoryOptimization';
import { Phase3DAICustomerBehaviorAnalysis } from '../components/admin/ai/Phase3DAICustomerBehaviorAnalysis';
import { Phase3ESmartPricingOptimization } from '../components/admin/ai/Phase3ESmartPricingOptimization';
import { Phase3FAutomatedStaffScheduling } from '../components/admin/ai/Phase3FAutomatedStaffScheduling';
import {
  Users,
  DollarSign,
  Activity,
  Server,
  TrendingUp,
  Clock,
  BarChart3,
  Shield,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Wifi,
  WifiOff,
  Globe,
  Bot,
  Smartphone,
  Brain,
  Target
} from 'lucide-react';

// Real API Service
import {
  adminApiService,
  SystemMetrics,
  Tenant,
  SystemAnalytics,
  SystemActivity
} from '../services/adminApiService';

export function EnhancedAdminDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [analytics, setAnalytics] = useState<SystemAnalytics | null>(null);
  const [activities, setActivities] = useState<SystemActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [currentView, setCurrentView] = useState('dashboard');
  const [selectedTenant, setSelectedTenant] = useState<{id: number, name: string} | null>(null);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false); // Disabled by default to reduce server load
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Fetch all dashboard data from real API
  const fetchDashboardData = useCallback(async (forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      setError(null);
      setIsOnline(true);

      console.log('🔄 Fetching dashboard data from real API...');

      // Fetch all data in parallel for better performance
      const [metricsData, tenantsData, analyticsData, activitiesData] = await Promise.all([
        adminApiService.getSystemMetrics(forceRefresh),
        adminApiService.getTenants(forceRefresh),
        adminApiService.getSystemAnalytics(forceRefresh),
        adminApiService.getSystemActivity(forceRefresh)
      ]);

      setMetrics(metricsData);
      setTenants(tenantsData);
      setAnalytics(analyticsData);
      setActivities(activitiesData);
      setLastRefresh(new Date());

      console.log('✅ Dashboard data loaded successfully');
      console.log('📊 Metrics:', metricsData);
      console.log('🏢 Tenants:', tenantsData.length);
      console.log('📈 Analytics:', analyticsData);
      console.log('📋 Activities:', activitiesData.length);

    } catch (error) {
      console.error('💥 Error fetching dashboard data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
      setIsOnline(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Manual refresh function
  const handleRefresh = useCallback(() => {
    console.log('🔄 Manual refresh triggered');
    fetchDashboardData(true);
  }, [fetchDashboardData]);

  // Enhanced view change handler with loading states
  const handleViewChange = useCallback(async (newView: string) => {
    if (newView === currentView) return;

    setIsTransitioning(true);
    setError(null);

    try {
      // Simulate loading time for better UX
      await new Promise(resolve => setTimeout(resolve, 300));
      setCurrentView(newView);

      // Update URL hash
      window.location.hash = newView;

      console.log(`🔄 View changed to: ${newView}`);
    } catch (err) {
      setError('Failed to load view');
      console.error('View change error:', err);
    } finally {
      setIsTransitioning(false);
    }
  }, [currentView]);

  useEffect(() => {
    // Listen for hash changes to handle navigation
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash) {
        setCurrentView(hash);
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    // Initial data fetch
    fetchDashboardData();

    // Set up auto-refresh if enabled
    let autoRefreshCleanup: (() => void) | null = null;
    if (autoRefreshEnabled) {
      const serviceCleanup = adminApiService.startAutoRefresh(120000); // 2 minutes to reduce server load

      // Listen for cache clears to refresh UI
      const refreshInterval = setInterval(() => {
        if (autoRefreshEnabled && !loading) {
          console.log('🔄 Auto-refresh triggered');
          fetchDashboardData();
        }
      }, 120000); // 2 minutes to reduce server load

      autoRefreshCleanup = () => {
        clearInterval(refreshInterval);
        if (serviceCleanup) serviceCleanup();
      };
    }

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
      if (autoRefreshCleanup) autoRefreshCleanup();
    };
  }, [fetchDashboardData, autoRefreshEnabled, loading]);

  // Enhanced navigation configuration
  const navigationConfig = {
    'dashboard': { label: 'Dashboard Overview', icon: '📊', color: 'blue', category: 'main' },
    'tenant-management': { label: 'Tenant Management', icon: '🏢', color: 'green', category: 'core' },
    'user-management': { label: 'User Management', icon: '👥', color: 'purple', category: 'core' },
    'analytics': { label: 'Advanced Analytics', icon: '📈', color: 'indigo', category: 'core' },
    'billing-plans': { label: 'Billing Management', icon: '💳', color: 'emerald', category: 'core' },
    'system-health': { label: 'System Health', icon: '🔧', color: 'orange', category: 'monitoring' },
    'security-audit': { label: 'Security Audit', icon: '🛡️', color: 'red', category: 'monitoring' },
    'performance-monitor': { label: 'Performance Monitor', icon: '⚡', color: 'yellow', category: 'monitoring' },
    'api-management': { label: 'API Management', icon: '🔌', color: 'cyan', category: 'technical' },
    'backup-recovery': { label: 'Backup & Recovery', icon: '💾', color: 'gray', category: 'technical' },
    'notifications': { label: 'Notifications', icon: '🔔', color: 'pink', category: 'technical' },
    'ai-analytics-dashboard': { label: 'AI Analytics', icon: '🤖', color: 'violet', category: 'ai' },
    'predictive-sales-forecasting': { label: 'Sales Forecasting', icon: '📊', color: 'blue', category: 'ai' },
    'intelligent-inventory-optimization': { label: 'Inventory AI', icon: '📦', color: 'green', category: 'ai' },
    'smart-pricing-optimization': { label: 'Smart Pricing', icon: '💰', color: 'yellow', category: 'ai' },
  };

  const renderContent = () => {
    if (isTransitioning) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-8">
            <div className="w-16 h-16 border-4 border-gray-200 border-t-red-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading...</h3>
            <p className="text-gray-600">Please wait while we load the content</p>
          </div>
        </div>
      );
    }

    switch (currentView) {
      case 'tenant-management':
        return <TenantManagement />;
      case 'user-management':
        return <EnhancedUserManager />;
      case 'analytics':
        return <AdvancedAnalyticsDashboard />;
      case 'multi-location':
        return selectedTenant ? (
          <MultiLocationManager
            tenantId={selectedTenant.id}
            tenantName={selectedTenant.name}
          />
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Tenant</h3>
            <p className="text-gray-500">Choose a tenant to manage their locations.</p>
          </div>
        );
      case 'system-health':
        return <SystemHealthDashboard />;
      case 'security-audit':
        return <SecurityAuditDashboard />;
      case 'backup-recovery':
        return <BackupManagement />;
      case 'api-management':
        return <ApiManagementDashboard />;
      case 'billing-plans':
        return <BillingManagement />;
      case 'notifications':
        return <NotificationCenter />;
      case 'performance-monitor':
        return <Phase2CPerformanceMonitor />;
      case 'system-optimization':
        return <Phase2COptimizationDashboard />;
      case 'enterprise-integrations':
        return <Phase2DEnterpriseIntegrations />;
      case 'advanced-automation':
        return <Phase2DAdvancedAutomation />;
      case 'mobile-api-management':
        return <Phase2DMobileAPIManagement />;
      case 'omnichannel-experience':
        return <Phase2DOmnichannelExperience />;
      case 'deployment-readiness':
        return <Phase2DDeploymentReadiness />;
      case 'ai-analytics-dashboard':
        return <Phase3AAIAnalyticsDashboard />;
      case 'predictive-sales-forecasting':
        return <Phase3BPredictiveSalesForecasting />;
      case 'intelligent-inventory-optimization':
        return <Phase3CIntelligentInventoryOptimization />;
      case 'ai-customer-behavior-analysis':
        return <Phase3DAICustomerBehaviorAnalysis />;
      case 'smart-pricing-optimization':
        return <Phase3ESmartPricingOptimization />;
      case 'automated-staff-scheduling':
        return <Phase3FAutomatedStaffScheduling />;
      case 'dashboard':
      default:
        return renderDashboard();
    }
  };

  // Main dashboard layout
  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
              title="Toggle Sidebar"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-red-600 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Super Admin Dashboard
                </h1>
                <p className="text-sm text-gray-500">Multi-Tenant System Management</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-red-100 to-pink-100 text-red-800 rounded-full border border-red-200">
                SUPER ADMIN
              </span>
              {isOnline ? (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </span>
              ) : (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-1"></div>
                  OFFLINE
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                Super Administrator
              </div>
              <div className="text-xs text-gray-500">
                System-wide Access
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 disabled:opacity-50"
                title="Refresh Data"
              >
                <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  autoRefreshEnabled
                    ? 'text-green-600 bg-green-100 hover:bg-green-200'
                    : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                }`}
                title={`${autoRefreshEnabled ? 'Disable' : 'Enable'} Auto-refresh (2min)`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>

              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 shadow-sm"
              >
                <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6">
          <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
            {Object.entries(navigationConfig).map(([key, config]) => {
              const isActive = currentView === key;

              return (
                <button
                  key={key}
                  onClick={() => handleViewChange(key)}
                  disabled={isTransitioning}
                  className={`flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                    isActive
                      ? `border-${config.color}-500 text-${config.color}-600 bg-${config.color}-50`
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  title={config.label}
                >
                  <span className="text-base">{config.icon}</span>
                  <span className="hidden sm:inline">{config.label}</span>
                  {isTransitioning && currentView === key && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-red-600 rounded-full animate-spin ml-1"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="flex-1 overflow-hidden bg-gray-50">
        <div className="h-full p-4">
          <div className="h-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={() => {
                      setError(null);
                      handleViewChange('dashboard');
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                  >
                    Go to Dashboard
                  </button>
                </div>
              </div>
            ) : (
              renderContent()
            )}
          </div>
        </div>
      </main>

      {/* Enhanced Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-6 py-3 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Tenants: <span className="font-bold text-blue-600">{metrics?.totalTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Active: <span className="font-bold text-green-600">{metrics?.activeTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-emerald-400 animate-pulse' : 'bg-red-400'}`}></div>
              <span className="text-sm font-medium text-gray-700">
                {isOnline ? 'Database Connected' : 'Database Offline'}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                View: <span className="font-bold text-purple-600 capitalize">{currentView.replace('-', ' ')}</span>
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
            <div className="text-sm font-mono text-gray-700">
              {new Date().toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}
            </div>
            <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
              v2.0.0 Admin
            </div>
          </div>
        </div>
      </footer>
    </div>
  );

  const renderDashboard = () => {
    return (
      <div className="h-full overflow-auto custom-scrollbar">
        <div className="p-6 space-y-8">
          {/* Enhanced Page Header with Status */}
          <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold mb-2">Super Admin Overview</h2>
                <p className="text-red-100 text-lg">
                  Monitor your multi-tenant POS ecosystem performance and key metrics in real-time
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleRefresh}
                  disabled={loading}
                  className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors disabled:opacity-50 backdrop-blur-sm"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  <span className="text-sm font-medium">Refresh Data</span>
                </button>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="flex items-center">
                  {isOnline ? (
                    <>
                      <Wifi className="h-5 w-5 mr-2" />
                      <span className="font-medium">Database Connected</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="h-5 w-5 mr-2" />
                      <span className="font-medium">Database Offline</span>
                    </>
                  )}
                </div>
                <div className="text-sm text-red-100 mt-1">
                  PostgreSQL Connection Status
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span className="font-medium">Last Updated</span>
                </div>
                <div className="text-sm text-red-100 mt-1">
                  {lastRefresh.toLocaleTimeString()}
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  <span className="font-medium">{metrics?.systemUptime || 99.9}% Uptime</span>
                </div>
                <div className="text-sm text-red-100 mt-1">
                  System Availability
                </div>
              </div>
            </div>

            {error && (
              <div className="mt-4 bg-red-500/20 border border-red-300/50 rounded-lg px-4 py-3 backdrop-blur-sm">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  <span className="font-medium">System Alert: {error}</span>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Key Metrics Grid */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Key Performance Indicators</h3>
              <div className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  Real-time Data
                </span>
                <span className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                  Auto-refresh: {autoRefreshEnabled ? '2min' : 'OFF'}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <EnhancedCard
                title="Total Tenants"
                value={metrics?.totalTenants || 0}
                change={12.5}
                icon={<Users className="h-6 w-6" />}
                loading={loading}
                type="number"
              />
              <EnhancedCard
                title="Active Tenants"
                value={metrics?.activeTenants || 0}
                change={8.2}
                icon={<Activity className="h-6 w-6" />}
                loading={loading}
                type="number"
              />
              <EnhancedCard
                title="Total Revenue"
                value={metrics?.totalRevenue || 0}
                change={15.3}
                icon={<DollarSign className="h-6 w-6" />}
                loading={loading}
                type="currency"
              />
              <EnhancedCard
                title="System Uptime"
                value={metrics?.systemUptime || 0}
                change={0.1}
                icon={<Server className="h-6 w-6" />}
                loading={loading}
                type="percentage"
              />
            </div>
          </div>

          {/* Enhanced Secondary Metrics */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6">System Performance & Health</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <EnhancedCard
                title="Active Users"
                value={metrics?.activeUsers || 0}
                change={-2.1}
                icon={<TrendingUp className="h-6 w-6" />}
                loading={loading}
                type="number"
              />
              <EnhancedCard
                title="Transactions Today"
                value={metrics?.transactionsToday || 0}
                change={22.8}
                icon={<Activity className="h-6 w-6" />}
                loading={loading}
                type="number"
              />
              <EnhancedCard
                title="Avg Response Time"
                value={`${metrics?.averageResponseTime || 0}ms`}
                change={-5.2}
                icon={<Clock className="h-6 w-6" />}
                loading={loading}
              />
              <EnhancedCard
                title="Error Rate"
                value={`${metrics?.errorRate || 0}%`}
                change={-12.3}
                icon={<AlertTriangle className="h-6 w-6" />}
                loading={loading}
                type="percentage"
              />
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
              <span className="px-3 py-1 bg-gray-100 text-gray-700 text-xs font-medium rounded-full">
                Most Used Features
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <button
                onClick={() => handleViewChange('tenant-management')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                    <Users className="h-6 w-6 text-blue-600 group-hover:text-blue-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {metrics?.totalTenants || 0} total
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Manage Tenants</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Add, edit, suspend, or view detailed tenant information and analytics
                </div>
              </button>

              <button
                onClick={() => handleViewChange('analytics')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-green-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                    <BarChart3 className="h-6 w-6 text-green-600 group-hover:text-green-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    Live data
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">View Analytics</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Comprehensive system analytics, revenue trends, and performance insights
                </div>
              </button>

              <button
                onClick={() => handleViewChange('system-health')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                    <Shield className="h-6 w-6 text-purple-600 group-hover:text-purple-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {metrics?.systemUptime || 99.9}% uptime
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">System Health</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Monitor system performance, alerts, and infrastructure status
                </div>
              </button>

              <button
                onClick={() => handleViewChange('billing-plans')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-emerald-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center group-hover:bg-emerald-200 transition-colors duration-200">
                    <DollarSign className="h-6 w-6 text-emerald-600 group-hover:text-emerald-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    ${metrics?.totalRevenue || 0}
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Billing Management</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Manage subscriptions, payments, and billing configurations
                </div>
              </button>

              <button
                onClick={() => handleViewChange('user-management')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors duration-200">
                    <Users className="h-6 w-6 text-indigo-600 group-hover:text-indigo-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {metrics?.activeUsers || 0} active
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">User Management</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Manage user roles, permissions, and access controls
                </div>
              </button>

              <button
                onClick={() => handleViewChange('security-audit')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-red-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors duration-200">
                    <Shield className="h-6 w-6 text-red-600 group-hover:text-red-700" />
                  </div>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    Secure
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Security Audit</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Review security logs, audit trails, and compliance status
                </div>
              </button>
            </div>
          </div>

          {/* Enhanced AI Features Section */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">AI-Powered Intelligence & Analytics</h3>
              <div className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-gradient-to-r from-cyan-100 to-blue-100 text-cyan-800 text-xs font-medium rounded-full">
                  Phase 3 Features
                </span>
                <span className="px-3 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                  AI Powered
                </span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <button
                onClick={() => handleViewChange('ai-analytics-dashboard')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-cyan-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center group-hover:bg-cyan-200 transition-colors duration-200">
                    <Brain className="h-6 w-6 text-cyan-600 group-hover:text-cyan-700" />
                  </div>
                  <span className="text-xs text-white bg-cyan-500 px-2 py-1 rounded-full font-medium">
                    PHASE 3A
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">AI Analytics Dashboard</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  Machine learning insights and intelligent recommendations
                </div>
              </button>

              <button
                onClick={() => handleViewChange('predictive-sales-forecasting')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-emerald-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center group-hover:bg-emerald-200 transition-colors duration-200">
                    <TrendingUp className="h-6 w-6 text-emerald-600 group-hover:text-emerald-700" />
                  </div>
                  <span className="text-xs text-white bg-emerald-500 px-2 py-1 rounded-full font-medium">
                    PHASE 3B
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Predictive Sales Forecasting</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  AI-powered revenue predictions and demand forecasting
                </div>
              </button>

              <button
                onClick={() => handleViewChange('intelligent-inventory-optimization')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-violet-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center group-hover:bg-violet-200 transition-colors duration-200">
                    <Target className="h-6 w-6 text-violet-600 group-hover:text-violet-700" />
                  </div>
                  <span className="text-xs text-white bg-violet-500 px-2 py-1 rounded-full font-medium">
                    PHASE 3C
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Intelligent Inventory Optimization</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  AI-powered inventory management and optimization
                </div>
              </button>

              <button
                onClick={() => handleViewChange('smart-pricing-optimization')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-amber-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center group-hover:bg-amber-200 transition-colors duration-200">
                    <DollarSign className="h-6 w-6 text-amber-600 group-hover:text-amber-700" />
                  </div>
                  <span className="text-xs text-white bg-amber-500 px-2 py-1 rounded-full font-medium">
                    PHASE 3E
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Smart Pricing Optimization</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  AI-powered dynamic pricing and revenue optimization
                </div>
              </button>

              <button
                onClick={() => handleViewChange('automated-staff-scheduling')}
                disabled={isTransitioning}
                className="group p-6 text-left border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors duration-200">
                    <Users className="h-6 w-6 text-indigo-600 group-hover:text-indigo-700" />
                  </div>
                  <span className="text-xs text-white bg-indigo-500 px-2 py-1 rounded-full font-medium">
                    PHASE 3F
                  </span>
                </div>
                <div className="font-bold text-gray-900 mb-2 text-lg">Automated Staff Scheduling</div>
                <div className="text-sm text-gray-600 leading-relaxed">
                  AI-powered workforce management and predictive scheduling
                </div>
              </button>
            </div>
          </div>
        </div>
      );
    };

  // Return the main dashboard layout
  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200"
              title="Toggle Sidebar"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-red-600 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Super Admin Dashboard
                </h1>
                <p className="text-sm text-gray-500">Multi-Tenant System Management</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-red-100 to-pink-100 text-red-800 rounded-full border border-red-200">
                SUPER ADMIN
              </span>
              {isOnline ? (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                  LIVE
                </span>
              ) : (
                <span className="flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  <div className="w-2 h-2 bg-red-400 rounded-full mr-1"></div>
                  OFFLINE
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900">
                Super Administrator
              </div>
              <div className="text-xs text-gray-500">
                System-wide Access
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200 disabled:opacity-50"
                title="Refresh Data"
              >
                <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 shadow-sm"
              >
                <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200 shadow-sm">
        <div className="px-6">
          <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
            {Object.entries(navigationConfig).map(([key, config]) => {
              const isActive = currentView === key;

              return (
                <button
                  key={key}
                  onClick={() => handleViewChange(key)}
                  disabled={isTransitioning}
                  className={`flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                    isActive
                      ? `border-${config.color}-500 text-${config.color}-600 bg-${config.color}-50`
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  title={config.label}
                >
                  <span className="text-base">{config.icon}</span>
                  <span className="hidden sm:inline">{config.label}</span>
                  {isTransitioning && currentView === key && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-red-600 rounded-full animate-spin ml-1"></div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="flex-1 overflow-hidden bg-gray-50">
        <div className="h-full p-4">
          <div className="h-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Something went wrong</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={() => {
                      setError(null);
                      handleViewChange('dashboard');
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                  >
                    Go to Dashboard
                  </button>
                </div>
              </div>
            ) : (
              renderContent()
            )}
          </div>
        </div>
      </main>

      {/* Enhanced Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-6 py-3 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Tenants: <span className="font-bold text-blue-600">{metrics?.totalTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                Active: <span className="font-bold text-green-600">{metrics?.activeTenants || 0}</span>
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-emerald-400 animate-pulse' : 'bg-red-400'}`}></div>
              <span className="text-sm font-medium text-gray-700">
                {isOnline ? 'Database Connected' : 'Database Offline'}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">
                View: <span className="font-bold text-purple-600 capitalize">{currentView.replace('-', ' ')}</span>
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
            <div className="text-sm font-mono text-gray-700">
              {new Date().toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}
            </div>
            <div className="px-2 py-1 bg-gray-100 rounded text-xs font-medium text-gray-600">
              v2.0.0 Admin
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
