<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Security Test</title>
</head>
<body>
    <h1>Super Admin Login</h1>
    <form id="loginForm">
        <label for="pin">PIN:</label>
        <input type="password" id="pin" name="pin" required>
        <button type="submit">Login</button>
    </form>
    <div id="response"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(event) {
            event.preventDefault();
            const pin = document.getElementById('pin').value;
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = '';

            try {
                const response = await fetch('http://localhost:4000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pin })
                });

                const data = await response.json();

                if (response.ok) {
                    responseDiv.innerHTML = `<p>Login successful!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    console.log('Login successful:', data);
                } else {
                    responseDiv.innerHTML = `<p>Login failed!</p><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    console.error('Login failed:', data);
                }
            } catch (error) {
                responseDiv.innerHTML = `<p>An error occurred:</p><pre>${error.message}</pre>`;
                console.error('An error occurred:', error);
            }
        });
    </script>
</body>
</html>