const db = require('./pool');

async function runMigrations() {
  try {
    // Drop tables if they exist
    await db.query(`DROP TABLE IF EXISTS employees CASCADE;`);
    await db.query(`DROP TABLE IF EXISTS tenants CASCADE;`);

    // Create tenants table
    await db.query(`
      CREATE TABLE IF NOT EXISTS tenants (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create employees table with updated_at column
    await db.query(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        tenant_id INTEGER REFERENCES tenants(id),
        name VARCHAR(255) NOT NULL,
        pin VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL,
        permissions JSONB DEFAULT '[]',
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert demo data
    await db.query(`
      INSERT INTO tenants (name, slug) 
      VALUES ('Demo Restaurant', 'demo-restaurant')
      ON CONFLICT (slug) DO NOTHING;
    `);

    const demoTenant = await db.query(`SELECT id FROM tenants WHERE slug = 'demo-restaurant';`);
    const tenantId = demoTenant.rows[0].id;

    // Insert demo employees
    const employees = [
      { name: 'Enhanced Admin', pin: '999999', role: 'super_admin' },
      { name: 'Enhanced Employee', pin: '123456', role: 'employee' },
      { name: 'Main Admin', pin: '888888', role: 'super_admin' },
      { name: 'Main Employee', pin: '234567', role: 'employee' }
    ];

    for (const emp of employees) {
      await db.query(`
        INSERT INTO employees (tenant_id, name, pin, role)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (id) DO NOTHING;
      `, [tenantId, emp.name, emp.pin, emp.role]);
    }

    console.log('✅ Migrations completed successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

runMigrations().catch(console.error);
