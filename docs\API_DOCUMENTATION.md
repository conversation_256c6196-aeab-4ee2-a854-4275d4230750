# BARPOS API Documentation

## 🚀 Overview

The BARPOS API provides comprehensive endpoints for managing a multi-tenant Point of Sale system with advanced features including AI analytics, global compliance, and enterprise-grade security.

**Base URL**: `http://localhost:4000/api`
**Version**: 7.0.0
**Authentication**: JWT Bearer Token

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [Core POS Endpoints](#core-pos-endpoints)
3. [Admin Endpoints](#admin-endpoints)
4. [Tenant Management](#tenant-management)
5. [Payment Processing](#payment-processing)
6. [Analytics & Reporting](#analytics--reporting)
7. [AI & Automation](#ai--automation)
8. [Global Features](#global-features)
9. [Error Handling](#error-handling)
10. [Rate Limiting](#rate-limiting)

## 🔐 Authentication

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "pin": "123456",
  "tenant_slug": "demo-restaurant"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "emp_123",
    "name": "John Doe",
    "role": "manager",
    "tenantId": "1",
    "permissions": ["pos_access", "reports_view"]
  }
}
```

### Token Verification
```http
GET /api/auth/verify
Authorization: Bearer <token>
```

### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```

## 🛒 Core POS Endpoints

### Products

#### Get Products
```http
GET /api/products
Authorization: Bearer <token>
```

#### Get Categories
```http
GET /api/categories
Authorization: Bearer <token>
```

### Orders

#### Create Order
```http
POST /api/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {
      "product_id": "prod_123",
      "quantity": 2,
      "price": 15.99,
      "modifiers": []
    }
  ],
  "customer_info": {
    "name": "John Smith",
    "phone": "555-0123"
  },
  "order_type": "dine_in",
  "table_id": "table_5"
}
```

#### Get Orders
```http
GET /api/orders?limit=50&status=completed&date=2024-01-15
Authorization: Bearer <token>
```

### Inventory

#### Get Inventory
```http
GET /api/inventory
Authorization: Bearer <token>
```

#### Update Stock
```http
PUT /api/inventory/:productId
Authorization: Bearer <token>
Content-Type: application/json

{
  "current_stock": 25,
  "minimum_stock": 5
}
```

## 👑 Admin Endpoints

### Super Admin Dashboard

#### Get System Metrics
```http
GET /api/admin/metrics
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalTenants": 156,
    "activeTenants": 142,
    "totalRevenue": 2847392.50,
    "systemUptime": 99.9,
    "activeUsers": 1247
  }
}
```

#### Get System Activity
```http
GET /api/admin/activity?limit=100
Authorization: Bearer <token>
```

#### Get Tenants
```http
GET /api/admin/tenants?page=1&limit=20&status=active
Authorization: Bearer <token>
```

### User Management

#### Get Users
```http
GET /api/admin/users?page=1&limit=50&role=manager
Authorization: Bearer <token>
```

#### Create User
```http
POST /api/admin/users
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Jane Doe",
  "email": "<EMAIL>",
  "role": "manager",
  "tenant_id": "1",
  "permissions": ["pos_access", "reports_view"]
}
```

## 🏢 Tenant Management

### Tenant Operations

#### Create Tenant
```http
POST /api/admin/tenants
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New Restaurant",
  "slug": "new-restaurant",
  "email": "<EMAIL>",
  "plan": "pro",
  "features": {
    "ai_analytics": true,
    "multi_location": false,
    "advanced_reporting": true
  }
}
```

#### Update Tenant Status
```http
PUT /api/admin/tenants/:id/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "active"
}
```

#### Update Tenant Features
```http
PUT /api/admin/tenants/:id/features
Authorization: Bearer <token>
Content-Type: application/json

{
  "features": {
    "ai_analytics": true,
    "multi_location": true
  }
}
```

## 💳 Payment Processing

### Payment Methods

#### Get Payment Methods
```http
GET /api/payments/methods
Authorization: Bearer <token>
```

### Process Payment

#### Standard Payment
```http
POST /api/payments/process
Authorization: Bearer <token>
Content-Type: application/json

{
  "order_id": "order_123",
  "amount": 45.99,
  "payment_method": "card",
  "card_details": {
    "number": "****************",
    "expiry": "12/25",
    "cvv": "123"
  },
  "customer_info": {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
}
```

#### Enhanced Payment (Phase 4)
```http
POST /api/payments/process/enhanced
Authorization: Bearer <token>
Content-Type: application/json

{
  "order_id": "order_123",
  "payment_splits": [
    {
      "method": "card",
      "amount": 30.00
    },
    {
      "method": "cash",
      "amount": 15.99
    }
  ],
  "receipt_options": {
    "email": true,
    "print": true,
    "sms": false
  }
}
```

### Payment History

#### Get Payment History
```http
GET /api/payments/history?limit=50&status=completed
Authorization: Bearer <token>
```

#### Process Refund
```http
POST /api/payments/refund
Authorization: Bearer <token>
Content-Type: application/json

{
  "transaction_id": "txn_123",
  "refund_amount": 25.00,
  "refund_reason": "Customer request",
  "refund_type": "partial"
}
```

## 📊 Analytics & Reporting

### Sales Analytics

#### Get Sales Analytics
```http
GET /api/analytics/sales?period=today&location=all
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "today",
    "total_sales": 2450.75,
    "total_orders": 45,
    "average_order_value": 54.46,
    "top_products": [
      {
        "product_id": "prod_123",
        "name": "Grilled Chicken",
        "quantity_sold": 15,
        "revenue": 239.85
      }
    ]
  }
}
```

#### Get Customer Analytics
```http
GET /api/analytics/customers?range=30d
Authorization: Bearer <token>
```

### Advanced Analytics

#### Get Table Performance
```http
GET /api/analytics/table-performance?range=7d
Authorization: Bearer <token>
```

#### Get Enterprise Analytics
```http
GET /api/enterprise/analytics?range=30d&location=all
Authorization: Bearer <token>
```

## 🤖 AI & Automation

### Fraud Detection

#### Analyze Transaction
```http
POST /api/ai/fraud/analyze-transaction
Authorization: Bearer <token>
Content-Type: application/json

{
  "transaction_id": "txn_123",
  "amount": 150.00,
  "payment_method": "card",
  "customer_behavior": {
    "order_frequency": "high",
    "location_consistency": "low"
  }
}
```

### Predictive Analytics

#### Get Sales Forecast
```http
GET /api/ai/predictions/sales-forecast?period=7d
Authorization: Bearer <token>
```

#### Get Demand Forecast
```http
GET /api/ai/predictions/demand-forecast?product_id=prod_123
Authorization: Bearer <token>
```

#### Get Inventory Recommendations
```http
GET /api/ai/predictions/inventory-recommendations
Authorization: Bearer <token>
```

### Automation Workflows

#### Get Workflows
```http
GET /api/ai/automation/workflows
Authorization: Bearer <token>
```

#### Create Workflow
```http
POST /api/ai/automation/create-workflow
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Low Stock Alert",
  "trigger": "inventory_low",
  "conditions": {
    "stock_level": "< 5"
  },
  "actions": [
    {
      "type": "email",
      "recipient": "<EMAIL>",
      "template": "low_stock_alert"
    }
  ]
}
```

## 🌍 Global Features

### Currency Management

#### Get Supported Currencies
```http
GET /api/global/currencies/supported
Authorization: Bearer <token>
```

#### Get Exchange Rates
```http
GET /api/global/currencies/exchange-rates?base=USD
Authorization: Bearer <token>
```

#### Convert Currency
```http
POST /api/global/currencies/convert
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 100.00,
  "from_currency": "USD",
  "to_currency": "EUR"
}
```

### International Payments

#### Process International Payment
```http
POST /api/global/payments/process-international
Authorization: Bearer <token>
Content-Type: application/json

{
  "order_id": "order_123",
  "amount": 100.00,
  "currency": "EUR",
  "payment_method": "card",
  "region": "EU"
}
```

### Compliance

#### Validate Compliance
```http
POST /api/global/compliance/validate
Authorization: Bearer <token>
Content-Type: application/json

{
  "data_type": "customer_data",
  "region": "EU",
  "processing_purpose": "order_fulfillment"
}
```

## ❌ Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "statusCode": 400,
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/users",
  "method": "POST"
}
```

### Common Error Codes
- `VALIDATION_ERROR` (400) - Invalid request data
- `UNAUTHORIZED` (401) - Invalid or missing authentication
- `FORBIDDEN` (403) - Insufficient permissions
- `NOT_FOUND` (404) - Resource not found
- `CONFLICT` (409) - Resource already exists
- `RATE_LIMITED` (429) - Too many requests
- `INTERNAL_ERROR` (500) - Server error
- `DATABASE_ERROR` (500) - Database operation failed

## 🚦 Rate Limiting

### Limits
- **Authentication**: 10 requests per minute per IP
- **General API**: 100 requests per minute per user
- **Payment Processing**: 20 requests per minute per tenant
- **Analytics**: 50 requests per minute per user

### Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## 📝 Notes

### Authentication Requirements
- All endpoints except `/api/auth/login` require authentication
- Super admin endpoints require `super_admin` role
- Tenant admin endpoints require `tenant_admin` or `super_admin` role

### Data Formats
- All timestamps are in ISO 8601 format
- All monetary amounts are in decimal format with 2 decimal places
- All IDs use UUID format unless specified otherwise

### Pagination
- Default page size: 20 items
- Maximum page size: 100 items
- Use `page` and `limit` query parameters

### Versioning
- API version is included in response headers: `X-API-Version: 7.0.0`
- Breaking changes will increment major version
- Backward compatibility maintained for minor versions
