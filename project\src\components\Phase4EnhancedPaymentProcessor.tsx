import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  Banknote, 
  Gift,
  Loader2,
  CheckCircle,
  XCircle,
  Calculator,
  Percent,
  DollarSign,
  Users,
  Clock,
  Receipt,
  Printer,
  ArrowLeft,
  AlertTriangle,
  Zap,
  Shield,
  TrendingUp
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface PaymentMethod {
  id: string;
  name: string;
  display_name: string;
  provider: string;
  processing_fee_percentage: number;
  processing_fee_fixed: number;
  requires_authorization: boolean;
  supports_tips: boolean;
  supports_split_payment: boolean;
  supports_refunds: boolean;
  min_amount: number;
  max_amount?: number;
}

interface SplitPayment {
  customer_name?: string;
  amount: number;
  percentage: number;
  payment_method_id: string;
  tip_amount: number;
}

interface PaymentProcessorProps {
  orderData: {
    id?: string;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
      total: number;
    }>;
    subtotal: number;
    tax: number;
    total: number;
    tableId?: string;
    tableNumber?: number;
    guestCount?: number;
    serverName?: string;
  };
  onPaymentComplete: (result: any) => void;
  onCancel: () => void;
}

const Phase4EnhancedPaymentProcessor: React.FC<PaymentProcessorProps> = ({
  orderData,
  onPaymentComplete,
  onCancel
}) => {
  const { apiCall } = useEnhancedAppContext();
  
  // State management
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [tipAmount, setTipAmount] = useState(0);
  const [tipPercentage, setTipPercentage] = useState(0);
  const [customTipAmount, setCustomTipAmount] = useState('');
  const [splitPayments, setSplitPayments] = useState<SplitPayment[]>([]);
  const [isSplitPayment, setIsSplitPayment] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingTime, setProcessingTime] = useState(0);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<'method' | 'details' | 'processing' | 'complete'>('method');
  
  // Customer info
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });
  
  // Receipt options
  const [receiptOptions, setReceiptOptions] = useState({
    print: true,
    email: false,
    sms: false
  });

  // Load payment methods on component mount
  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      const response = await apiCall('/api/payments/methods/enhanced');
      if (response.ok) {
        const data = await response.json();
        setPaymentMethods(data.methods || []);
        
        // Auto-select cash as default
        const cashMethod = data.methods?.find((m: PaymentMethod) => m.name === 'cash');
        if (cashMethod) {
          setSelectedPaymentMethod(cashMethod);
        }
      }
    } catch (error) {
      console.error('Failed to load payment methods:', error);
    }
  };

  const calculateProcessingFee = () => {
    if (!selectedPaymentMethod) return 0;
    
    const percentageFee = orderData.total * selectedPaymentMethod.processing_fee_percentage;
    const fixedFee = selectedPaymentMethod.processing_fee_fixed;
    return percentageFee + fixedFee;
  };

  const calculateFinalTotal = () => {
    return orderData.total + tipAmount + calculateProcessingFee();
  };

  const handleTipPercentage = (percentage: number) => {
    setTipPercentage(percentage);
    const calculatedTip = orderData.total * (percentage / 100);
    setTipAmount(calculatedTip);
    setCustomTipAmount(calculatedTip.toFixed(2));
  };

  const handleCustomTip = (value: string) => {
    setCustomTipAmount(value);
    const tip = parseFloat(value) || 0;
    setTipAmount(tip);
    setTipPercentage((tip / orderData.total) * 100);
  };

  const processPayment = async () => {
    if (!selectedPaymentMethod) {
      alert('Please select a payment method');
      return;
    }

    setIsProcessing(true);
    setCurrentStep('processing');
    const startTime = Date.now();

    try {
      const paymentData = {
        order_id: orderData.id || `order_${Date.now()}`,
        order_data: orderData,
        payment_method_id: selectedPaymentMethod.id,
        payment_method_name: selectedPaymentMethod.display_name,
        subtotal: orderData.subtotal,
        tax_amount: orderData.tax,
        tip_amount: tipAmount,
        total_amount: orderData.total,
        customer_info: customerInfo,
        receipt_options: receiptOptions,
        split_payments: isSplitPayment ? splitPayments : null,
        metadata: {
          table_id: orderData.tableId,
          table_number: orderData.tableNumber,
          guest_count: orderData.guestCount,
          server_name: orderData.serverName
        }
      };

      const response = await apiCall('/api/payments/process/enhanced', {
        method: 'POST',
        body: JSON.stringify(paymentData)
      });

      const result = await response.json();
      const endTime = Date.now();
      setProcessingTime(endTime - startTime);

      if (result.success) {
        setPaymentResult(result);
        setCurrentStep('complete');
        
        // Auto-print receipt if enabled
        if (receiptOptions.print) {
          await printReceipt(result.receipt_data);
        }
        
        // Complete payment after 3 seconds
        setTimeout(() => {
          onPaymentComplete(result);
        }, 3000);
      } else {
        throw new Error(result.error || 'Payment processing failed');
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      alert(`Payment failed: ${error.message}`);
      setCurrentStep('method');
    } finally {
      setIsProcessing(false);
    }
  };

  const printReceipt = async (receiptData: any) => {
    try {
      // This would integrate with actual receipt printer
      console.log('🖨️ Printing receipt:', receiptData);
      
      // Mock receipt printing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return { success: true };
    } catch (error) {
      console.error('Receipt printing error:', error);
      return { success: false, error: error.message };
    }
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    switch (method.provider) {
      case 'cash':
        return <Banknote className="h-6 w-6" />;
      case 'stripe':
      case 'moneris':
        return <CreditCard className="h-6 w-6" />;
      case 'digital_wallet':
        return <Smartphone className="h-6 w-6" />;
      default:
        return <Gift className="h-6 w-6" />;
    }
  };

  const getProviderBadge = (provider: string) => {
    const badges = {
      stripe: { color: 'bg-purple-100 text-purple-800', text: 'Stripe' },
      moneris: { color: 'bg-red-100 text-red-800', text: 'Moneris (CA)' },
      cash: { color: 'bg-green-100 text-green-800', text: 'Cash' },
      digital_wallet: { color: 'bg-blue-100 text-blue-800', text: 'Digital' }
    };
    
    const badge = badges[provider as keyof typeof badges] || { color: 'bg-gray-100 text-gray-800', text: provider };
    
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
        {badge.text}
      </span>
    );
  };

  // Render payment method selection
  const renderMethodSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Select Payment Method</h3>
        <p className="text-sm text-gray-600">Choose how you'd like to process this payment</p>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`relative rounded-lg border-2 p-4 cursor-pointer transition-all ${
              selectedPaymentMethod?.id === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedPaymentMethod(method)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${
                  selectedPaymentMethod?.id === method.id ? 'bg-blue-100' : 'bg-gray-100'
                }`}>
                  {getPaymentMethodIcon(method)}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{method.display_name}</h4>
                  <div className="flex items-center space-x-2 mt-1">
                    {getProviderBadge(method.provider)}
                    {method.processing_fee_percentage > 0 && (
                      <span className="text-xs text-gray-500">
                        {(method.processing_fee_percentage * 100).toFixed(1)}% + ${method.processing_fee_fixed}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {method.supports_tips && (
                  <div className="flex items-center text-green-600">
                    <DollarSign className="h-4 w-4" />
                    <span className="text-xs">Tips</span>
                  </div>
                )}
                {method.supports_split_payment && (
                  <div className="flex items-center text-blue-600">
                    <Users className="h-4 w-4" />
                    <span className="text-xs">Split</span>
                  </div>
                )}
                {selectedPaymentMethod?.id === method.id && (
                  <CheckCircle className="h-5 w-5 text-blue-500" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedPaymentMethod && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">Payment Details</h4>
          
          {/* Tip Calculator */}
          {selectedPaymentMethod.supports_tips && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add Tip
              </label>
              <div className="grid grid-cols-4 gap-2 mb-3">
                {[15, 18, 20, 25].map((percentage) => (
                  <button
                    key={percentage}
                    onClick={() => handleTipPercentage(percentage)}
                    className={`px-3 py-2 text-sm rounded-md border ${
                      tipPercentage === percentage
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {percentage}%
                  </button>
                ))}
              </div>
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-gray-400" />
                <input
                  type="number"
                  step="0.01"
                  value={customTipAmount}
                  onChange={(e) => handleCustomTip(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Custom tip amount"
                />
              </div>
            </div>
          )}

          {/* Payment Summary */}
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>${orderData.subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax:</span>
              <span>${orderData.tax.toFixed(2)}</span>
            </div>
            {tipAmount > 0 && (
              <div className="flex justify-between">
                <span>Tip:</span>
                <span>${tipAmount.toFixed(2)}</span>
              </div>
            )}
            {calculateProcessingFee() > 0 && (
              <div className="flex justify-between text-gray-600">
                <span>Processing Fee:</span>
                <span>${calculateProcessingFee().toFixed(2)}</span>
              </div>
            )}
            <div className="flex justify-between font-semibold text-lg border-t pt-2">
              <span>Total:</span>
              <span>${calculateFinalTotal().toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}

      <div className="flex space-x-3">
        <button
          onClick={onCancel}
          className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={() => setCurrentStep('details')}
          disabled={!selectedPaymentMethod}
          className="flex-1 px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Continue
        </button>
      </div>
    </div>
  );

  // Render payment details and confirmation
  const renderPaymentDetails = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Confirm Payment</h3>
        <p className="text-sm text-gray-600">Review details and process payment</p>
      </div>

      {/* Selected Payment Method */}
      <div className="bg-blue-50 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            {getPaymentMethodIcon(selectedPaymentMethod!)}
          </div>
          <div>
            <h4 className="font-medium text-gray-900">{selectedPaymentMethod?.display_name}</h4>
            {getProviderBadge(selectedPaymentMethod?.provider || '')}
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Customer Information (Optional)</h4>
        <div className="grid grid-cols-1 gap-3">
          <input
            type="text"
            placeholder="Customer Name"
            value={customerInfo.name}
            onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <input
            type="email"
            placeholder="Email Address"
            value={customerInfo.email}
            onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Receipt Options */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Receipt Options</h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={receiptOptions.print}
              onChange={(e) => setReceiptOptions({...receiptOptions, print: e.target.checked})}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Print Receipt</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={receiptOptions.email}
              onChange={(e) => setReceiptOptions({...receiptOptions, email: e.target.checked})}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Email Receipt</span>
          </label>
        </div>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setCurrentStep('method')}
          className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 inline mr-2" />
          Back
        </button>
        <button
          onClick={processPayment}
          disabled={isProcessing}
          className="flex-1 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 inline mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4 inline mr-2" />
              Process Payment ${calculateFinalTotal().toFixed(2)}
            </>
          )}
        </button>
      </div>
    </div>
  );

  // Render processing state
  const renderProcessing = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <div className="relative">
          <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />
          <div className="absolute inset-0 flex items-center justify-center">
            <Zap className="h-6 w-6 text-blue-600" />
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing Payment</h3>
        <p className="text-sm text-gray-600">Please wait while we process your payment...</p>
        <p className="text-xs text-gray-500 mt-2">Target: &lt;3 seconds</p>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm">
          <span>Payment Method:</span>
          <span className="font-medium">{selectedPaymentMethod?.display_name}</span>
        </div>
        <div className="flex items-center justify-between text-sm mt-2">
          <span>Amount:</span>
          <span className="font-medium">${calculateFinalTotal().toFixed(2)}</span>
        </div>
      </div>
    </div>
  );

  // Render completion state
  const renderComplete = () => (
    <div className="text-center space-y-6">
      <div className="flex justify-center">
        <div className="relative">
          <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="h-10 w-10 text-green-500" />
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Successful!</h3>
        <p className="text-sm text-gray-600">Your payment has been processed successfully</p>
      </div>

      {paymentResult && (
        <div className="bg-green-50 rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>Transaction ID:</span>
            <span className="font-mono text-xs">{paymentResult.transaction_id}</span>
          </div>
          {paymentResult.authorization_code && (
            <div className="flex items-center justify-between text-sm">
              <span>Authorization:</span>
              <span className="font-mono text-xs">{paymentResult.authorization_code}</span>
            </div>
          )}
          <div className="flex items-center justify-between text-sm">
            <span>Processing Time:</span>
            <span className={`font-medium ${processingTime < 3000 ? 'text-green-600' : 'text-yellow-600'}`}>
              {processingTime}ms
              {processingTime < 3000 && <Shield className="h-4 w-4 inline ml-1" />}
            </span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span>Amount Charged:</span>
            <span className="font-semibold">${calculateFinalTotal().toFixed(2)}</span>
          </div>
        </div>
      )}

      {receiptOptions.print && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
          <Printer className="h-4 w-4" />
          <span>Receipt is printing...</span>
        </div>
      )}

      <p className="text-xs text-gray-500">This window will close automatically in 3 seconds</p>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              <h2 className="text-xl font-semibold text-gray-900">Phase 4 Payment</h2>
            </div>
            {currentStep !== 'processing' && (
              <button
                onClick={onCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="h-6 w-6" />
              </button>
            )}
          </div>

          {/* Progress Indicator */}
          <div className="flex items-center justify-center space-x-2 mb-6">
            {['method', 'details', 'processing', 'complete'].map((step, index) => (
              <div
                key={step}
                className={`h-2 w-8 rounded-full ${
                  currentStep === step
                    ? 'bg-blue-500'
                    : index < ['method', 'details', 'processing', 'complete'].indexOf(currentStep)
                    ? 'bg-green-500'
                    : 'bg-gray-200'
                }`}
              />
            ))}
          </div>

          {/* Content */}
          {currentStep === 'method' && renderMethodSelection()}
          {currentStep === 'details' && renderPaymentDetails()}
          {currentStep === 'processing' && renderProcessing()}
          {currentStep === 'complete' && renderComplete()}
        </div>
      </div>
    </div>
  );
};

export default Phase4EnhancedPaymentProcessor;
