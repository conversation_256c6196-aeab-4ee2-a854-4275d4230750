describe('Offline Mode and Cache Sync Tests', () => {
  before(() => {
    cy.visit('/');
    cy.get('input[type="text"]', { timeout: 10000 }).should('be.visible').type('1234'); // Login PIN
    cy.get('button', { timeout: 10000 }).contains('Login').click();
    cy.contains('POS', { timeout: 10000 }).should('be.visible');
  });

  it('should allow browsing products offline from cache', () => {
    cy.log('Simulating offline mode');
    cy.window().then((win) => {
      win.navigator.__defineGetter__('onLine', () => false);
    });

    cy.reload();

    cy.get('.product-grid').should('be.visible');
    cy.get('.product-item').should('have.length.greaterThan', 0);
  });

  it('should queue orders placed offline and sync when back online', () => {
    cy.log('Simulating offline mode');
    cy.window().then((win) => {
      win.navigator.__defineGetter__('onLine', () => false);
    });

    cy.get('.product-item').first().click();
    cy.get('button').contains('Checkout').click();
    cy.get('button').contains('Pay').click();

    cy.contains(/order queued|offline mode/i).should('be.visible');

    cy.log('Simulating back online');
    cy.window().then((win) => {
      win.navigator.__defineGetter__('onLine', () => true);
    });

    cy.intercept('POST', '/api/orders', { statusCode: 201, body: { id: 'order123' } }).as('postOrder');

    cy.get('button').contains(/sync orders|retry/i).click();

    cy.wait('@postOrder');

    cy.contains('Receipt').should('be.visible');
  });
});
