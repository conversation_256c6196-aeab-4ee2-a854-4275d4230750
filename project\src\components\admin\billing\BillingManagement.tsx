import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  DollarSign,
  Calendar,
  Users,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Package,
  Zap
} from 'lucide-react';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface SubscriptionPlan {
  id: number;
  plan_name: string;
  plan_code: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: Record<string, boolean>;
  limits: Record<string, number>;
  is_active: boolean;
  trial_days: number;
}

interface TenantSubscription {
  id: number;
  tenant_id: number;
  tenant_name: string;
  plan_name: string;
  status: 'trial' | 'active' | 'suspended' | 'cancelled' | 'expired';
  billing_cycle: 'monthly' | 'yearly';
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  auto_renew: boolean;
  mrr: number; // Monthly Recurring Revenue
}

interface Invoice {
  id: number;
  tenant_name: string;
  invoice_number: string;
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  amount_due: number;
  amount_paid: number;
  currency: string;
  due_date: string;
  paid_at?: string;
  created_at: string;
}

interface BillingMetrics {
  total_mrr: number;
  total_arr: number;
  active_subscriptions: number;
  trial_subscriptions: number;
  churn_rate: number;
  avg_revenue_per_user: number;
  outstanding_invoices: number;
  overdue_amount: number;
}

export function BillingManagement() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [subscriptions, setSubscriptions] = useState<TenantSubscription[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [metrics, setMetrics] = useState<BillingMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'subscriptions' | 'invoices' | 'plans'>('overview');

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock subscription plans
      const mockPlans: SubscriptionPlan[] = [
        {
          id: 1,
          plan_name: 'Starter',
          plan_code: 'starter',
          description: 'Perfect for small restaurants',
          price_monthly: 29.99,
          price_yearly: 299.99,
          features: {
            pos: true,
            basic_analytics: true,
            email_support: true,
            multi_location: false,
            advanced_analytics: false
          },
          limits: {
            api_calls: 1000,
            users: 5,
            locations: 1,
            storage_gb: 1
          },
          is_active: true,
          trial_days: 14
        },
        {
          id: 2,
          plan_name: 'Professional',
          plan_code: 'professional',
          description: 'For growing restaurant chains',
          price_monthly: 79.99,
          price_yearly: 799.99,
          features: {
            pos: true,
            basic_analytics: true,
            advanced_analytics: true,
            email_support: true,
            multi_location: true,
            priority_support: true
          },
          limits: {
            api_calls: 10000,
            users: 25,
            locations: 5,
            storage_gb: 10
          },
          is_active: true,
          trial_days: 14
        },
        {
          id: 3,
          plan_name: 'Enterprise',
          plan_code: 'enterprise',
          description: 'For large restaurant enterprises',
          price_monthly: 199.99,
          price_yearly: 1999.99,
          features: {
            pos: true,
            basic_analytics: true,
            advanced_analytics: true,
            multi_location: true,
            white_label: true,
            dedicated_support: true,
            custom_integrations: true
          },
          limits: {
            api_calls: 100000,
            users: 100,
            locations: 50,
            storage_gb: 100
          },
          is_active: true,
          trial_days: 30
        }
      ];

      // Mock tenant subscriptions
      const mockSubscriptions: TenantSubscription[] = [
        {
          id: 1,
          tenant_id: 1,
          tenant_name: 'Demo Restaurant',
          plan_name: 'Professional',
          status: 'active',
          billing_cycle: 'monthly',
          current_period_start: '2024-12-01',
          current_period_end: '2024-12-31',
          auto_renew: true,
          mrr: 79.99
        },
        {
          id: 2,
          tenant_id: 2,
          tenant_name: 'Pizza Palace',
          plan_name: 'Starter',
          status: 'trial',
          billing_cycle: 'monthly',
          current_period_start: '2024-12-01',
          current_period_end: '2024-12-15',
          trial_end: '2024-12-15',
          auto_renew: true,
          mrr: 0
        },
        {
          id: 3,
          tenant_id: 3,
          tenant_name: 'Burger Chain Inc',
          plan_name: 'Enterprise',
          status: 'active',
          billing_cycle: 'yearly',
          current_period_start: '2024-01-01',
          current_period_end: '2024-12-31',
          auto_renew: true,
          mrr: 166.66 // 1999.99 / 12
        },
        {
          id: 4,
          tenant_id: 4,
          tenant_name: 'Cafe Corner',
          plan_name: 'Professional',
          status: 'suspended',
          billing_cycle: 'monthly',
          current_period_start: '2024-11-01',
          current_period_end: '2024-11-30',
          auto_renew: false,
          mrr: 0
        }
      ];

      // Mock invoices
      const mockInvoices: Invoice[] = [
        {
          id: 1,
          tenant_name: 'Demo Restaurant',
          invoice_number: 'INV-2024-001',
          status: 'paid',
          amount_due: 79.99,
          amount_paid: 79.99,
          currency: 'USD',
          due_date: '2024-12-01',
          paid_at: '2024-11-28T10:30:00Z',
          created_at: '2024-11-25T00:00:00Z'
        },
        {
          id: 2,
          tenant_name: 'Burger Chain Inc',
          invoice_number: 'INV-2024-002',
          status: 'pending',
          amount_due: 1999.99,
          amount_paid: 0,
          currency: 'USD',
          due_date: '2024-12-31',
          created_at: '2024-12-01T00:00:00Z'
        },
        {
          id: 3,
          tenant_name: 'Cafe Corner',
          invoice_number: 'INV-2024-003',
          status: 'failed',
          amount_due: 79.99,
          amount_paid: 0,
          currency: 'USD',
          due_date: '2024-11-30',
          created_at: '2024-11-25T00:00:00Z'
        }
      ];

      // Mock billing metrics
      const mockMetrics: BillingMetrics = {
        total_mrr: 326.64, // Sum of active MRR
        total_arr: 3919.68, // MRR * 12
        active_subscriptions: 2,
        trial_subscriptions: 1,
        churn_rate: 5.2,
        avg_revenue_per_user: 108.88,
        outstanding_invoices: 2,
        overdue_amount: 79.99
      };

      setPlans(mockPlans);
      setSubscriptions(mockSubscriptions);
      setInvoices(mockInvoices);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-700 bg-green-100';
      case 'trial': return 'text-blue-700 bg-blue-100';
      case 'suspended': return 'text-orange-700 bg-orange-100';
      case 'cancelled': return 'text-red-700 bg-red-100';
      case 'expired': return 'text-gray-700 bg-gray-100';
      case 'paid': return 'text-green-700 bg-green-100';
      case 'pending': return 'text-yellow-700 bg-yellow-100';
      case 'failed': return 'text-red-700 bg-red-100';
      case 'refunded': return 'text-purple-700 bg-purple-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'paid': return <CheckCircle className="h-4 w-4" />;
      case 'trial': return <Clock className="h-4 w-4" />;
      case 'suspended':
      case 'failed': return <AlertTriangle className="h-4 w-4" />;
      case 'pending': return <RefreshCw className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Revenue trend data (mock)
  const revenueData = [
    { month: 'Jan', mrr: 2500, arr: 30000 },
    { month: 'Feb', mrr: 2800, arr: 33600 },
    { month: 'Mar', mrr: 3200, arr: 38400 },
    { month: 'Apr', mrr: 3100, arr: 37200 },
    { month: 'May', mrr: 3400, arr: 40800 },
    { month: 'Jun', mrr: 3600, arr: 43200 },
    { month: 'Jul', mrr: 3800, arr: 45600 },
    { month: 'Aug', mrr: 3500, arr: 42000 },
    { month: 'Sep', mrr: 3700, arr: 44400 },
    { month: 'Oct', mrr: 3900, arr: 46800 },
    { month: 'Nov', mrr: 4100, arr: 49200 },
    { month: 'Dec', mrr: 4300, arr: 51600 }
  ];

  const planDistribution = plans.map(plan => ({
    name: plan.plan_name,
    value: subscriptions.filter(sub => sub.plan_name === plan.plan_name && sub.status === 'active').length
  }));

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-96 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Billing & Subscriptions</h2>
          <p className="text-gray-600 mt-1">
            Manage subscription plans, billing, and revenue analytics
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4 mr-2" />
            New Plan
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Monthly Recurring Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(metrics.total_mrr)}
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600 ml-1">+12.5%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Annual Recurring Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(metrics.total_arr)}
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600 ml-1">+8.3%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Active Subscriptions</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.active_subscriptions}</p>
                <div className="flex items-center mt-1">
                  <span className="text-sm text-gray-600">{metrics.trial_subscriptions} trials</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Overdue Amount</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(metrics.overdue_amount)}
                </p>
                <div className="flex items-center mt-1">
                  <span className="text-sm text-gray-600">{metrics.outstanding_invoices} invoices</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <TrendingUp className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('subscriptions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'subscriptions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Users className="h-4 w-4 inline mr-2" />
            Subscriptions ({subscriptions.length})
          </button>
          <button
            onClick={() => setActiveTab('invoices')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'invoices'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <CreditCard className="h-4 w-4 inline mr-2" />
            Invoices ({invoices.length})
          </button>
          <button
            onClick={() => setActiveTab('plans')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'plans'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Package className="h-4 w-4 inline mr-2" />
            Plans ({plans.length})
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Revenue Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* MRR Trend */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Recurring Revenue</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => [formatCurrency(value), 'MRR']} />
                  <Line
                    type="monotone"
                    dataKey="mrr"
                    stroke="#10B981"
                    strokeWidth={3}
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Plan Distribution */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Distribution</h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={planDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {planDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Churn Rate</p>
                  <p className="text-2xl font-semibold text-gray-900">{metrics?.churn_rate}%</p>
                </div>
                <TrendingDown className="h-8 w-8 text-red-500" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">ARPU</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(metrics?.avg_revenue_per_user || 0)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Trial Conversion</p>
                  <p className="text-2xl font-semibold text-gray-900">68.5%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'subscriptions' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tenant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Billing
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  MRR
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Next Billing
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscriptions.map(subscription => (
                <tr key={subscription.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{subscription.tenant_name}</div>
                    <div className="text-sm text-gray-500">ID: {subscription.tenant_id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{subscription.plan_name}</div>
                    <div className="text-sm text-gray-500 capitalize">{subscription.billing_cycle}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                      {getStatusIcon(subscription.status)}
                      <span className="ml-1 capitalize">{subscription.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium capitalize">{subscription.billing_cycle}</div>
                      <div className="text-gray-500">
                        {subscription.auto_renew ? 'Auto-renew' : 'Manual'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {formatCurrency(subscription.mrr)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(subscription.current_period_end).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Edit className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'invoices' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tenant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {invoices.map(invoice => (
                <tr key={invoice.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{invoice.invoice_number}</div>
                    <div className="text-sm text-gray-500">
                      {new Date(invoice.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.tenant_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.amount_due, invoice.currency)}
                    </div>
                    {invoice.amount_paid > 0 && (
                      <div className="text-sm text-gray-500">
                        Paid: {formatCurrency(invoice.amount_paid, invoice.currency)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                      {getStatusIcon(invoice.status)}
                      <span className="ml-1 capitalize">{invoice.status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(invoice.due_date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <Download className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'plans' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {plans.map(plan => (
            <div key={plan.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{plan.plan_name}</h3>
                  <p className="text-sm text-gray-500 mt-1">{plan.description}</p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  plan.is_active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                }`}>
                  {plan.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div className="space-y-4">
                <div className="text-center py-4 border-b border-gray-200">
                  <div className="text-3xl font-bold text-gray-900">
                    {formatCurrency(plan.price_monthly)}
                  </div>
                  <div className="text-sm text-gray-500">per month</div>
                  <div className="text-sm text-gray-500 mt-1">
                    {formatCurrency(plan.price_yearly)} yearly
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Features</h4>
                  <div className="space-y-1">
                    {Object.entries(plan.features).map(([feature, enabled]) => (
                      <div key={feature} className="flex items-center text-sm">
                        {enabled ? (
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <div className="h-4 w-4 mr-2" />
                        )}
                        <span className={enabled ? 'text-gray-900' : 'text-gray-400'}>
                          {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Limits</h4>
                  <div className="space-y-1 text-sm text-gray-600">
                    {Object.entries(plan.limits).map(([limit, value]) => (
                      <div key={limit} className="flex justify-between">
                        <span>{limit.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                        <span className="font-medium">{value.toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <button className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                      <Edit className="h-4 w-4 inline mr-1" />
                      Edit
                    </button>
                    <button className="flex-1 px-3 py-2 text-sm bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                      <Eye className="h-4 w-4 inline mr-1" />
                      View
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}