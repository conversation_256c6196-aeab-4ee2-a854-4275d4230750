// 💰 BARPOS Billing & Subscription Service
// Phase 7B: Customer Systems & Billing
// Comprehensive subscription management with Stripe integration

const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { Pool } = require('pg');

class BillingService {
    constructor() {
        this.pool = new Pool({
            user: process.env.POSTGRES_USER || 'BARPOS',
            host: process.env.POSTGRES_HOST || 'localhost',
            database: process.env.POSTGRES_DB || 'BARPOS',
            password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
            port: process.env.POSTGRES_PORT || 5432,
        });
        
        this.plans = {
            starter: {
                id: 'starter',
                name: 'Starter Plan',
                price: 9900, // $99.00 in cents
                currency: 'usd',
                interval: 'month',
                features: ['Single location', 'Up to 5 employees', 'Basic POS', 'Email support'],
                limits: { locations: 1, employees: 5, transactions: 1000 }
            },
            professional: {
                id: 'professional',
                name: 'Professional Plan',
                price: 29900, // $299.00 in cents
                currency: 'usd',
                interval: 'month',
                features: ['Up to 3 locations', 'Up to 25 employees', 'Advanced analytics', 'Phone support', 'Kitchen display'],
                limits: { locations: 3, employees: 25, transactions: 10000 }
            },
            enterprise: {
                id: 'enterprise',
                name: 'Enterprise Plan',
                price: 79900, // $799.00 in cents
                currency: 'usd',
                interval: 'month',
                features: ['Unlimited locations', 'Unlimited employees', 'AI features', 'Priority support', 'Custom integrations'],
                limits: { locations: -1, employees: -1, transactions: -1 }
            }
        };
    }

    // Create customer in Stripe and database
    async createCustomer(tenantData) {
        try {
            console.log('🏢 Creating customer for tenant:', tenantData.name);

            // Create Stripe customer
            const stripeCustomer = await stripe.customers.create({
                name: tenantData.name,
                email: tenantData.email,
                phone: tenantData.phone,
                address: {
                    line1: tenantData.address,
                    city: tenantData.city,
                    state: tenantData.state,
                    postal_code: tenantData.postal_code,
                    country: tenantData.country || 'US'
                },
                metadata: {
                    tenant_id: tenantData.id,
                    plan: tenantData.plan || 'starter',
                    created_by: 'barpos_system'
                }
            });

            // Store billing info in database
            await this.pool.query(`
                INSERT INTO billing_customers (
                    tenant_id, stripe_customer_id, plan_id, status, 
                    trial_start, trial_end, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            `, [
                tenantData.id,
                stripeCustomer.id,
                tenantData.plan || 'starter',
                'trialing',
                new Date(),
                new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days trial
            ]);

            console.log('✅ Customer created successfully:', stripeCustomer.id);
            return {
                success: true,
                customer: stripeCustomer,
                trial_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            };

        } catch (error) {
            console.error('❌ Error creating customer:', error);
            throw new Error(`Failed to create customer: ${error.message}`);
        }
    }

    // Create subscription for customer
    async createSubscription(tenantId, planId, paymentMethodId) {
        try {
            console.log('💳 Creating subscription for tenant:', tenantId, 'plan:', planId);

            // Get customer from database
            const customerResult = await this.pool.query(
                'SELECT stripe_customer_id FROM billing_customers WHERE tenant_id = $1',
                [tenantId]
            );

            if (customerResult.rows.length === 0) {
                throw new Error('Customer not found');
            }

            const customerId = customerResult.rows[0].stripe_customer_id;
            const plan = this.plans[planId];

            if (!plan) {
                throw new Error('Invalid plan ID');
            }

            // Attach payment method to customer
            await stripe.paymentMethods.attach(paymentMethodId, {
                customer: customerId,
            });

            // Set as default payment method
            await stripe.customers.update(customerId, {
                invoice_settings: {
                    default_payment_method: paymentMethodId,
                },
            });

            // Create subscription
            const subscription = await stripe.subscriptions.create({
                customer: customerId,
                items: [{
                    price_data: {
                        currency: plan.currency,
                        product_data: {
                            name: plan.name,
                            description: plan.features.join(', ')
                        },
                        unit_amount: plan.price,
                        recurring: {
                            interval: plan.interval,
                        },
                    },
                }],
                payment_behavior: 'default_incomplete',
                payment_settings: { save_default_payment_method: 'on_subscription' },
                expand: ['latest_invoice.payment_intent'],
                trial_period_days: 30,
                metadata: {
                    tenant_id: tenantId,
                    plan_id: planId
                }
            });

            // Update database
            await this.pool.query(`
                UPDATE billing_customers 
                SET stripe_subscription_id = $1, plan_id = $2, status = $3, updated_at = NOW()
                WHERE tenant_id = $4
            `, [subscription.id, planId, subscription.status, tenantId]);

            console.log('✅ Subscription created successfully:', subscription.id);
            return {
                success: true,
                subscription: subscription,
                client_secret: subscription.latest_invoice.payment_intent.client_secret
            };

        } catch (error) {
            console.error('❌ Error creating subscription:', error);
            throw new Error(`Failed to create subscription: ${error.message}`);
        }
    }

    // Handle subscription updates
    async updateSubscription(tenantId, newPlanId) {
        try {
            console.log('🔄 Updating subscription for tenant:', tenantId, 'to plan:', newPlanId);

            const customerResult = await this.pool.query(
                'SELECT stripe_subscription_id, plan_id FROM billing_customers WHERE tenant_id = $1',
                [tenantId]
            );

            if (customerResult.rows.length === 0) {
                throw new Error('Customer not found');
            }

            const { stripe_subscription_id, plan_id: currentPlan } = customerResult.rows[0];
            const newPlan = this.plans[newPlanId];

            if (!newPlan) {
                throw new Error('Invalid plan ID');
            }

            // Get current subscription
            const subscription = await stripe.subscriptions.retrieve(stripe_subscription_id);

            // Update subscription
            const updatedSubscription = await stripe.subscriptions.update(stripe_subscription_id, {
                items: [{
                    id: subscription.items.data[0].id,
                    price_data: {
                        currency: newPlan.currency,
                        product_data: {
                            name: newPlan.name,
                            description: newPlan.features.join(', ')
                        },
                        unit_amount: newPlan.price,
                        recurring: {
                            interval: newPlan.interval,
                        },
                    },
                }],
                proration_behavior: 'create_prorations',
                metadata: {
                    tenant_id: tenantId,
                    plan_id: newPlanId,
                    previous_plan: currentPlan
                }
            });

            // Update database
            await this.pool.query(`
                UPDATE billing_customers 
                SET plan_id = $1, updated_at = NOW()
                WHERE tenant_id = $2
            `, [newPlanId, tenantId]);

            console.log('✅ Subscription updated successfully');
            return { success: true, subscription: updatedSubscription };

        } catch (error) {
            console.error('❌ Error updating subscription:', error);
            throw new Error(`Failed to update subscription: ${error.message}`);
        }
    }

    // Cancel subscription
    async cancelSubscription(tenantId, reason = 'customer_request') {
        try {
            console.log('❌ Canceling subscription for tenant:', tenantId);

            const customerResult = await this.pool.query(
                'SELECT stripe_subscription_id FROM billing_customers WHERE tenant_id = $1',
                [tenantId]
            );

            if (customerResult.rows.length === 0) {
                throw new Error('Customer not found');
            }

            const subscriptionId = customerResult.rows[0].stripe_subscription_id;

            // Cancel subscription at period end
            const subscription = await stripe.subscriptions.update(subscriptionId, {
                cancel_at_period_end: true,
                metadata: {
                    cancellation_reason: reason,
                    cancelled_at: new Date().toISOString()
                }
            });

            // Update database
            await this.pool.query(`
                UPDATE billing_customers 
                SET status = $1, cancelled_at = NOW(), cancellation_reason = $2, updated_at = NOW()
                WHERE tenant_id = $3
            `, ['cancelled', reason, tenantId]);

            console.log('✅ Subscription cancelled successfully');
            return { success: true, subscription: subscription };

        } catch (error) {
            console.error('❌ Error cancelling subscription:', error);
            throw new Error(`Failed to cancel subscription: ${error.message}`);
        }
    }

    // Get billing information
    async getBillingInfo(tenantId) {
        try {
            const result = await this.pool.query(`
                SELECT bc.*, t.name as tenant_name, t.email as tenant_email
                FROM billing_customers bc
                JOIN tenants t ON bc.tenant_id = t.id
                WHERE bc.tenant_id = $1
            `, [tenantId]);

            if (result.rows.length === 0) {
                return { success: false, message: 'Billing information not found' };
            }

            const billingInfo = result.rows[0];
            const plan = this.plans[billingInfo.plan_id];

            // Get Stripe subscription if exists
            let subscription = null;
            if (billingInfo.stripe_subscription_id) {
                subscription = await stripe.subscriptions.retrieve(billingInfo.stripe_subscription_id);
            }

            return {
                success: true,
                billing: {
                    ...billingInfo,
                    plan_details: plan,
                    subscription: subscription
                }
            };

        } catch (error) {
            console.error('❌ Error getting billing info:', error);
            throw new Error(`Failed to get billing info: ${error.message}`);
        }
    }

    // Get usage statistics
    async getUsageStats(tenantId) {
        try {
            const stats = await this.pool.query(`
                SELECT 
                    COUNT(DISTINCT l.id) as locations_count,
                    COUNT(DISTINCT e.id) as employees_count,
                    COUNT(DISTINCT o.id) as transactions_count,
                    COALESCE(SUM(o.total_amount), 0) as total_revenue
                FROM tenants t
                LEFT JOIN locations l ON t.id = l.tenant_id AND l.is_active = true
                LEFT JOIN employees e ON t.id = e.tenant_id AND e.is_active = true
                LEFT JOIN orders o ON t.id = o.tenant_id AND o.created_at >= date_trunc('month', CURRENT_DATE)
                WHERE t.id = $1
                GROUP BY t.id
            `, [tenantId]);

            const usage = stats.rows[0] || {
                locations_count: 0,
                employees_count: 0,
                transactions_count: 0,
                total_revenue: 0
            };

            // Get plan limits
            const billingInfo = await this.getBillingInfo(tenantId);
            const limits = billingInfo.success ? billingInfo.billing.plan_details.limits : {};

            return {
                success: true,
                usage: {
                    ...usage,
                    limits: limits,
                    usage_percentage: {
                        locations: limits.locations > 0 ? (usage.locations_count / limits.locations) * 100 : 0,
                        employees: limits.employees > 0 ? (usage.employees_count / limits.employees) * 100 : 0,
                        transactions: limits.transactions > 0 ? (usage.transactions_count / limits.transactions) * 100 : 0
                    }
                }
            };

        } catch (error) {
            console.error('❌ Error getting usage stats:', error);
            throw new Error(`Failed to get usage stats: ${error.message}`);
        }
    }

    // Handle webhook events
    async handleWebhook(event) {
        try {
            console.log('🔔 Processing webhook event:', event.type);

            switch (event.type) {
                case 'customer.subscription.created':
                    await this.handleSubscriptionCreated(event.data.object);
                    break;
                case 'customer.subscription.updated':
                    await this.handleSubscriptionUpdated(event.data.object);
                    break;
                case 'customer.subscription.deleted':
                    await this.handleSubscriptionDeleted(event.data.object);
                    break;
                case 'invoice.payment_succeeded':
                    await this.handlePaymentSucceeded(event.data.object);
                    break;
                case 'invoice.payment_failed':
                    await this.handlePaymentFailed(event.data.object);
                    break;
                default:
                    console.log('🔄 Unhandled webhook event:', event.type);
            }

            return { success: true };

        } catch (error) {
            console.error('❌ Error handling webhook:', error);
            throw new Error(`Failed to handle webhook: ${error.message}`);
        }
    }

    // Helper methods for webhook handling
    async handleSubscriptionCreated(subscription) {
        const tenantId = subscription.metadata.tenant_id;
        await this.pool.query(`
            UPDATE billing_customers 
            SET status = $1, updated_at = NOW()
            WHERE tenant_id = $2
        `, [subscription.status, tenantId]);
    }

    async handleSubscriptionUpdated(subscription) {
        const tenantId = subscription.metadata.tenant_id;
        await this.pool.query(`
            UPDATE billing_customers 
            SET status = $1, updated_at = NOW()
            WHERE tenant_id = $2
        `, [subscription.status, tenantId]);
    }

    async handleSubscriptionDeleted(subscription) {
        const tenantId = subscription.metadata.tenant_id;
        await this.pool.query(`
            UPDATE billing_customers 
            SET status = 'cancelled', cancelled_at = NOW(), updated_at = NOW()
            WHERE tenant_id = $1
        `, [tenantId]);
    }

    async handlePaymentSucceeded(invoice) {
        const customerId = invoice.customer;
        await this.pool.query(`
            UPDATE billing_customers 
            SET last_payment_date = NOW(), status = 'active', updated_at = NOW()
            WHERE stripe_customer_id = $1
        `, [customerId]);
    }

    async handlePaymentFailed(invoice) {
        const customerId = invoice.customer;
        await this.pool.query(`
            UPDATE billing_customers 
            SET status = 'past_due', updated_at = NOW()
            WHERE stripe_customer_id = $1
        `, [customerId]);
    }

    // Get available plans
    getPlans() {
        return {
            success: true,
            plans: Object.values(this.plans)
        };
    }
}

module.exports = new BillingService();
