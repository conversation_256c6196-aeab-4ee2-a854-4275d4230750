// Production Payment Gateway Service
// Phase 7: Live payment gateway integration

const stripe = require('stripe');
const axios = require('axios');
const { Pool } = require('pg');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class ProductionPaymentService {
  constructor() {
    this.gateways = this.initializeGateways();
    this.retryAttempts = 3;
    this.retryDelay = 1000;
    this.timeout = 30000;
    
    // Performance tracking
    this.metrics = {
      totalTransactions: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageProcessingTime: 0,
      gatewayPerformance: {}
    };
  }

  initializeGateways() {
    const gateways = {};

    // Stripe Gateway
    if (process.env.STRIPE_SECRET_KEY) {
      gateways.stripe = {
        client: stripe(process.env.STRIPE_SECRET_KEY),
        name: 'Stripe',
        priority: 1,
        supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
        supportedMethods: ['card', 'bank_transfer', 'digital_wallet'],
        fees: { percentage: 2.9, fixed: 0.30 },
        maxAmount: 999999.99,
        minAmount: 0.50
      };
    }

    // PayPal Gateway
    if (process.env.PAYPAL_CLIENT_ID && process.env.PAYPAL_CLIENT_SECRET) {
      gateways.paypal = {
        clientId: process.env.PAYPAL_CLIENT_ID,
        clientSecret: process.env.PAYPAL_CLIENT_SECRET,
        name: 'PayPal',
        priority: 2,
        environment: process.env.PAYPAL_ENVIRONMENT || 'production',
        baseUrl: process.env.PAYPAL_ENVIRONMENT === 'production' 
          ? 'https://api.paypal.com'
          : 'https://api.sandbox.paypal.com',
        supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY'],
        supportedMethods: ['paypal', 'credit_card', 'bank_transfer'],
        fees: { percentage: 3.49, fixed: 0.49 },
        maxAmount: 10000.00,
        minAmount: 1.00
      };
    }

    // Square Gateway
    if (process.env.SQUARE_ACCESS_TOKEN) {
      gateways.square = {
        accessToken: process.env.SQUARE_ACCESS_TOKEN,
        applicationId: process.env.SQUARE_APPLICATION_ID,
        name: 'Square',
        priority: 3,
        environment: process.env.SQUARE_ENVIRONMENT || 'production',
        baseUrl: process.env.SQUARE_ENVIRONMENT === 'production'
          ? 'https://connect.squareup.com'
          : 'https://connect.squareupsandbox.com',
        supportedCurrencies: ['USD', 'CAD', 'AUD', 'GBP', 'JPY'],
        supportedMethods: ['card', 'cash', 'gift_card'],
        fees: { percentage: 2.6, fixed: 0.10 },
        maxAmount: 50000.00,
        minAmount: 1.00
      };
    }

    console.log(`🏦 Initialized ${Object.keys(gateways).length} payment gateways`);
    return gateways;
  }

  // =====================================================
  // PAYMENT PROCESSING
  // =====================================================

  async processPayment(paymentData) {
    const startTime = Date.now();
    const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      console.log(`💳 Processing payment: ${transactionId}`);
      
      // Validate payment data
      const validation = this.validatePaymentData(paymentData);
      if (!validation.valid) {
        throw new Error(`Payment validation failed: ${validation.errors.join(', ')}`);
      }

      // Select optimal gateway
      const gateway = await this.selectOptimalGateway(paymentData);
      if (!gateway) {
        throw new Error('No suitable payment gateway available');
      }

      console.log(`🎯 Selected gateway: ${gateway.name}`);

      // Process payment with selected gateway
      let result;
      switch (gateway.name) {
        case 'Stripe':
          result = await this.processStripePayment(gateway, paymentData, transactionId);
          break;
        case 'PayPal':
          result = await this.processPayPalPayment(gateway, paymentData, transactionId);
          break;
        case 'Square':
          result = await this.processSquarePayment(gateway, paymentData, transactionId);
          break;
        default:
          throw new Error(`Unsupported gateway: ${gateway.name}`);
      }

      // Calculate processing time
      const processingTime = Date.now() - startTime;
      
      // Store transaction record
      await this.storeTransactionRecord({
        ...result,
        transaction_id: transactionId,
        processing_time_ms: processingTime,
        gateway_used: gateway.name
      });

      // Update metrics
      this.updateMetrics(true, processingTime, gateway.name);

      console.log(`✅ Payment processed successfully: ${transactionId} (${processingTime}ms)`);

      return {
        success: true,
        transaction_id: transactionId,
        gateway_used: gateway.name,
        processing_time_ms: processingTime,
        ...result
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      console.error(`❌ Payment processing failed: ${transactionId}`, error);
      
      // Store failed transaction
      await this.storeFailedTransaction(transactionId, paymentData, error.message, processingTime);
      
      // Update metrics
      this.updateMetrics(false, processingTime);

      return {
        success: false,
        transaction_id: transactionId,
        error: error.message,
        processing_time_ms: processingTime
      };
    }
  }

  // =====================================================
  // STRIPE PAYMENT PROCESSING
  // =====================================================

  async processStripePayment(gateway, paymentData, transactionId) {
    try {
      const paymentIntent = await gateway.client.paymentIntents.create({
        amount: Math.round(paymentData.amount * 100), // Convert to cents
        currency: paymentData.currency.toLowerCase(),
        payment_method_types: ['card'],
        metadata: {
          transaction_id: transactionId,
          tenant_id: paymentData.tenant_id || '1',
          order_id: paymentData.order_id || 'unknown'
        },
        description: `BARPOS Transaction ${transactionId}`,
        receipt_email: paymentData.customer_email,
        confirm: true,
        payment_method: paymentData.payment_method_id || 'pm_card_visa' // Test payment method
      });

      if (paymentIntent.status === 'succeeded') {
        return {
          status: 'completed',
          gateway_transaction_id: paymentIntent.id,
          amount: paymentData.amount,
          currency: paymentData.currency,
          fees: this.calculateFees(paymentData.amount, gateway.fees),
          receipt_url: paymentIntent.charges.data[0]?.receipt_url
        };
      } else {
        throw new Error(`Stripe payment failed: ${paymentIntent.status}`);
      }

    } catch (error) {
      throw new Error(`Stripe processing error: ${error.message}`);
    }
  }

  // =====================================================
  // PAYPAL PAYMENT PROCESSING
  // =====================================================

  async processPayPalPayment(gateway, paymentData, transactionId) {
    try {
      // Get PayPal access token
      const accessToken = await this.getPayPalAccessToken(gateway);
      
      // Create payment
      const payment = await axios.post(`${gateway.baseUrl}/v1/payments/payment`, {
        intent: 'sale',
        payer: {
          payment_method: 'paypal'
        },
        transactions: [{
          amount: {
            total: paymentData.amount.toFixed(2),
            currency: paymentData.currency
          },
          description: `BARPOS Transaction ${transactionId}`,
          custom: transactionId
        }],
        redirect_urls: {
          return_url: process.env.PAYPAL_RETURN_URL || 'https://example.com/success',
          cancel_url: process.env.PAYPAL_CANCEL_URL || 'https://example.com/cancel'
        }
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        status: 'pending',
        gateway_transaction_id: payment.data.id,
        amount: paymentData.amount,
        currency: paymentData.currency,
        fees: this.calculateFees(paymentData.amount, gateway.fees),
        approval_url: payment.data.links.find(link => link.rel === 'approval_url')?.href
      };

    } catch (error) {
      throw new Error(`PayPal processing error: ${error.response?.data?.message || error.message}`);
    }
  }

  async getPayPalAccessToken(gateway) {
    try {
      const auth = Buffer.from(`${gateway.clientId}:${gateway.clientSecret}`).toString('base64');
      
      const response = await axios.post(`${gateway.baseUrl}/v1/oauth2/token`, 
        'grant_type=client_credentials', {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      return response.data.access_token;

    } catch (error) {
      throw new Error(`PayPal authentication failed: ${error.message}`);
    }
  }

  // =====================================================
  // SQUARE PAYMENT PROCESSING
  // =====================================================

  async processSquarePayment(gateway, paymentData, transactionId) {
    try {
      const payment = await axios.post(`${gateway.baseUrl}/v2/payments`, {
        source_id: paymentData.source_id || 'cnon:card-nonce-ok', // Test nonce
        amount_money: {
          amount: Math.round(paymentData.amount * 100), // Convert to cents
          currency: paymentData.currency
        },
        idempotency_key: transactionId,
        reference_id: transactionId,
        note: `BARPOS Transaction ${transactionId}`
      }, {
        headers: {
          'Authorization': `Bearer ${gateway.accessToken}`,
          'Content-Type': 'application/json',
          'Square-Version': '2023-10-18'
        }
      });

      if (payment.data.payment.status === 'COMPLETED') {
        return {
          status: 'completed',
          gateway_transaction_id: payment.data.payment.id,
          amount: paymentData.amount,
          currency: paymentData.currency,
          fees: this.calculateFees(paymentData.amount, gateway.fees),
          receipt_number: payment.data.payment.receipt_number
        };
      } else {
        throw new Error(`Square payment failed: ${payment.data.payment.status}`);
      }

    } catch (error) {
      throw new Error(`Square processing error: ${error.response?.data?.errors?.[0]?.detail || error.message}`);
    }
  }

  // =====================================================
  // GATEWAY SELECTION & OPTIMIZATION
  // =====================================================

  async selectOptimalGateway(paymentData) {
    const availableGateways = [];

    for (const [key, gateway] of Object.entries(this.gateways)) {
      // Check currency support
      if (!gateway.supportedCurrencies.includes(paymentData.currency)) {
        continue;
      }

      // Check amount limits
      if (paymentData.amount < gateway.minAmount || paymentData.amount > gateway.maxAmount) {
        continue;
      }

      // Check payment method support
      if (paymentData.payment_method && !gateway.supportedMethods.includes(paymentData.payment_method)) {
        continue;
      }

      // Check gateway health
      const health = await this.checkGatewayHealth(gateway);
      if (!health.healthy) {
        continue;
      }

      availableGateways.push({
        ...gateway,
        key: key,
        health: health,
        estimatedFees: this.calculateFees(paymentData.amount, gateway.fees)
      });
    }

    if (availableGateways.length === 0) {
      return null;
    }

    // Sort by priority and performance
    availableGateways.sort((a, b) => {
      // Primary sort: priority (lower number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      
      // Secondary sort: performance (response time)
      return a.health.responseTime - b.health.responseTime;
    });

    return availableGateways[0];
  }

  async checkGatewayHealth(gateway) {
    try {
      const start = Date.now();
      
      let healthUrl;
      switch (gateway.name) {
        case 'Stripe':
          // Stripe doesn't have a public health endpoint, so we'll use a simple API call
          await gateway.client.balance.retrieve();
          break;
        case 'PayPal':
          healthUrl = `${gateway.baseUrl}/v1/oauth2/token`;
          await axios.post(healthUrl, 'grant_type=client_credentials', {
            timeout: 5000,
            validateStatus: () => true
          });
          break;
        case 'Square':
          healthUrl = `${gateway.baseUrl}/v2/locations`;
          await axios.get(healthUrl, {
            headers: { 'Authorization': `Bearer ${gateway.accessToken}` },
            timeout: 5000,
            validateStatus: () => true
          });
          break;
      }
      
      const responseTime = Date.now() - start;
      
      return {
        healthy: true,
        responseTime: responseTime,
        lastChecked: new Date()
      };

    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        lastChecked: new Date()
      };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  validatePaymentData(paymentData) {
    const errors = [];

    if (!paymentData.amount || paymentData.amount <= 0) {
      errors.push('Invalid amount');
    }

    if (!paymentData.currency || paymentData.currency.length !== 3) {
      errors.push('Invalid currency code');
    }

    if (paymentData.amount > 999999.99) {
      errors.push('Amount exceeds maximum limit');
    }

    if (paymentData.amount < 0.01) {
      errors.push('Amount below minimum limit');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  calculateFees(amount, feeStructure) {
    const percentageFee = amount * (feeStructure.percentage / 100);
    const fixedFee = feeStructure.fixed || 0;
    return percentageFee + fixedFee;
  }

  updateMetrics(success, processingTime, gatewayName = null) {
    this.metrics.totalTransactions++;
    
    if (success) {
      this.metrics.successfulTransactions++;
    } else {
      this.metrics.failedTransactions++;
    }

    // Update average processing time
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * (this.metrics.totalTransactions - 1) + processingTime) 
      / this.metrics.totalTransactions;

    // Update gateway-specific metrics
    if (gatewayName) {
      if (!this.metrics.gatewayPerformance[gatewayName]) {
        this.metrics.gatewayPerformance[gatewayName] = {
          transactions: 0,
          successful: 0,
          failed: 0,
          averageTime: 0
        };
      }

      const gateway = this.metrics.gatewayPerformance[gatewayName];
      gateway.transactions++;
      
      if (success) {
        gateway.successful++;
      } else {
        gateway.failed++;
      }

      gateway.averageTime = 
        (gateway.averageTime * (gateway.transactions - 1) + processingTime) 
        / gateway.transactions;
    }
  }

  // =====================================================
  // DATABASE OPERATIONS
  // =====================================================

  async storeTransactionRecord(transactionData) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO payment_transactions (
          transaction_id, tenant_id, order_id, amount, currency, status,
          gateway_used, gateway_transaction_id, processing_time_ms,
          fees_amount, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
      `, [
        transactionData.transaction_id,
        transactionData.tenant_id || 1,
        transactionData.order_id || null,
        transactionData.amount,
        transactionData.currency,
        transactionData.status,
        transactionData.gateway_used,
        transactionData.gateway_transaction_id,
        transactionData.processing_time_ms,
        transactionData.fees || 0
      ]);
      
      client.release();
      
    } catch (error) {
      console.error('❌ Error storing transaction record:', error);
    }
  }

  async storeFailedTransaction(transactionId, paymentData, errorMessage, processingTime) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO payment_transactions (
          transaction_id, tenant_id, amount, currency, status,
          error_message, processing_time_ms, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      `, [
        transactionId,
        paymentData.tenant_id || 1,
        paymentData.amount,
        paymentData.currency,
        'failed',
        errorMessage,
        processingTime
      ]);
      
      client.release();
      
    } catch (error) {
      console.error('❌ Error storing failed transaction:', error);
    }
  }

  // =====================================================
  // MONITORING & HEALTH
  // =====================================================

  getServiceHealth() {
    const successRate = this.metrics.totalTransactions > 0 
      ? (this.metrics.successfulTransactions / this.metrics.totalTransactions) * 100 
      : 100;

    return {
      status: successRate >= 95 ? 'healthy' : successRate >= 90 ? 'degraded' : 'unhealthy',
      metrics: this.metrics,
      successRate: successRate,
      availableGateways: Object.keys(this.gateways).length,
      uptime: process.uptime()
    };
  }

  async getDetailedMetrics() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          gateway_used,
          COUNT(*) as total_transactions,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_transactions,
          AVG(processing_time_ms) as avg_processing_time,
          SUM(amount) as total_volume,
          SUM(fees_amount) as total_fees
        FROM payment_transactions 
        WHERE created_at >= NOW() - INTERVAL '24 hours'
        GROUP BY gateway_used
        ORDER BY total_transactions DESC
      `);
      
      client.release();
      
      return {
        success: true,
        period: '24 hours',
        gateway_performance: result.rows,
        system_metrics: this.metrics
      };

    } catch (error) {
      console.error('❌ Error getting detailed metrics:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ProductionPaymentService;
