import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CombinedApp from '../CombinedApp';
import { act } from 'react-dom/test-utils';

// Mock EnhancedApp and TenantLandingPage for isolated testing
jest.mock('../UnifiedPOS', () => () => <div>UnifiedPOS Component</div>);
jest.mock('../TenantLandingPage', () => () => <div>TenantLandingPage Component</div>);

// Mock AppProvider to just render children
jest.mock('../context/AppContext', () => ({
  AppProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('CombinedApp UI', () => {
  test('renders UnifiedPOS component by default', () => {
    render(<CombinedApp />);
    expect(screen.getByText('UnifiedPOS Component')).toBeInTheDocument();
  });
});

// Additional tests for EnhancedApp tab navigation and switching
import EnhancedApp from '../EnhancedApp';

describe('EnhancedApp UI and tab navigation', () => {
  test('renders login screen initially', () => {
    render(<EnhancedApp />);
    expect(screen.getByText(/Enter your PIN to continue/i)).toBeInTheDocument();
  });

  // Since login requires async and context, we can mock context or simulate login in integration tests
});

// TenantLandingPage test
import TenantLandingPage from '../TenantLandingPage';

describe('TenantLandingPage UI', () => {
  test('renders welcome message', () => {
    render(<TenantLandingPage />);
    expect(screen.getByText(/Welcome to Tenant Management/i)).toBeInTheDocument();
  });
});
