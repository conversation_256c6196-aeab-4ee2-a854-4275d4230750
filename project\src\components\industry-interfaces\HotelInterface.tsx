import React, { useState, useEffect } from 'react';
import { 
  Building, 
  Users, 
  MapPin, 
  Clock, 
  CreditCard,
  Globe,
  DollarSign,
  Phone,
  Bed,
  Utensils,
  Coffee,
  Wine,
  Calendar,
  Bell,
  Star,
  Languages,
  Navigation,
  CheckCircle,
  AlertCircle,
  Timer
} from 'lucide-react';

interface HotelGuest {
  id: string;
  firstName: string;
  lastName: string;
  roomNumber: string;
  checkInDate: Date;
  checkOutDate: Date;
  guestType: 'individual' | 'group' | 'vip' | 'corporate';
  loyaltyTier: 'Standard' | 'Gold' | 'Platinum' | 'Diamond';
  language: string;
  country: string;
  specialRequests: string[];
  dietaryRestrictions: string[];
  roomCharges: number;
  isCheckedIn: boolean;
}

interface RoomServiceOrder {
  id: string;
  guestId: string;
  roomNumber: string;
  items: {
    name: string;
    price: number;
    quantity: number;
    specialInstructions?: string;
  }[];
  total: number;
  orderTime: Date;
  requestedDeliveryTime?: Date;
  status: 'pending' | 'preparing' | 'ready' | 'delivering' | 'delivered';
  deliveryNotes: string;
  currency: string;
  exchangeRate: number;
}

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: 'breakfast' | 'lunch' | 'dinner' | 'beverages' | 'desserts' | 'room-service';
  cuisine: string;
  allergens: string[];
  spicyLevel: number;
  preparationTime: number;
  available24h: boolean;
  translations: Record<string, { name: string; description: string }>;
}

interface BanquetEvent {
  id: string;
  eventName: string;
  organizer: string;
  roomName: string;
  guestCount: number;
  eventDate: Date;
  startTime: string;
  endTime: string;
  menuType: 'buffet' | 'plated' | 'cocktail';
  totalAmount: number;
  status: 'confirmed' | 'in-progress' | 'completed';
  specialRequests: string;
}

const HotelInterface: React.FC = () => {
  const [currentGuest, setCurrentGuest] = useState<HotelGuest | null>(null);
  const [roomServiceOrders, setRoomServiceOrders] = useState<RoomServiceOrder[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [selectedCurrency, setSelectedCurrency] = useState<string>('USD');
  const [currentView, setCurrentView] = useState<'restaurant' | 'room-service' | 'banquet'>('restaurant');
  const [selectedCategory, setSelectedCategory] = useState<string>('breakfast');

  // Mock hotel guests
  const hotelGuests: HotelGuest[] = [
    {
      id: '1',
      firstName: 'Hiroshi',
      lastName: 'Tanaka',
      roomNumber: '1205',
      checkInDate: new Date('2024-02-10'),
      checkOutDate: new Date('2024-02-15'),
      guestType: 'individual',
      loyaltyTier: 'Platinum',
      language: 'ja',
      country: 'Japan',
      specialRequests: ['Late checkout', 'High floor'],
      dietaryRestrictions: ['No beef', 'Vegetarian options'],
      roomCharges: 1250.00,
      isCheckedIn: true
    },
    {
      id: '2',
      firstName: 'Maria',
      lastName: 'Rodriguez',
      roomNumber: '0847',
      checkInDate: new Date('2024-02-12'),
      checkOutDate: new Date('2024-02-14'),
      guestType: 'corporate',
      loyaltyTier: 'Gold',
      language: 'es',
      country: 'Spain',
      specialRequests: ['Business center access'],
      dietaryRestrictions: ['Gluten-free'],
      roomCharges: 890.00,
      isCheckedIn: true
    }
  ];

  // Mock menu with translations
  const hotelMenu: Record<string, MenuItem[]> = {
    breakfast: [
      {
        id: '1',
        name: 'Continental Breakfast',
        description: 'Fresh pastries, fruits, coffee, and juice',
        price: 28.00,
        category: 'breakfast',
        cuisine: 'International',
        allergens: ['Gluten', 'Dairy'],
        spicyLevel: 0,
        preparationTime: 15,
        available24h: false,
        translations: {
          ja: { name: 'コンチネンタル朝食', description: '新鮮なペストリー、フルーツ、コーヒー、ジュース' },
          es: { name: 'Desayuno Continental', description: 'Pasteles frescos, frutas, café y jugo' },
          fr: { name: 'Petit-déjeuner Continental', description: 'Pâtisseries fraîches, fruits, café et jus' }
        }
      },
      {
        id: '2',
        name: 'American Breakfast',
        description: 'Eggs, bacon, hash browns, toast, and coffee',
        price: 32.00,
        category: 'breakfast',
        cuisine: 'American',
        allergens: ['Eggs', 'Gluten'],
        spicyLevel: 0,
        preparationTime: 20,
        available24h: false,
        translations: {
          ja: { name: 'アメリカン朝食', description: '卵、ベーコン、ハッシュブラウン、トースト、コーヒー' },
          es: { name: 'Desayuno Americano', description: 'Huevos, tocino, papas hash, tostadas y café' }
        }
      }
    ],
    'room-service': [
      {
        id: '3',
        name: 'Club Sandwich',
        description: 'Triple-decker with turkey, bacon, lettuce, tomato',
        price: 24.00,
        category: 'room-service',
        cuisine: 'American',
        allergens: ['Gluten', 'Dairy'],
        spicyLevel: 0,
        preparationTime: 15,
        available24h: true,
        translations: {
          ja: { name: 'クラブサンドイッチ', description: 'ターキー、ベーコン、レタス、トマトの三段重ね' },
          es: { name: 'Club Sándwich', description: 'Triple con pavo, tocino, lechuga, tomate' }
        }
      },
      {
        id: '4',
        name: 'Caesar Salad',
        description: 'Romaine lettuce, parmesan, croutons, caesar dressing',
        price: 18.00,
        category: 'room-service',
        cuisine: 'Italian',
        allergens: ['Dairy', 'Gluten'],
        spicyLevel: 0,
        preparationTime: 10,
        available24h: true,
        translations: {
          ja: { name: 'シーザーサラダ', description: 'ロメインレタス、パルメザン、クルトン、シーザードレッシング' },
          es: { name: 'Ensalada César', description: 'Lechuga romana, parmesano, crutones, aderezo césar' }
        }
      }
    ]
  };

  // Mock room service orders
  const mockRoomServiceOrders: RoomServiceOrder[] = [
    {
      id: '1',
      guestId: '1',
      roomNumber: '1205',
      items: [
        { name: 'Club Sandwich', price: 24.00, quantity: 1 },
        { name: 'Green Tea', price: 8.00, quantity: 1 }
      ],
      total: 32.00,
      orderTime: new Date(Date.now() - 1800000),
      requestedDeliveryTime: new Date(Date.now() + 600000),
      status: 'preparing',
      deliveryNotes: 'Guest prefers no mayo',
      currency: 'JPY',
      exchangeRate: 150
    }
  ];

  useEffect(() => {
    setCurrentGuest(hotelGuests[0]);
    setRoomServiceOrders(mockRoomServiceOrders);
  }, []);

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'de', name: 'Deutsch', flag: '🇩🇪' }
  ];

  const currencies = [
    { code: 'USD', symbol: '$', rate: 1.0 },
    { code: 'EUR', symbol: '€', rate: 0.85 },
    { code: 'JPY', symbol: '¥', rate: 150 },
    { code: 'GBP', symbol: '£', rate: 0.75 }
  ];

  const getLocalizedMenuItem = (item: MenuItem) => {
    if (selectedLanguage === 'en' || !item.translations[selectedLanguage]) {
      return { name: item.name, description: item.description };
    }
    return item.translations[selectedLanguage];
  };

  const convertPrice = (price: number) => {
    const currency = currencies.find(c => c.code === selectedCurrency);
    if (!currency) return price;
    return (price * currency.rate).toFixed(2);
  };

  const getCurrencySymbol = () => {
    const currency = currencies.find(c => c.code === selectedCurrency);
    return currency?.symbol || '$';
  };

  const renderRestaurantView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Menu */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Utensils className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-bold text-gray-900">Restaurant Menu</h2>
            </div>
            
            {/* Category Tabs */}
            <div className="flex space-x-2 mb-6 overflow-x-auto">
              {Object.keys(hotelMenu).map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                </button>
              ))}
            </div>
            
            {/* Menu Items */}
            <div className="space-y-4">
              {hotelMenu[selectedCategory]?.map((item) => {
                const localizedItem = getLocalizedMenuItem(item);
                return (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{localizedItem.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">{localizedItem.description}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">
                          {getCurrencySymbol()}{convertPrice(item.price)}
                        </div>
                        {item.available24h && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            24/7
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>⏱️ {item.preparationTime} min</span>
                      <span>🌶️ {item.spicyLevel}/5</span>
                      <span>{item.cuisine}</span>
                    </div>
                    
                    {item.allergens.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {item.allergens.map((allergen, index) => (
                          <span key={index} className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                            {allergen}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Guest Info & Controls */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Language & Currency */}
            <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Globe className="w-6 h-6 text-blue-600" />
                <h3 className="text-lg font-bold text-gray-900">Preferences</h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                  <select
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                  <select
                    value={selectedCurrency}
                    onChange={(e) => setSelectedCurrency(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.symbol} {currency.code}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Current Guest */}
            {currentGuest && (
              <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Users className="w-6 h-6 text-blue-600" />
                  <h3 className="text-lg font-bold text-gray-900">Current Guest</h3>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {currentGuest.firstName} {currentGuest.lastName}
                    </h4>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Bed className="w-4 h-4" />
                      <span>Room {currentGuest.roomNumber}</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Loyalty:</span>
                      <span className="ml-2 font-semibold">{currentGuest.loyaltyTier}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Country:</span>
                      <span className="ml-2 font-semibold">{currentGuest.country}</span>
                    </div>
                  </div>
                  
                  {currentGuest.dietaryRestrictions.length > 0 && (
                    <div>
                      <h5 className="font-semibold text-gray-900 mb-2">Dietary Restrictions</h5>
                      <div className="flex flex-wrap gap-1">
                        {currentGuest.dietaryRestrictions.map((restriction, index) => (
                          <span key={index} className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                            {restriction}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Room Charges:</span>
                      <span className="font-semibold">${currentGuest.roomCharges.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderRoomServiceView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Orders */}
        <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Bell className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">Room Service Orders</h2>
          </div>
          
          <div className="space-y-4">
            {roomServiceOrders.map((order) => (
              <div key={order.id} className={`p-4 border-2 rounded-lg ${
                order.status === 'pending' ? 'border-yellow-200 bg-yellow-50' :
                order.status === 'preparing' ? 'border-blue-200 bg-blue-50' :
                order.status === 'ready' ? 'border-green-200 bg-green-50' :
                order.status === 'delivering' ? 'border-purple-200 bg-purple-50' :
                'border-gray-200 bg-gray-50'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-bold">Room {order.roomNumber}</span>
                  <span className="text-sm font-medium">
                    {order.currency === 'USD' ? '$' : '¥'}{order.total.toFixed(2)}
                  </span>
                </div>
                
                <div className="text-sm text-gray-600 mb-2">
                  {order.items.map(item => `${item.quantity}x ${item.name}`).join(', ')}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span>{order.orderTime.toLocaleTimeString()}</span>
                  <span className={`px-2 py-1 rounded-full font-medium ${
                    order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    order.status === 'preparing' ? 'bg-blue-100 text-blue-800' :
                    order.status === 'ready' ? 'bg-green-100 text-green-800' :
                    order.status === 'delivering' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
                
                {order.deliveryNotes && (
                  <div className="mt-2 text-xs text-gray-600 bg-gray-100 p-2 rounded">
                    Note: {order.deliveryNotes}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Room Service Menu */}
        <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Coffee className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900">24/7 Room Service</h2>
          </div>
          
          <div className="space-y-4">
            {hotelMenu['room-service']?.map((item) => {
              const localizedItem = getLocalizedMenuItem(item);
              return (
                <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{localizedItem.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{localizedItem.description}</p>
                    </div>
                    <div className="text-lg font-bold text-blue-600">
                      {getCurrencySymbol()}{convertPrice(item.price)}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>⏱️ {item.preparationTime} min</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      Available 24/7
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );

  const views = [
    { id: 'restaurant', label: 'Restaurant', icon: Utensils },
    { id: 'room-service', label: 'Room Service', icon: Bell },
    { id: 'banquet', label: 'Banquet', icon: Calendar }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-800 to-indigo-700 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Building className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Grand Hotel Restaurant</h1>
              <p className="text-blue-100">International cuisine & hospitality excellence</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{roomServiceOrders.filter(o => o.status !== 'delivered').length}</div>
              <div className="text-blue-200">Active Orders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">🌍</div>
              <div className="text-blue-200">Multi-Language</div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-2 mb-6">
        <div className="flex space-x-2">
          {views.map((view) => {
            const Icon = view.icon;
            return (
              <button
                key={view.id}
                onClick={() => setCurrentView(view.id as any)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                  currentView === view.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{view.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        {currentView === 'restaurant' && renderRestaurantView()}
        {currentView === 'room-service' && renderRoomServiceView()}
        {currentView === 'banquet' && (
          <div className="bg-white rounded-2xl shadow-lg border border-blue-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Banquet Management</h2>
            <p className="text-gray-600">Banquet event management and group dining coming soon...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HotelInterface;
