import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Industry-specific theme types
export interface ColorPalette {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    accent: string;
  };
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

export interface TypographyConfig {
  fontFamily: {
    primary: string;
    secondary: string;
    mono: string;
  };
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
  };
  fontWeight: {
    light: number;
    normal: number;
    medium: number;
    semibold: number;
    bold: number;
  };
}

export interface LayoutConfig {
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

export interface ComponentOverrides {
  button: {
    variants: Record<string, string>;
    sizes: Record<string, string>;
  };
  card: {
    variants: Record<string, string>;
  };
  input: {
    variants: Record<string, string>;
  };
  navigation: {
    variants: Record<string, string>;
  };
}

export interface IndustryTheme {
  id: string;
  name: string;
  code: string;
  colors: ColorPalette;
  typography: TypographyConfig;
  layout: LayoutConfig;
  components: ComponentOverrides;
}

export interface BusinessType {
  id: string;
  name: string;
  code: string;
  description: string;
  icon: string;
  colorScheme: string;
  defaultTheme: IndustryTheme;
  featureSet: Record<string, boolean | object>;
  workflowConfig: Record<string, any>;
}

// Predefined industry themes
export const INDUSTRY_THEMES: Record<string, IndustryTheme> = {
  FINE_DINING: {
    id: 'fine_dining',
    name: 'Fine Dining',
    code: 'FINE_DINING',
    colors: {
      primary: '#8B0000',
      secondary: '#DAA520',
      accent: '#CD853F',
      background: '#FFF8DC',
      surface: '#FFFFFF',
      text: {
        primary: '#2D1B1B',
        secondary: '#5D4E37',
        accent: '#8B0000'
      },
      status: {
        success: '#228B22',
        warning: '#DAA520',
        error: '#DC143C',
        info: '#4682B4'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #8B0000 0%, #A0522D 100%)',
        secondary: 'linear-gradient(135deg, #DAA520 0%, #F4A460 100%)',
        accent: 'linear-gradient(135deg, #CD853F 0%, #DEB887 100%)'
      }
    },
    typography: {
      fontFamily: {
        primary: 'Playfair Display, serif',
        secondary: 'Lato, sans-serif',
        mono: 'Monaco, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      }
    },
    layout: {
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem'
      },
      borderRadius: {
        sm: '0.375rem',
        md: '0.5rem',
        lg: '0.75rem',
        xl: '1rem'
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(139, 0, 0, 0.05)',
        md: '0 4px 6px -1px rgba(139, 0, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(139, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(139, 0, 0, 0.1)'
      }
    },
    components: {
      button: {
        variants: {
          primary: 'bg-gradient-to-r from-red-900 to-red-800 hover:from-red-800 hover:to-red-700 text-white',
          secondary: 'bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-500 hover:to-yellow-400 text-white',
          outline: 'border-2 border-red-900 text-red-900 hover:bg-red-900 hover:text-white'
        },
        sizes: {
          sm: 'px-3 py-1.5 text-sm',
          md: 'px-4 py-2 text-base',
          lg: 'px-6 py-3 text-lg'
        }
      },
      card: {
        variants: {
          default: 'bg-white border border-yellow-200 shadow-lg rounded-xl',
          elevated: 'bg-white border border-yellow-300 shadow-xl rounded-2xl'
        }
      },
      input: {
        variants: {
          default: 'border-yellow-300 focus:border-red-900 focus:ring-red-900',
          error: 'border-red-500 focus:border-red-500 focus:ring-red-500'
        }
      },
      navigation: {
        variants: {
          primary: 'bg-gradient-to-r from-red-900 to-red-800 text-white',
          secondary: 'bg-cream-100 text-red-900'
        }
      }
    }
  },

  QUICK_SERVICE: {
    id: 'quick_service',
    name: 'Quick Service',
    code: 'QUICK_SERVICE',
    colors: {
      primary: '#FF4500',
      secondary: '#FF6347',
      accent: '#FFA500',
      background: '#FFFFFF',
      surface: '#F8F9FA',
      text: {
        primary: '#212529',
        secondary: '#6C757D',
        accent: '#FF4500'
      },
      status: {
        success: '#28A745',
        warning: '#FFC107',
        error: '#DC3545',
        info: '#17A2B8'
      },
      gradients: {
        primary: 'linear-gradient(135deg, #FF4500 0%, #FF6347 100%)',
        secondary: 'linear-gradient(135deg, #FFA500 0%, #FFD700 100%)',
        accent: 'linear-gradient(135deg, #FF8C00 0%, #FFA500 100%)'
      }
    },
    typography: {
      fontFamily: {
        primary: 'Inter, sans-serif',
        secondary: 'Roboto, sans-serif',
        mono: 'Fira Code, monospace'
      },
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem'
      },
      fontWeight: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      }
    },
    layout: {
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem'
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem'
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(255, 69, 0, 0.05)',
        md: '0 4px 6px -1px rgba(255, 69, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(255, 69, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(255, 69, 0, 0.1)'
      }
    },
    components: {
      button: {
        variants: {
          primary: 'bg-gradient-to-r from-orange-600 to-red-500 hover:from-orange-700 hover:to-red-600 text-white',
          secondary: 'bg-gradient-to-r from-orange-400 to-yellow-400 hover:from-orange-500 hover:to-yellow-500 text-white',
          outline: 'border-2 border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white'
        },
        sizes: {
          sm: 'px-3 py-1.5 text-sm',
          md: 'px-4 py-2 text-base',
          lg: 'px-6 py-3 text-lg'
        }
      },
      card: {
        variants: {
          default: 'bg-white border border-orange-200 shadow-md rounded-lg',
          elevated: 'bg-white border border-orange-300 shadow-lg rounded-xl'
        }
      },
      input: {
        variants: {
          default: 'border-orange-300 focus:border-orange-600 focus:ring-orange-600',
          error: 'border-red-500 focus:border-red-500 focus:ring-red-500'
        }
      },
      navigation: {
        variants: {
          primary: 'bg-gradient-to-r from-orange-600 to-red-500 text-white',
          secondary: 'bg-orange-50 text-orange-600'
        }
      }
    }
  }

  // Additional themes for CAFE, BAR, FOOD_TRUCK, CATERING, HOTEL would be defined here...
};

interface IndustryThemeContextType {
  currentTheme: IndustryTheme;
  businessType: BusinessType | null;
  setBusinessType: (businessType: BusinessType) => void;
  updateTheme: (themeOverrides: Partial<IndustryTheme>) => void;
  resetTheme: () => void;
}

const IndustryThemeContext = createContext<IndustryThemeContextType | undefined>(undefined);

interface IndustryThemeProviderProps {
  children: ReactNode;
  initialBusinessType?: BusinessType;
}

export const IndustryThemeProvider: React.FC<IndustryThemeProviderProps> = ({
  children,
  initialBusinessType
}) => {
  const [businessType, setBusinessTypeState] = useState<BusinessType | null>(initialBusinessType || null);
  const [currentTheme, setCurrentTheme] = useState<IndustryTheme>(
    initialBusinessType?.defaultTheme || INDUSTRY_THEMES.QUICK_SERVICE
  );

  const setBusinessType = (newBusinessType: BusinessType) => {
    setBusinessTypeState(newBusinessType);
    setCurrentTheme(newBusinessType.defaultTheme);
    
    // Apply theme to CSS custom properties
    applyThemeToDOM(newBusinessType.defaultTheme);
  };

  const updateTheme = (themeOverrides: Partial<IndustryTheme>) => {
    const updatedTheme = { ...currentTheme, ...themeOverrides };
    setCurrentTheme(updatedTheme);
    applyThemeToDOM(updatedTheme);
  };

  const resetTheme = () => {
    if (businessType) {
      setCurrentTheme(businessType.defaultTheme);
      applyThemeToDOM(businessType.defaultTheme);
    }
  };

  const applyThemeToDOM = (theme: IndustryTheme) => {
    const root = document.documentElement;
    
    // Apply color variables
    root.style.setProperty('--color-primary', theme.colors.primary);
    root.style.setProperty('--color-secondary', theme.colors.secondary);
    root.style.setProperty('--color-accent', theme.colors.accent);
    root.style.setProperty('--color-background', theme.colors.background);
    root.style.setProperty('--color-surface', theme.colors.surface);
    
    // Apply text colors
    root.style.setProperty('--color-text-primary', theme.colors.text.primary);
    root.style.setProperty('--color-text-secondary', theme.colors.text.secondary);
    root.style.setProperty('--color-text-accent', theme.colors.text.accent);
    
    // Apply status colors
    root.style.setProperty('--color-success', theme.colors.status.success);
    root.style.setProperty('--color-warning', theme.colors.status.warning);
    root.style.setProperty('--color-error', theme.colors.status.error);
    root.style.setProperty('--color-info', theme.colors.status.info);
    
    // Apply gradients
    root.style.setProperty('--gradient-primary', theme.colors.gradients.primary);
    root.style.setProperty('--gradient-secondary', theme.colors.gradients.secondary);
    root.style.setProperty('--gradient-accent', theme.colors.gradients.accent);
    
    // Apply typography
    root.style.setProperty('--font-primary', theme.typography.fontFamily.primary);
    root.style.setProperty('--font-secondary', theme.typography.fontFamily.secondary);
    root.style.setProperty('--font-mono', theme.typography.fontFamily.mono);
  };

  useEffect(() => {
    if (currentTheme) {
      applyThemeToDOM(currentTheme);
    }
  }, [currentTheme]);

  const value: IndustryThemeContextType = {
    currentTheme,
    businessType,
    setBusinessType,
    updateTheme,
    resetTheme
  };

  return (
    <IndustryThemeContext.Provider value={value}>
      {children}
    </IndustryThemeContext.Provider>
  );
};

export const useIndustryTheme = (): IndustryThemeContextType => {
  const context = useContext(IndustryThemeContext);
  if (context === undefined) {
    throw new Error('useIndustryTheme must be used within an IndustryThemeProvider');
  }
  return context;
};

export default IndustryThemeContext;
