import React, { useState, useEffect } from 'react';
import { 
  X, 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Users,
  DollarSign,
  Activity,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  BarChart3
} from 'lucide-react';

interface TenantDetails {
  id: number;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  status: 'active' | 'suspended' | 'inactive';
  employeeCount: number;
  activeEmployees: number;
  locationCount: number;
  totalRevenue: number;
  orderCount: number;
  ordersLast30Days: number;
  ordersLast7Days: number;
  lastOrderDate?: string;
  lastEmployeeLogin?: string;
  planType: string;
  subscriptionStatus: string;
  createdAt: string;
  updatedAt: string;
}

interface TenantDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  tenantId: number | null;
  onEdit: (tenant: TenantDetails) => void;
  onDelete: (tenant: TenantDetails) => void;
}

export const TenantDetailsModal: React.FC<TenantDetailsModalProps> = ({
  isOpen,
  onClose,
  tenantId,
  onEdit,
  onDelete
}) => {
  const [tenant, setTenant] = useState<TenantDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && tenantId) {
      fetchTenantDetails();
    }
  }, [isOpen, tenantId]);

  const fetchTenantDetails = async () => {
    if (!tenantId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/tenants/${tenantId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch tenant details');
      }

      const data = await response.json();
      setTenant(data);
    } catch (error) {
      console.error('Error fetching tenant details:', error);
      setError('Failed to load tenant details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'suspended':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'inactive':
        return <Clock className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'suspended':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Building className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {tenant?.name || 'Tenant Details'}
              </h2>
              {tenant && (
                <p className="text-sm text-gray-500">ID: {tenant.id} • Slug: {tenant.slug}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {tenant && (
              <>
                <button
                  onClick={() => onEdit(tenant)}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  title="Edit Tenant"
                >
                  <Edit className="h-5 w-5" />
                </button>
                <button
                  onClick={() => onDelete(tenant)}
                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  title="Delete Tenant"
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading tenant details...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Details</h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <button
                onClick={fetchTenantDetails}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          ) : tenant ? (
            <div className="space-y-8">
              {/* Status and Basic Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  {/* Status */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getStatusIcon(tenant.status)}
                      <span className={`ml-2 ${getStatusBadge(tenant.status)}`}>
                        {tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      Plan: <span className="font-medium capitalize">{tenant.planType}</span>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-gray-900">{tenant.email}</span>
                      </div>
                      {tenant.phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-900">{tenant.phone}</span>
                        </div>
                      )}
                      {tenant.address && (
                        <div className="flex items-start">
                          <MapPin className="h-4 w-4 text-gray-400 mr-3 mt-0.5" />
                          <span className="text-gray-900">{tenant.address}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Dates */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm text-gray-500">Created</div>
                          <div className="text-gray-900">
                            {new Date(tenant.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm text-gray-500">Last Updated</div>
                          <div className="text-gray-900">
                            {new Date(tenant.updatedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      {tenant.lastOrderDate && (
                        <div className="flex items-center">
                          <Activity className="h-4 w-4 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm text-gray-500">Last Order</div>
                            <div className="text-gray-900">
                              {new Date(tenant.lastOrderDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      )}
                      {tenant.lastEmployeeLogin && (
                        <div className="flex items-center">
                          <Users className="h-4 w-4 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm text-gray-500">Last Employee Login</div>
                            <div className="text-gray-900">
                              {new Date(tenant.lastEmployeeLogin).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Metrics Sidebar */}
                <div className="space-y-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <span className="text-2xl font-bold text-blue-900">{tenant.employeeCount}</span>
                    </div>
                    <div className="text-sm text-blue-700">Total Employees</div>
                    <div className="text-xs text-blue-600 mt-1">
                      {tenant.activeEmployees} active
                    </div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      <span className="text-2xl font-bold text-green-900">
                        ${tenant.totalRevenue.toFixed(2)}
                      </span>
                    </div>
                    <div className="text-sm text-green-700">Total Revenue</div>
                    <div className="text-xs text-green-600 mt-1">
                      {tenant.orderCount} orders
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Activity className="h-5 w-5 text-purple-600" />
                      <span className="text-2xl font-bold text-purple-900">{tenant.ordersLast7Days}</span>
                    </div>
                    <div className="text-sm text-purple-700">Orders (7 days)</div>
                    <div className="text-xs text-purple-600 mt-1">
                      {tenant.ordersLast30Days} in 30 days
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Building className="h-5 w-5 text-orange-600" />
                      <span className="text-2xl font-bold text-orange-900">{tenant.locationCount}</span>
                    </div>
                    <div className="text-sm text-orange-700">Locations</div>
                  </div>
                </div>
              </div>

              {/* Performance Overview */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
                  <BarChart3 className="h-5 w-5 text-gray-400" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {tenant.orderCount > 0 ? (tenant.totalRevenue / tenant.orderCount).toFixed(2) : '0.00'}
                    </div>
                    <div className="text-sm text-gray-600">Avg Order Value</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {tenant.employeeCount > 0 ? (tenant.orderCount / tenant.employeeCount).toFixed(0) : '0'}
                    </div>
                    <div className="text-sm text-gray-600">Orders per Employee</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {tenant.employeeCount > 0 ? (tenant.totalRevenue / tenant.employeeCount).toFixed(2) : '0.00'}
                    </div>
                    <div className="text-sm text-gray-600">Revenue per Employee</div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => onEdit(tenant)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Tenant
                </button>
                <button
                  onClick={() => onDelete(tenant)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Tenant
                </button>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
