const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: '<PERSON><PERSON>@0319',
  port: 5432,
});

async function fixDatabaseSchema() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Fixing database schema...');
    
    // Check current schema
    const tablesResult = await client.query(`
      SELECT table_name, column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN ('tenants', 'employees', 'locations', 'tenant_settings')
      ORDER BY table_name, ordinal_position
    `);
    
    console.log('📋 Current schema:');
    tablesResult.rows.forEach(row => {
      console.log(`  ${row.table_name}.${row.column_name}: ${row.data_type}`);
    });
    
    // Add missing columns
    console.log('\n🔧 Adding missing columns...');
    
    // Add status column to tenants if it doesn't exist
    try {
      await client.query(`
        ALTER TABLE tenants ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'active' 
        CHECK (status IN ('active', 'suspended', 'canceled'))
      `);
      console.log('✅ Added status column to tenants table');
    } catch (error) {
      console.log('⚠️ Status column might already exist:', error.message);
    }
    
    // Check if we need to fix ID types
    const tenantIdType = await client.query(`
      SELECT data_type FROM information_schema.columns 
      WHERE table_name = 'tenants' AND column_name = 'id'
    `);
    
    console.log('🔍 Tenant ID type:', tenantIdType.rows[0]?.data_type);
    
    // If IDs are not UUIDs, we need to handle this differently
    if (tenantIdType.rows[0]?.data_type !== 'uuid') {
      console.log('⚠️ IDs are not UUIDs, using string-based approach');
      
      // Update the tenant creation to use string IDs
      console.log('✅ Will use string-based IDs for compatibility');
    }
    
    // Test a simple insert to verify schema
    console.log('\n🧪 Testing schema with sample data...');
    
    await client.query('BEGIN');
    
    const testTenant = await client.query(`
      INSERT INTO tenants (name, slug, email, phone, address, status)
      VALUES ('Test Schema', 'test-schema', '<EMAIL>', '555-0123', 'Test Address', 'active')
      RETURNING id, name, slug, email, status, created_at
    `);
    
    console.log('✅ Test tenant created:', testTenant.rows[0]);
    
    // Clean up test data
    await client.query('DELETE FROM tenants WHERE slug = $1', ['test-schema']);
    await client.query('COMMIT');
    
    console.log('✅ Schema test successful, test data cleaned up');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('💥 Schema fix failed:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

fixDatabaseSchema();
