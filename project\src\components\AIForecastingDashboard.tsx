import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Brain, TrendingUp, Calendar, Clock, Users, DollarSign, Package, Zap, RefreshCw, AlertTriangle } from 'lucide-react';

interface ForecastData {
  demand_forecast: {
    next_hour: {
      predicted_orders: number;
      confidence: number;
      peak_items: Array<{
        item_name: string;
        predicted_quantity: number;
        confidence: number;
      }>;
    };
    next_day: {
      predicted_revenue: number;
      predicted_orders: number;
      confidence: number;
      hourly_breakdown: Array<{
        hour: number;
        predicted_orders: number;
        confidence: number;
      }>;
    };
    next_week: {
      predicted_revenue: number;
      predicted_orders: number;
      confidence: number;
      daily_breakdown: Array<{
        day: string;
        predicted_revenue: number;
        predicted_orders: number;
      }>;
    };
  };
  inventory_predictions: Array<{
    item_name: string;
    current_stock: number;
    predicted_usage: number;
    reorder_recommendation: {
      quantity: number;
      timing: string;
      urgency: 'low' | 'medium' | 'high';
    };
    stockout_risk: number;
  }>;
  staffing_recommendations: {
    next_shift: {
      recommended_staff: number;
      current_scheduled: number;
      adjustment_needed: number;
      confidence: number;
    };
    peak_hours: Array<{
      hour: number;
      recommended_staff: number;
      predicted_workload: number;
    }>;
  };
  ai_insights: Array<{
    id: string;
    type: 'opportunity' | 'warning' | 'optimization';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    confidence: number;
    action_items: string[];
  }>;
}

const AIForecastingDashboard: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [forecastData, setForecastData] = useState<ForecastData | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'hour' | 'day' | 'week'>('day');
  const [activeView, setActiveView] = useState<'forecast' | 'inventory' | 'staffing' | 'insights'>('forecast');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load AI forecasting data
  useEffect(() => {
    const loadForecastData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🤖 Loading AI forecasting data...');
        
        const response = await apiCall('/api/ai/forecasting');
        if (response.ok) {
          const data = await response.json();
          setForecastData(data);
          console.log('✅ AI forecasting data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading AI forecasting data:', error);
        setError('Failed to load AI forecasting data. Using mock data.');
        
        // Fallback to mock data
        const mockData: ForecastData = {
          demand_forecast: {
            next_hour: {
              predicted_orders: 23,
              confidence: 87,
              peak_items: [
                { item_name: 'Classic Burger', predicted_quantity: 8, confidence: 92 },
                { item_name: 'Caesar Salad', predicted_quantity: 5, confidence: 85 },
                { item_name: 'Coffee', predicted_quantity: 12, confidence: 94 }
              ]
            },
            next_day: {
              predicted_revenue: 4850.75,
              predicted_orders: 156,
              confidence: 91,
              hourly_breakdown: Array.from({ length: 24 }, (_, i) => ({
                hour: i,
                predicted_orders: Math.max(0, Math.round(
                  10 + 15 * Math.sin((i - 6) * Math.PI / 12) + Math.random() * 5
                )),
                confidence: 85 + Math.random() * 10
              }))
            },
            next_week: {
              predicted_revenue: 32500.25,
              predicted_orders: 1050,
              confidence: 88,
              daily_breakdown: [
                { day: 'Monday', predicted_revenue: 4200, predicted_orders: 135 },
                { day: 'Tuesday', predicted_revenue: 4500, predicted_orders: 145 },
                { day: 'Wednesday', predicted_revenue: 4800, predicted_orders: 155 },
                { day: 'Thursday', predicted_revenue: 5100, predicted_orders: 165 },
                { day: 'Friday', predicted_revenue: 5800, predicted_orders: 185 },
                { day: 'Saturday', predicted_revenue: 4600, predicted_orders: 150 },
                { day: 'Sunday', predicted_revenue: 3500, predicted_orders: 115 }
              ]
            }
          },
          inventory_predictions: [
            {
              item_name: 'Burger Patties',
              current_stock: 45,
              predicted_usage: 38,
              reorder_recommendation: {
                quantity: 100,
                timing: 'Tomorrow morning',
                urgency: 'medium'
              },
              stockout_risk: 15
            },
            {
              item_name: 'Coffee Beans',
              current_stock: 12,
              predicted_usage: 25,
              reorder_recommendation: {
                quantity: 50,
                timing: 'Today',
                urgency: 'high'
              },
              stockout_risk: 85
            },
            {
              item_name: 'Lettuce',
              current_stock: 28,
              predicted_usage: 18,
              reorder_recommendation: {
                quantity: 30,
                timing: 'Next week',
                urgency: 'low'
              },
              stockout_risk: 5
            }
          ],
          staffing_recommendations: {
            next_shift: {
              recommended_staff: 8,
              current_scheduled: 6,
              adjustment_needed: 2,
              confidence: 89
            },
            peak_hours: [
              { hour: 12, recommended_staff: 10, predicted_workload: 95 },
              { hour: 13, recommended_staff: 12, predicted_workload: 100 },
              { hour: 18, recommended_staff: 9, predicted_workload: 85 },
              { hour: 19, recommended_staff: 11, predicted_workload: 98 }
            ]
          },
          ai_insights: [
            {
              id: 'insight_1',
              type: 'opportunity',
              title: 'Menu Optimization Opportunity',
              description: 'Classic Burger shows 23% higher demand on Fridays. Consider promoting it as a weekend special.',
              impact: 'high',
              confidence: 92,
              action_items: [
                'Create Friday burger promotion',
                'Increase burger patty inventory for weekends',
                'Train staff on upselling techniques'
              ]
            },
            {
              id: 'insight_2',
              type: 'warning',
              title: 'Potential Stockout Risk',
              description: 'Coffee beans inventory is critically low with 85% stockout risk in the next 24 hours.',
              impact: 'high',
              confidence: 95,
              action_items: [
                'Place emergency coffee bean order',
                'Contact backup supplier',
                'Consider temporary menu adjustments'
              ]
            },
            {
              id: 'insight_3',
              type: 'optimization',
              title: 'Staffing Efficiency',
              description: 'Tuesday afternoons are consistently overstaffed by 15%. Optimize scheduling to reduce labor costs.',
              impact: 'medium',
              confidence: 87,
              action_items: [
                'Adjust Tuesday afternoon schedules',
                'Redistribute staff to busier periods',
                'Implement flexible scheduling system'
              ]
            }
          ]
        };
        setForecastData(mockData);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadForecastData();
    
    // Refresh every 15 minutes for real-time AI insights
    const interval = setInterval(loadForecastData, 15 * 60 * 1000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600';
    if (confidence >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="h-5 w-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'optimization': return <Zap className="h-5 w-5 text-blue-500" />;
      default: return <Brain className="h-5 w-5 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading AI forecasting...</p>
        </div>
      </div>
    );
  }

  if (!forecastData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No AI forecasting data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">AI Forecasting Dashboard</h2>
            <p className="text-sm text-gray-500">Intelligent demand prediction and optimization</p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="hour">Next Hour</option>
              <option value="day">Next Day</option>
              <option value="week">Next Week</option>
            </select>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh AI Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Quick Stats */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Next Hour Orders</p>
                <p className="text-2xl font-bold text-gray-900">{forecastData.demand_forecast.next_hour.predicted_orders}</p>
                <p className={`text-sm ${getConfidenceColor(forecastData.demand_forecast.next_hour.confidence)}`}>
                  {forecastData.demand_forecast.next_hour.confidence}% confidence
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Daily Revenue</p>
                <p className="text-2xl font-bold text-gray-900">${forecastData.demand_forecast.next_day.predicted_revenue.toFixed(0)}</p>
                <p className={`text-sm ${getConfidenceColor(forecastData.demand_forecast.next_day.confidence)}`}>
                  {forecastData.demand_forecast.next_day.confidence}% confidence
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Staff Needed</p>
                <p className="text-2xl font-bold text-gray-900">{forecastData.staffing_recommendations.next_shift.recommended_staff}</p>
                <p className="text-sm text-gray-600">
                  {forecastData.staffing_recommendations.next_shift.adjustment_needed > 0 ? '+' : ''}
                  {forecastData.staffing_recommendations.next_shift.adjustment_needed} adjustment
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">High Risk Items</p>
                <p className="text-2xl font-bold text-red-600">
                  {forecastData.inventory_predictions.filter(item => item.stockout_risk > 50).length}
                </p>
                <p className="text-sm text-gray-600">Stockout risk</p>
              </div>
              <Package className="h-8 w-8 text-red-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'forecast', label: 'Demand Forecast', icon: TrendingUp },
            { id: 'inventory', label: 'Inventory AI', icon: Package },
            { id: 'staffing', label: 'Staff Optimization', icon: Users },
            { id: 'insights', label: 'AI Insights', icon: Brain }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'forecast' && (
          <div className="space-y-6">
            {/* Timeframe-specific forecast */}
            {selectedTimeframe === 'hour' && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Hour Forecast</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Predicted Orders</p>
                    <p className="text-3xl font-bold text-blue-600">{forecastData.demand_forecast.next_hour.predicted_orders}</p>
                    <p className={`text-sm ${getConfidenceColor(forecastData.demand_forecast.next_hour.confidence)}`}>
                      {forecastData.demand_forecast.next_hour.confidence}% confidence
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Peak Items</p>
                    <div className="space-y-2">
                      {forecastData.demand_forecast.next_hour.peak_items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm text-gray-900">{item.item_name}</span>
                          <div className="text-right">
                            <span className="text-sm font-medium">{item.predicted_quantity}</span>
                            <span className={`text-xs ml-2 ${getConfidenceColor(item.confidence)}`}>
                              {item.confidence}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedTimeframe === 'day' && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Forecast</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="mb-4">
                      <p className="text-sm text-gray-500">Predicted Revenue</p>
                      <p className="text-2xl font-bold text-green-600">${forecastData.demand_forecast.next_day.predicted_revenue.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Predicted Orders</p>
                      <p className="text-2xl font-bold text-blue-600">{forecastData.demand_forecast.next_day.predicted_orders}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Hourly Breakdown (Peak Hours)</p>
                    <div className="space-y-2">
                      {forecastData.demand_forecast.next_day.hourly_breakdown
                        .sort((a, b) => b.predicted_orders - a.predicted_orders)
                        .slice(0, 5)
                        .map((hour, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm text-gray-900">{hour.hour}:00</span>
                            <div className="text-right">
                              <span className="text-sm font-medium">{hour.predicted_orders} orders</span>
                              <span className={`text-xs ml-2 ${getConfidenceColor(hour.confidence)}`}>
                                {hour.confidence.toFixed(0)}%
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedTimeframe === 'week' && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Weekly Forecast</h3>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="mb-4">
                      <p className="text-sm text-gray-500">Predicted Revenue</p>
                      <p className="text-2xl font-bold text-green-600">${forecastData.demand_forecast.next_week.predicted_revenue.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Predicted Orders</p>
                      <p className="text-2xl font-bold text-blue-600">{forecastData.demand_forecast.next_week.predicted_orders}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Daily Breakdown</p>
                    <div className="space-y-2">
                      {forecastData.demand_forecast.next_week.daily_breakdown.map((day, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="text-sm text-gray-900">{day.day}</span>
                          <div className="text-right">
                            <span className="text-sm font-medium">${day.predicted_revenue.toFixed(0)}</span>
                            <span className="text-xs text-gray-500 ml-2">({day.predicted_orders} orders)</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeView === 'inventory' && (
          <div className="space-y-4">
            {forecastData.inventory_predictions.map((item, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900">{item.item_name}</h4>
                    <p className="text-sm text-gray-600">Current Stock: {item.current_stock} units</p>
                  </div>
                  <div className="text-right">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getUrgencyColor(item.reorder_recommendation.urgency)}`}>
                      {item.reorder_recommendation.urgency.toUpperCase()} PRIORITY
                    </span>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Predicted Usage</p>
                    <p className="font-semibold text-gray-900">{item.predicted_usage} units</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Stockout Risk</p>
                    <p className={`font-semibold ${item.stockout_risk > 50 ? 'text-red-600' : 'text-green-600'}`}>
                      {item.stockout_risk}%
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Reorder Quantity</p>
                    <p className="font-semibold text-gray-900">{item.reorder_recommendation.quantity} units</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-sm font-medium text-gray-900">AI Recommendation:</p>
                  <p className="text-sm text-gray-600">
                    Order {item.reorder_recommendation.quantity} units {item.reorder_recommendation.timing.toLowerCase()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'staffing' && (
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Shift Recommendation</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Recommended Staff</p>
                  <p className="text-2xl font-bold text-blue-600">{forecastData.staffing_recommendations.next_shift.recommended_staff}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Currently Scheduled</p>
                  <p className="text-2xl font-bold text-gray-900">{forecastData.staffing_recommendations.next_shift.current_scheduled}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Adjustment Needed</p>
                  <p className={`text-2xl font-bold ${forecastData.staffing_recommendations.next_shift.adjustment_needed > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {forecastData.staffing_recommendations.next_shift.adjustment_needed > 0 ? '+' : ''}
                    {forecastData.staffing_recommendations.next_shift.adjustment_needed}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Peak Hours Staffing</h3>
              <div className="space-y-3">
                {forecastData.staffing_recommendations.peak_hours.map((hour, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{hour.hour}:00</p>
                      <p className="text-sm text-gray-600">Workload: {hour.predicted_workload}%</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-blue-600">{hour.recommended_staff} staff</p>
                      <p className="text-xs text-gray-500">recommended</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeView === 'insights' && (
          <div className="space-y-4">
            {forecastData.ai_insights.map((insight) => (
              <div key={insight.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-900">{insight.title}</h4>
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getImpactColor(insight.impact)}`}>
                          {insight.impact.toUpperCase()} IMPACT
                        </span>
                        <span className={`text-xs ${getConfidenceColor(insight.confidence)}`}>
                          {insight.confidence}% confidence
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-600 mb-3">{insight.description}</p>
                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-2">Recommended Actions:</p>
                      <ul className="space-y-1">
                        {insight.action_items.map((action, index) => (
                          <li key={index} className="text-sm text-gray-600 flex items-center">
                            <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></span>
                            {action}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AIForecastingDashboard;
