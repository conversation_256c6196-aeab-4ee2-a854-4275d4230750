# BARPOS Testing Suite

## 🧪 Comprehensive Testing Framework

This directory contains all tests for the BARPOS system, organized by type and functionality.

## 📁 Directory Structure

```
tests/
├── unit/                    # Unit tests for individual components
│   ├── components/         # React component tests
│   ├── services/          # Service layer tests
│   └── utils/             # Utility function tests
├── integration/            # Integration tests
│   ├── api/               # API endpoint tests
│   ├── database/          # Database integration tests
│   └── auth/              # Authentication flow tests
├── e2e/                   # End-to-end tests
│   ├── pos/               # POS system workflows
│   ├── admin/             # Admin dashboard workflows
│   └── tenant/            # Tenant management workflows
├── performance/           # Performance and load tests
├── security/              # Security and penetration tests
├── fixtures/              # Test data and fixtures
├── helpers/               # Test utilities and helpers
└── config/                # Test configuration files
```

## 🚀 Running Tests

### All Tests
```bash
npm run test:all
```

### Specific Test Types
```bash
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e          # End-to-end tests only
npm run test:performance  # Performance tests only
npm run test:security     # Security tests only
```

### Coverage Reports
```bash
npm run test:coverage     # Generate coverage report
npm run test:watch        # Watch mode for development
```

## 📊 Test Standards

### Unit Tests
- **Coverage Target**: 90%+ line coverage
- **Framework**: Jest + React Testing Library
- **Naming**: `*.test.ts` or `*.test.tsx`
- **Location**: Adjacent to source files or in `tests/unit/`

### Integration Tests
- **Coverage Target**: 80%+ API endpoint coverage
- **Framework**: Jest + Supertest
- **Database**: Test database with fixtures
- **Naming**: `*.integration.test.js`

### E2E Tests
- **Coverage Target**: 100% critical user flows
- **Framework**: Playwright or Cypress
- **Environment**: Isolated test environment
- **Naming**: `*.e2e.test.js`

### Performance Tests
- **Metrics**: Response time, throughput, memory usage
- **Framework**: Artillery or k6
- **Thresholds**: <200ms API response, <3s page load
- **Naming**: `*.perf.test.js`

### Security Tests
- **Coverage**: Authentication, authorization, input validation
- **Framework**: Custom security test suite
- **Standards**: OWASP compliance
- **Naming**: `*.security.test.js`

## 🔧 Test Configuration

### Environment Variables
```bash
NODE_ENV=test
TEST_DB_HOST=localhost
TEST_DB_NAME=BARPOS_TEST
TEST_API_URL=http://localhost:4001
```

### Database Setup
- Separate test database: `BARPOS_TEST`
- Automatic schema migration before tests
- Data cleanup after each test suite

### Mock Services
- Payment gateway mocks
- External API mocks
- Email service mocks

## 📈 Continuous Integration

### Pre-commit Hooks
- Run unit tests
- Check code coverage
- Lint and format code

### CI Pipeline
1. **Unit Tests**: Fast feedback on code changes
2. **Integration Tests**: Verify API contracts
3. **E2E Tests**: Validate user workflows
4. **Performance Tests**: Ensure performance standards
5. **Security Tests**: Validate security measures

### Quality Gates
- **Unit Test Coverage**: ≥90%
- **Integration Test Coverage**: ≥80%
- **E2E Test Coverage**: 100% critical flows
- **Performance**: All thresholds met
- **Security**: Zero critical vulnerabilities

## 🛠️ Test Utilities

### Database Helpers
- `createTestTenant()` - Create test tenant
- `createTestUser()` - Create test user
- `cleanupTestData()` - Clean test data

### API Helpers
- `authenticateTestUser()` - Get auth token
- `makeAuthenticatedRequest()` - Authenticated API calls
- `expectSuccessResponse()` - Assert success responses

### Component Helpers
- `renderWithProviders()` - Render with contexts
- `mockAuthContext()` - Mock authentication
- `waitForAsyncUpdate()` - Wait for async updates

## 📝 Writing Tests

### Best Practices
1. **Arrange-Act-Assert** pattern
2. **Descriptive test names** that explain the scenario
3. **Independent tests** that don't rely on each other
4. **Proper cleanup** after each test
5. **Mock external dependencies**

### Example Unit Test
```typescript
describe('OrderService', () => {
  it('should calculate total amount including tax', () => {
    // Arrange
    const order = { subtotal: 100, taxRate: 0.1 };
    
    // Act
    const total = OrderService.calculateTotal(order);
    
    // Assert
    expect(total).toBe(110);
  });
});
```

### Example Integration Test
```typescript
describe('POST /api/orders', () => {
  it('should create order with valid data', async () => {
    // Arrange
    const orderData = { items: [{ id: 1, quantity: 2 }] };
    const token = await authenticateTestUser();
    
    // Act
    const response = await request(app)
      .post('/api/orders')
      .set('Authorization', `Bearer ${token}`)
      .send(orderData);
    
    // Assert
    expect(response.status).toBe(201);
    expect(response.body.data.id).toBeDefined();
  });
});
```

## 🎯 Test Metrics

### Current Coverage
- **Unit Tests**: 85% (Target: 90%)
- **Integration Tests**: 75% (Target: 80%)
- **E2E Tests**: 90% (Target: 100%)

### Performance Benchmarks
- **API Response Time**: <150ms average
- **Page Load Time**: <2s average
- **Database Query Time**: <50ms average

### Security Compliance
- **OWASP Top 10**: 100% covered
- **Authentication Tests**: 100% covered
- **Authorization Tests**: 100% covered
