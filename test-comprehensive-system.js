// Comprehensive System Test - Phase 1 Fixes Verification
const http = require('http');

const baseURL = 'http://localhost:4000';
const frontendURL = 'http://localhost:3000';

console.log('🔍 COMPREHENSIVE SYSTEM TEST - PHASE 1 FIXES');
console.log('=' .repeat(60));

async function runComprehensiveTest() {
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  // Test 1: Session Management & Authentication
  console.log('\n🔐 Testing Session Management & Authentication...');
  totalTests++;
  
  try {
    // Test login endpoint
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      pin: '888888',
      tenant_slug: 'demo-restaurant'
    });
    
    if (loginResponse.status === 200 && loginResponse.data.token && loginResponse.data.user) {
      console.log('✅ Login endpoint working correctly');
      
      // Test token verification
      const verifyResponse = await axios.get(`${baseURL}/api/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${loginResponse.data.token}`
        }
      });
      
      if (verifyResponse.status === 200) {
        console.log('✅ Token verification working correctly');
        passedTests++;
      } else {
        console.log('❌ Token verification failed');
        failedTests++;
      }
    } else {
      console.log('❌ Login endpoint failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
    failedTests++;
  }

  // Test 2: Order Management API
  console.log('\n📋 Testing Order Management API...');
  totalTests++;
  
  try {
    // Test order creation
    const orderData = {
      items: [
        { name: 'Coffee', price: 4.99, quantity: 2 },
        { name: 'Sandwich', price: 8.99, quantity: 1 }
      ],
      total: 18.97,
      payment_method: 'card'
    };
    
    const createOrderResponse = await axios.post(`${baseURL}/api/orders`, orderData);
    
    if (createOrderResponse.status === 201 && createOrderResponse.data.success) {
      console.log('✅ Order creation working correctly');
      
      // Test order retrieval
      const getOrdersResponse = await axios.get(`${baseURL}/api/orders`);
      
      if (getOrdersResponse.status === 200 && Array.isArray(getOrdersResponse.data)) {
        console.log('✅ Order retrieval working correctly');
        passedTests++;
      } else {
        console.log('❌ Order retrieval failed');
        failedTests++;
      }
    } else {
      console.log('❌ Order creation failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Order management test failed:', error.message);
    failedTests++;
  }

  // Test 3: Payment Processing
  console.log('\n💳 Testing Payment Processing...');
  totalTests++;
  
  try {
    const paymentData = {
      amount: 18.97,
      method: 'card',
      orderId: 'test_order_123'
    };
    
    const paymentResponse = await axios.post(`${baseURL}/api/payments/process`, paymentData);
    
    if (paymentResponse.status === 200 && paymentResponse.data.success) {
      console.log('✅ Payment processing working correctly');
      passedTests++;
    } else {
      console.log('❌ Payment processing failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Payment processing test failed:', error.message);
    failedTests++;
  }

  // Test 4: Floor Layout API
  console.log('\n🏢 Testing Floor Layout API...');
  totalTests++;
  
  try {
    const floorResponse = await axios.get(`${baseURL}/api/floor/layout`);
    
    if (floorResponse.status === 200 && floorResponse.data.tables && floorResponse.data.sections) {
      console.log('✅ Floor layout API working correctly');
      passedTests++;
    } else {
      console.log('❌ Floor layout API failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Floor layout test failed:', error.message);
    failedTests++;
  }

  // Test 5: Product & Category APIs
  console.log('\n🍽️ Testing Product & Category APIs...');
  totalTests++;
  
  try {
    const productsResponse = await axios.get(`${baseURL}/api/products`);
    const categoriesResponse = await axios.get(`${baseURL}/api/categories`);
    
    if (productsResponse.status === 200 && categoriesResponse.status === 200) {
      console.log('✅ Product and category APIs working correctly');
      passedTests++;
    } else {
      console.log('❌ Product/category APIs failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Product/category test failed:', error.message);
    failedTests++;
  }

  // Test 6: Health Check
  console.log('\n🏥 Testing System Health...');
  totalTests++;
  
  try {
    const healthResponse = await axios.get(`${baseURL}/api/health`);
    
    if (healthResponse.status === 200 && healthResponse.data.status === 'healthy') {
      console.log('✅ System health check passed');
      passedTests++;
    } else {
      console.log('❌ System health check failed');
      failedTests++;
    }
  } catch (error) {
    console.log('❌ Health check test failed:', error.message);
    failedTests++;
  }

  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! Phase 1 fixes are working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the issues above.');
  }
  
  console.log('\n🔄 NEXT STEPS:');
  console.log('1. Start backend server: cd backend && node server.js');
  console.log('2. Start frontend: cd frontend && npm start');
  console.log('3. Test the complete system manually');
  console.log('4. Verify session persistence by refreshing the page');
  console.log('5. Test order creation and payment processing');
}

// Run the test
runComprehensiveTest().catch(console.error);
