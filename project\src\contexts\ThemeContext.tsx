import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Check localStorage first, then system preference
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme) {
      return savedTheme;
    }
    
    // Check system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    
    return 'light';
  });

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement;
    
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    
    // Save to localStorage
    localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setThemeState(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme-aware utility functions
export const getThemeClasses = (theme: Theme) => ({
  // Background classes
  bg: {
    primary: theme === 'dark' ? 'bg-gray-900' : 'bg-white',
    secondary: theme === 'dark' ? 'bg-gray-800' : 'bg-gray-50',
    tertiary: theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100',
    card: theme === 'dark' ? 'bg-gray-800' : 'bg-white',
    hover: theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
    accent: theme === 'dark' ? 'bg-blue-900' : 'bg-blue-50',
  },
  
  // Text classes
  text: {
    primary: theme === 'dark' ? 'text-white' : 'text-gray-900',
    secondary: theme === 'dark' ? 'text-gray-300' : 'text-gray-600',
    tertiary: theme === 'dark' ? 'text-gray-400' : 'text-gray-500',
    accent: theme === 'dark' ? 'text-blue-400' : 'text-blue-600',
    success: theme === 'dark' ? 'text-green-400' : 'text-green-600',
    warning: theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600',
    error: theme === 'dark' ? 'text-red-400' : 'text-red-600',
  },
  
  // Border classes
  border: {
    primary: theme === 'dark' ? 'border-gray-700' : 'border-gray-200',
    secondary: theme === 'dark' ? 'border-gray-600' : 'border-gray-300',
    accent: theme === 'dark' ? 'border-blue-600' : 'border-blue-300',
  },
  
  // Ring classes for focus states
  ring: {
    primary: theme === 'dark' ? 'ring-blue-500' : 'ring-blue-500',
    offset: theme === 'dark' ? 'ring-offset-gray-900' : 'ring-offset-white',
  },
  
  // Shadow classes
  shadow: {
    card: theme === 'dark' ? 'shadow-xl shadow-black/20' : 'shadow-lg shadow-gray-200/50',
    hover: theme === 'dark' ? 'hover:shadow-2xl hover:shadow-black/30' : 'hover:shadow-xl hover:shadow-gray-300/50',
  }
});

// Predefined theme color schemes
export const themeColors = {
  light: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
    accent: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    text: {
      primary: '#111827',
      secondary: '#6b7280',
      tertiary: '#9ca3af',
    }
  },
  dark: {
    primary: '#111827',
    secondary: '#1f2937',
    tertiary: '#374151',
    accent: '#60a5fa',
    success: '#34d399',
    warning: '#fbbf24',
    error: '#f87171',
    text: {
      primary: '#ffffff',
      secondary: '#d1d5db',
      tertiary: '#9ca3af',
    }
  }
};

// Animation classes
export const animations = {
  fadeIn: 'animate-in fade-in duration-300',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  transition: 'transition-all duration-200 ease-in-out',
  hover: 'transition-all duration-150 ease-in-out transform hover:scale-105',
  card: 'transition-all duration-200 ease-in-out hover:shadow-lg',
};

// Responsive breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};
