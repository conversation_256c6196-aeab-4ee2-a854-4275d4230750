#!/usr/bin/env node

/**
 * Database Integration Testing Suite
 * Tests database operations and data integrity
 */

import { Pool } from 'pg';
import colors from 'colors';

const TEST_RESULTS = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  switch(type) {
    case 'success': console.log(`[${timestamp}] ✅ ${message}`.green); break;
    case 'error': console.log(`[${timestamp}] ❌ ${message}`.red); break;
    case 'warning': console.log(`[${timestamp}] ⚠️  ${message}`.yellow); break;
    case 'info': console.log(`[${timestamp}] ℹ️  ${message}`.blue); break;
    default: console.log(`[${timestamp}] ${message}`);
  }
};

const recordTest = (testName, passed, details = '') => {
  TEST_RESULTS.total++;
  if (passed) {
    TEST_RESULTS.passed++;
    log(`${testName} - PASSED ${details}`, 'success');
  } else {
    TEST_RESULTS.failed++;
    log(`${testName} - FAILED ${details}`, 'error');
  }
  TEST_RESULTS.details.push({ testName, passed, details, timestamp: new Date().toISOString() });
};

const testDatabaseConnection = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as version');
    client.release();
    
    recordTest('Database Connection', true, 
      `Connected successfully, Version: ${result.rows[0].version.substring(0, 50)}...`);
    return true;
  } catch (error) {
    recordTest('Database Connection', false, `Error: ${error.message}`);
    return false;
  }
};

const testTableExistence = async () => {
  try {
    const client = await pool.connect();
    
    const tableQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('tenants', 'employees', 'locations', 'tenant_settings', 'products', 'categories', 'orders')
      ORDER BY table_name
    `;
    
    const result = await client.query(tableQuery);
    client.release();
    
    const expectedTables = ['tenants', 'employees', 'locations', 'tenant_settings'];
    const existingTables = result.rows.map(row => row.table_name);
    
    recordTest('Required Tables Exist', existingTables.length >= 2, 
      `Found tables: ${existingTables.join(', ')}`);
    
    // Test individual table structures
    for (const table of existingTables) {
      try {
        const client2 = await pool.connect();
        const columnQuery = `
          SELECT column_name, data_type 
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position
        `;
        const columnResult = await client2.query(columnQuery, [table]);
        client2.release();
        
        recordTest(`Table Structure - ${table}`, columnResult.rows.length > 0, 
          `Columns: ${columnResult.rows.length}`);
      } catch (error) {
        recordTest(`Table Structure - ${table}`, false, `Error: ${error.message}`);
      }
    }
    
  } catch (error) {
    recordTest('Table Existence Check', false, `Error: ${error.message}`);
  }
};

const testDataOperations = async () => {
  try {
    const client = await pool.connect();
    
    // Test SELECT operations on existing data
    try {
      const tenantResult = await client.query('SELECT COUNT(*) as count FROM tenants');
      recordTest('Tenant Data Query', true, 
        `Found ${tenantResult.rows[0].count} tenants`);
    } catch (error) {
      recordTest('Tenant Data Query', false, `Error: ${error.message}`);
    }
    
    try {
      const employeeResult = await client.query('SELECT COUNT(*) as count FROM employees');
      recordTest('Employee Data Query', true, 
        `Found ${employeeResult.rows[0].count} employees`);
    } catch (error) {
      recordTest('Employee Data Query', false, `Error: ${error.message}`);
    }
    
    try {
      const locationResult = await client.query('SELECT COUNT(*) as count FROM locations');
      recordTest('Location Data Query', true, 
        `Found ${locationResult.rows[0].count} locations`);
    } catch (error) {
      recordTest('Location Data Query', false, `Error: ${error.message}`);
    }
    
    client.release();
    
  } catch (error) {
    recordTest('Data Operations Test', false, `Error: ${error.message}`);
  }
};

const testConnectionPooling = async () => {
  try {
    const connections = [];
    const startTime = Date.now();
    
    // Create multiple connections
    for (let i = 0; i < 3; i++) {
      const client = await pool.connect();
      connections.push(client);
    }
    
    const connectionTime = Date.now() - startTime;
    
    // Test concurrent queries
    const queryPromises = connections.map((client, index) => 
      client.query('SELECT $1 as connection_id, NOW() as timestamp', [index])
    );
    
    const results = await Promise.all(queryPromises);
    
    // Release connections
    connections.forEach(client => client.release());
    
    recordTest('Connection Pooling', results.length === 3, 
      `Created ${connections.length} connections in ${connectionTime}ms`);
    
    recordTest('Concurrent Queries', results.every(r => r.rows.length > 0), 
      `All ${results.length} queries executed successfully`);
    
  } catch (error) {
    recordTest('Connection Pooling Test', false, `Error: ${error.message}`);
  }
};

const testDataIntegrity = async () => {
  try {
    const client = await pool.connect();
    
    // Test foreign key relationships
    try {
      const relationshipQuery = `
        SELECT 
          tc.table_name, 
          kcu.column_name, 
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name 
        FROM 
          information_schema.table_constraints AS tc 
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
      `;
      
      const relationshipResult = await client.query(relationshipQuery);
      recordTest('Foreign Key Constraints', relationshipResult.rows.length >= 0, 
        `Found ${relationshipResult.rows.length} foreign key relationships`);
      
    } catch (error) {
      recordTest('Foreign Key Constraints', false, `Error: ${error.message}`);
    }
    
    // Test data consistency
    try {
      const consistencyQuery = `
        SELECT 
          t.name as tenant_name,
          COUNT(e.id) as employee_count
        FROM tenants t
        LEFT JOIN employees e ON t.id = e.tenant_id
        GROUP BY t.id, t.name
        ORDER BY t.name
      `;
      
      const consistencyResult = await client.query(consistencyQuery);
      recordTest('Data Consistency Check', consistencyResult.rows.length >= 0, 
        `Checked ${consistencyResult.rows.length} tenant-employee relationships`);
      
    } catch (error) {
      recordTest('Data Consistency Check', false, `Error: ${error.message}`);
    }
    
    client.release();
    
  } catch (error) {
    recordTest('Data Integrity Test', false, `Error: ${error.message}`);
  }
};

const testPerformance = async () => {
  try {
    const client = await pool.connect();
    
    // Test query performance
    const queries = [
      { name: 'Simple SELECT', query: 'SELECT 1 as test' },
      { name: 'Tenant Query', query: 'SELECT * FROM tenants LIMIT 10' },
      { name: 'Employee Query', query: 'SELECT * FROM employees LIMIT 10' },
      { name: 'Join Query', query: 'SELECT t.name, e.name FROM tenants t LEFT JOIN employees e ON t.id = e.tenant_id LIMIT 10' }
    ];
    
    for (const testQuery of queries) {
      try {
        const startTime = Date.now();
        const result = await client.query(testQuery.query);
        const queryTime = Date.now() - startTime;
        
        recordTest(`Query Performance - ${testQuery.name}`, queryTime < 1000, 
          `Execution time: ${queryTime}ms, Rows: ${result.rows.length}`);
      } catch (error) {
        recordTest(`Query Performance - ${testQuery.name}`, false, `Error: ${error.message}`);
      }
    }
    
    client.release();
    
  } catch (error) {
    recordTest('Performance Test', false, `Error: ${error.message}`);
  }
};

const runDatabaseTests = async () => {
  console.log('🗄️ Starting Database Integration Testing Suite...'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  
  try {
    await testDatabaseConnection();
    await testTableExistence();
    await testDataOperations();
    await testConnectionPooling();
    await testDataIntegrity();
    await testPerformance();
    
  } catch (error) {
    console.error('💥 Database test suite failed:', error.message);
  } finally {
    await pool.end();
  }
  
  // Print results
  console.log('\n' + '=' .repeat(60).cyan);
  console.log('📊 DATABASE TEST RESULTS'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  console.log(`Total Tests: ${TEST_RESULTS.total}`.white);
  console.log(`Passed: ${TEST_RESULTS.passed}`.green);
  console.log(`Failed: ${TEST_RESULTS.failed}`.red);
  console.log(`Success Rate: ${((TEST_RESULTS.passed / TEST_RESULTS.total) * 100).toFixed(1)}%`.yellow);
  
  if (TEST_RESULTS.failed > 0) {
    console.log('\n❌ FAILED TESTS:'.red.bold);
    TEST_RESULTS.details.filter(t => !t.passed).forEach(test => {
      console.log(`  - ${test.testName}: ${test.details}`.red);
    });
  }
  
  console.log('\n' + (TEST_RESULTS.failed === 0 ? '✅ ALL DATABASE TESTS PASSED!'.green.bold : '⚠️ SOME DATABASE TESTS FAILED'.red.bold));
};

// Run tests
runDatabaseTests();
