import React, { useState, useEffect } from 'react';
import {
  Monitor,
  Printer,
  Scan,
  Wifi,
  Bluetooth,
  Usb,
  Settings,
  Power,
  PowerOff,
  RefreshCw,
  Plus,
  Edit3,
  Trash2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Activity,
  Signal,
  Battery,
  HardDrive,
  Cpu,
  MemoryStick,
  Thermometer,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Download,
  Upload,
  Search,
  Filter
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface HardwareDevice {
  id: string;
  name: string;
  type: 'printer' | 'scanner' | 'terminal' | 'display' | 'cash_drawer' | 'scale' | 'camera';
  brand: string;
  model: string;
  connection_type: 'usb' | 'bluetooth' | 'wifi' | 'ethernet' | 'serial';
  status: 'connected' | 'disconnected' | 'error' | 'maintenance';
  ip_address?: string;
  mac_address?: string;
  serial_number: string;
  firmware_version: string;
  last_connected: string;
  capabilities: string[];
  settings: Record<string, any>;
  health: {
    temperature?: number;
    battery_level?: number;
    signal_strength?: number;
    error_count: number;
    uptime: number;
  };
}

interface HardwareAction {
  id: string;
  type: 'connect' | 'disconnect' | 'restart' | 'configure' | 'test' | 'update' | 'calibrate' | 'delete';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresConfirmation?: boolean;
}

export const EnhancedHardwareManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [devices, setDevices] = useState<HardwareDevice[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<HardwareDevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showConfigModal, setShowConfigModal] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Hardware actions configuration
  const hardwareActions: HardwareAction[] = [
    {
      id: 'connect_device',
      type: 'connect',
      label: 'Connect',
      icon: Power,
      color: 'green'
    },
    {
      id: 'disconnect_device',
      type: 'disconnect',
      label: 'Disconnect',
      icon: PowerOff,
      color: 'gray'
    },
    {
      id: 'restart_device',
      type: 'restart',
      label: 'Restart',
      icon: RefreshCw,
      color: 'blue',
      requiresConfirmation: true
    },
    {
      id: 'configure_device',
      type: 'configure',
      label: 'Configure',
      icon: Settings,
      color: 'purple'
    },
    {
      id: 'test_device',
      type: 'test',
      label: 'Test',
      icon: Activity,
      color: 'orange'
    },
    {
      id: 'update_firmware',
      type: 'update',
      label: 'Update',
      icon: Download,
      color: 'indigo',
      requiresConfirmation: true
    },
    {
      id: 'calibrate_device',
      type: 'calibrate',
      label: 'Calibrate',
      icon: Settings,
      color: 'yellow'
    },
    {
      id: 'delete_device',
      type: 'delete',
      label: 'Delete',
      icon: Trash2,
      color: 'red',
      requiresConfirmation: true
    }
  ];

  const deviceTypes = [
    { id: 'printer', name: 'Receipt Printers', icon: Printer },
    { id: 'scanner', name: 'Barcode Scanners', icon: Scan },
    { id: 'terminal', name: 'Payment Terminals', icon: Monitor },
    { id: 'display', name: 'Customer Displays', icon: Monitor },
    { id: 'cash_drawer', name: 'Cash Drawers', icon: HardDrive },
    { id: 'scale', name: 'Digital Scales', icon: MemoryStick },
    { id: 'camera', name: 'Security Cameras', icon: Eye }
  ];

  // Load devices
  useEffect(() => {
    loadDevices();
  }, []);

  // Filter devices
  useEffect(() => {
    let filtered = devices.filter(device => {
      const matchesSearch = device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           device.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           device.serial_number.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = filterType === 'all' || device.type === filterType;
      const matchesStatus = filterStatus === 'all' || device.status === filterStatus;

      return matchesSearch && matchesType && matchesStatus;
    });

    setFilteredDevices(filtered);
  }, [devices, searchTerm, filterType, filterStatus]);

  const loadDevices = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiCall('/api/hardware/devices');
      if (response.ok) {
        const data = await response.json();
        setDevices(data);
      } else {
        throw new Error('Failed to load devices');
      }
    } catch (error) {
      console.error('Error loading devices:', error);
      setError('Failed to load hardware devices');

      // Mock devices data
      setDevices([
        {
          id: 'device_001',
          name: 'Main Counter Printer',
          type: 'printer',
          brand: 'Epson',
          model: 'TM-T88VI',
          connection_type: 'usb',
          status: 'connected',
          serial_number: 'EP001234567',
          firmware_version: '1.2.3',
          last_connected: new Date().toISOString(),
          capabilities: ['receipt_printing', 'logo_printing', 'barcode_printing'],
          settings: {
            paper_width: '80mm',
            auto_cut: true,
            print_speed: 'fast'
          },
          health: {
            temperature: 45,
            error_count: 0,
            uptime: 86400
          }
        },
        {
          id: 'device_002',
          name: 'Handheld Scanner',
          type: 'scanner',
          brand: 'Honeywell',
          model: 'Voyager 1200g',
          connection_type: 'usb',
          status: 'connected',
          serial_number: 'HW987654321',
          firmware_version: '2.1.0',
          last_connected: new Date(Date.now() - 3600000).toISOString(),
          capabilities: ['1d_barcodes', '2d_barcodes', 'qr_codes'],
          settings: {
            scan_mode: 'auto',
            beep_volume: 'medium',
            led_brightness: 'high'
          },
          health: {
            error_count: 2,
            uptime: 72000
          }
        },
        {
          id: 'device_003',
          name: 'Payment Terminal',
          type: 'terminal',
          brand: 'Ingenico',
          model: 'iCT250',
          connection_type: 'ethernet',
          status: 'error',
          ip_address: '*************',
          serial_number: 'IN555666777',
          firmware_version: '3.0.1',
          last_connected: new Date(Date.now() - 7200000).toISOString(),
          capabilities: ['chip_cards', 'contactless', 'magnetic_stripe'],
          settings: {
            timeout: 30,
            receipt_printing: true,
            signature_required: false
          },
          health: {
            signal_strength: 85,
            error_count: 5,
            uptime: 43200
          }
        },
        {
          id: 'device_004',
          name: 'Kitchen Display',
          type: 'display',
          brand: 'Samsung',
          model: 'DB22D',
          connection_type: 'wifi',
          status: 'disconnected',
          ip_address: '*************',
          mac_address: '00:11:22:33:44:55',
          serial_number: 'SM888999000',
          firmware_version: '1.5.2',
          last_connected: new Date(Date.now() - 14400000).toISOString(),
          capabilities: ['touch_screen', 'high_resolution', 'multi_order_display'],
          settings: {
            brightness: 80,
            orientation: 'landscape',
            auto_sleep: true
          },
          health: {
            temperature: 52,
            signal_strength: 65,
            error_count: 1,
            uptime: 28800
          }
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleHardwareAction = async (action: HardwareAction, deviceId: string) => {
    if (action.requiresConfirmation) {
      setShowConfirmDialog(`${action.id}_${deviceId}`);
      return;
    }

    await executeHardwareAction(action, deviceId);
  };

  const executeHardwareAction = async (action: HardwareAction, deviceId: string) => {
    setActionLoading(`${action.id}_${deviceId}`);
    setError(null);

    try {
      switch (action.type) {
        case 'connect':
          await handleConnectDevice(deviceId);
          break;

        case 'disconnect':
          await handleDisconnectDevice(deviceId);
          break;

        case 'restart':
          await handleRestartDevice(deviceId);
          break;

        case 'configure':
          setShowConfigModal(deviceId);
          break;

        case 'test':
          await handleTestDevice(deviceId);
          break;

        case 'update':
          await handleUpdateFirmware(deviceId);
          break;

        case 'calibrate':
          await handleCalibrateDevice(deviceId);
          break;

        case 'delete':
          await handleDeleteDevice(deviceId);
          break;

        default:
          console.warn('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      setError(`Failed to ${action.label.toLowerCase()} device`);
    } finally {
      setActionLoading(null);
      setShowConfirmDialog(null);
    }
  };

  const handleConnectDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/connect`, {
      method: 'POST'
    });

    if (response.ok) {
      setDevices(prev => prev.map(device =>
        device.id === deviceId
          ? { ...device, status: 'connected', last_connected: new Date().toISOString() }
          : device
      ));
    } else {
      throw new Error('Failed to connect device');
    }
  };

  const handleDisconnectDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/disconnect`, {
      method: 'POST'
    });

    if (response.ok) {
      setDevices(prev => prev.map(device =>
        device.id === deviceId ? { ...device, status: 'disconnected' } : device
      ));
    } else {
      throw new Error('Failed to disconnect device');
    }
  };

  const handleRestartDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/restart`, {
      method: 'POST'
    });

    if (response.ok) {
      setDevices(prev => prev.map(device =>
        device.id === deviceId
          ? { ...device, status: 'connected', health: { ...device.health, uptime: 0 } }
          : device
      ));
    } else {
      throw new Error('Failed to restart device');
    }
  };

  const handleTestDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/test`, {
      method: 'POST'
    });

    if (response.ok) {
      const result = await response.json();
      alert(`Device test completed: ${result.message}`);
    } else {
      throw new Error('Device test failed');
    }
  };

  const handleUpdateFirmware = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/update`, {
      method: 'POST'
    });

    if (response.ok) {
      const result = await response.json();
      setDevices(prev => prev.map(device =>
        device.id === deviceId
          ? { ...device, firmware_version: result.new_version }
          : device
      ));
      alert('Firmware updated successfully');
    } else {
      throw new Error('Failed to update firmware');
    }
  };

  const handleCalibrateDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}/calibrate`, {
      method: 'POST'
    });

    if (response.ok) {
      alert('Device calibration completed');
    } else {
      throw new Error('Failed to calibrate device');
    }
  };

  const handleDeleteDevice = async (deviceId: string) => {
    const response = await apiCall(`/api/hardware/devices/${deviceId}`, {
      method: 'DELETE'
    });

    if (response.ok) {
      setDevices(prev => prev.filter(device => device.id !== deviceId));
    } else {
      throw new Error('Failed to delete device');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'disconnected': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConnectionIcon = (type: string) => {
    switch (type) {
      case 'usb': return <Usb className="h-4 w-4" />;
      case 'bluetooth': return <Bluetooth className="h-4 w-4" />;
      case 'wifi': return <Wifi className="h-4 w-4" />;
      case 'ethernet': return <Signal className="h-4 w-4" />;
      default: return <Signal className="h-4 w-4" />;
    }
  };

  const getDeviceIcon = (type: string) => {
    const deviceType = deviceTypes.find(dt => dt.id === type);
    return deviceType?.icon || Monitor;
  };

  const getActionButtonClass = (action: HardwareAction) => {
    const baseClass = "flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";

    switch (action.color) {
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'gray': return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'indigo': return `${baseClass} bg-indigo-600 hover:bg-indigo-700 text-white`;
      case 'yellow': return `${baseClass} bg-yellow-600 hover:bg-yellow-700 text-white`;
      case 'red': return `${baseClass} bg-red-600 hover:bg-red-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading hardware devices...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Hardware Management</h2>
          <p className="text-gray-600">Manage POS hardware devices and connections</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            <span>Add Device</span>
          </button>
          <button
            onClick={loadDevices}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search devices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Type Filter */}
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Device Types</option>
          {deviceTypes.map(type => (
            <option key={type.id} value={type.id}>{type.name}</option>
          ))}
        </select>

        {/* Status Filter */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="connected">Connected</option>
          <option value="disconnected">Disconnected</option>
          <option value="error">Error</option>
          <option value="maintenance">Maintenance</option>
        </select>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 font-medium text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Devices Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDevices.map((device) => {
          const DeviceIcon = getDeviceIcon(device.type);
          return (
            <div key={device.id} className="border border-gray-200 rounded-lg p-4">
              {/* Device Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <DeviceIcon className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{device.name}</h3>
                    <p className="text-sm text-gray-500">{device.brand} {device.model}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                  {device.status.toUpperCase()}
                </span>
              </div>

              {/* Device Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Connection:</span>
                  <div className="flex items-center space-x-1">
                    {getConnectionIcon(device.connection_type)}
                    <span className="text-gray-900">{device.connection_type.toUpperCase()}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Serial:</span>
                  <span className="text-gray-900 font-mono">{device.serial_number}</span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Firmware:</span>
                  <span className="text-gray-900">{device.firmware_version}</span>
                </div>

                {device.health.temperature && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Temperature:</span>
                    <div className="flex items-center space-x-1">
                      <Thermometer className="h-3 w-3 text-orange-500" />
                      <span className="text-gray-900">{device.health.temperature}°C</span>
                    </div>
                  </div>
                )}

                {device.health.signal_strength && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Signal:</span>
                    <div className="flex items-center space-x-1">
                      <Signal className="h-3 w-3 text-green-500" />
                      <span className="text-gray-900">{device.health.signal_strength}%</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Device Actions */}
              <div className="flex flex-wrap gap-1">
                {hardwareActions.map((action) => {
                  // Filter actions based on device status
                  if (action.type === 'connect' && device.status === 'connected') return null;
                  if (action.type === 'disconnect' && device.status === 'disconnected') return null;

                  return (
                    <button
                      key={action.id}
                      onClick={() => handleHardwareAction(action, device.id)}
                      disabled={actionLoading === `${action.id}_${device.id}`}
                      className={getActionButtonClass(action)}
                      title={action.label}
                    >
                      {actionLoading === `${action.id}_${device.id}` ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <action.icon className="h-3 w-3" />
                      )}
                      <span className="hidden sm:inline">{action.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      {filteredDevices.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Monitor className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p>No hardware devices found</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="mt-3 text-blue-600 hover:text-blue-800 font-medium"
          >
            Add your first device
          </button>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Action</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to perform this action? This may affect device operation.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const [actionId, deviceId] = showConfirmDialog.split('_');
                  const action = hardwareActions.find(a => a.id === actionId);
                  if (action) executeHardwareAction(action, deviceId);
                }}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};