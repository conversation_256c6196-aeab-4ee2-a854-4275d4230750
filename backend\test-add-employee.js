const http = require('http');

const employeeData = JSON.stringify({
  name: "Test Employee",
  pin: "1234",
  role: "server"
});

const options = {
  hostname: 'localhost',
  port: 4000,
  path: '/employees',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': employeeData.length
  }
};

const req = http.request(options, res => {
  console.log(`Add Employee Status: ${res.statusCode}`);
  let data = '';
  
  res.on('data', chunk => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response:', JSON.parse(data));
    
    // Now try to get all employees
    http.get('http://localhost:4000/employees', (getRes) => {
      let getAllData = '';
      
      getRes.on('data', (chunk) => {
        getAllData += chunk;
      });
      
      getRes.on('end', () => {
        console.log('\nAll Employees:', JSON.parse(getAllData));
      });
    });
  });
});

req.on('error', error => {
  console.error('Error:', error);
});

req.write(employeeData);
req.end();
