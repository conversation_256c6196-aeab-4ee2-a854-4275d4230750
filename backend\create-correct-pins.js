const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function createCorrectPinUsers() {
  const client = await pool.connect();
  
  try {
    console.log('Creating users with correct PIN formats...');
    
    // Get the demo tenant
    const tenantResult = await client.query(`
      SELECT id FROM tenants WHERE slug = 'demo-restaurant'
    `);
    
    if (tenantResult.rows.length === 0) {
      console.error('Demo tenant not found. Please run create-demo-user.js first.');
      return;
    }
    
    const tenantId = tenantResult.rows[0].id;
    
    // Delete existing demo users to recreate with correct PINs
    await client.query(`
      DELETE FROM employees WHERE tenant_id = $1 AND name IN ('Demo Admin', 'Demo Employee', 'Enhanced Admin', 'Main Admin')
    `, [tenantId]);
    
    console.log('Deleted existing demo users...');
    
    // Create Enhanced Interface Admin (6-digit PIN)
    console.log('Creating Enhanced Interface Admin (6-digit PIN)...');
    const enhancedPin = await bcrypt.hash('123456', 10);
    
    await client.query(`
      INSERT INTO employees (
        id, name, pin, role, tenant_id, permissions, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, true)
    `, [
      uuidv4(),
      'Enhanced Admin',
      enhancedPin,
      'super_admin',
      tenantId,
      JSON.stringify(['all'])
    ]);
    
    // Create Main Interface Admin (4-digit PIN)
    console.log('Creating Main Interface Admin (4-digit PIN)...');
    const mainPin = await bcrypt.hash('1234', 10);
    
    await client.query(`
      INSERT INTO employees (
        id, name, pin, role, tenant_id, permissions, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, true)
    `, [
      uuidv4(),
      'Main Admin',
      mainPin,
      'super_admin',
      tenantId,
      JSON.stringify(['all'])
    ]);
    
    // Create Enhanced Interface Employee (6-digit PIN)
    console.log('Creating Enhanced Interface Employee (6-digit PIN)...');
    const enhancedEmpPin = await bcrypt.hash('567890', 10);
    
    await client.query(`
      INSERT INTO employees (
        id, name, pin, role, tenant_id, permissions, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, true)
    `, [
      uuidv4(),
      'Enhanced Employee',
      enhancedEmpPin,
      'employee',
      tenantId,
      JSON.stringify([])
    ]);
    
    // Create Main Interface Employee (4-digit PIN)
    console.log('Creating Main Interface Employee (4-digit PIN)...');
    const mainEmpPin = await bcrypt.hash('5678', 10);
    
    await client.query(`
      INSERT INTO employees (
        id, name, pin, role, tenant_id, permissions, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, true)
    `, [
      uuidv4(),
      'Main Employee',
      mainEmpPin,
      'employee',
      tenantId,
      JSON.stringify([])
    ]);
    
    console.log('\n🎉 Users created successfully!');
    console.log('\n=== LOGIN CREDENTIALS ===');
    console.log('');
    console.log('🔥 ENHANCED INTERFACE (6-digit PINs)');
    console.log('   URL: http://localhost:5175/enhanced.html');
    console.log('   📱 Admin PIN: 123456 (Super Admin)');
    console.log('   📱 Employee PIN: 567890 (Employee)');
    console.log('   🏢 Tenant: demo-restaurant');
    console.log('');
    console.log('🔥 MAIN INTERFACE (4-digit PINs)');
    console.log('   URL: http://localhost:5175/');
    console.log('   📱 Admin PIN: 1234 (Super Admin)');
    console.log('   📱 Employee PIN: 5678 (Employee)');
    console.log('   🏢 Tenant: demo-restaurant');
    console.log('');
    console.log('🔥 OTHER INTERFACES');
    console.log('   Combined: http://localhost:5175/combined.html');
    console.log('   Kitchen: http://localhost:5175/kitchen.html');
    console.log('   Tenant: http://localhost:5175/tenant.html');
    console.log('');
    console.log('✨ All interfaces should now work with their respective PIN formats!');
    console.log('========================');
    
  } catch (error) {
    console.error('Error creating users:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

createCorrectPinUsers().catch(console.error);
