#!/usr/bin/env node

/**
 * Comprehensive Frontend Testing Suite for POS System
 * Tests all components, authentication, and functionality
 */

import puppeteer from 'puppeteer';
import colors from 'colors';

// Test configuration
const BASE_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:4000';
const TEST_TIMEOUT = 30000;

// Development credentials to test
const TEST_CREDENTIALS = [
  { pin: '888888', role: 'Super Admin', expected: true },
  { pin: '999999', role: 'Super Admin', expected: true },
  { pin: '123456', role: 'Employee', expected: true },
  { pin: '234567', role: 'Employee', expected: true },
  { pin: '567890', role: 'Manager', expected: true },
  { pin: '000000', role: 'Invalid', expected: false },
  { pin: '111111', role: 'Invalid', expected: false }
];

// Test results storage
const testResults = {
  loginInterface: { passed: 0, failed: 0, errors: [] },
  corePOS: { passed: 0, failed: 0, errors: [] },
  adminDashboard: { passed: 0, failed: 0, errors: [] },
  multiTenant: { passed: 0, failed: 0, errors: [] },
  performance: { passed: 0, failed: 0, errors: [] },
  consoleErrors: [],
  networkErrors: [],
  uiIssues: []
};

// Utility functions
const logTest = (category, test, status, details = '') => {
  const symbol = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  const color = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  console.log(`${symbol} [${category}] ${test}`[color]);
  if (details) console.log(`   ${details}`.gray);
  
  if (status === 'PASS') {
    testResults[category.toLowerCase().replace(' ', '')].passed++;
  } else {
    testResults[category.toLowerCase().replace(' ', '')].failed++;
    testResults[category.toLowerCase().replace(' ', '')].errors.push({ test, details });
  }
};

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Main testing function
async function runComprehensiveTests() {
  console.log('🧪 Starting Comprehensive Frontend Testing Suite...'.cyan.bold);
  console.log('=' .repeat(80).cyan);
  
  let browser, page;
  
  try {
    // Launch browser
    console.log('\n📱 Launching browser...'.blue);
    browser = await puppeteer.launch({ 
      headless: false, 
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    page = await browser.newPage();
    
    // Set up console monitoring
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'error') {
        testResults.consoleErrors.push({ type, text, timestamp: new Date().toISOString() });
        console.log(`🔴 Console Error: ${text}`.red);
      } else if (type === 'warning') {
        console.log(`🟡 Console Warning: ${text}`.yellow);
      }
    });
    
    // Set up network monitoring
    page.on('requestfailed', request => {
      testResults.networkErrors.push({
        url: request.url(),
        failure: request.failure().errorText,
        timestamp: new Date().toISOString()
      });
      console.log(`🌐 Network Error: ${request.url()} - ${request.failure().errorText}`.red);
    });
    
    // Test 1: Login Interface Testing
    await testLoginInterface(page);
    
    // Test 2: Core POS System Testing
    await testCorePOSSystem(page);
    
    // Test 3: Admin Dashboard Testing
    await testAdminDashboard(page);
    
    // Test 4: Multi-tenant Testing
    await testMultiTenant(page);
    
    // Test 5: Performance Testing
    await testPerformance(page);
    
  } catch (error) {
    console.error('💥 Critical test failure:', error.message);
    testResults.criticalError = error.message;
  } finally {
    if (browser) {
      await browser.close();
    }
    
    // Generate final report
    generateTestReport();
  }
}

// Test 1: Login Interface Testing
async function testLoginInterface(page) {
  console.log('\n🔐 Testing Login Interface...'.blue.bold);
  
  try {
    // Navigate to login page
    await page.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: TEST_TIMEOUT });
    
    // Test 1.1: Page loads correctly
    const title = await page.title();
    if (title.includes('POS')) {
      logTest('loginInterface', 'Page loads with correct title', 'PASS', `Title: ${title}`);
    } else {
      logTest('loginInterface', 'Page loads with correct title', 'FAIL', `Expected POS in title, got: ${title}`);
    }
    
    // Test 1.2: Enhanced Login indicator visible
    const enhancedIndicator = await page.$('div:contains("ENHANCED LOGIN ACTIVE")');
    if (enhancedIndicator) {
      logTest('loginInterface', 'Enhanced Login indicator visible', 'PASS');
    } else {
      logTest('loginInterface', 'Enhanced Login indicator visible', 'FAIL', 'Green indicator not found');
    }
    
    // Test 1.3: Number pad functionality
    await testNumberPad(page);
    
    // Test 1.4: PIN dot display
    await testPinDots(page);
    
    // Test 1.5: Authentication with all credentials
    for (const credential of TEST_CREDENTIALS) {
      await testAuthentication(page, credential);
    }
    
    // Test 1.6: Visual elements
    await testVisualElements(page);
    
  } catch (error) {
    logTest('loginInterface', 'Login interface testing', 'FAIL', error.message);
  }
}

// Test number pad functionality
async function testNumberPad(page) {
  try {
    // Test clicking number buttons
    for (let i = 1; i <= 9; i++) {
      const button = await page.$(`button:contains("${i}")`);
      if (button) {
        await button.click();
        await delay(100);
      }
    }
    
    // Test special buttons
    const clearButton = await page.$('button:contains("Clear")');
    const zeroButton = await page.$('button:contains("0")');
    const backspaceButton = await page.$('button:contains("⌫")');
    
    if (clearButton && zeroButton && backspaceButton) {
      logTest('loginInterface', 'Number pad buttons present', 'PASS');
      
      // Test clear functionality
      await clearButton.click();
      logTest('loginInterface', 'Clear button functionality', 'PASS');
    } else {
      logTest('loginInterface', 'Number pad buttons present', 'FAIL', 'Missing number pad buttons');
    }
    
  } catch (error) {
    logTest('loginInterface', 'Number pad functionality', 'FAIL', error.message);
  }
}

// Test PIN dots display
async function testPinDots(page) {
  try {
    // Count PIN dot elements
    const pinDots = await page.$$('div[style*="width: 48px"][style*="height: 48px"]');
    if (pinDots.length === 6) {
      logTest('loginInterface', 'PIN dots display (6 dots)', 'PASS');
    } else {
      logTest('loginInterface', 'PIN dots display (6 dots)', 'FAIL', `Found ${pinDots.length} dots instead of 6`);
    }
  } catch (error) {
    logTest('loginInterface', 'PIN dots display', 'FAIL', error.message);
  }
}

// Test authentication with credentials
async function testAuthentication(page, credential) {
  try {
    // Clear any existing PIN
    const clearButton = await page.$('button:contains("Clear")');
    if (clearButton) await clearButton.click();
    
    // Enter PIN
    for (const digit of credential.pin) {
      const button = await page.$(`button:contains("${digit}")`);
      if (button) {
        await button.click();
        await delay(100);
      }
    }
    
    // Click Sign In
    const signInButton = await page.$('button:contains("Sign In")');
    if (signInButton) {
      await signInButton.click();
      await delay(2000); // Wait for authentication
      
      // Check result
      const currentUrl = page.url();
      const isLoggedIn = !currentUrl.includes('login') || await page.$('.pos-interface');
      
      if (credential.expected && isLoggedIn) {
        logTest('loginInterface', `Authentication with ${credential.role} PIN ${credential.pin}`, 'PASS');
      } else if (!credential.expected && !isLoggedIn) {
        logTest('loginInterface', `Authentication rejection for invalid PIN ${credential.pin}`, 'PASS');
      } else {
        logTest('loginInterface', `Authentication with PIN ${credential.pin}`, 'FAIL', 
          `Expected ${credential.expected ? 'success' : 'failure'}, got ${isLoggedIn ? 'success' : 'failure'}`);
      }
    } else {
      logTest('loginInterface', `Sign In button availability for PIN ${credential.pin}`, 'FAIL', 'Sign In button not found');
    }
    
  } catch (error) {
    logTest('loginInterface', `Authentication test for PIN ${credential.pin}`, 'FAIL', error.message);
  }
}

// Test visual elements
async function testVisualElements(page) {
  try {
    // Test gradient background
    const backgroundElement = await page.$('div[style*="linear-gradient"]');
    if (backgroundElement) {
      logTest('loginInterface', 'Gradient background present', 'PASS');
    } else {
      logTest('loginInterface', 'Gradient background present', 'FAIL', 'Gradient background not found');
    }
    
    // Test animated blobs
    const blobElements = await page.$$('div[style*="animation: blob"]');
    if (blobElements.length >= 3) {
      logTest('loginInterface', 'Animated blob backgrounds', 'PASS', `Found ${blobElements.length} animated blobs`);
    } else {
      logTest('loginInterface', 'Animated blob backgrounds', 'FAIL', `Expected 3+ blobs, found ${blobElements.length}`);
    }
    
  } catch (error) {
    logTest('loginInterface', 'Visual elements testing', 'FAIL', error.message);
  }
}

// Test 2: Core POS System Testing
async function testCorePOSSystem(page) {
  console.log('\n🏪 Testing Core POS System...'.blue.bold);

  try {
    // First login with valid credentials
    await loginWithCredentials(page, '888888');

    // Test 2.1: UnifiedPOSSystem loads
    await page.waitForSelector('.pos-interface, .unified-pos', { timeout: 10000 });
    logTest('corePOS', 'UnifiedPOSSystem loads after login', 'PASS');

    // Test 2.2: Navigation components
    await testNavigationComponents(page);

    // Test 2.3: Product Grid functionality
    await testProductGrid(page);

    // Test 2.4: Order Panel functionality
    await testOrderPanel(page);

    // Test 2.5: Floor Layout functionality
    await testFloorLayout(page);

    // Test 2.6: Inventory management
    await testInventoryManagement(page);

  } catch (error) {
    logTest('corePOS', 'Core POS system testing', 'FAIL', error.message);
  }
}

// Test 3: Admin Dashboard Testing
async function testAdminDashboard(page) {
  console.log('\n👑 Testing Admin Dashboard...'.blue.bold);

  try {
    // Test 3.1: Enhanced Admin Dashboard access
    await testEnhancedAdminDashboard(page);

    // Test 3.2: Phase 3A-3F features
    await testPhase3Features(page);

    // Test 3.3: Super Admin Dashboard
    await testSuperAdminDashboard(page);

  } catch (error) {
    logTest('adminDashboard', 'Admin dashboard testing', 'FAIL', error.message);
  }
}

// Test 4: Multi-tenant Testing
async function testMultiTenant(page) {
  console.log('\n🏢 Testing Multi-tenant Functionality...'.blue.bold);

  try {
    // Test 4.1: Tenant switching
    await testTenantSwitching(page);

    // Test 4.2: Role-based access control
    await testRoleBasedAccess(page);

    // Test 4.3: Location management
    await testLocationManagement(page);

  } catch (error) {
    logTest('multiTenant', 'Multi-tenant testing', 'FAIL', error.message);
  }
}

// Test 5: Performance Testing
async function testPerformance(page) {
  console.log('\n⚡ Testing Performance...'.blue.bold);

  try {
    // Test 5.1: Page load times
    await testPageLoadTimes(page);

    // Test 5.2: Component rendering performance
    await testComponentPerformance(page);

    // Test 5.3: Memory usage
    await testMemoryUsage(page);

    // Test 5.4: Network performance
    await testNetworkPerformance(page);

  } catch (error) {
    logTest('performance', 'Performance testing', 'FAIL', error.message);
  }
}

// Helper function to login with credentials
async function loginWithCredentials(page, pin) {
  try {
    // Navigate to login if not already there
    if (!page.url().includes(BASE_URL)) {
      await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
    }

    // Clear any existing PIN
    const clearButton = await page.$('button:contains("Clear")');
    if (clearButton) await clearButton.click();

    // Enter PIN
    for (const digit of pin) {
      const button = await page.$(`button:contains("${digit}")`);
      if (button) {
        await button.click();
        await delay(100);
      }
    }

    // Click Sign In
    const signInButton = await page.$('button:contains("Sign In")');
    if (signInButton) {
      await signInButton.click();
      await delay(3000); // Wait for login to complete
    }

    return true;
  } catch (error) {
    console.error('Login failed:', error.message);
    return false;
  }
}

// Test navigation components
async function testNavigationComponents(page) {
  try {
    // Look for common navigation elements
    const navElements = await page.$$('nav, .navigation, .menu, .tabs');
    if (navElements.length > 0) {
      logTest('corePOS', 'Navigation components present', 'PASS', `Found ${navElements.length} navigation elements`);
    } else {
      logTest('corePOS', 'Navigation components present', 'FAIL', 'No navigation elements found');
    }

    // Test for specific POS navigation items
    const posNavItems = ['Products', 'Orders', 'Floor', 'Inventory', 'Analytics', 'Admin'];
    let foundItems = 0;

    for (const item of posNavItems) {
      const element = await page.$(`*:contains("${item}")`);
      if (element) foundItems++;
    }

    if (foundItems >= 3) {
      logTest('corePOS', 'POS navigation items', 'PASS', `Found ${foundItems}/${posNavItems.length} navigation items`);
    } else {
      logTest('corePOS', 'POS navigation items', 'FAIL', `Only found ${foundItems}/${posNavItems.length} navigation items`);
    }

  } catch (error) {
    logTest('corePOS', 'Navigation components testing', 'FAIL', error.message);
  }
}

// Test product grid functionality
async function testProductGrid(page) {
  try {
    // Look for product grid elements
    const productElements = await page.$$('.product, .product-card, .product-item, [data-testid*="product"]');
    if (productElements.length > 0) {
      logTest('corePOS', 'Product grid displays products', 'PASS', `Found ${productElements.length} product elements`);

      // Test clicking on a product
      await productElements[0].click();
      await delay(1000);
      logTest('corePOS', 'Product selection functionality', 'PASS');
    } else {
      logTest('corePOS', 'Product grid displays products', 'FAIL', 'No product elements found');
    }
  } catch (error) {
    logTest('corePOS', 'Product grid testing', 'FAIL', error.message);
  }
}

// Test order panel functionality
async function testOrderPanel(page) {
  try {
    // Look for order panel elements
    const orderElements = await page.$$('.order, .order-panel, .cart, [data-testid*="order"]');
    if (orderElements.length > 0) {
      logTest('corePOS', 'Order panel present', 'PASS', `Found ${orderElements.length} order elements`);
    } else {
      logTest('corePOS', 'Order panel present', 'FAIL', 'No order panel elements found');
    }
  } catch (error) {
    logTest('corePOS', 'Order panel testing', 'FAIL', error.message);
  }
}

// Test floor layout functionality
async function testFloorLayout(page) {
  try {
    // Look for floor layout elements
    const floorElements = await page.$$('.floor, .table, .floor-layout, [data-testid*="floor"]');
    if (floorElements.length > 0) {
      logTest('corePOS', 'Floor layout components', 'PASS', `Found ${floorElements.length} floor elements`);
    } else {
      logTest('corePOS', 'Floor layout components', 'FAIL', 'No floor layout elements found');
    }
  } catch (error) {
    logTest('corePOS', 'Floor layout testing', 'FAIL', error.message);
  }
}

// Test inventory management
async function testInventoryManagement(page) {
  try {
    // Look for inventory elements
    const inventoryElements = await page.$$('.inventory, .stock, [data-testid*="inventory"]');
    if (inventoryElements.length > 0) {
      logTest('corePOS', 'Inventory management components', 'PASS', `Found ${inventoryElements.length} inventory elements`);
    } else {
      logTest('corePOS', 'Inventory management components', 'FAIL', 'No inventory elements found');
    }
  } catch (error) {
    logTest('corePOS', 'Inventory management testing', 'FAIL', error.message);
  }
}

// Test Enhanced Admin Dashboard
async function testEnhancedAdminDashboard(page) {
  try {
    // Look for admin dashboard access
    const adminButton = await page.$('*:contains("Admin"), *:contains("Dashboard")');
    if (adminButton) {
      await adminButton.click();
      await delay(2000);
      logTest('adminDashboard', 'Enhanced Admin Dashboard access', 'PASS');
    } else {
      logTest('adminDashboard', 'Enhanced Admin Dashboard access', 'FAIL', 'Admin dashboard button not found');
    }
  } catch (error) {
    logTest('adminDashboard', 'Enhanced Admin Dashboard testing', 'FAIL', error.message);
  }
}

// Test Phase 3A-3F features
async function testPhase3Features(page) {
  try {
    const phase3Features = [
      'PHASE 3A', 'PHASE 3B', 'PHASE 3C', 'PHASE 3D', 'PHASE 3E', 'PHASE 3F'
    ];

    let foundFeatures = 0;
    for (const feature of phase3Features) {
      const element = await page.$(`*:contains("${feature}")`);
      if (element) foundFeatures++;
    }

    if (foundFeatures >= 4) {
      logTest('adminDashboard', 'Phase 3A-3F features accessibility', 'PASS', `Found ${foundFeatures}/6 Phase 3 features`);
    } else {
      logTest('adminDashboard', 'Phase 3A-3F features accessibility', 'FAIL', `Only found ${foundFeatures}/6 Phase 3 features`);
    }
  } catch (error) {
    logTest('adminDashboard', 'Phase 3 features testing', 'FAIL', error.message);
  }
}

// Test Super Admin Dashboard
async function testSuperAdminDashboard(page) {
  try {
    // Look for super admin elements
    const superAdminElements = await page.$$('*:contains("Super Admin"), *:contains("Tenant Management")');
    if (superAdminElements.length > 0) {
      logTest('adminDashboard', 'Super Admin Dashboard components', 'PASS', `Found ${superAdminElements.length} super admin elements`);
    } else {
      logTest('adminDashboard', 'Super Admin Dashboard components', 'FAIL', 'No super admin elements found');
    }
  } catch (error) {
    logTest('adminDashboard', 'Super Admin Dashboard testing', 'FAIL', error.message);
  }
}

// Test tenant switching
async function testTenantSwitching(page) {
  try {
    // Look for tenant switching elements
    const tenantElements = await page.$$('*:contains("Tenant"), *:contains("Restaurant"), select[name*="tenant"]');
    if (tenantElements.length > 0) {
      logTest('multiTenant', 'Tenant switching components', 'PASS', `Found ${tenantElements.length} tenant elements`);
    } else {
      logTest('multiTenant', 'Tenant switching components', 'FAIL', 'No tenant switching elements found');
    }
  } catch (error) {
    logTest('multiTenant', 'Tenant switching testing', 'FAIL', error.message);
  }
}

// Test role-based access control
async function testRoleBasedAccess(page) {
  try {
    // Test different role access by checking for role-specific elements
    const roleElements = await page.$$('*:contains("Admin"), *:contains("Manager"), *:contains("Employee")');
    if (roleElements.length > 0) {
      logTest('multiTenant', 'Role-based access control', 'PASS', `Found ${roleElements.length} role-related elements`);
    } else {
      logTest('multiTenant', 'Role-based access control', 'FAIL', 'No role-based elements found');
    }
  } catch (error) {
    logTest('multiTenant', 'Role-based access testing', 'FAIL', error.message);
  }
}

// Test location management
async function testLocationManagement(page) {
  try {
    // Look for location management elements
    const locationElements = await page.$$('*:contains("Location"), *:contains("Store"), select[name*="location"]');
    if (locationElements.length > 0) {
      logTest('multiTenant', 'Location management components', 'PASS', `Found ${locationElements.length} location elements`);
    } else {
      logTest('multiTenant', 'Location management components', 'FAIL', 'No location management elements found');
    }
  } catch (error) {
    logTest('multiTenant', 'Location management testing', 'FAIL', error.message);
  }
}

// Test page load times
async function testPageLoadTimes(page) {
  try {
    const startTime = Date.now();
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
    const loadTime = Date.now() - startTime;

    if (loadTime < 5000) {
      logTest('performance', 'Page load time', 'PASS', `Loaded in ${loadTime}ms`);
    } else {
      logTest('performance', 'Page load time', 'FAIL', `Slow load time: ${loadTime}ms`);
    }
  } catch (error) {
    logTest('performance', 'Page load time testing', 'FAIL', error.message);
  }
}

// Test component rendering performance
async function testComponentPerformance(page) {
  try {
    // Measure component rendering time
    const startTime = Date.now();
    await page.evaluate(() => {
      // Trigger component re-renders
      window.dispatchEvent(new Event('resize'));
    });
    const renderTime = Date.now() - startTime;

    if (renderTime < 1000) {
      logTest('performance', 'Component rendering performance', 'PASS', `Rendered in ${renderTime}ms`);
    } else {
      logTest('performance', 'Component rendering performance', 'FAIL', `Slow rendering: ${renderTime}ms`);
    }
  } catch (error) {
    logTest('performance', 'Component performance testing', 'FAIL', error.message);
  }
}

// Test memory usage
async function testMemoryUsage(page) {
  try {
    const metrics = await page.metrics();
    const memoryUsage = metrics.JSHeapUsedSize / 1024 / 1024; // Convert to MB

    if (memoryUsage < 100) {
      logTest('performance', 'Memory usage', 'PASS', `Using ${memoryUsage.toFixed(2)}MB`);
    } else {
      logTest('performance', 'Memory usage', 'WARN', `High memory usage: ${memoryUsage.toFixed(2)}MB`);
    }
  } catch (error) {
    logTest('performance', 'Memory usage testing', 'FAIL', error.message);
  }
}

// Test network performance
async function testNetworkPerformance(page) {
  try {
    // Test API response times
    const startTime = Date.now();
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/health');
        return res.ok;
      } catch (error) {
        return false;
      }
    });
    const responseTime = Date.now() - startTime;

    if (response && responseTime < 2000) {
      logTest('performance', 'API response time', 'PASS', `API responded in ${responseTime}ms`);
    } else {
      logTest('performance', 'API response time', 'FAIL', `Slow API response: ${responseTime}ms or failed`);
    }
  } catch (error) {
    logTest('performance', 'Network performance testing', 'FAIL', error.message);
  }
}

// Generate final test report
function generateTestReport() {
  console.log('\n📊 COMPREHENSIVE TEST REPORT'.cyan.bold);
  console.log('=' .repeat(80).cyan);
  
  const categories = ['loginInterface', 'corePOS', 'adminDashboard', 'multiTenant', 'performance'];
  let totalPassed = 0, totalFailed = 0;
  
  categories.forEach(category => {
    const result = testResults[category];
    totalPassed += result.passed;
    totalFailed += result.failed;
    
    const status = result.failed === 0 ? '✅ PASS' : '❌ FAIL';
    console.log(`\n${status} ${category.toUpperCase()}: ${result.passed} passed, ${result.failed} failed`);
    
    if (result.errors.length > 0) {
      result.errors.forEach(error => {
        console.log(`   ❌ ${error.test}: ${error.details}`.red);
      });
    }
  });
  
  console.log(`\n🎯 OVERALL RESULTS: ${totalPassed} passed, ${totalFailed} failed`.bold);
  
  if (testResults.consoleErrors.length > 0) {
    console.log(`\n🔴 CONSOLE ERRORS (${testResults.consoleErrors.length}):`.red.bold);
    testResults.consoleErrors.forEach(error => {
      console.log(`   ${error.timestamp}: ${error.text}`.red);
    });
  }
  
  if (testResults.networkErrors.length > 0) {
    console.log(`\n🌐 NETWORK ERRORS (${testResults.networkErrors.length}):`.red.bold);
    testResults.networkErrors.forEach(error => {
      console.log(`   ${error.timestamp}: ${error.url} - ${error.failure}`.red);
    });
  }
}

// Run the tests
runComprehensiveTests().catch(console.error);
