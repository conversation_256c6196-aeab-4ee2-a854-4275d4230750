-- Database setup for POS system
-- Run this SQL to create the missing tables

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    sku VARCHAR(100) UNIQUE,
    tenant_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50) DEFAULT 'folder',
    tenant_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE(name, tenant_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_tenant_id ON products(tenant_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_categories_tenant_id ON categories(tenant_id);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);

-- Insert some sample data for testing
INSERT INTO categories (name, description, color, icon, tenant_id, sort_order) VALUES
('Main Course', 'Main course dishes', '#FF6B6B', 'utensils', 17, 1),
('Appetizers', 'Starter dishes', '#4ECDC4', 'cookie-bite', 17, 2),
('Beverages', 'Drinks and beverages', '#45B7D1', 'glass-whiskey', 17, 3),
('Desserts', 'Sweet treats', '#F7DC6F', 'ice-cream', 17, 4)
ON CONFLICT (name, tenant_id) DO NOTHING;

INSERT INTO products (name, price, category, description, sku, tenant_id) VALUES
('Grilled Chicken Breast', 18.99, 'Main Course', 'Tender grilled chicken breast with herbs', 'GCB001', 17),
('Caesar Salad', 12.99, 'Appetizers', 'Fresh romaine lettuce with caesar dressing', 'CS001', 17),
('Beef Burger', 16.99, 'Main Course', 'Juicy beef burger with fries', 'BB001', 17),
('Coca Cola', 2.99, 'Beverages', 'Refreshing cola drink', 'CC001', 17),
('Chocolate Cake', 8.99, 'Desserts', 'Rich chocolate cake slice', 'CHOC001', 17)
ON CONFLICT (sku) DO NOTHING;

-- Verify tables were created
SELECT 'Products table created' as status, COUNT(*) as product_count FROM products WHERE tenant_id = 17;
SELECT 'Categories table created' as status, COUNT(*) as category_count FROM categories WHERE tenant_id = 17;
