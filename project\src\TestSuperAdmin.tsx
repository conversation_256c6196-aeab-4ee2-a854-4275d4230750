import React from 'react';

const TestSuperAdmin: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 25%, #991b1b 50%, #7f1d1d 75%, #450a0a 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        textAlign: 'center',
        padding: '40px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '20px',
        backdropFilter: 'blur(10px)',
        border: '2px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{ fontSize: '48px', marginBottom: '20px' }}>🔒 SUPER ADMIN TEST</h1>
        <h2 style={{ fontSize: '24px', marginBottom: '20px' }}>Super Admin Component Loading Successfully!</h2>
        
        <div style={{ marginBottom: '30px' }}>
          <p style={{ fontSize: '18px', marginBottom: '10px' }}>✅ Super Admin HTML: Loaded</p>
          <p style={{ fontSize: '18px', marginBottom: '10px' }}>✅ Main Super Admin TSX: Loaded</p>
          <p style={{ fontSize: '18px', marginBottom: '10px' }}>✅ Test Component: Loaded</p>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '20px',
          borderRadius: '10px',
          marginBottom: '20px'
        }}>
          <h3 style={{ marginBottom: '15px' }}>🔍 Debug Information</h3>
          <p><strong>Current URL:</strong> {window.location.href}</p>
          <p><strong>Timestamp:</strong> {new Date().toISOString()}</p>
          <p><strong>Component:</strong> TestSuperAdmin</p>
        </div>

        <button
          onClick={() => {
            console.log('🔄 Reloading to actual Super Admin System...');
            window.location.reload();
          }}
          style={{
            background: 'linear-gradient(45deg, #ef4444, #dc2626)',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '10px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'transform 0.2s'
          }}
          onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
          onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
        >
          🔄 Load Actual Super Admin System
        </button>
      </div>
    </div>
  );
};

export default TestSuperAdmin;
