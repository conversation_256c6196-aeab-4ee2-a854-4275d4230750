import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { 
  Lock, 
  User, 
  Shield, 
  Building, 
  Eye, 
  EyeOff, 
  ArrowRight,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface ModernLoginProps {
  onLogin: (success: boolean) => void;
  onShowRegistration?: () => void;
  loginType?: 'pos' | 'admin';
}

const ModernLogin: React.FC<ModernLoginProps> = ({ 
  onLogin, 
  onShowRegistration,
  loginType = 'pos'
}) => {
  const { login, state } = useEnhancedAppContext();
  const [pin, setPin] = useState('');
  const [tenantSlug, setTenantSlug] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check for dark mode preference
  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  // Set default tenant slug
  useEffect(() => {
    const storedTenantSlug = localStorage.getItem('tenantSlug');
    if (storedTenantSlug) {
      setTenantSlug(storedTenantSlug);
    } else {
      setTenantSlug('barpos-system');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin.trim()) {
      setError('Please enter your PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const success = await login(pin, tenantSlug || undefined);
      if (success) {
        // Check access level for admin login
        if (loginType === 'admin') {
          setTimeout(() => {
            const currentEmployeeData = localStorage.getItem('currentEmployee');
            if (currentEmployeeData) {
              try {
                const currentEmployee = JSON.parse(currentEmployeeData);
                if (currentEmployee.role === 'super_admin' || currentEmployee.role === 'tenant_admin') {
                  onLogin(true);
                } else {
                  setError('Access Denied: Administrative privileges required');
                }
              } catch (parseError) {
                setError('Access Denied: Invalid credentials');
              }
            } else {
              setError('Access Denied: Authentication failed');
            }
          }, 100);
        } else {
          onLogin(true);
        }
      } else {
        setError('Invalid PIN. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Connection failed. Please check your network connection.');
      } else {
        setError('Login failed. Please check your PIN and try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  const handleBackspace = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handleClear = () => {
    setPin('');
    setError('');
  };

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  const isAdminLogin = loginType === 'admin';

  return (
    <div className={`min-h-screen w-full flex items-center justify-center p-4 relative overflow-hidden transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900' 
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
    }`}>
      
      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className={`fixed top-4 right-4 p-3 rounded-full transition-all duration-300 z-50 ${
          isDarkMode 
            ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' 
            : 'bg-white text-gray-600 hover:bg-gray-100'
        } shadow-lg hover:shadow-xl`}
      >
        {isDarkMode ? '☀️' : '🌙'}
      </button>

      {/* Main Login Card */}
      <div className={`w-full max-w-md relative z-10 transition-all duration-300 ${
        isDarkMode 
          ? 'bg-gray-800/90 border-gray-700' 
          : 'bg-white/90 border-gray-200'
      } backdrop-blur-xl rounded-2xl shadow-2xl border p-8`}>
        
        {/* Header */}
        <div className="text-center mb-8">
          <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
            isAdminLogin 
              ? 'bg-gradient-to-r from-red-500 to-pink-600' 
              : 'bg-gradient-to-r from-blue-500 to-indigo-600'
          } shadow-lg`}>
            {isAdminLogin ? (
              <Shield className="w-8 h-8 text-white" />
            ) : (
              <Building className="w-8 h-8 text-white" />
            )}
          </div>
          
          <h1 className={`text-2xl font-bold mb-2 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            {isAdminLogin ? 'Admin Dashboard' : 'RestroFlow POS'}
          </h1>
          
          <p className={`text-sm ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            {isAdminLogin 
              ? 'Administrative Access Portal' 
              : 'Restaurant Management System'
            }
          </p>
        </div>

        {/* PIN Input Section */}
        <form onSubmit={handleSubmit} className="space-y-6">
          
          {/* PIN Display */}
          <div>
            <label className={`block text-sm font-medium mb-3 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-700'
            }`}>
              <Lock className="w-4 h-4 inline mr-2" />
              Enter Your PIN
            </label>
            
            <div className="relative">
              <input
                type={showPin ? 'text' : 'password'}
                value={pin}
                onChange={(e) => setPin(e.target.value.slice(0, 6))}
                className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 text-center text-lg font-mono tracking-widest ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                    : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                placeholder="••••••"
                maxLength={6}
                autoComplete="off"
              />
              
              <button
                type="button"
                onClick={() => setShowPin(!showPin)}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${
                  isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'
                } transition-colors`}
              >
                {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Number Pad */}
          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
              <button
                key={digit}
                type="button"
                onClick={() => handlePinInput(digit.toString())}
                disabled={isLoading}
                className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                } hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {digit}
              </button>
            ))}
            
            <button
              type="button"
              onClick={handleClear}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium text-sm transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-red-600 hover:bg-red-500 text-white' 
                  : 'bg-red-100 hover:bg-red-200 text-red-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              Clear
            </button>
            
            <button
              type="button"
              onClick={() => handlePinInput('0')}
              disabled={isLoading}
              className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              0
            </button>
            
            <button
              type="button"
              onClick={handleBackspace}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-yellow-600 hover:bg-yellow-500 text-white' 
                  : 'bg-yellow-100 hover:bg-yellow-200 text-yellow-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50 flex items-center justify-center`}
            >
              ⌫
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 ${
              isDarkMode 
                ? 'bg-red-900/50 border border-red-700 text-red-300' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Login Button */}
          <button
            type="submit"
            disabled={pin.length === 0 || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              pin.length === 0 || isLoading
                ? isDarkMode 
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : isAdminLogin
                  ? 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl'
                  : 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl'
            } transform hover:scale-105 active:scale-95`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Signing In...</span>
              </>
            ) : (
              <>
                <span>{isAdminLogin ? 'Access Dashboard' : 'Sign In'}</span>
                <ArrowRight className="w-5 h-5" />
              </>
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>Secure {isAdminLogin ? 'Administrative' : 'Point of Sale'} System</p>
            <p className="mt-1">© 2024 RestroFlow. All rights reserved.</p>
          </div>
          
          {onShowRegistration && !isAdminLogin && (
            <button
              type="button"
              onClick={onShowRegistration}
              className={`mt-4 text-sm font-medium transition-colors ${
                isDarkMode 
                  ? 'text-blue-400 hover:text-blue-300' 
                  : 'text-blue-600 hover:text-blue-800'
              }`}
            >
              Create New Business Account
            </button>
          )}
        </div>
      </div>

      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob ${
          isDarkMode ? 'bg-blue-600' : 'bg-purple-300'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000 ${
          isDarkMode ? 'bg-indigo-600' : 'bg-yellow-300'
        }`}></div>
        <div className={`absolute top-40 left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000 ${
          isDarkMode ? 'bg-purple-600' : 'bg-pink-300'
        }`}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob { animation: blob 7s infinite; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-4000 { animation-delay: 4s; }
      `}</style>
    </div>
  );
};

export default ModernLogin;
