import React, { useState, useEffect } from 'react';
import { 
  Building, 
  Settings, 
  AlertCircle, 
  CheckCircle,
  Loader,
  RefreshCw
} from 'lucide-react';
import { BusinessType, IndustryThemeProvider } from '../contexts/IndustryThemeContext';
import FineDiningInterface from './industry-interfaces/FineDiningInterface';
import QuickServiceInterface from './industry-interfaces/QuickServiceInterface';
import CafeInterface from './industry-interfaces/CafeInterface';
import BarInterface from './industry-interfaces/BarInterface';
import FoodTruckInterface from './industry-interfaces/FoodTruckInterface';
import CateringInterface from './industry-interfaces/CateringInterface';
import HotelInterface from './industry-interfaces/HotelInterface';
import BusinessTypeSelector from './BusinessTypeSelector';

interface TenantBusinessType {
  id: string;
  tenantId: string;
  businessTypeCode: string;
  businessTypeName: string;
  features: Record<string, boolean>;
  theme: Record<string, any>;
  configuredAt: Date;
  isActive: boolean;
}

interface IndustrySpecificPOSInterfaceProps {
  tenantId?: string;
  userRole?: string;
  onBusinessTypeChange?: (businessType: BusinessType) => void;
}

const IndustrySpecificPOSInterface: React.FC<IndustrySpecificPOSInterfaceProps> = ({
  tenantId,
  userRole = 'staff',
  onBusinessTypeChange
}) => {
  const [tenantBusinessType, setTenantBusinessType] = useState<TenantBusinessType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showBusinessTypeSelector, setShowBusinessTypeSelector] = useState(false);
  const [isConfiguring, setIsConfiguring] = useState(false);

  // Mock business types for demo
  const BUSINESS_TYPES: BusinessType[] = [
    {
      id: '1',
      name: 'Fine Dining Restaurant',
      code: 'FINE_DINING',
      description: 'Upscale restaurants with table service, wine pairings, and multi-course meals',
      icon: '🍽️',
      colorScheme: 'burgundy',
      defaultTheme: {},
      featureSet: {
        wineManagement: true,
        courseTiming: true,
        guestProfiles: true,
        sommelierNotes: true,
        tableSideService: true
      },
      workflowConfig: {
        serviceType: 'table',
        orderFlow: 'multi_course',
        paymentTiming: 'end_of_meal',
        reservationRequired: true
      }
    },
    {
      id: '2',
      name: 'Quick Service Restaurant',
      code: 'QUICK_SERVICE',
      description: 'Fast food restaurants with counter service and quick turnover',
      icon: '⚡',
      colorScheme: 'orange',
      defaultTheme: {},
      featureSet: {
        orderQueue: true,
        kitchenDisplay: true,
        mobileOrdering: true,
        loyaltyProgram: true,
        driveThrough: true
      },
      workflowConfig: {
        serviceType: 'counter',
        orderFlow: 'single_step',
        paymentTiming: 'immediate',
        reservationRequired: false
      }
    }
    // Add other business types as needed
  ];

  useEffect(() => {
    loadTenantBusinessType();
  }, [tenantId]);

  const loadTenantBusinessType = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch from the API
      // For now, we'll simulate loading tenant business type
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data - in real app, this would come from the database
      const mockTenantBusinessType: TenantBusinessType = {
        id: '1',
        tenantId: tenantId || 'demo-tenant',
        businessTypeCode: 'FINE_DINING',
        businessTypeName: 'Fine Dining Restaurant',
        features: {
          wineManagement: true,
          courseTiming: true,
          guestProfiles: true
        },
        theme: {
          primaryColor: '#7C2D12',
          secondaryColor: '#DC2626'
        },
        configuredAt: new Date(),
        isActive: true
      };
      
      setTenantBusinessType(mockTenantBusinessType);
    } catch (err) {
      setError('Failed to load business type configuration');
      console.error('Error loading tenant business type:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessTypeSelection = async (businessType: BusinessType) => {
    setIsConfiguring(true);
    setError(null);
    
    try {
      // In a real implementation, this would save to the API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newTenantBusinessType: TenantBusinessType = {
        id: Date.now().toString(),
        tenantId: tenantId || 'demo-tenant',
        businessTypeCode: businessType.code,
        businessTypeName: businessType.name,
        features: businessType.featureSet,
        theme: businessType.defaultTheme,
        configuredAt: new Date(),
        isActive: true
      };
      
      setTenantBusinessType(newTenantBusinessType);
      setShowBusinessTypeSelector(false);
      onBusinessTypeChange?.(businessType);
      
    } catch (err) {
      setError('Failed to configure business type');
      console.error('Error configuring business type:', err);
    } finally {
      setIsConfiguring(false);
    }
  };

  const renderIndustryInterface = () => {
    if (!tenantBusinessType) return null;
    
    const businessType = BUSINESS_TYPES.find(bt => bt.code === tenantBusinessType.businessTypeCode);
    
    switch (tenantBusinessType.businessTypeCode) {
      case 'FINE_DINING':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <FineDiningInterface />
          </IndustryThemeProvider>
        );
      case 'QUICK_SERVICE':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <QuickServiceInterface />
          </IndustryThemeProvider>
        );
      case 'CAFE':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <CafeInterface />
          </IndustryThemeProvider>
        );
      case 'BAR':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <BarInterface />
          </IndustryThemeProvider>
        );
      case 'FOOD_TRUCK':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <FoodTruckInterface />
          </IndustryThemeProvider>
        );
      case 'CATERING':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <CateringInterface />
          </IndustryThemeProvider>
        );
      case 'HOTEL':
        return (
          <IndustryThemeProvider initialBusinessType={businessType}>
            <HotelInterface />
          </IndustryThemeProvider>
        );
      default:
        return (
          <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Unsupported Business Type
              </h3>
              <p className="text-gray-600">
                The configured business type is not yet supported.
              </p>
            </div>
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading business type configuration...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration Error</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadTenantBusinessType}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center mx-auto"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!tenantBusinessType || showBusinessTypeSelector) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl shadow-xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <Building className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Business Type Configuration</h1>
                <p className="text-blue-100">
                  {tenantBusinessType ? 'Change your business type' : 'Configure your restaurant type to get started'}
                </p>
              </div>
            </div>
            {tenantBusinessType && (
              <button
                onClick={() => setShowBusinessTypeSelector(false)}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Cancel
              </button>
            )}
          </div>
        </div>

        {/* Business Type Selector */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
          {isConfiguring ? (
            <div className="text-center py-12">
              <Loader className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Configuring Business Type
              </h3>
              <p className="text-gray-600">
                Setting up your industry-specific POS interface...
              </p>
            </div>
          ) : (
            <BusinessTypeSelector
              onSelect={handleBusinessTypeSelection}
              selectedType={null}
              showDetails={true}
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Business Type Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <div>
              <h3 className="font-medium text-gray-900">
                {tenantBusinessType.businessTypeName}
              </h3>
              <p className="text-sm text-gray-600">
                Configured on {tenantBusinessType.configuredAt.toLocaleDateString()}
              </p>
            </div>
          </div>
          
          {(userRole === 'tenant_admin' || userRole === 'super_admin') && (
            <button
              onClick={() => setShowBusinessTypeSelector(true)}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-sm transition-colors flex items-center"
            >
              <Settings className="w-4 h-4 mr-1" />
              Change Type
            </button>
          )}
        </div>
      </div>

      {/* Industry-Specific Interface */}
      <div className="min-h-screen">
        {renderIndustryInterface()}
      </div>
    </div>
  );
};

export default IndustrySpecificPOSInterface;
