import axios from 'axios';
import { Tenant, CreateTenantRequest, UpdateTenantRequest } from '../types/tenant';

const API_BASE_URL = 'http://localhost:4000/api';

export class TenantService {
  private static instance: TenantService;
  private token: string | null = null;

  private constructor() {}

  static getInstance(): TenantService {
    if (!TenantService.instance) {
      TenantService.instance = new TenantService();
    }
    return TenantService.instance;
  }

  setToken(token: string) {
    this.token = token;
  }

  private getHeaders() {
    return {
      'Content-Type': 'application/json',
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
    };
  }

  async getAllTenants(): Promise<Tenant[]> {
    try {
      console.log('🌐 TenantService: Making API call to /api/tenants');
      console.log('🔑 TenantService: Token available:', !!this.token);
      console.log('📡 TenantService: Headers:', this.getHeaders());

      const response = await axios.get(`${API_BASE_URL}/tenants`, {
        headers: this.getHeaders(),
      });

      console.log('✅ TenantService: API response:', response.status, response.data);

      // Transform backend data to frontend format
      const backendData = response.data as any[];
      const transformedTenants = backendData.map((tenant: any) => this.transformTenantData(tenant));
      console.log('🔄 TenantService: Transformed tenants:', transformedTenants);

      return transformedTenants;
    } catch (error) {
      console.error('❌ TenantService: API error:', error);
      this.handleError(error);
      return [];
    }
  }

  async getTenantById(id: string): Promise<Tenant | null> {
    try {
      const response = await axios.get(`${API_BASE_URL}/tenants/${id}`, {
        headers: this.getHeaders(),
      });
      return response.data;
    } catch (error) {
      this.handleError(error);
      return null;
    }
  }

  async createTenant(data: CreateTenantRequest): Promise<Tenant | null> {
    try {
      const response = await axios.post(`${API_BASE_URL}/tenants`, data, {
        headers: this.getHeaders(),
      });
      return response.data;
    } catch (error) {
      this.handleError(error);
      return null;
    }
  }

  async updateTenant(id: string, data: UpdateTenantRequest): Promise<Tenant | null> {
    try {
      const response = await axios.put(`${API_BASE_URL}/tenants/${id}`, data, {
        headers: this.getHeaders(),
      });
      return response.data;
    } catch (error) {
      this.handleError(error);
      return null;
    }
  }

  async deleteTenant(id: string): Promise<boolean> {
    try {
      await axios.delete(`${API_BASE_URL}/tenants/${id}`, {
        headers: this.getHeaders(),
      });
      return true;
    } catch (error) {
      this.handleError(error);
      return false;
    }
  }

  async updateTenantStatus(id: string, status: string): Promise<Tenant | null> {
    try {
      const response = await axios.patch(
        `${API_BASE_URL}/tenants/${id}/status`,
        { status },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
      return null;
    }
  }

  async updateTenantFeatures(id: string, features: Record<string, boolean>): Promise<Tenant | null> {
    try {
      const response = await axios.patch(
        `${API_BASE_URL}/tenants/${id}/features`,
        { features },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      this.handleError(error);
      return null;
    }
  }

  private transformTenantData(backendTenant: any): Tenant {
    return {
      id: backendTenant.id,
      name: backendTenant.name || backendTenant.business_name,
      slug: backendTenant.slug || (backendTenant.name || backendTenant.business_name || '').toLowerCase().replace(/[^a-z0-9]/g, '-'),
      subscription_plan: this.mapPlanType(backendTenant.plan_type),
      status: this.mapStatus(backendTenant.status),
      created_at: backendTenant.created_at || new Date().toISOString(),
      updated_at: backendTenant.updated_at || new Date().toISOString(),
      features: this.mapFeatures(backendTenant.features),
      settings: this.mapSettings(backendTenant)
    };
  }

  private mapPlanType(planType: string): 'basic' | 'professional' | 'enterprise' {
    switch (planType) {
      case 'starter':
      case 'basic':
        return 'basic';
      case 'professional':
      case 'pro':
        return 'professional';
      case 'enterprise':
        return 'enterprise';
      default:
        return 'basic';
    }
  }

  private mapStatus(status: string): 'active' | 'suspended' | 'pending' | 'cancelled' {
    switch (status) {
      case 'active':
        return 'active';
      case 'suspended':
        return 'suspended';
      case 'pending':
        return 'pending';
      case 'cancelled':
      case 'canceled':
        return 'cancelled';
      default:
        return 'active';
    }
  }

  private mapFeatures(backendFeatures: any): any {
    return {
      inventory_management: backendFeatures?.inventory_management || false,
      staff_scheduling: backendFeatures?.staff_scheduling || false,
      analytics_dashboard: backendFeatures?.analytics_dashboard || false,
      loyalty_program: backendFeatures?.loyalty_program || false,
      kitchen_display: backendFeatures?.kitchen_display || true,
      online_ordering: backendFeatures?.online_ordering || false,
      equipment_management: backendFeatures?.equipment_management || false,
      multi_location: backendFeatures?.multi_location || false,
      advanced_reporting: backendFeatures?.advanced_reporting || false
    };
  }

  private mapSettings(backendTenant: any): any {
    return {
      business_hours: {
        monday: { open: '09:00', close: '22:00', is_closed: false },
        tuesday: { open: '09:00', close: '22:00', is_closed: false },
        wednesday: { open: '09:00', close: '22:00', is_closed: false },
        thursday: { open: '09:00', close: '22:00', is_closed: false },
        friday: { open: '09:00', close: '22:00', is_closed: false },
        saturday: { open: '09:00', close: '22:00', is_closed: false },
        sunday: { open: '09:00', close: '22:00', is_closed: false }
      },
      currency: 'CAD',
      timezone: 'America/New_York',
      tax_rate: 0.13,
      notification_preferences: {
        email: true,
        sms: false,
        push: true,
        low_stock_alerts: true,
        order_notifications: true,
        staff_updates: true
      },
      branding: {
        primary_color: '#3B82F6',
        secondary_color: '#1E40AF',
        logo_url: '',
        font_family: 'Inter'
      }
    };
  }

  private handleError(error: any) {
    if (error?.response) {
      const message = error.response?.data?.message || error.message;
      console.error('Tenant Service Error:', message);
      throw new Error(message);
    }
    console.error('Unexpected error:', error);
    throw error;
  }
}

export const tenantService = TenantService.getInstance();
