import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Calendar,
  Clock,
  TrendingUp,
  Brain,
  Zap,
  AlertTriangle,
  CheckCircle,
  UserCheck,
  UserX,
  CalendarDays,
  Timer,
  Target,
  BarChart3,
  PieChart,
  Activity,
  RefreshCw,
  Eye,
  Settings,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  Play,
  Pause,
  RotateCcw,
  MapPin,
  DollarSign,
  Star,
  Award,
  Coffee,
  Utensils,
  ShoppingCart,
  Phone,
  Mail,
  Filter,
  Search,
  ChevronDown,
  ChevronUp,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface StaffMember {
  id: string;
  name: string;
  role: 'manager' | 'server' | 'chef' | 'bartender' | 'host' | 'cashier';
  email: string;
  phone: string;
  hourlyRate: number;
  availability: {
    [key: string]: {
      available: boolean;
      startTime?: string;
      endTime?: string;
      preferences?: string[];
    };
  };
  skills: string[];
  performance: {
    rating: number;
    efficiency: number;
    customerSatisfaction: number;
    punctuality: number;
  };
  totalHours: number;
  status: 'active' | 'inactive' | 'on_leave';
  lastWorked: Date;
}

interface ScheduleShift {
  id: string;
  staffId: string;
  staffName: string;
  role: string;
  date: Date;
  startTime: string;
  endTime: string;
  duration: number;
  position: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'no_show' | 'cancelled';
  estimatedRevenue: number;
  actualRevenue?: number;
  notes?: string;
  aiGenerated: boolean;
  confidence: number;
}

interface WorkloadPrediction {
  date: Date;
  dayOfWeek: string;
  expectedCustomers: number;
  expectedRevenue: number;
  peakHours: string[];
  requiredStaff: {
    managers: number;
    servers: number;
    chefs: number;
    bartenders: number;
    hosts: number;
    cashiers: number;
  };
  confidence: number;
  factors: string[];
  recommendations: string[];
}

interface SchedulingAnalytics {
  totalStaff: number;
  activeStaff: number;
  scheduledHours: number;
  laborCost: number;
  efficiency: number;
  coverage: number;
  aiOptimization: number;
  costSavings: number;
}

export function Phase3FAutomatedStaffScheduling() {
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [schedule, setSchedule] = useState<ScheduleShift[]>([]);
  const [workloadPredictions, setWorkloadPredictions] = useState<WorkloadPrediction[]>([]);
  const [analytics, setAnalytics] = useState<SchedulingAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedView, setSelectedView] = useState<'schedule' | 'staff' | 'predictions' | 'analytics'>('schedule');
  const [selectedWeek, setSelectedWeek] = useState(new Date());

  const mockStaff: StaffMember[] = [
    {
      id: 'staff-1',
      name: 'Sarah Johnson',
      role: 'manager',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 25.00,
      availability: {
        monday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        tuesday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        wednesday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        thursday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        friday: { available: true, startTime: '09:00', endTime: '22:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['evening'] },
        sunday: { available: false }
      },
      skills: ['leadership', 'customer_service', 'inventory_management', 'training'],
      performance: {
        rating: 4.8,
        efficiency: 94.2,
        customerSatisfaction: 96.5,
        punctuality: 98.1
      },
      totalHours: 45,
      status: 'active',
      lastWorked: new Date(Date.now() - 86400000)
    },
    {
      id: 'staff-2',
      name: 'Mike Rodriguez',
      role: 'chef',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 22.00,
      availability: {
        monday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        tuesday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        wednesday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        thursday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        friday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['dinner'] },
        saturday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['dinner'] },
        sunday: { available: true, startTime: '11:00', endTime: '21:00', preferences: ['brunch', 'dinner'] }
      },
      skills: ['cooking', 'food_prep', 'kitchen_management', 'menu_development'],
      performance: {
        rating: 4.6,
        efficiency: 91.8,
        customerSatisfaction: 94.2,
        punctuality: 95.7
      },
      totalHours: 42,
      status: 'active',
      lastWorked: new Date(Date.now() - 172800000)
    },
    {
      id: 'staff-3',
      name: 'Emma Thompson',
      role: 'server',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 15.00,
      availability: {
        monday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        tuesday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        wednesday: { available: false },
        thursday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        friday: { available: true, startTime: '17:00', endTime: '23:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '17:00', endTime: '23:00', preferences: ['evening'] },
        sunday: { available: true, startTime: '10:00', endTime: '16:00', preferences: ['brunch'] }
      },
      skills: ['customer_service', 'order_taking', 'upselling', 'wine_knowledge'],
      performance: {
        rating: 4.4,
        efficiency: 88.5,
        customerSatisfaction: 92.8,
        punctuality: 94.3
      },
      totalHours: 35,
      status: 'active',
      lastWorked: new Date(Date.now() - 259200000)
    },
    {
      id: 'staff-4',
      name: 'David Chen',
      role: 'bartender',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 18.00,
      availability: {
        monday: { available: false },
        tuesday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        wednesday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        thursday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        friday: { available: true, startTime: '16:00', endTime: '02:00', preferences: ['evening', 'night'] },
        saturday: { available: true, startTime: '16:00', endTime: '02:00', preferences: ['evening', 'night'] },
        sunday: { available: true, startTime: '14:00', endTime: '22:00', preferences: ['afternoon', 'evening'] }
      },
      skills: ['mixology', 'customer_service', 'inventory_management', 'cash_handling'],
      performance: {
        rating: 4.7,
        efficiency: 92.3,
        customerSatisfaction: 95.1,
        punctuality: 96.8
      },
      totalHours: 38,
      status: 'active',
      lastWorked: new Date(Date.now() - 345600000)
    },
    {
      id: 'staff-5',
      name: 'Lisa Park',
      role: 'host',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 14.00,
      availability: {
        monday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        tuesday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        wednesday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        thursday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        friday: { available: true, startTime: '17:00', endTime: '22:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '17:00', endTime: '22:00', preferences: ['evening'] },
        sunday: { available: true, startTime: '10:00', endTime: '15:00', preferences: ['brunch'] }
      },
      skills: ['customer_service', 'reservation_management', 'phone_etiquette', 'organization'],
      performance: {
        rating: 4.5,
        efficiency: 89.7,
        customerSatisfaction: 93.4,
        punctuality: 97.2
      },
      totalHours: 32,
      status: 'active',
      lastWorked: new Date(Date.now() - 432000000)
    }
  ];

  const mockSchedule: ScheduleShift[] = [
    {
      id: 'shift-1',
      staffId: 'staff-1',
      staffName: 'Sarah Johnson',
      role: 'manager',
      date: new Date(),
      startTime: '09:00',
      endTime: '18:00',
      duration: 9,
      position: 'Floor Manager',
      status: 'scheduled',
      estimatedRevenue: 2840.00,
      aiGenerated: true,
      confidence: 94.2
    },
    {
      id: 'shift-2',
      staffId: 'staff-2',
      staffName: 'Mike Rodriguez',
      role: 'chef',
      date: new Date(),
      startTime: '10:00',
      endTime: '22:00',
      duration: 12,
      position: 'Head Chef',
      status: 'confirmed',
      estimatedRevenue: 3250.00,
      aiGenerated: true,
      confidence: 91.8
    },
    {
      id: 'shift-3',
      staffId: 'staff-3',
      staffName: 'Emma Thompson',
      role: 'server',
      date: new Date(),
      startTime: '11:00',
      endTime: '20:00',
      duration: 9,
      position: 'Dining Room Server',
      status: 'scheduled',
      estimatedRevenue: 1890.00,
      aiGenerated: true,
      confidence: 88.5
    },
    {
      id: 'shift-4',
      staffId: 'staff-4',
      staffName: 'David Chen',
      role: 'bartender',
      date: new Date(),
      startTime: '16:00',
      endTime: '24:00',
      duration: 8,
      position: 'Main Bar',
      status: 'confirmed',
      estimatedRevenue: 1650.00,
      aiGenerated: true,
      confidence: 92.3
    },
    {
      id: 'shift-5',
      staffId: 'staff-5',
      staffName: 'Lisa Park',
      role: 'host',
      date: new Date(),
      startTime: '11:00',
      endTime: '19:00',
      duration: 8,
      position: 'Front Desk',
      status: 'scheduled',
      estimatedRevenue: 980.00,
      aiGenerated: true,
      confidence: 89.7
    }
  ];

  const mockWorkloadPredictions: WorkloadPrediction[] = [
    {
      date: new Date(),
      dayOfWeek: 'Today',
      expectedCustomers: 245,
      expectedRevenue: 8940.00,
      peakHours: ['12:00-14:00', '18:00-21:00'],
      requiredStaff: {
        managers: 1,
        servers: 4,
        chefs: 2,
        bartenders: 2,
        hosts: 1,
        cashiers: 1
      },
      confidence: 94.2,
      factors: ['Weekend traffic', 'Local events', 'Weather forecast', 'Historical data'],
      recommendations: [
        'Schedule additional server for lunch rush',
        'Ensure bartender coverage for evening peak',
        'Consider prep chef for kitchen support'
      ]
    },
    {
      date: new Date(Date.now() + 86400000),
      dayOfWeek: 'Tomorrow',
      expectedCustomers: 189,
      expectedRevenue: 6780.00,
      peakHours: ['12:00-14:00', '19:00-21:00'],
      requiredStaff: {
        managers: 1,
        servers: 3,
        chefs: 2,
        bartenders: 1,
        hosts: 1,
        cashiers: 1
      },
      confidence: 91.7,
      factors: ['Weekday pattern', 'Regular customer base', 'No special events'],
      recommendations: [
        'Standard weekday staffing sufficient',
        'Monitor lunch rush for potential overflow',
        'Single bartender adequate for expected volume'
      ]
    }
  ];

  const mockAnalytics: SchedulingAnalytics = {
    totalStaff: 5,
    activeStaff: 5,
    scheduledHours: 192,
    laborCost: 3840.00,
    efficiency: 92.4,
    coverage: 98.7,
    aiOptimization: 15.3,
    costSavings: 580.00
  };

  useEffect(() => {
    setTimeout(() => {
      setStaff(mockStaff);
      setSchedule(mockSchedule);
      setWorkloadPredictions(mockWorkloadPredictions);
      setAnalytics(mockAnalytics);
      setLoading(false);
    }, 1000);
  }, []);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager': return 'bg-purple-100 text-purple-800';
      case 'chef': return 'bg-orange-100 text-orange-800';
      case 'server': return 'bg-blue-100 text-blue-800';
      case 'bartender': return 'bg-green-100 text-green-800';
      case 'host': return 'bg-pink-100 text-pink-800';
      case 'cashier': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'no_show': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'manager': return <Award className="h-4 w-4" />;
      case 'chef': return <Utensils className="h-4 w-4" />;
      case 'server': return <Coffee className="h-4 w-4" />;
      case 'bartender': return <Coffee className="h-4 w-4" />;
      case 'host': return <Users className="h-4 w-4" />;
      case 'cashier': return <ShoppingCart className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Automated Staff Scheduling</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3F Automated Staff Scheduling</h2>
          <p className="text-gray-600">AI-powered workforce management and predictive scheduling</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Generate Schedule
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Schedule
          </Button>
        </div>
      </div>

      {/* View Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={selectedView === 'schedule' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('schedule')}
        >
          <Calendar className="h-4 w-4 mr-2" />
          Schedule
        </Button>
        <Button
          variant={selectedView === 'staff' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('staff')}
        >
          <Users className="h-4 w-4 mr-2" />
          Staff
        </Button>
        <Button
          variant={selectedView === 'predictions' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('predictions')}
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          Predictions
        </Button>
        <Button
          variant={selectedView === 'analytics' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('analytics')}
        >
          <BarChart3 className="h-4 w-4 mr-2" />
          Analytics
        </Button>
      </div>

      {/* Scheduling Overview */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Staff</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {analytics.totalStaff}
                  </p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Scheduled Hours</p>
                  <p className="text-2xl font-bold text-green-600">
                    {analytics.scheduledHours}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Labor Cost</p>
                  <p className="text-2xl font-bold text-orange-600">
                    ${analytics.laborCost.toLocaleString()}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">AI Optimization</p>
                  <p className="text-2xl font-bold text-purple-600">
                    +{analytics.aiOptimization}%
                  </p>
                </div>
                <Brain className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedView === 'schedule' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Today's Schedule</h3>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline">
                <CalendarDays className="h-4 w-4 mr-2" />
                Week View
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Shift
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {schedule.map((shift) => (
              <Card key={shift.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 bg-gray-100 rounded-lg">
                        {getRoleIcon(shift.role)}
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg text-gray-900">{shift.staffName}</h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getRoleColor(shift.role)}>
                            {shift.role}
                          </Badge>
                          <Badge className={getStatusColor(shift.status)}>
                            {shift.status}
                          </Badge>
                          {shift.aiGenerated && (
                            <Badge variant="outline" className="bg-purple-50 text-purple-700">
                              AI Generated
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{shift.confidence}%</div>
                      <div className="text-xs text-gray-500">confidence</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Schedule</p>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{shift.startTime} - {shift.endTime}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Timer className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{shift.duration} hours</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 mb-2">Position</p>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{shift.position}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          {shift.date.toLocaleDateString()}
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 mb-2">Revenue Impact</p>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <span className="font-medium text-green-600">
                            ${shift.estimatedRevenue.toLocaleString()}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">
                          Estimated revenue
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-end space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        <Phone className="h-3 w-3 mr-1" />
                        Contact
                      </Button>
                    </div>
                  </div>

                  {shift.notes && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                      <p className="text-sm text-blue-800">{shift.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {selectedView === 'staff' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Staff Management</h3>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Staff
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {staff.map((member) => (
              <Card key={member.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getRoleIcon(member.role)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{member.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getRoleColor(member.role)}>
                            {member.role}
                          </Badge>
                          <Badge className={member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {member.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="font-bold text-yellow-600">{member.performance.rating}</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Hourly Rate</p>
                        <p className="font-semibold">${member.hourlyRate}/hr</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Hours</p>
                        <p className="font-semibold">{member.totalHours}h</p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 mb-2">Performance Metrics</p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Efficiency:</span>
                          <span className="font-medium">{member.performance.efficiency}%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Customer Satisfaction:</span>
                          <span className="font-medium">{member.performance.customerSatisfaction}%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Punctuality:</span>
                          <span className="font-medium">{member.performance.punctuality}%</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 mb-2">Skills</p>
                      <div className="flex flex-wrap gap-1">
                        {member.skills.slice(0, 3).map((skill, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {skill.replace('_', ' ')}
                          </Badge>
                        ))}
                        {member.skills.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{member.skills.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="text-xs text-gray-500">
                      Last worked: {member.lastWorked.toLocaleDateString()}
                    </div>

                    <div className="flex space-x-2 pt-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Mail className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Phone className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {selectedView === 'predictions' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Workload Predictions</h3>

          <div className="space-y-6">
            {workloadPredictions.map((prediction, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{prediction.dayOfWeek}</CardTitle>
                      <p className="text-gray-600">{prediction.date.toLocaleDateString()}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{prediction.confidence}%</div>
                      <div className="text-xs text-gray-500">confidence</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-600 mb-2">Expected Volume</p>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Customers:</span>
                            <span className="font-semibold">{prediction.expectedCustomers}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Revenue:</span>
                            <span className="font-semibold text-green-600">
                              ${prediction.expectedRevenue.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600 mb-2">Peak Hours</p>
                        <div className="flex flex-wrap gap-1">
                          {prediction.peakHours.map((hour, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {hour}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-600 mb-2">Required Staff</p>
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Managers:</span>
                            <span className="font-medium">{prediction.requiredStaff.managers}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Servers:</span>
                            <span className="font-medium">{prediction.requiredStaff.servers}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Chefs:</span>
                            <span className="font-medium">{prediction.requiredStaff.chefs}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Bartenders:</span>
                            <span className="font-medium">{prediction.requiredStaff.bartenders}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Hosts:</span>
                            <span className="font-medium">{prediction.requiredStaff.hosts}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-600 mb-2">AI Recommendations</p>
                        <div className="space-y-2">
                          {prediction.recommendations.map((rec, idx) => (
                            <div key={idx} className="text-sm bg-blue-50 p-2 rounded border-l-2 border-blue-400">
                              {rec}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-600 mb-2">Factors</p>
                        <div className="flex flex-wrap gap-1">
                          {prediction.factors.map((factor, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {selectedView === 'analytics' && analytics && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900">Scheduling Analytics</h3>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Efficiency</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {analytics.efficiency}%
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Coverage</p>
                    <p className="text-2xl font-bold text-green-600">
                      {analytics.coverage}%
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Cost Savings</p>
                    <p className="text-2xl font-bold text-purple-600">
                      ${analytics.costSavings}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Staff</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {analytics.activeStaff}/{analytics.totalStaff}
                    </p>
                  </div>
                  <UserCheck className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Charts Placeholder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Scheduling Efficiency Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Efficiency Analysis</h3>
                  <p className="text-gray-600 mb-4">
                    Historical scheduling efficiency and optimization performance metrics.
                  </p>
                  <Button>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Trends
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  Labor Cost Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Cost Analysis</h3>
                  <p className="text-gray-600 mb-4">
                    Labor cost distribution across roles and time periods.
                  </p>
                  <Button>
                    <PieChart className="h-4 w-4 mr-2" />
                    View Distribution
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Scheduling Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Scheduling Performance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    ${analytics.laborCost.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Labor Cost</div>
                  <div className="text-xs text-green-600 mt-1">
                    ${analytics.costSavings} saved with AI optimization
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.scheduledHours}
                  </div>
                  <div className="text-sm text-gray-600">Scheduled Hours</div>
                  <div className="text-xs text-blue-600 mt-1">
                    {analytics.efficiency}% efficiency rating
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    +{analytics.aiOptimization}%
                  </div>
                  <div className="text-sm text-gray-600">AI Optimization</div>
                  <div className="text-xs text-purple-600 mt-1">
                    Improved scheduling efficiency
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
