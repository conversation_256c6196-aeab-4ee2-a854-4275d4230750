import React, { useState, useEffect } from 'react';
import {
  Shield,
  AlertTriangle,
  Lock,
  Eye,
  Activity,
  Users,
  Globe,
  Server,
  Database,
  Wifi,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Zap,
  Bell,
  Settings,
  RefreshCw,
  Download,
  Search,
  Filter,
  MoreVertical,
  Ban,
  UserX,
  FileText,
  Key,
  Fingerprint,
  Smartphone
} from 'lucide-react';

interface SecurityThreat {
  id: string;
  type: 'brute_force' | 'sql_injection' | 'xss' | 'ddos' | 'malware' | 'unauthorized_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  target: string;
  timestamp: string;
  status: 'active' | 'blocked' | 'investigating' | 'resolved';
  description: string;
  affectedSystems: string[];
  mitigationActions: string[];
}

interface SecurityMetrics {
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  activeThreats: number;
  blockedAttempts: number;
  successfulLogins: number;
  failedLogins: number;
  complianceScore: number;
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  lastScan: string;
  uptime: number;
}

interface ComplianceStatus {
  framework: string;
  status: 'compliant' | 'partial' | 'non_compliant';
  score: number;
  lastAudit: string;
  nextAudit: string;
  requirements: {
    total: number;
    met: number;
    pending: number;
    failed: number;
  };
}

const EnterpriseSecurityCenter: React.FC = () => {
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [threats, setThreats] = useState<SecurityThreat[]>([]);
  const [complianceStatus, setComplianceStatus] = useState<ComplianceStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadSecurityData();

    if (autoRefresh) {
      const interval = setInterval(loadSecurityData, 10000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedTimeRange]);

  const loadSecurityData = async () => {
    try {
      setIsLoading(true);

      // Mock data for demonstration
      setSecurityMetrics({
        threatLevel: 'low',
        activeThreats: 2,
        blockedAttempts: 147,
        successfulLogins: 1250,
        failedLogins: 23,
        complianceScore: 98.5,
        vulnerabilities: {
          critical: 0,
          high: 1,
          medium: 3,
          low: 8
        },
        lastScan: new Date().toISOString(),
        uptime: 2592000 // 30 days
      });

      setThreats([
        {
          id: '1',
          type: 'brute_force',
          severity: 'medium',
          source: '*************',
          target: '/api/auth/login',
          timestamp: new Date().toISOString(),
          status: 'blocked',
          description: 'Multiple failed login attempts detected',
          affectedSystems: ['Authentication Service'],
          mitigationActions: ['IP blocked', 'Rate limiting applied']
        },
        {
          id: '2',
          type: 'sql_injection',
          severity: 'high',
          source: '*********',
          target: '/api/orders',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'investigating',
          description: 'Suspicious SQL patterns in request parameters',
          affectedSystems: ['Order Management'],
          mitigationActions: ['Request blocked', 'Security team notified']
        }
      ]);

      setComplianceStatus([
        {
          framework: 'GDPR',
          status: 'compliant',
          score: 98.5,
          lastAudit: '2024-01-15',
          nextAudit: '2024-07-15',
          requirements: {
            total: 25,
            met: 24,
            pending: 1,
            failed: 0
          }
        },
        {
          framework: 'PCI-DSS',
          status: 'compliant',
          score: 96.2,
          lastAudit: '2024-02-01',
          nextAudit: '2024-08-01',
          requirements: {
            total: 12,
            met: 11,
            pending: 1,
            failed: 0
          }
        },
        {
          framework: 'SOC 2',
          status: 'partial',
          score: 87.3,
          lastAudit: '2024-01-20',
          nextAudit: '2024-07-20',
          requirements: {
            total: 15,
            met: 12,
            pending: 2,
            failed: 1
          }
        }
      ]);

    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'medium':
        return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-orange-600" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Shield className="w-5 h-5 text-gray-600" />;
    }
  };

  const getComplianceColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'partial':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'non_compliant':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Shield className="w-8 h-8 animate-pulse mx-auto mb-4 text-red-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading security data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>

      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                🛡️ Enterprise Security Center
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Advanced threat detection, compliance monitoring, and incident response
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                <Shield className="w-4 h-4" />
                <span>Security Scan</span>
              </button>

              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Download className="w-4 h-4" />
                <span>Export Report</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Security Overview */}
        {securityMetrics && (
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

              {/* Threat Level */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Threat Level
                  </h3>
                  <Shield className="w-6 h-6 text-red-600" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-2xl font-bold ${
                      getThreatLevelColor(securityMetrics.threatLevel).split(' ')[0]
                    }`}>
                      {securityMetrics.threatLevel.toUpperCase()}
                    </span>
                    {getSeverityIcon(securityMetrics.threatLevel)}
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <p>Active Threats: {securityMetrics.activeThreats}</p>
                    <p>Blocked: {securityMetrics.blockedAttempts}</p>
                  </div>
                </div>
              </div>

              {/* Authentication */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Authentication
                  </h3>
                  <Lock className="w-6 h-6 text-green-600" />
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Successful
                    </span>
                    <span className={`font-medium text-green-600`}>
                      {securityMetrics.successfulLogins}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Failed
                    </span>
                    <span className={`font-medium ${
                      securityMetrics.failedLogins > 10 ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {securityMetrics.failedLogins}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Success Rate
                    </span>
                    <span className={`font-medium text-green-600`}>
                      {((securityMetrics.successfulLogins / (securityMetrics.successfulLogins + securityMetrics.failedLogins)) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Compliance Score */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Compliance
                  </h3>
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-2xl font-bold ${
                      securityMetrics.complianceScore >= 95 ? 'text-green-600' :
                      securityMetrics.complianceScore >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {securityMetrics.complianceScore.toFixed(1)}%
                    </span>
                    {securityMetrics.complianceScore >= 95 ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-yellow-600" />
                    )}
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div
                      className={`h-2 rounded-full ${
                        securityMetrics.complianceScore >= 95 ? 'bg-green-600' :
                        securityMetrics.complianceScore >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{ width: `${securityMetrics.complianceScore}%` }}
                    ></div>
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <p>Last Scan: {new Date(securityMetrics.lastScan).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>

              {/* System Uptime */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    System Uptime
                  </h3>
                  <Activity className="w-6 h-6 text-purple-600" />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-lg font-bold text-green-600`}>
                      {formatUptime(securityMetrics.uptime)}
                    </span>
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <p>99.9% availability</p>
                    <p>All systems operational</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Threats */}
        <div className="mb-8">
          <h2 className={`text-2xl font-bold mb-6 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            🚨 Active Security Threats
          </h2>

          <div className="space-y-4">
            {threats.map((threat) => (
              <div key={threat.id} className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg border-l-4 ${
                threat.severity === 'critical' ? 'border-red-500' :
                threat.severity === 'high' ? 'border-orange-500' :
                threat.severity === 'medium' ? 'border-yellow-500' : 'border-green-500'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getSeverityIcon(threat.severity)}
                      <h3 className={`text-lg font-semibold ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {threat.type.replace('_', ' ').toUpperCase()}
                      </h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getThreatLevelColor(threat.severity)}`}>
                        {threat.severity}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        threat.status === 'active' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                        threat.status === 'blocked' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}>
                        {threat.status}
                      </span>
                    </div>

                    <p className={`text-sm mb-2 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>
                      {threat.description}
                    </p>

                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Source: {threat.source}</span>
                      <span>Target: {threat.target}</span>
                      <span>Time: {new Date(threat.timestamp).toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                      Investigate
                    </button>

                    {threat.status === 'active' && (
                      <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Block
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Compliance Status */}
        <div className="mb-8">
          <h2 className={`text-2xl font-bold mb-6 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            📋 Compliance Status
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {complianceStatus.map((compliance) => (
              <div key={compliance.framework} className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {compliance.framework}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${getComplianceColor(compliance.status)}`}>
                    {compliance.status.replace('_', ' ')}
                  </span>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Score
                    </span>
                    <span className={`font-medium ${
                      compliance.score >= 95 ? 'text-green-600' :
                      compliance.score >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {compliance.score.toFixed(1)}%
                    </span>
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                    <div
                      className={`h-2 rounded-full ${
                        compliance.score >= 95 ? 'bg-green-600' :
                        compliance.score >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{ width: `${compliance.score}%` }}
                    ></div>
                  </div>

                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <p>Met: {compliance.requirements.met}/{compliance.requirements.total}</p>
                    <p>Next Audit: {new Date(compliance.nextAudit).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseSecurityCenter;