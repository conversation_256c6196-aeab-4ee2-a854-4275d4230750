const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost', 
  database: 'BARPOS',
  password: '<PERSON><PERSON>@0319',
  port: 5432
});

pool.query("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'ai_fraud_analysis')").then(result => {
  console.log('ai_fraud_analysis table exists:', result.rows[0].exists);
  pool.end();
}).catch(console.error);
