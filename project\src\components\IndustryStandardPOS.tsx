import React, { useState, useEffect } from 'react';
import {
  ShoppingCart,
  CreditCard,
  Users,
  Clock,
  Search,
  Filter,
  Grid,
  List,
  Plus,
  Minus,
  Trash2,
  Receipt,
  Settings,
  Menu,
  X,
  DollarSign,
  Percent,
  Calculator,
  Printer,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  User,
  LogOut,
  Home,
  Package,
  BarChart3,
  FileText,
  Utensils,
  Coffee,
  Pizza,
  Sandwich,
  IceCream,
  Wine,
  Star,
  Heart,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string;
  description?: string;
  isAvailable: boolean;
  preparationTime?: number;
  allergens?: string[];
  nutritionalInfo?: any;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
}

interface ProductVariant {
  id: string;
  name: string;
  priceAdjustment: number;
}

interface ProductModifier {
  id: string;
  name: string;
  price: number;
  category: string;
  required: boolean;
  maxSelections?: number;
}

interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
  specialInstructions?: string;
  total: number;
}

interface Order {
  id: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  total: number;
  tableNumber?: string;
  customerName?: string;
  orderType: 'dine-in' | 'takeout' | 'delivery';
  status: 'draft' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  timestamp: Date;
  paymentStatus: 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
}

interface IndustryStandardPOSProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const IndustryStandardPOS: React.FC<IndustryStandardPOSProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const { state, dispatch } = useEnhancedAppContext();
  
  // State management
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order>({
    id: `order_${Date.now()}`,
    items: [],
    subtotal: 0,
    tax: 0,
    tip: 0,
    discount: 0,
    total: 0,
    orderType: 'dine-in',
    status: 'draft',
    timestamp: new Date(),
    paymentStatus: 'pending'
  });
  
  // UI State
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showPayment, setShowPayment] = useState(false);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
  } | null>(null);

  // Load products and categories
  useEffect(() => {
    loadProducts();
    loadCategories();
    
    // Simulate network status
    const interval = setInterval(() => {
      setIsOnline(Math.random() > 0.1); // 90% uptime simulation
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/products', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProducts(data.map((p: any) => ({
          ...p,
          isAvailable: p.is_active !== false,
          preparationTime: Math.floor(Math.random() * 20) + 5, // 5-25 minutes
          allergens: ['gluten', 'dairy', 'nuts'].filter(() => Math.random() > 0.7)
        })));
      }
    } catch (error) {
      console.error('Error loading products:', error);
      showNotification('error', 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/categories', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCategories(['all', ...data.map((c: any) => c.name)]);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const addToOrder = (product: Product) => {
    const existingItem = currentOrder.items.find(item => item.productId === product.id);
    
    if (existingItem) {
      updateOrderItem(existingItem.id, { quantity: existingItem.quantity + 1 });
    } else {
      const newItem: OrderItem = {
        id: `item_${Date.now()}_${Math.random()}`,
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        total: product.price
      };
      
      setCurrentOrder(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }
    
    showNotification('success', `${product.name} added to order`);
  };

  const updateOrderItem = (itemId: string, updates: Partial<OrderItem>) => {
    setCurrentOrder(prev => ({
      ...prev,
      items: prev.items.map(item => 
        item.id === itemId 
          ? { ...item, ...updates, total: (updates.quantity || item.quantity) * item.price }
          : item
      )
    }));
  };

  const removeFromOrder = (itemId: string) => {
    setCurrentOrder(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  // Calculate order totals
  useEffect(() => {
    const subtotal = currentOrder.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + tax + currentOrder.tip - currentOrder.discount;
    
    setCurrentOrder(prev => ({
      ...prev,
      subtotal,
      tax,
      total
    }));
  }, [currentOrder.items, currentOrder.tip, currentOrder.discount]);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.isAvailable;
  });

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      'all': <Grid className="w-5 h-5" />,
      'Beverages': <Coffee className="w-5 h-5" />,
      'Food': <Utensils className="w-5 h-5" />,
      'Pizza': <Pizza className="w-5 h-5" />,
      'Sandwiches': <Sandwich className="w-5 h-5" />,
      'Desserts': <IceCream className="w-5 h-5" />,
      'Alcohol': <Wine className="w-5 h-5" />
    };
    return icons[category] || <Package className="w-5 h-5" />;
  };

  return (
    <div className={`h-screen flex flex-col transition-all duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`}>
      {/* Modern Header */}
      <header className={`flex items-center justify-between px-6 py-4 border-b transition-colors duration-300 ${
        isDarkMode 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
      }`}>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
            }`}>
              <Home className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold">RestroFlow POS</h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {state.currentTenant?.name || 'Restaurant Name'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="w-5 h-5 text-green-500" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-500" />
            )}
            <Signal className="w-5 h-5 text-green-500" />
            <Battery className="w-5 h-5 text-green-500" />
          </div>

          {/* User Info */}
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${
              isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
            }`}>
              <User className="w-5 h-5" />
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">
                {state.currentEmployee?.name || 'Employee'}
              </p>
              <p className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {state.currentEmployee?.role || 'Staff'}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onThemeToggle}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600' 
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              {isDarkMode ? '☀️' : '🌙'}
            </button>
            
            <button className={`p-2 rounded-lg transition-colors duration-200 ${
              isDarkMode 
                ? 'bg-gray-700 hover:bg-gray-600' 
                : 'bg-gray-100 hover:bg-gray-200'
            }`}>
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Notification */}
      {notification && (
        <div className={`mx-6 mt-4 p-4 rounded-lg border-l-4 ${
          notification.type === 'success' ? 'bg-green-50 border-green-400 text-green-800' :
          notification.type === 'error' ? 'bg-red-50 border-red-400 text-red-800' :
          notification.type === 'warning' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
          'bg-blue-50 border-blue-400 text-blue-800'
        }`}>
          <div className="flex items-center space-x-2">
            {notification.type === 'success' && <CheckCircle className="w-5 h-5" />}
            {notification.type === 'error' && <AlertCircle className="w-5 h-5" />}
            {notification.type === 'warning' && <AlertCircle className="w-5 h-5" />}
            {notification.type === 'info' && <Info className="w-5 h-5" />}
            <span className="font-medium">{notification.message}</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Product Section */}
        <div className="flex-1 flex flex-col">
          {/* Search and Filters */}
          <div className={`p-4 border-b transition-colors duration-300 ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex-1 relative">
                <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors duration-300 ${
                    isDarkMode 
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className={`p-3 rounded-lg transition-colors duration-200 ${
                    isDarkMode 
                      ? 'bg-gray-800 hover:bg-gray-700' 
                      : 'bg-white hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
                </button>
                
                <button className={`p-3 rounded-lg transition-colors duration-200 ${
                  isDarkMode 
                    ? 'bg-gray-800 hover:bg-gray-700' 
                    : 'bg-white hover:bg-gray-50 border border-gray-300'
                }`}>
                  <Filter className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Category Tabs */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all duration-200 ${
                    selectedCategory === category
                      ? isDarkMode 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-blue-500 text-white'
                      : isDarkMode 
                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-700' 
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  {getCategoryIcon(category)}
                  <span className="font-medium capitalize">
                    {category === 'all' ? 'All Items' : category}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Products Grid/List */}
          <div className="flex-1 p-4 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className={`${
                viewMode === 'grid'
                  ? 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4'
                  : 'space-y-2'
              }`}>
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    onClick={() => addToOrder(product)}
                    className={`${
                      viewMode === 'grid'
                        ? 'aspect-square'
                        : 'h-20'
                    } ${
                      isDarkMode
                        ? 'bg-gray-800 hover:bg-gray-700 border-gray-700'
                        : 'bg-white hover:bg-gray-50 border-gray-200'
                    } border rounded-xl p-4 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 group`}
                  >
                    {viewMode === 'grid' ? (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 flex items-center justify-center mb-3">
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                            isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                          } group-hover:scale-110 transition-transform duration-200`}>
                            {getCategoryIcon(product.category)}
                          </div>
                        </div>
                        <div className="text-center">
                          <h3 className="font-semibold text-sm mb-1 line-clamp-2">
                            {product.name}
                          </h3>
                          <p className={`text-lg font-bold ${
                            isDarkMode ? 'text-blue-400' : 'text-blue-600'
                          }`}>
                            ${product.price.toFixed(2)}
                          </p>
                          {product.preparationTime && (
                            <p className={`text-xs mt-1 ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-500'
                            }`}>
                              <Clock className="w-3 h-3 inline mr-1" />
                              {product.preparationTime}min
                            </p>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="h-full flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                        }`}>
                          {getCategoryIcon(product.category)}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold">{product.name}</h3>
                          <p className={`text-sm ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {product.description || product.category}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className={`text-lg font-bold ${
                            isDarkMode ? 'text-blue-400' : 'text-blue-600'
                          }`}>
                            ${product.price.toFixed(2)}
                          </p>
                          {product.preparationTime && (
                            <p className={`text-xs ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-500'
                            }`}>
                              <Clock className="w-3 h-3 inline mr-1" />
                              {product.preparationTime}min
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Order Panel */}
        <div className={`w-96 border-l flex flex-col transition-colors duration-300 ${
          isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
        }`}>
          {/* Order Header */}
          <div className={`p-4 border-b transition-colors duration-300 ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-bold flex items-center">
                <ShoppingCart className="w-5 h-5 mr-2" />
                Current Order
              </h2>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  currentOrder.items.length > 0
                    ? 'bg-green-100 text-green-800'
                    : isDarkMode
                      ? 'bg-gray-700 text-gray-300'
                      : 'bg-gray-100 text-gray-600'
                }`}>
                  {currentOrder.items.length} items
                </span>
              </div>
            </div>

            {/* Order Type Selector */}
            <div className="flex space-x-2">
              {(['dine-in', 'takeout', 'delivery'] as const).map((type) => (
                <button
                  key={type}
                  onClick={() => setCurrentOrder(prev => ({ ...prev, orderType: type }))}
                  className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                    currentOrder.orderType === type
                      ? isDarkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-500 text-white'
                      : isDarkMode
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type === 'dine-in' && <Users className="w-4 h-4 inline mr-1" />}
                  {type === 'takeout' && <Package className="w-4 h-4 inline mr-1" />}
                  {type === 'delivery' && <Home className="w-4 h-4 inline mr-1" />}
                  {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                </button>
              ))}
            </div>
          </div>

          {/* Order Items */}
          <div className="flex-1 overflow-y-auto p-4">
            {currentOrder.items.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <ShoppingCart className={`w-16 h-16 mb-4 ${
                  isDarkMode ? 'text-gray-600' : 'text-gray-400'
                }`} />
                <p className={`text-lg font-medium mb-2 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  No items in order
                </p>
                <p className={`text-sm ${
                  isDarkMode ? 'text-gray-500' : 'text-gray-500'
                }`}>
                  Select products to add to order
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {currentOrder.items.map((item) => (
                  <div
                    key={item.id}
                    className={`p-3 rounded-lg border transition-colors duration-300 ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium flex-1">{item.name}</h4>
                      <button
                        onClick={() => removeFromOrder(item.id)}
                        className={`p-1 rounded transition-colors duration-200 ${
                          isDarkMode
                            ? 'text-gray-400 hover:text-red-400 hover:bg-gray-600'
                            : 'text-gray-500 hover:text-red-500 hover:bg-gray-200'
                        }`}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateOrderItem(item.id, {
                            quantity: Math.max(1, item.quantity - 1)
                          })}
                          className={`p-1 rounded transition-colors duration-200 ${
                            isDarkMode
                              ? 'bg-gray-600 hover:bg-gray-500'
                              : 'bg-gray-200 hover:bg-gray-300'
                          }`}
                        >
                          <Minus className="w-4 h-4" />
                        </button>

                        <span className="w-8 text-center font-medium">
                          {item.quantity}
                        </span>

                        <button
                          onClick={() => updateOrderItem(item.id, {
                            quantity: item.quantity + 1
                          })}
                          className={`p-1 rounded transition-colors duration-200 ${
                            isDarkMode
                              ? 'bg-gray-600 hover:bg-gray-500'
                              : 'bg-gray-200 hover:bg-gray-300'
                          }`}
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>

                      <div className="text-right">
                        <p className="font-semibold">${item.total.toFixed(2)}</p>
                        <p className={`text-sm ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          ${item.price.toFixed(2)} each
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Order Summary & Payment */}
          {currentOrder.items.length > 0 && (
            <div className={`border-t p-4 transition-colors duration-300 ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              {/* Order Totals */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${currentOrder.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax (10%):</span>
                  <span>${currentOrder.tax.toFixed(2)}</span>
                </div>
                {currentOrder.tip > 0 && (
                  <div className="flex justify-between">
                    <span>Tip:</span>
                    <span>${currentOrder.tip.toFixed(2)}</span>
                  </div>
                )}
                {currentOrder.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-${currentOrder.discount.toFixed(2)}</span>
                  </div>
                )}
                <div className={`flex justify-between text-lg font-bold pt-2 border-t ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-200'
                }`}>
                  <span>Total:</span>
                  <span>${currentOrder.total.toFixed(2)}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowOrderDetails(true)}
                    className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                      isDarkMode
                        ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                  >
                    <Receipt className="w-4 h-4 inline mr-2" />
                    Details
                  </button>

                  <button
                    onClick={() => setCurrentOrder(prev => ({
                      ...prev,
                      items: [],
                      subtotal: 0,
                      tax: 0,
                      total: 0
                    }))}
                    className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                      isDarkMode
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-red-500 hover:bg-red-600 text-white'
                    }`}
                  >
                    <Trash2 className="w-4 h-4 inline mr-2" />
                    Clear
                  </button>
                </div>

                <button
                  onClick={() => setShowPayment(true)}
                  className={`w-full py-4 px-4 rounded-lg font-bold text-lg transition-all duration-200 ${
                    isDarkMode
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-green-500 hover:bg-green-600 text-white'
                  } hover:shadow-lg`}
                >
                  <CreditCard className="w-5 h-5 inline mr-2" />
                  Process Payment
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IndustryStandardPOS;
