import React, { useState } from 'react';
import { AlertTriangle, X, Trash2, Shield } from 'lucide-react';

interface Tenant {
  id: number;
  name: string;
  slug: string;
  email: string;
  orderCount?: number;
  totalRevenue?: number;
  employeeCount?: number;
}

interface DeleteTenantModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (tenant: Tenant) => Promise<void>;
  tenant: Tenant | null;
}

export const DeleteTenantModal: React.FC<DeleteTenantModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  tenant
}) => {
  const [loading, setLoading] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [error, setError] = useState<string | null>(null);

  const expectedConfirmText = tenant?.name || '';
  const isConfirmValid = confirmText === expectedConfirmText;

  const handleConfirm = async () => {
    if (!tenant || !isConfirmValid) return;

    setLoading(true);
    setError(null);

    try {
      await onConfirm(tenant);
      onClose();
      setConfirmText('');
    } catch (error: any) {
      console.error('Error deleting tenant:', error);
      setError(error.message || 'Failed to delete tenant');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
      setConfirmText('');
      setError(null);
    }
  };

  if (!isOpen || !tenant) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-gray-900">Delete Tenant</h3>
              <p className="text-sm text-gray-500">This action cannot be undone</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          <div className="mb-6">
            <p className="text-gray-700 mb-4">
              You are about to permanently delete the tenant <strong>"{tenant.name}"</strong>. 
              This will remove all associated data including:
            </p>
            
            <ul className="list-disc list-inside text-sm text-gray-600 space-y-1 mb-4">
              <li>All employee accounts ({tenant.employeeCount || 0} employees)</li>
              <li>Order history ({tenant.orderCount || 0} orders)</li>
              <li>Revenue data (${tenant.totalRevenue?.toFixed(2) || '0.00'})</li>
              <li>Location and menu configurations</li>
              <li>All tenant-specific settings</li>
            </ul>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <Shield className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Safety Check</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Tenants with existing orders cannot be deleted for data integrity. 
                    Consider suspending the tenant instead.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type the tenant name to confirm deletion:
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder={expectedConfirmText}
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50 disabled:bg-gray-50"
            />
            <p className="text-xs text-gray-500 mt-1">
              Expected: <code className="bg-gray-100 px-1 rounded">{expectedConfirmText}</code>
            </p>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={loading || !isConfirmValid}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Tenant
                </>
              )}
            </button>
          </div>

          {!isConfirmValid && confirmText.length > 0 && (
            <p className="text-xs text-red-600 mt-2">
              The tenant name doesn't match. Please type exactly: {expectedConfirmText}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
