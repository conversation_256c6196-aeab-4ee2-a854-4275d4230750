// Phase 3I: Multi-Language Localization System
// Translation Hook for React Components

import { useState, useEffect, useContext, createContext } from 'react';

// Translation Context
const TranslationContext = createContext();

// Translation Provider Component
export const TranslationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en-US');
  const [translations, setTranslations] = useState({});
  const [culturalSettings, setCulturalSettings] = useState({});
  const [supportedLanguages, setSupportedLanguages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load supported languages on mount
  useEffect(() => {
    loadSupportedLanguages();
  }, []);

  // Load translations when language changes
  useEffect(() => {
    if (currentLanguage) {
      loadTranslations(currentLanguage);
      loadCulturalSettings(currentLanguage);
    }
  }, [currentLanguage]);

  // Load supported languages from API
  const loadSupportedLanguages = async () => {
    try {
      const response = await fetch('/api/i18n/languages');
      const data = await response.json();
      setSupportedLanguages(data.languages);
    } catch (error) {
      console.error('Failed to load supported languages:', error);
      setError('Failed to load language options');
    }
  };

  // Load translations for specific language
  const loadTranslations = async (language) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/i18n/translations/${language}`);
      const data = await response.json();
      
      setTranslations(prev => ({
        ...prev,
        [language]: data.translations
      }));
      
      console.log(`✅ Loaded ${data.totalKeys} translations for ${language} (${data.coverage}% coverage)`);
    } catch (error) {
      console.error(`Failed to load translations for ${language}:`, error);
      setError(`Failed to load translations for ${language}`);
    } finally {
      setLoading(false);
    }
  };

  // Load cultural settings for specific language
  const loadCulturalSettings = async (language) => {
    try {
      const response = await fetch(`/api/i18n/cultural/${language}`);
      const data = await response.json();
      
      setCulturalSettings(prev => ({
        ...prev,
        [language]: data.cultural
      }));
      
      // Apply RTL layout if needed
      if (data.isRTL) {
        document.documentElement.setAttribute('dir', 'rtl');
        document.documentElement.classList.add('rtl');
      } else {
        document.documentElement.setAttribute('dir', 'ltr');
        document.documentElement.classList.remove('rtl');
      }
      
      console.log(`✅ Loaded cultural settings for ${language} (RTL: ${data.isRTL})`);
    } catch (error) {
      console.error(`Failed to load cultural settings for ${language}:`, error);
    }
  };

  // Change language
  const changeLanguage = async (newLanguage) => {
    if (newLanguage === currentLanguage) return;
    
    console.log(`🌍 Changing language from ${currentLanguage} to ${newLanguage}`);
    setCurrentLanguage(newLanguage);
    
    // Save to localStorage
    localStorage.setItem('preferred-language', newLanguage);
  };

  // Get translation for a key
  const t = (key, fallback = key) => {
    const languageTranslations = translations[currentLanguage] || {};
    return languageTranslations[key] || fallback;
  };

  // Get cultural setting
  const getCultural = (setting, fallback = null) => {
    const settings = culturalSettings[currentLanguage] || {};
    return settings[setting] || fallback;
  };

  // Format number according to cultural settings
  const formatNumber = (number, options = {}) => {
    const locale = currentLanguage;
    return new Intl.NumberFormat(locale, options).format(number);
  };

  // Format currency according to cultural settings
  const formatCurrency = (amount, currency = 'USD') => {
    const locale = currentLanguage;
    const position = getCultural('currencyPosition', 'before');
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      currencyDisplay: position === 'before' ? 'symbol' : 'code'
    }).format(amount);
  };

  // Format date according to cultural settings
  const formatDate = (date, options = {}) => {
    const locale = currentLanguage;
    return new Intl.DateTimeFormat(locale, {
      dateStyle: 'medium',
      timeStyle: 'short',
      ...options
    }).format(new Date(date));
  };

  // Get current language info
  const getCurrentLanguageInfo = () => {
    return supportedLanguages.find(lang => lang.code === currentLanguage) || {};
  };

  // Check if current language is RTL
  const isRTL = () => {
    const settings = culturalSettings[currentLanguage] || {};
    return settings.layoutDirection === 'rtl';
  };

  // AI Translation function
  const translateText = async (text, targetLanguage = currentLanguage, context = 'general') => {
    try {
      const response = await fetch('/api/i18n/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text,
          to: targetLanguage,
          context
        })
      });
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Translation failed:', error);
      return { translatedText: text, confidence: 0, error: error.message };
    }
  };

  const value = {
    // State
    currentLanguage,
    translations,
    culturalSettings,
    supportedLanguages,
    loading,
    error,
    
    // Functions
    changeLanguage,
    t,
    getCultural,
    formatNumber,
    formatCurrency,
    formatDate,
    getCurrentLanguageInfo,
    isRTL,
    translateText,
    
    // Utilities
    loadTranslations,
    loadCulturalSettings
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

// Custom hook to use translation
export const useTranslation = () => {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  
  return context;
};

// Higher-order component for translation
export const withTranslation = (Component) => {
  return function TranslatedComponent(props) {
    const translation = useTranslation();
    return <Component {...props} {...translation} />;
  };
};

// Translation key helper
export const createTranslationKey = (namespace, key) => {
  return `${namespace}.${key}`;
};

// Batch translation loader
export const loadTranslationBatch = async (keys, language) => {
  const translations = {};
  
  for (const key of keys) {
    try {
      const response = await fetch(`/api/i18n/translations/${language}`);
      const data = await response.json();
      
      if (data.translations[key]) {
        translations[key] = data.translations[key];
      }
    } catch (error) {
      console.error(`Failed to load translation for key ${key}:`, error);
    }
  }
  
  return translations;
};

export default useTranslation;
