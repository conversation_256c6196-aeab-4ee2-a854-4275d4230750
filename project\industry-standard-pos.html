<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - Industry Standard POS System</title>
    
    <!-- Meta Tags for SEO and Social Media -->
    <meta name="description" content="Industry-standard restaurant POS system with modern UI/UX, comprehensive features, and real-time analytics.">
    <meta name="keywords" content="restaurant POS, point of sale, industry standard, modern UI, payment processing">
    <meta name="author" content="RestroFlow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://restroflow.com/">
    <meta property="og:title" content="RestroFlow - Industry Standard POS System">
    <meta property="og:description" content="Modern restaurant POS system with industry-standard features and intuitive design.">
    <meta property="og:image" content="/assets/og-image.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://restroflow.com/">
    <meta property="twitter:title" content="RestroFlow - Industry Standard POS System">
    <meta property="twitter:description" content="Modern restaurant POS system with industry-standard features and intuitive design.">
    <meta property="twitter:image" content="/assets/twitter-image.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/src/main.tsx" as="script">
    <link rel="preload" href="/src/IndustryStandardPOSSystem.tsx" as="script">
    
    <!-- CSS Variables for Theming -->
    <style>
        :root {
            --primary-color: #3B82F6;
            --secondary-color: #10B981;
            --accent-color: #8B5CF6;
            --success-color: #059669;
            --warning-color: #D97706;
            --error-color: #DC2626;
            --info-color: #0284C7;
            
            /* Light Theme */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F9FAFB;
            --bg-tertiary: #F3F4F6;
            --text-primary: #111827;
            --text-secondary: #6B7280;
            --text-tertiary: #9CA3AF;
            --border-color: #E5E7EB;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        
        .dark {
            /* Dark Theme */
            --bg-primary: #1F2937;
            --bg-secondary: #111827;
            --bg-tertiary: #374151;
            --text-primary: #F9FAFB;
            --text-secondary: #D1D5DB;
            --text-tertiary: #9CA3AF;
            --border-color: #4B5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }
        
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }
        
        /* Loading Screen Styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        .loading-logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }
        
        .loading-text {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            text-align: center;
            margin-bottom: 32px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 24px;
        }
        
        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: progress 3s ease-out forwards;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes progress {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .loading-text {
                font-size: 20px;
            }
            
            .loading-subtitle {
                font-size: 14px;
            }
            
            .loading-logo {
                width: 60px;
                height: 60px;
            }
        }
        
        /* Print Styles */
        @media print {
            .loading-screen {
                display: none !important;
            }
        }
        
        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            :root {
                --border-color: #000000;
                --text-secondary: #000000;
            }
            
            .dark {
                --border-color: #FFFFFF;
                --text-secondary: #FFFFFF;
            }
        }
        
        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            .loading-spinner {
                animation: none;
            }
            
            .loading-logo {
                animation: none;
            }
            
            .loading-progress-bar {
                animation: none;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-logo">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 7H21L19 2H5L3 7Z" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 7L5 22H19L21 7" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 11V15" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
                <path d="M15 11V15" stroke="#3B82F6" stroke-width="2" stroke-linecap="round"/>
            </svg>
        </div>
        <div class="loading-text">RestroFlow POS</div>
        <div class="loading-subtitle">Industry Standard Point of Sale System</div>
        <div class="loading-spinner"></div>
        <div class="loading-progress">
            <div class="loading-progress-bar"></div>
        </div>
    </div>
    
    <!-- Main Application Container -->
    <div id="root"></div>
    
    <!-- Error Boundary Fallback -->
    <div id="error-fallback" style="display: none; padding: 40px; text-align: center; background: #FEF2F2; color: #991B1B; border-radius: 8px; margin: 20px;">
        <h2>Something went wrong</h2>
        <p>The POS system encountered an error. Please refresh the page or contact support.</p>
        <button onclick="window.location.reload()" style="margin-top: 16px; padding: 8px 16px; background: #DC2626; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Reload Application
        </button>
    </div>
    
    <!-- Service Worker Registration -->
    <script>
        // Register service worker for offline functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Hide loading screen when app is ready
        window.addEventListener('load', () => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
            }, 2000); // Minimum loading time for better UX
        });
        
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            const errorFallback = document.getElementById('error-fallback');
            const root = document.getElementById('root');
            if (errorFallback && root && !root.innerHTML) {
                errorFallback.style.display = 'block';
            }
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
        
        // Performance monitoring
        window.addEventListener('load', () => {
            if (window.performance) {
                const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                console.log(`🚀 Page load time: ${loadTime}ms`);
                
                // Report to analytics if available
                if (window.gtag) {
                    window.gtag('event', 'page_load_time', {
                        value: loadTime,
                        event_category: 'Performance'
                    });
                }
            }
        });
        
        // Theme detection and application
        (function() {
            const savedTheme = localStorage.getItem('industryPOSTheme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
            }
        })();
        
        // Keyboard shortcuts for accessibility
        document.addEventListener('keydown', (event) => {
            // Alt + T: Toggle theme (global shortcut)
            if (event.altKey && event.key === 't') {
                event.preventDefault();
                document.documentElement.classList.toggle('dark');
                const isDark = document.documentElement.classList.contains('dark');
                localStorage.setItem('industryPOSTheme', isDark ? 'dark' : 'light');
            }
            
            // Alt + R: Reload application
            if (event.altKey && event.key === 'r') {
                event.preventDefault();
                window.location.reload();
            }
        });
    </script>
    
    <!-- Module Script for React Application -->
    <script type="module">
        import React from 'react';
        import ReactDOM from 'react-dom/client';
        import IndustryStandardPOSSystem from './src/IndustryStandardPOSSystem.tsx';
        
        // Create root and render application
        const root = ReactDOM.createRoot(document.getElementById('root'));
        
        // Render with error boundary
        try {
            root.render(
                React.createElement(React.StrictMode, null,
                    React.createElement(IndustryStandardPOSSystem, {
                        initialTheme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                        companyBranding: {
                            companyName: 'RestroFlow',
                            primaryColor: '#3B82F6',
                            secondaryColor: '#10B981'
                        }
                    })
                )
            );
            
            console.log('🚀 Industry Standard POS System initialized successfully');
        } catch (error) {
            console.error('Failed to render POS application:', error);
            document.getElementById('error-fallback').style.display = 'block';
        }
    </script>
    
    <!-- Analytics (Optional) -->
    <!-- 
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    -->
</body>
</html>
