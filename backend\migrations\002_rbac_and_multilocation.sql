-- Phase 2: RBAC and Multi-location Support Migration
-- Execute this migration to add advanced administrative features

-- =====================================================
-- RBAC (Role-Based Access Control) Tables
-- =====================================================

-- Admin Permissions Table
CREATE TABLE IF NOT EXISTS admin_permissions (
  id SERIAL PRIMARY KEY,
  resource VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(resource, action)
);

-- Admin Roles Table
CREATE TABLE IF NOT EXISTS admin_roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '[]',
  is_system_role BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Admin Users Table (Enhanced)
CREATE TABLE IF NOT EXISTS admin_users (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER REFERENCES employees(id),
  role_id INTEGER REFERENCES admin_roles(id),
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  login_attempts INTEGER DEFAULT 0,
  locked_until TIMESTAMP,
  preferences JSONB DEFAULT '{}',
  created_by INTEGER REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- Multi-location Support Tables
-- =====================================================

-- Tenant Locations Table
CREATE TABLE IF NOT EXISTS tenant_locations (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  address TEXT,
  city VARCHAR(50),
  state VARCHAR(50),
  country VARCHAR(50) DEFAULT 'Canada',
  postal_code VARCHAR(20),
  phone VARCHAR(20),
  email VARCHAR(100),
  timezone VARCHAR(50) DEFAULT 'America/Toronto',
  is_primary BOOLEAN DEFAULT false,
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Location-specific configurations
CREATE TABLE IF NOT EXISTS location_configurations (
  id SERIAL PRIMARY KEY,
  location_id INTEGER REFERENCES tenant_locations(id) ON DELETE CASCADE,
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(location_id, config_key)
);

-- =====================================================
-- Analytics and Reporting Tables
-- =====================================================

-- Tenant Analytics (Enhanced)
CREATE TABLE IF NOT EXISTS tenant_analytics (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  location_id INTEGER REFERENCES tenant_locations(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  total_orders INTEGER DEFAULT 0,
  total_revenue DECIMAL(15,2) DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  avg_order_value DECIMAL(10,2) DEFAULT 0,
  peak_hour INTEGER, -- 0-23
  customer_count INTEGER DEFAULT 0,
  refund_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(tenant_id, location_id, date)
);

-- System Performance Metrics
CREATE TABLE IF NOT EXISTS system_performance (
  id SERIAL PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT NOW(),
  cpu_usage DECIMAL(5,2),
  memory_usage DECIMAL(5,2),
  disk_usage DECIMAL(5,2),
  active_connections INTEGER,
  response_time_avg DECIMAL(8,3),
  error_rate DECIMAL(5,4),
  throughput INTEGER,
  tenant_count INTEGER,
  active_tenant_count INTEGER
);

-- System Alerts
CREATE TABLE IF NOT EXISTS system_alerts (
  id SERIAL PRIMARY KEY,
  alert_type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
  title VARCHAR(200) NOT NULL,
  description TEXT,
  tenant_id INTEGER REFERENCES tenants(id),
  location_id INTEGER REFERENCES tenant_locations(id),
  is_resolved BOOLEAN DEFAULT false,
  resolved_by INTEGER REFERENCES admin_users(id),
  resolved_at TIMESTAMP,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Audit Log (Enhanced)
CREATE TABLE IF NOT EXISTS admin_audit_log (
  id SERIAL PRIMARY KEY,
  admin_user_id INTEGER REFERENCES admin_users(id),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id VARCHAR(100),
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(100),
  tenant_id INTEGER REFERENCES tenants(id),
  location_id INTEGER REFERENCES tenant_locations(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- Automated Onboarding Tables
-- =====================================================

-- Onboarding Templates
CREATE TABLE IF NOT EXISTS onboarding_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  steps JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tenant Onboarding Progress
CREATE TABLE IF NOT EXISTS tenant_onboarding (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  template_id INTEGER REFERENCES onboarding_templates(id),
  current_step INTEGER DEFAULT 1,
  completed_steps JSONB DEFAULT '[]',
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'failed'
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- RBAC Indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_employee_id ON admin_users(employee_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_role_id ON admin_users(role_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

-- Multi-location Indexes
CREATE INDEX IF NOT EXISTS idx_tenant_locations_tenant_id ON tenant_locations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_locations_active ON tenant_locations(is_active);
CREATE INDEX IF NOT EXISTS idx_location_configurations_location_id ON location_configurations(location_id);

-- Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_tenant_analytics_tenant_date ON tenant_analytics(tenant_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_tenant_analytics_location_date ON tenant_analytics(location_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_system_performance_timestamp ON system_performance(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_created ON system_alerts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts(severity);

-- Audit Indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_admin_user ON admin_audit_log(admin_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON admin_audit_log(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_tenant ON admin_audit_log(tenant_id, created_at DESC);

-- =====================================================
-- Insert Default Data
-- =====================================================

-- Default Admin Permissions
INSERT INTO admin_permissions (resource, action, description) VALUES
('tenants', 'view', 'View tenant information'),
('tenants', 'create', 'Create new tenants'),
('tenants', 'edit', 'Edit tenant information'),
('tenants', 'delete', 'Delete tenants'),
('tenants', 'suspend', 'Suspend tenant accounts'),
('tenants', 'activate', 'Activate tenant accounts'),
('locations', 'view', 'View location information'),
('locations', 'create', 'Create new locations'),
('locations', 'edit', 'Edit location information'),
('locations', 'delete', 'Delete locations'),
('analytics', 'view', 'View analytics and reports'),
('analytics', 'export', 'Export analytics data'),
('system', 'monitor', 'Monitor system health'),
('system', 'configure', 'Configure system settings'),
('users', 'view', 'View admin users'),
('users', 'create', 'Create admin users'),
('users', 'edit', 'Edit admin users'),
('users', 'delete', 'Delete admin users'),
('audit', 'view', 'View audit logs'),
('onboarding', 'manage', 'Manage tenant onboarding')
ON CONFLICT (resource, action) DO NOTHING;

-- Default Admin Roles
INSERT INTO admin_roles (name, description, permissions, is_system_role) VALUES
('super_admin', 'Super Administrator with full system access', 
 '["tenants:*", "locations:*", "analytics:*", "system:*", "users:*", "audit:*", "onboarding:*"]', true),
('tenant_manager', 'Tenant Manager with tenant and location management access',
 '["tenants:view", "tenants:create", "tenants:edit", "tenants:suspend", "tenants:activate", "locations:*", "analytics:view", "onboarding:manage"]', true),
('analyst', 'Analytics Specialist with read-only access to reports',
 '["tenants:view", "locations:view", "analytics:*", "audit:view"]', true),
('support', 'Support Staff with limited access',
 '["tenants:view", "locations:view", "analytics:view"]', true)
ON CONFLICT (name) DO NOTHING;

-- Default Onboarding Template
INSERT INTO onboarding_templates (name, description, steps) VALUES
('standard_restaurant', 'Standard restaurant onboarding process',
 '[
   {"step": 1, "title": "Business Information", "description": "Collect basic business details"},
   {"step": 2, "title": "Location Setup", "description": "Configure primary location"},
   {"step": 3, "title": "Menu Configuration", "description": "Set up initial menu items"},
   {"step": 4, "title": "Payment Setup", "description": "Configure payment methods"},
   {"step": 5, "title": "Staff Training", "description": "Provide system training"},
   {"step": 6, "title": "Go Live", "description": "Activate the system"}
 ]')
ON CONFLICT DO NOTHING;

-- Add primary location for existing tenants
INSERT INTO tenant_locations (tenant_id, name, address, city, state, country, is_primary, is_active)
SELECT 
  id,
  business_name || ' - Main Location',
  COALESCE(address, 'Address not provided'),
  COALESCE(city, 'City not provided'),
  COALESCE(province, 'Province not provided'),
  'Canada',
  true,
  true
FROM tenants
WHERE NOT EXISTS (
  SELECT 1 FROM tenant_locations WHERE tenant_id = tenants.id AND is_primary = true
);

COMMIT;
