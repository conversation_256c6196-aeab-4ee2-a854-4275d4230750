import React, { useState, useEffect } from 'react';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:4000';

interface OrderItem {
  name: string;
  price: number;
  emoji: string;
  quantity: number;
}

interface AIMetrics {
  fraudDetection: { accuracy: number; alertsToday: number; riskLevel: string };
  salesPrediction: { todayForecast: number; accuracy: number; trend: string };
  performance: { responseTime: string; uptime: string; transactions: number };
}

interface UnifiedPOSSystemProps {
  onLogout: () => void;
  user: any;
}

const UnifiedPOSSystem: React.FC<UnifiedPOSSystemProps> = ({ onLogout, user }) => {
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [exchangeRate, setExchangeRate] = useState(1);
  const [aiMetrics, setAIMetrics] = useState<AIMetrics>({
    fraudDetection: { accuracy: 96.5, alertsToday: 3, riskLevel: 'Low' },
    salesPrediction: { todayForecast: 15420, accuracy: 87.5, trend: 'Up 12%' },
    performance: { responseTime: '<500ms', uptime: '99.9%', transactions: 1247 }
  });

  // AI-powered features state
  const [fraudAlert, setFraudAlert] = useState<string | null>(null);
  const [aiRecommendations, setAIRecommendations] = useState<string[]>([]);
  const [riskScore, setRiskScore] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [salesPrediction, setSalesPrediction] = useState<string>('');
  const [aiInsights, setAIInsights] = useState<string[]>([]);

  // Order persistence and API integration
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [orderHistory, setOrderHistory] = useState<any[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);

  const products = [
    { name: 'Coffee', price: 4.99, emoji: '☕' },
    { name: 'Sandwich', price: 8.99, emoji: '🥪' },
    { name: 'Salad', price: 12.99, emoji: '🥗' },
    { name: 'Pizza', price: 15.99, emoji: '🍕' },
    { name: 'Burger', price: 11.99, emoji: '🍔' },
    { name: 'Pasta', price: 13.99, emoji: '🍝' },
    { name: 'Soup', price: 6.99, emoji: '🍲' },
    { name: 'Dessert', price: 5.99, emoji: '🍰' }
  ];

  const currencies = [
    { code: 'USD', symbol: '$', rate: 1, flag: '🇺🇸' },
    { code: 'EUR', symbol: '€', rate: 0.871, flag: '🇪🇺' },
    { code: 'GBP', symbol: '£', rate: 0.731, flag: '🇬🇧' },
    { code: 'JPY', symbol: '¥', rate: 110.0, flag: '🇯🇵' },
    { code: 'CAD', symbol: 'C$', rate: 1.25, flag: '🇨🇦' }
  ];

  const addToOrder = (product: any) => {
    const existingItem = currentOrder.find(item => item.name === product.name);
    if (existingItem) {
      setCurrentOrder(currentOrder.map(item =>
        item.name === product.name
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCurrentOrder([...currentOrder, { ...product, quantity: 1 }]);
    }
    const newTotal = total + product.price;
    setTotal(newTotal);

    // AI-powered features when adding items
    runAIAnalysis(product, newTotal);
    generateAIRecommendations(product);
    updateSalesPrediction(newTotal);
  };

  // AI Analysis Function
  const runAIAnalysis = (product: any, orderTotal: number) => {
    setIsAnalyzing(true);

    // Simulate AI fraud detection
    setTimeout(() => {
      const risk = Math.random() * 100;
      setRiskScore(risk);

      if (risk > 85) {
        setFraudAlert(`🚨 HIGH RISK: Unusual order pattern detected for ${product.name}`);
      } else if (risk > 60) {
        setFraudAlert(`⚠️ MEDIUM RISK: Order value ${getCurrentCurrency().symbol}${convertPrice(orderTotal)} requires attention`);
      } else {
        setFraudAlert(`✅ LOW RISK: Transaction appears normal`);
      }

      setIsAnalyzing(false);
    }, 1000);
  };

  // AI Recommendations Function
  const generateAIRecommendations = (product: any) => {
    const recommendations = [
      `🤖 Customers who ordered ${product.name} also liked Pizza (+15% sales)`,
      `📊 ${product.name} pairs well with Coffee (87% success rate)`,
      `💡 Suggest dessert to increase order value by $5.99`,
      `🎯 This customer profile typically orders 2.3 items`
    ];

    setAIRecommendations(recommendations.slice(0, 2));
  };

  // Sales Prediction Function
  const updateSalesPrediction = (orderTotal: number) => {
    const predictions = [
      `📈 Today's sales forecast: $${(15420 + orderTotal).toLocaleString()}`,
      `🔮 Peak hour prediction: 2:30 PM (87% confidence)`,
      `📊 This order type increases daily revenue by 3.2%`
    ];

    setSalesPrediction(predictions[Math.floor(Math.random() * predictions.length)]);

    // AI Insights
    const insights = [
      `🧠 AI detected: Popular combo building`,
      `⚡ Real-time: Order processing in 0.3s`,
      `🌍 Global trend: Menu items +12% worldwide`,
      `🎯 Upsell opportunity: 78% success rate`
    ];

    setAIInsights(insights.slice(0, 2));
  };

  const removeFromOrder = (productName: string, price: number) => {
    const existingItem = currentOrder.find(item => item.name === productName);
    if (existingItem && existingItem.quantity > 1) {
      setCurrentOrder(currentOrder.map(item => 
        item.name === productName 
          ? { ...item, quantity: item.quantity - 1 }
          : item
      ));
    } else {
      setCurrentOrder(currentOrder.filter(item => item.name !== productName));
    }
    setTotal(total - price);
  };

  const clearOrder = () => {
    setCurrentOrder([]);
    setTotal(0);
    setFraudAlert(null);
    setAIRecommendations([]);
    setRiskScore(0);
    setSalesPrediction('');
    setAIInsights([]);

    // Clear saved order data
    localStorage.removeItem('currentOrder');
    localStorage.removeItem('orderTotal');
  };

  // Real payment processing function
  const processPayment = async (paymentMethod: string) => {
    if (currentOrder.length === 0) return;

    setIsProcessingPayment(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please login again');
        return;
      }

      // Prepare order data
      const orderData = {
        items: currentOrder.map(item => ({
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          emoji: item.emoji
        })),
        total: total,
        currency: selectedCurrency,
        payment_method: paymentMethod,
        risk_score: riskScore,
        fraud_alert: fraudAlert,
        ai_recommendations: aiRecommendations
      };

      // Submit order to backend
      const response = await fetch(`${API_BASE_URL}/api/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const result = await response.json();

        // Process payment through backend
        const paymentResponse = await fetch(`${API_BASE_URL}/api/payments/process`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            order_id: result.order_id,
            amount: total,
            currency: selectedCurrency,
            payment_method: paymentMethod,
            risk_score: riskScore
          })
        });

        if (paymentResponse.ok) {
          const paymentResult = await paymentResponse.json();

          alert(`✅ Payment Successful!\nOrder ID: ${result.order_id}\nTransaction ID: ${paymentResult.transaction_id}\nAmount: ${getCurrentCurrency().symbol}${convertPrice(total)}`);

          // Clear order after successful payment
          clearOrder();

          // Reload order history
          loadOrderHistory();

          // Update AI metrics
          setAIMetrics(prev => ({
            ...prev,
            performance: {
              ...prev.performance,
              transactions: prev.performance.transactions + 1
            }
          }));

        } else {
          const errorData = await paymentResponse.json();
          alert(`❌ Payment Failed: ${errorData.message}`);
        }

      } else {
        const errorData = await response.json();
        alert(`❌ Order Failed: ${errorData.message}`);
      }

    } catch (error) {
      console.error('Payment processing error:', error);
      alert('❌ Payment processing failed. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const getCurrentCurrency = () => {
    return currencies.find(c => c.code === selectedCurrency) || currencies[0];
  };

  const convertPrice = (price: number) => {
    const currency = getCurrentCurrency();
    return (price * currency.rate).toFixed(2);
  };

  // Load saved order data on component mount
  useEffect(() => {
    const savedOrder = localStorage.getItem('currentOrder');
    const savedTotal = localStorage.getItem('orderTotal');
    const savedCurrency = localStorage.getItem('selectedCurrency');

    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder);
        setCurrentOrder(parsedOrder);
      } catch (error) {
        console.error('Error loading saved order:', error);
      }
    }

    if (savedTotal) {
      setTotal(parseFloat(savedTotal));
    }

    if (savedCurrency) {
      setSelectedCurrency(savedCurrency);
    }

    // Load order history from backend
    loadOrderHistory();
  }, []);

  // Save order data whenever it changes
  useEffect(() => {
    localStorage.setItem('currentOrder', JSON.stringify(currentOrder));
    localStorage.setItem('orderTotal', total.toString());
    localStorage.setItem('selectedCurrency', selectedCurrency);
  }, [currentOrder, total, selectedCurrency]);

  useEffect(() => {
    const currency = getCurrentCurrency();
    setExchangeRate(currency.rate);
  }, [selectedCurrency]);

  // Load order history from backend
  const loadOrderHistory = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(`${API_BASE_URL}/api/orders`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const orders = await response.json();
        setOrderHistory(orders);
      }
    } catch (error) {
      console.error('Error loading order history:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Enhanced Header with Compliance Status */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">B</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">BARPOS</h1>
                  <p className="text-sm text-gray-600">AI-Powered Global POS System</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex gap-2 flex-wrap">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  🛡️ GDPR ✅
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  🇺🇸 CCPA ✅
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  🇨🇦 PIPEDA ✅
                </span>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  💳 PCI-DSS ✅
                </span>
              </div>

              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-600">{user?.role || 'Administrator'}</p>
                </div>
                <button
                  onClick={onLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-full mx-auto p-6">
        {/* AI Intelligence Bar */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm opacity-90">🤖 AI Fraud Detection</div>
                <div className="text-xl font-bold">{aiMetrics.fraudDetection.accuracy}%</div>
                <div className="text-xs opacity-75">{aiMetrics.fraudDetection.alertsToday} alerts today</div>
              </div>
              <div className="text-2xl">🛡️</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm opacity-90">📈 Sales Forecast</div>
                <div className="text-xl font-bold">${aiMetrics.salesPrediction.todayForecast.toLocaleString()}</div>
                <div className="text-xs opacity-75">{aiMetrics.salesPrediction.trend}</div>
              </div>
              <div className="text-2xl">📊</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm opacity-90">⚡ Performance</div>
                <div className="text-xl font-bold">{aiMetrics.performance.responseTime}</div>
                <div className="text-xs opacity-75">{aiMetrics.performance.uptime} uptime</div>
              </div>
              <div className="text-2xl">🚀</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm opacity-90">🌍 Global Ready</div>
                <div className="text-xl font-bold">23</div>
                <div className="text-xs opacity-75">Countries supported</div>
              </div>
              <div className="text-2xl">🌎</div>
            </div>
          </div>
        </div>

        {/* Main POS Interface */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Product Grid - Takes up 2.5 columns */}
          <div className="xl:col-span-3 space-y-6">
            {/* Product Grid */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                <h3 className="text-lg font-semibold text-gray-900">🍽️ Menu Items</h3>
                <p className="text-sm text-gray-600">Click any item to add to order - AI analysis enabled</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {products.map((product, index) => (
                    <button
                      key={index}
                      onClick={() => addToOrder(product)}
                      className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg hover:from-blue-50 hover:to-blue-100 transition-all duration-200 text-center border border-gray-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                    >
                      <div className="text-3xl mb-2">{product.emoji}</div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-600">
                        {getCurrentCurrency().symbol}{convertPrice(product.price)}
                      </div>
                      <div className="text-xs text-blue-600 mt-1">
                        {selectedCurrency !== 'USD' && `($${product.price} USD)`}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* AI Analysis Panel */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Fraud Detection */}
              <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-red-50 to-orange-50">
                  <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                    🛡️ AI Fraud Detection
                    {isAnalyzing && <span className="text-xs text-blue-600 animate-pulse">Analyzing...</span>}
                  </h4>
                </div>
                <div className="p-4">
                  {fraudAlert ? (
                    <div className={`p-3 rounded-lg text-sm ${
                      fraudAlert.includes('HIGH') ? 'bg-red-100 text-red-800' :
                      fraudAlert.includes('MEDIUM') ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {fraudAlert}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm text-center py-4">
                      Add items to order for AI fraud analysis
                    </div>
                  )}
                  {riskScore > 0 && (
                    <div className="mt-3">
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Risk Score</span>
                        <span>{riskScore.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-500 ${
                            riskScore > 85 ? 'bg-red-500' :
                            riskScore > 60 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${riskScore}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* AI Recommendations */}
              <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <h4 className="font-semibold text-gray-900">🤖 AI Recommendations</h4>
                </div>
                <div className="p-4">
                  {aiRecommendations.length > 0 ? (
                    <div className="space-y-2">
                      {aiRecommendations.map((rec, index) => (
                        <div key={index} className="p-2 bg-blue-50 rounded text-sm text-blue-800">
                          {rec}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm text-center py-4">
                      Add items to get AI-powered recommendations
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sales Prediction & AI Insights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Sales Prediction */}
              <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                  <h4 className="font-semibold text-gray-900">📈 Sales Prediction</h4>
                </div>
                <div className="p-4">
                  {salesPrediction ? (
                    <div className="p-3 bg-green-50 rounded-lg text-sm text-green-800">
                      {salesPrediction}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm text-center py-4">
                      Start ordering to see AI sales predictions
                    </div>
                  )}
                </div>
              </div>

              {/* AI Insights */}
              <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
                  <h4 className="font-semibold text-gray-900">🧠 AI Insights</h4>
                </div>
                <div className="p-4">
                  {aiInsights.length > 0 ? (
                    <div className="space-y-2">
                      {aiInsights.map((insight, index) => (
                        <div key={index} className="p-2 bg-purple-50 rounded text-sm text-purple-800">
                          {insight}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500 text-sm text-center py-4">
                      AI insights will appear as you build your order
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Order Management & Payment - Takes up 1.5 columns */}
          <div className="xl:col-span-1 space-y-6">
            {/* Currency Selector */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
                <h4 className="font-semibold text-gray-900">💱 Currency</h4>
              </div>
              <div className="p-4">
                <select
                  value={selectedCurrency}
                  onChange={(e) => setSelectedCurrency(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                >
                  {currencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.flag} {currency.code} ({currency.symbol})
                    </option>
                  ))}
                </select>
                {selectedCurrency !== 'USD' && (
                  <div className="text-xs text-gray-500 mt-2">
                    Rate: 1 USD = {exchangeRate} {selectedCurrency}
                  </div>
                )}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                <h4 className="font-semibold text-gray-900">🧾 Current Order</h4>
              </div>
              <div className="p-4">
                {currentOrder.length === 0 ? (
                  <p className="text-gray-500 text-center py-8 text-sm">No items in order</p>
                ) : (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {currentOrder.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{item.emoji}</span>
                          <div>
                            <div className="text-sm font-medium">{item.name}</div>
                            <div className="text-xs text-gray-500">Qty: {item.quantity}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {getCurrentCurrency().symbol}{convertPrice(item.price * item.quantity)}
                          </span>
                          <button
                            onClick={() => removeFromOrder(item.name, item.price)}
                            className="text-red-500 hover:text-red-700 text-xs"
                          >
                            ❌
                          </button>
                        </div>
                      </div>
                    ))}
                    <div className="border-t pt-3 mt-4">
                      <div className="flex justify-between items-center font-bold text-lg">
                        <span>Total:</span>
                        <span className="text-green-600">
                          {getCurrentCurrency().symbol}{convertPrice(total)}
                        </span>
                      </div>
                      {selectedCurrency !== 'USD' && (
                        <div className="text-xs text-gray-500 text-right">
                          (${total.toFixed(2)} USD)
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* AI-Enhanced Payment Options */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <h4 className="font-semibold text-gray-900">💳 AI-Enhanced Payment</h4>
                {riskScore > 0 && (
                  <p className="text-xs text-gray-600 mt-1">
                    Risk Score: {riskScore.toFixed(1)}% |
                    {riskScore > 85 ? ' ⚠️ High Risk' : riskScore > 60 ? ' 🟡 Medium Risk' : ' ✅ Low Risk'}
                  </p>
                )}
              </div>
              <div className="p-4 space-y-3">
                {/* AI Fraud Alert in Payment */}
                {fraudAlert && riskScore > 60 && (
                  <div className={`p-2 rounded text-xs ${
                    riskScore > 85 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    🤖 AI Alert: {fraudAlert}
                  </div>
                )}

                <button
                  onClick={() => processPayment('credit_card')}
                  disabled={currentOrder.length === 0 || isProcessingPayment}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
                    riskScore > 85 ? 'bg-gradient-to-r from-red-600 to-red-700 text-white' :
                    'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800'
                  }`}
                >
                  {isProcessingPayment ? (
                    <span className="flex items-center justify-center">
                      <span className="animate-spin mr-2">⏳</span>
                      Processing...
                    </span>
                  ) : (
                    <>
                      💳 Credit Card ({getCurrentCurrency().symbol}{convertPrice(total)})
                      {riskScore > 85 && <span className="block text-xs">⚠️ High Risk - Manual Review Required</span>}
                    </>
                  )}
                </button>

                <button
                  onClick={() => processPayment('cash')}
                  disabled={currentOrder.length === 0 || isProcessingPayment}
                  className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 hover:to-green-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessingPayment ? (
                    <span className="flex items-center justify-center">
                      <span className="animate-spin mr-2">⏳</span>
                      Processing...
                    </span>
                  ) : (
                    <>
                      💰 Cash ({getCurrentCurrency().symbol}{convertPrice(total)})
                      {riskScore < 30 && <span className="block text-xs">✅ AI Recommended</span>}
                    </>
                  )}
                </button>

                <button
                  onClick={() => processPayment('digital_wallet')}
                  disabled={currentOrder.length === 0 || isProcessingPayment}
                  className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-purple-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessingPayment ? (
                    <span className="flex items-center justify-center">
                      <span className="animate-spin mr-2">⏳</span>
                      Processing...
                    </span>
                  ) : (
                    <>
                      📱 Digital Wallet ({getCurrentCurrency().symbol}{convertPrice(total)})
                      {riskScore > 0 && riskScore < 50 && <span className="block text-xs">🤖 AI Verified Safe</span>}
                    </>
                  )}
                </button>

                <button
                  onClick={clearOrder}
                  disabled={currentOrder.length === 0 || isProcessingPayment}
                  className="w-full bg-gradient-to-r from-gray-600 to-gray-700 text-white py-3 px-4 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  🗑️ Clear Order
                </button>
              </div>
            </div>

            {/* Order History */}
            {orderHistory.length > 0 && (
              <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                  <h4 className="font-semibold text-gray-900">📋 Recent Orders</h4>
                </div>
                <div className="p-4">
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {orderHistory.slice(0, 5).map((order, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="text-sm font-medium">Order #{order.id}</div>
                            <div className="text-xs text-gray-600">
                              {order.items?.length || 0} items • {order.payment_method}
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(order.created_at).toLocaleString()}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-green-600">
                              {order.currency === 'USD' ? '$' : getCurrentCurrency().symbol}{order.total}
                            </div>
                            <div className="text-xs text-gray-500">
                              {order.status || 'Completed'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Real-time System Status */}
        <div className="mt-6 bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{aiMetrics.performance.transactions}</div>
                <div className="text-sm text-gray-600">Transactions Today</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{aiMetrics.fraudDetection.accuracy}%</div>
                <div className="text-sm text-gray-600">Fraud Detection</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{aiMetrics.performance.uptime}</div>
                <div className="text-sm text-gray-600">System Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">${aiMetrics.salesPrediction.todayForecast.toLocaleString()}</div>
                <div className="text-sm text-gray-600">Today's Forecast</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer with System Status */}
      <div className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-full mx-auto py-4 px-6">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-gray-600">System Operational</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600">AI Services Active</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span className="text-gray-600">Global Payments Ready</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-gray-600">Compliance: 99.2%</span>
              </div>
            </div>
            <div className="text-gray-500">
              BARPOS v7.0 - Unified AI-Powered Global POS | Transactions: {aiMetrics.performance.transactions}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedPOSSystem;
