const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const readline = require('readline');

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

// Create readline interface for interactive input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Interactive Owner Management System
 * Provides secure management of owner profile and system settings
 */
class OwnerManager {
  
  async showMenu() {
    console.log('\n🏢 BARPOS Owner Management System');
    console.log('=' .repeat(50));
    console.log('1. View Owner Profile');
    console.log('2. Change Owner PIN');
    console.log('3. Update Owner Information');
    console.log('4. View System Statistics');
    console.log('5. Create Additional Admin');
    console.log('6. List All Admins');
    console.log('7. Security Audit');
    console.log('8. Backup System Data');
    console.log('9. Exit');
    console.log('=' .repeat(50));
    
    return new Promise((resolve) => {
      rl.question('Select an option (1-9): ', (answer) => {
        resolve(answer.trim());
      });
    });
  }
  
  async viewOwnerProfile() {
    const client = await pool.connect();
    try {
      console.log('\n👑 Owner Profile Information');
      console.log('-' .repeat(40));
      
      const result = await client.query(`
        SELECT 
          e.id, e.name, e.email, e.role, e.is_active, e.created_at,
          e.last_login, e.department, e.employment_type, e.notes,
          t.name as tenant_name, t.slug as tenant_slug,
          l.name as location_name
        FROM employees e
        LEFT JOIN tenants t ON e.tenant_id = t.id
        LEFT JOIN locations l ON e.location_id = l.id
        WHERE e.email = '<EMAIL>'
      `);
      
      if (result.rows.length === 0) {
        console.log('❌ Owner profile not found. Please run create-owner-profile.js first.');
        return;
      }
      
      const owner = result.rows[0];
      console.log(`👤 Name: ${owner.name}`);
      console.log(`📧 Email: ${owner.email}`);
      console.log(`🎭 Role: ${owner.role}`);
      console.log(`🏛️ Tenant: ${owner.tenant_name} (${owner.tenant_slug})`);
      console.log(`📍 Location: ${owner.location_name}`);
      console.log(`🏢 Department: ${owner.department}`);
      console.log(`💼 Employment: ${owner.employment_type}`);
      console.log(`✅ Status: ${owner.is_active ? 'Active' : 'Inactive'}`);
      console.log(`📅 Created: ${new Date(owner.created_at).toLocaleDateString()}`);
      console.log(`🕐 Last Login: ${owner.last_login ? new Date(owner.last_login).toLocaleString() : 'Never'}`);
      console.log(`📝 Notes: ${owner.notes || 'None'}`);
      
    } catch (error) {
      console.error('❌ Error fetching owner profile:', error);
    } finally {
      client.release();
    }
  }
  
  async changeOwnerPin() {
    console.log('\n🔐 Change Owner PIN');
    console.log('-' .repeat(30));
    console.log('⚠️  Security Notice: This will change the owner login PIN');
    
    return new Promise((resolve) => {
      rl.question('Enter current PIN: ', async (currentPin) => {
        rl.question('Enter new PIN (6 digits): ', async (newPin) => {
          rl.question('Confirm new PIN: ', async (confirmPin) => {
            
            if (newPin !== confirmPin) {
              console.log('❌ PINs do not match. Please try again.');
              resolve();
              return;
            }
            
            if (newPin.length !== 6 || !/^\d{6}$/.test(newPin)) {
              console.log('❌ PIN must be exactly 6 digits. Please try again.');
              resolve();
              return;
            }
            
            const client = await pool.connect();
            try {
              // Verify current PIN
              const ownerResult = await client.query(`
                SELECT pin_hash FROM employees WHERE email = '<EMAIL>'
              `);
              
              if (ownerResult.rows.length === 0) {
                console.log('❌ Owner profile not found.');
                resolve();
                return;
              }
              
              const isCurrentPinValid = await bcrypt.compare(currentPin, ownerResult.rows[0].pin_hash);
              if (!isCurrentPinValid) {
                console.log('❌ Current PIN is incorrect.');
                resolve();
                return;
              }
              
              // Update PIN
              const hashedNewPin = await bcrypt.hash(newPin, 12);
              await client.query(`
                UPDATE employees 
                SET pin = $1, pin_hash = $2, updated_at = NOW()
                WHERE email = '<EMAIL>'
              `, [newPin, hashedNewPin]);
              
              console.log('✅ Owner PIN updated successfully!');
              console.log('🔒 Please use the new PIN for future logins.');
              
            } catch (error) {
              console.error('❌ Error updating PIN:', error);
            } finally {
              client.release();
              resolve();
            }
          });
        });
      });
    });
  }
  
  async updateOwnerInfo() {
    console.log('\n📝 Update Owner Information');
    console.log('-' .repeat(35));
    
    return new Promise((resolve) => {
      rl.question('Enter new name (or press Enter to skip): ', (name) => {
        rl.question('Enter new email (or press Enter to skip): ', (email) => {
          rl.question('Enter new department (or press Enter to skip): ', async (department) => {
            
            const client = await pool.connect();
            try {
              const updates = [];
              const values = [];
              let paramCount = 1;
              
              if (name.trim()) {
                updates.push(`name = $${paramCount++}`);
                values.push(name.trim());
              }
              
              if (email.trim()) {
                updates.push(`email = $${paramCount++}`);
                values.push(email.trim());
              }
              
              if (department.trim()) {
                updates.push(`department = $${paramCount++}`);
                values.push(department.trim());
              }
              
              if (updates.length === 0) {
                console.log('ℹ️  No changes made.');
                resolve();
                return;
              }
              
              updates.push(`updated_at = NOW()`);
              
              await client.query(`
                UPDATE employees 
                SET ${updates.join(', ')}
                WHERE email = '<EMAIL>'
              `, values);
              
              console.log('✅ Owner information updated successfully!');
              
            } catch (error) {
              console.error('❌ Error updating owner information:', error);
            } finally {
              client.release();
              resolve();
            }
          });
        });
      });
    });
  }
  
  async viewSystemStats() {
    const client = await pool.connect();
    try {
      console.log('\n📊 System Statistics');
      console.log('-' .repeat(30));
      
      // Get tenant count
      const tenantStats = await client.query(`
        SELECT 
          COUNT(*) as total_tenants,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_tenants,
          COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_tenants
        FROM tenants
      `);
      
      // Get user count
      const userStats = await client.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
          COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as super_admins
        FROM employees
      `);
      
      // Get location count
      const locationStats = await client.query(`
        SELECT COUNT(*) as total_locations FROM locations
      `);
      
      const tenantData = tenantStats.rows[0];
      const userData = userStats.rows[0];
      const locationData = locationStats.rows[0];
      
      console.log(`🏢 Tenants: ${tenantData.total_tenants} total (${tenantData.active_tenants} active, ${tenantData.suspended_tenants} suspended)`);
      console.log(`👥 Users: ${userData.total_users} total (${userData.active_users} active)`);
      console.log(`👑 Super Admins: ${userData.super_admins}`);
      console.log(`📍 Locations: ${locationData.total_locations}`);
      
      // System health
      console.log('\n🔍 System Health:');
      console.log(`✅ Database: Connected`);
      console.log(`✅ Owner Profile: Active`);
      console.log(`✅ Multi-tenancy: Enabled`);
      
    } catch (error) {
      console.error('❌ Error fetching system statistics:', error);
    } finally {
      client.release();
    }
  }
  
  async listAllAdmins() {
    const client = await pool.connect();
    try {
      console.log('\n👥 System Administrators');
      console.log('-' .repeat(40));
      
      const result = await client.query(`
        SELECT 
          e.id, e.name, e.email, e.role, e.is_active, e.last_login,
          t.name as tenant_name
        FROM employees e
        LEFT JOIN tenants t ON e.tenant_id = t.id
        WHERE e.role IN ('super_admin', 'tenant_admin')
        ORDER BY e.role DESC, e.name
      `);
      
      if (result.rows.length === 0) {
        console.log('❌ No administrators found.');
        return;
      }
      
      result.rows.forEach((admin, index) => {
        console.log(`${index + 1}. ${admin.name} (${admin.email})`);
        console.log(`   🎭 Role: ${admin.role}`);
        console.log(`   🏛️ Tenant: ${admin.tenant_name || 'System'}`);
        console.log(`   ✅ Status: ${admin.is_active ? 'Active' : 'Inactive'}`);
        console.log(`   🕐 Last Login: ${admin.last_login ? new Date(admin.last_login).toLocaleString() : 'Never'}`);
        console.log('');
      });
      
    } catch (error) {
      console.error('❌ Error fetching administrators:', error);
    } finally {
      client.release();
    }
  }
  
  async run() {
    console.log('🚀 Starting BARPOS Owner Management System...');
    
    while (true) {
      const choice = await this.showMenu();
      
      switch (choice) {
        case '1':
          await this.viewOwnerProfile();
          break;
        case '2':
          await this.changeOwnerPin();
          break;
        case '3':
          await this.updateOwnerInfo();
          break;
        case '4':
          await this.viewSystemStats();
          break;
        case '5':
          console.log('ℹ️  Feature coming soon: Create Additional Admin');
          break;
        case '6':
          await this.listAllAdmins();
          break;
        case '7':
          console.log('ℹ️  Feature coming soon: Security Audit');
          break;
        case '8':
          console.log('ℹ️  Feature coming soon: Backup System Data');
          break;
        case '9':
          console.log('👋 Goodbye!');
          rl.close();
          process.exit(0);
          break;
        default:
          console.log('❌ Invalid option. Please try again.');
      }
      
      // Wait for user to press Enter before showing menu again
      await new Promise((resolve) => {
        rl.question('\nPress Enter to continue...', () => resolve());
      });
    }
  }
}

// Run the management system
if (require.main === module) {
  const manager = new OwnerManager();
  manager.run().catch((error) => {
    console.error('💥 Owner management system error:', error);
    process.exit(1);
  });
}

module.exports = OwnerManager;
