import React, { createContext, useContext, useReducer } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { AppState, AppAction, Product, OrderItem, Category, Employee, AppContextType, SystemConfig, FloorLayout, Table, Schedule, Customer, LoyaltyReward, KitchenOrder } from '../types';

// Initialize with empty state
const initialState: AppState = {
  products: [],
  orders: [],
  currentOrder: null,
  employees: [],
  currentEmployee: null,
  isAuthenticated: false,
  categories: ['beer', 'wine', 'cocktails', 'spirits', 'non-alcoholic', 'food'],
  systemConfig: {
    tax_rate: 0.0825,
    receipt_header: 'Thank you for visiting!',
    receipt_footer: 'Please come again',
    business_name: 'Bar POS',
    business_address: '',
    business_phone: '',
    theme_primary_color: '#4f46e5',
    theme_secondary_color: '#10b981'
  },
  floorLayout: null,
  tables: [],
  schedules: [],
  customers: [],
  loyaltyRewards: [],
  kitchenOrders: [],
  selectedTable: null
};

// Helper function to calculate subtotal
const calculateSubtotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

const AppContext = createContext<AppContextType | null>(null);

// Reducer function to handle state updates
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'ADD_PRODUCT_TO_ORDER': {
      const product = action.payload;
      let currentOrder = state.currentOrder;
      
      if (!currentOrder) {
        currentOrder = {
          id: uuidv4(),
          items: [],
          timestamp: Date.now(),
          status: 'open',
          total: 0,
          subtotal: 0,
          tax: 0
        };
      }
      
      const existingItemIndex = currentOrder.items.findIndex(
        item => item.productId === product.id
      );
      
      if (existingItemIndex >= 0) {
        const updatedItems = [...currentOrder.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };
        
        const subtotal = calculateSubtotal(updatedItems);
        const tax = subtotal * state.systemConfig.tax_rate;
        
        return {
          ...state,
          currentOrder: {
            ...currentOrder,
            items: updatedItems,
            subtotal,
            tax,
            total: subtotal + tax
          }
        };
      } else {
        const newItem: OrderItem = {
          id: uuidv4(),
          productId: product.id,
          name: product.name,
          price: product.price,
          quantity: 1
        };
        
        const updatedItems = [...currentOrder.items, newItem];
        const subtotal = calculateSubtotal(updatedItems);
        const tax = subtotal * state.systemConfig.tax_rate;
        
        return {
          ...state,
          currentOrder: {
            ...currentOrder,
            items: updatedItems,
            subtotal,
            tax,
            total: subtotal + tax
          }
        };
      }
    }
    
    case 'REMOVE_ITEM_FROM_ORDER': {
      if (!state.currentOrder) return state;
      
      const updatedItems = state.currentOrder.items.filter(
        item => item.id !== action.payload
      );
      
      if (updatedItems.length === 0) {
        return {
          ...state,
          currentOrder: null
        };
      }
      
      const subtotal = calculateSubtotal(updatedItems);
      const tax = subtotal * state.systemConfig.tax_rate;
      
      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          items: updatedItems,
          subtotal,
          tax,
          total: subtotal + tax
        }
      };
    }
    
    case 'UPDATE_ITEM_QUANTITY': {
      if (!state.currentOrder) return state;
      
      const { id, quantity } = action.payload;
      
      if (quantity <= 0) {
        return appReducer(state, { type: 'REMOVE_ITEM_FROM_ORDER', payload: id });
      }
      
      const updatedItems = state.currentOrder.items.map(item => 
        item.id === id ? { ...item, quantity } : item
      );
      
      const subtotal = calculateSubtotal(updatedItems);
      const tax = subtotal * state.systemConfig.tax_rate;
      
      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          items: updatedItems,
          subtotal,
          tax,
          total: subtotal + tax
        }
      };
    }
    
    case 'CLEAR_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: null
      };
    
    case 'SET_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: action.payload
      };
    
    case 'COMPLETE_ORDER': {
      if (!state.currentOrder) return state;
      
      const { paymentMethod, tip = 0 } = action.payload;
      const completedOrder = {
        ...state.currentOrder,
        status: 'paid' as "paid",
        paymentMethod,
        tip,
        total: state.currentOrder.subtotal + state.currentOrder.tax + tip,
        employee_id: state.currentEmployee?.id
      };
      
      return {
        ...state,
        orders: [...state.orders, completedOrder],
        currentOrder: null
      };
    }
    
    case 'SET_TAB_NAME': {
      if (!state.currentOrder) return state;
      
      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          tabName: action.payload
        }
      };
    }
    
    case 'LOGIN':
      return {
        ...state,
        currentEmployee: action.payload,
        isAuthenticated: true
      };
    
    case 'LOGOUT':
      return {
        ...state,
        currentEmployee: null,
        isAuthenticated: false,
        currentOrder: null
      };

    case 'SET_PRODUCTS':
      return {
        ...state,
        products: action.payload
      };

    case 'ADD_PRODUCT':
      return {
        ...state,
        products: [...state.products, action.payload]
      };

    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? action.payload : product
        )
      };

    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload)
      };

    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload
      };

    case 'ADD_CATEGORY':
      if (state.categories.includes(action.payload)) {
        return state;
      }
      return {
        ...state,
        categories: [...state.categories, action.payload]
      };

    case 'SET_EMPLOYEES':
      return {
        ...state,
        employees: action.payload
      };

    case 'UPDATE_SYSTEM_CONFIG':
      return {
        ...state,
        systemConfig: {
          ...state.systemConfig,
          ...action.payload
        }
      };
    
    default:
      return state;
  }
};

// Provider component
function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Product management functions
  const downloadTemplate = async () => {
    try {
      const response = await fetch('http://localhost:4000/products/template');
      if (!response.ok) throw new Error('Failed to download template');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'product_template.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to download template:', error);
      throw error;
    }
  };

  const exportProducts = async () => {
    try {
      const response = await fetch('http://localhost:4000/products/export');
      if (!response.ok) throw new Error('Failed to export products');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'products.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export products:', error);
      throw error;
    }
  };

  const importProducts = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('http://localhost:4000/products/bulk', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to import products');
      }

      const result = await response.json();
      await fetchProducts();
      return result;
    } catch (error) {
      console.error('Failed to import products:', error);
      throw error;
    }
  };

  const addProduct = async (product: Product) => {
    try {
      const response = await fetch('http://localhost:4000/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(product),
      });
      if (!response.ok) throw new Error('Failed to add product');
      await fetchProducts();
    } catch (error) {
      console.error('Failed to add product:', error);
      throw error;
    }
  };

  const updateProduct = async (product: Product) => {
    try {
      const response = await fetch(`http://localhost:4000/products/${product.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(product),
      });
      if (!response.ok) throw new Error('Failed to update product');
      const updatedProduct = await response.json();
      dispatch({ type: 'UPDATE_PRODUCT', payload: updatedProduct });
    } catch (error) {
      console.error('Failed to update product:', error);
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      const response = await fetch(`http://localhost:4000/products/${productId}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete product');
      dispatch({ type: 'DELETE_PRODUCT', payload: productId });
    } catch (error) {
      console.error('Failed to delete product:', error);
      throw error;
    }
  };

  const addCategory = async (category: Category) => {
    try {
      const response = await fetch('http://localhost:4000/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: category }),
      });
      if (!response.ok) throw new Error('Failed to add category');
      dispatch({ type: 'ADD_CATEGORY', payload: category });
    } catch (error) {
      console.error('Failed to add category:', error);
      throw error;
    }
  };

  // Employee management functions
  const addEmployee = async (employee: Employee) => {
    try {
      const response = await fetch('http://localhost:4000/employees', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: employee.name,
          pin: employee.pin,
          role: employee.role
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add employee');
      }
      
      const newEmployee = await response.json();
      dispatch({ type: 'ADD_EMPLOYEE', payload: newEmployee });
      return newEmployee;
    } catch (error) {
      console.error('Failed to add employee:', error);
      throw error;
    }
  };

  const updateEmployee = async (employee: Employee) => {
    if (!employee.id) {
      throw new Error('Employee ID is required for update');
    }

    try {
      const response = await fetch(`http://localhost:4000/employees/${employee.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: employee.name,
          ...(employee.pin && { pin: employee.pin }),
          role: employee.role
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update employee');
      }
      
      const updatedEmployee = await response.json();
      dispatch({ type: 'UPDATE_EMPLOYEE', payload: updatedEmployee });
      return updatedEmployee;
    } catch (error) {
      console.error('Failed to update employee:', error);
      throw error;
    }
  };

  const deleteEmployee = async (employeeId: string) => {
    try {
      const response = await fetch(`http://localhost:4000/employees/${employeeId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete employee');
      }
      
      dispatch({ type: 'DELETE_EMPLOYEE', payload: employeeId });
    } catch (error) {
      console.error('Failed to delete employee:', error);
      throw error;
    }
  };

  const validateEmployeePin = async (pin: string) => {
    try {
      const response = await fetch('http://localhost:4000/employees/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pin }),
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          return null;
        }
        const error = await response.json();
        throw new Error(error.error || 'Failed to validate employee');
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to validate employee:', error);
      return null;
    }
  };

  // Fetch functions
  const fetchProducts = async () => {
    try {
      const response = await fetch('http://localhost:4000/products');
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch products');
      }
      const products = await response.json();
      dispatch({ type: 'SET_PRODUCTS', payload: products });
      return products;
    } catch (error) {
      console.error('Failed to fetch products:', error);
      throw error;
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('http://localhost:4000/categories');
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch categories');
      }
      const categories = await response.json();
      const normalizedCategories = [...new Set(categories.map((c: { name: string }) => c.name.toLowerCase()))] as string[];
      dispatch({ type: 'SET_CATEGORIES', payload: normalizedCategories });
      return categories;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      throw error;
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch('http://localhost:4000/employees');
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to fetch employees');
      }
      const employees = await response.json();
      dispatch({ type: 'SET_EMPLOYEES', payload: employees });
      return employees;
    } catch (error) {
      console.error('Failed to fetch employees:', error);
      throw error;
    }
  };

  // Settings functions
  const updateSettings = async (settings: Partial<SystemConfig>) => {
    try {
      const response = await fetch('http://localhost:4000/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });
      if (!response.ok) throw new Error('Failed to update settings');
      const updatedSettings = await response.json();
      dispatch({ type: 'UPDATE_SYSTEM_CONFIG', payload: updatedSettings });
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  };

  const fetchSettings = async () => {
    try {
      const response = await fetch('http://localhost:4000/settings');
      if (!response.ok) throw new Error('Failed to fetch settings');
      const settings = await response.json();
      dispatch({ type: 'UPDATE_SYSTEM_CONFIG', payload: settings });
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      throw error;
    }
  };

  // Fetch initial data
  React.useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          fetchEmployees(),
          fetchSettings(),
          fetchProducts(),
          fetchCategories()
        ]);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      }
    };
    loadInitialData();
  }, []);

  const contextValue: AppContextType = {
    state,
    dispatch,
    addProduct,
    updateProduct,
    deleteProduct,
    addCategory,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateSettings,
    fetchEmployees,
    fetchSettings,
    validateEmployeePin,
    fetchProducts,
    fetchCategories,
    importProducts,
    exportProducts,
    downloadTemplate
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the app context
function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

export { AppContext, AppProvider, useAppContext };
