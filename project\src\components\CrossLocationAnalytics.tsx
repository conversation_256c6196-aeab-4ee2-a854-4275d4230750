import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  MapPin, 
  DollarSign, 
  Users, 
  Clock,
  Target,
  Award,
  AlertTriangle,
  RefreshCw,
  Filter,
  Download
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface LocationComparison {
  location_id: string;
  location_name: string;
  revenue: number;
  orders: number;
  avg_order_value: number;
  efficiency_score: number;
  growth_rate: number;
  customer_satisfaction: number;
  table_turnover: number;
  peak_hours: string[];
}

interface CrossLocationData {
  summary: {
    total_locations: number;
    total_revenue: number;
    total_orders: number;
    avg_efficiency: number;
    best_performer: string;
    growth_rate: number;
  };
  location_comparison: LocationComparison[];
  performance_trends: Array<{
    date: string;
    [key: string]: string | number;
  }>;
  best_practices: Array<{
    location: string;
    practice: string;
    impact: string;
    applicable_to: string[];
  }>;
  optimization_opportunities: Array<{
    location: string;
    opportunity: string;
    potential_impact: string;
    priority: string;
  }>;
}

const CrossLocationAnalytics: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [analyticsData, setAnalyticsData] = useState<CrossLocationData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'today' | '7d' | '30d' | '90d'>('7d');
  const [selectedMetric, setSelectedMetric] = useState<'revenue' | 'orders' | 'efficiency' | 'growth'>('revenue');
  const [sortBy, setSortBy] = useState<'performance' | 'growth' | 'efficiency'>('performance');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchCrossLocationAnalytics();
    const interval = setInterval(fetchCrossLocationAnalytics, 300000); // Update every 5 minutes
    return () => clearInterval(interval);
  }, [selectedTimeRange, selectedMetric]);

  const fetchCrossLocationAnalytics = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall(`/api/analytics/cross-location?range=${selectedTimeRange}&metric=${selectedMetric}`);
      
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching cross-location analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getGrowthColor = (rate: number) => {
    if (rate > 0) return 'text-green-600';
    if (rate < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (rate: number) => {
    if (rate > 0) return <TrendingUp className="h-4 w-4" />;
    if (rate < 0) return <TrendingDown className="h-4 w-4" />;
    return <Target className="h-4 w-4" />;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPerformanceRank = (index: number) => {
    switch (index) {
      case 0: return { icon: <Award className="h-5 w-5 text-yellow-500" />, label: '1st' };
      case 1: return { icon: <Award className="h-5 w-5 text-gray-400" />, label: '2nd' };
      case 2: return { icon: <Award className="h-5 w-5 text-orange-600" />, label: '3rd' };
      default: return { icon: <Target className="h-5 w-5 text-gray-400" />, label: `${index + 1}th` };
    }
  };

  const sortedLocations = analyticsData?.location_comparison.sort((a, b) => {
    switch (sortBy) {
      case 'performance':
        return b.revenue - a.revenue;
      case 'growth':
        return b.growth_rate - a.growth_rate;
      case 'efficiency':
        return b.efficiency_score - a.efficiency_score;
      default:
        return 0;
    }
  }) || [];

  if (isLoading && !analyticsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading cross-location analytics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Cross-Location Analytics</h2>
          <p className="text-gray-600">Compare performance across all restaurant locations</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchCrossLocationAnalytics}
            className="flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button className="flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 bg-white p-4 rounded-lg shadow-sm border">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="today">Today</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Primary Metric</label>
          <select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="revenue">Revenue</option>
            <option value="orders">Orders</option>
            <option value="efficiency">Efficiency</option>
            <option value="growth">Growth Rate</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="performance">Performance</option>
            <option value="growth">Growth Rate</option>
            <option value="efficiency">Efficiency</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      {analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Locations</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.summary.total_locations}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(analyticsData.summary.total_revenue)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.summary.total_orders}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Efficiency</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.summary.avg_efficiency.toFixed(1)}%</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Top Performer</p>
                <p className="text-lg font-bold text-gray-900">{analyticsData.summary.best_performer}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Growth Rate</p>
                <p className={`text-2xl font-bold ${getGrowthColor(analyticsData.summary.growth_rate)}`}>
                  {analyticsData.summary.growth_rate > 0 ? '+' : ''}{analyticsData.summary.growth_rate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Location Performance Comparison */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Location Performance Comparison</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AOV</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Growth</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Satisfaction</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turnover</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedLocations.map((location, index) => {
                const rank = getPerformanceRank(index);
                return (
                  <tr key={location.location_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {rank.icon}
                        <span className="ml-2 text-sm font-medium text-gray-900">{rank.label}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{location.location_name}</div>
                      <div className="text-sm text-gray-500">Peak: {location.peak_hours.join(', ')}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(location.revenue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.orders}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(location.avg_order_value)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {location.efficiency_score.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`flex items-center ${getGrowthColor(location.growth_rate)}`}>
                        {getGrowthIcon(location.growth_rate)}
                        <span className="ml-1 text-sm font-medium">
                          {location.growth_rate > 0 ? '+' : ''}{location.growth_rate.toFixed(1)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.customer_satisfaction.toFixed(1)}/5.0
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {location.table_turnover.toFixed(1)}/day
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Best Practices & Optimization */}
      {analyticsData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Best Practices */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Best Practices to Share</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analyticsData.best_practices.map((practice, index) => (
                  <div key={index} className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-green-900">{practice.location}</h4>
                      <span className="text-xs text-green-600 font-medium">{practice.impact}</span>
                    </div>
                    <p className="text-sm text-green-800 mb-2">{practice.practice}</p>
                    <div className="text-xs text-green-600">
                      <span className="font-medium">Applicable to:</span> {practice.applicable_to.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Optimization Opportunities */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Optimization Opportunities</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {analyticsData.optimization_opportunities.map((opportunity, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{opportunity.location}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(opportunity.priority)}`}>
                        {opportunity.priority}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{opportunity.opportunity}</p>
                    <p className="text-xs text-blue-600 font-medium">{opportunity.potential_impact}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CrossLocationAnalytics;
