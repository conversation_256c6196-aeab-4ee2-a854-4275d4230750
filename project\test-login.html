<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
        }
        .success {
            background: rgba(76, 175, 80, 0.3);
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
        }
        .credentials {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .credentials h3 {
            margin-top: 0;
        }
        .pin-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .pin-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .pin-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 POS Login Test</h1>
        
        <div class="credentials">
            <h3>📋 Enhanced Admin Privileges Test</h3>
            <p><strong>Super Admin (Platform Management):</strong> 888888, 999999</p>
            <p><strong>Restaurant Admin (Full Operations):</strong> 123456, 111111, 000000</p>
            <p><strong>✨ NEW:</strong> Admin users now have FULL restaurant management privileges!</p>
            <p><strong>Backend:</strong> http://localhost:4000</p>
            <p><strong>Frontend:</strong> http://localhost:5173</p>
        </div>

        <div class="form-group">
            <label for="pin">Enter PIN:</label>
            <input type="text" id="pin" placeholder="Enter 6-digit PIN" maxlength="6" readonly>
        </div>

        <div class="pin-grid">
            <button class="pin-button" onclick="addDigit('1')">1</button>
            <button class="pin-button" onclick="addDigit('2')">2</button>
            <button class="pin-button" onclick="addDigit('3')">3</button>
            <button class="pin-button" onclick="addDigit('4')">4</button>
            <button class="pin-button" onclick="addDigit('5')">5</button>
            <button class="pin-button" onclick="addDigit('6')">6</button>
            <button class="pin-button" onclick="addDigit('7')">7</button>
            <button class="pin-button" onclick="addDigit('8')">8</button>
            <button class="pin-button" onclick="addDigit('9')">9</button>
            <button class="pin-button" onclick="clearPin()">Clear</button>
            <button class="pin-button" onclick="addDigit('0')">0</button>
            <button class="pin-button" onclick="backspace()">⌫</button>
        </div>

        <div class="form-group">
            <label for="tenant">Tenant Slug (Optional):</label>
            <input type="text" id="tenant" placeholder="demo-restaurant" value="demo-restaurant">
        </div>

        <button onclick="testLogin()">🔐 Test Login</button>
        <button onclick="testBackend()">🔧 Test Backend</button>
        <button onclick="quickTest('888888')">⚡ Quick Test (Super Admin)</button>
        <button onclick="quickTest('123456')">⚡ Quick Test (Admin)</button>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000';
        
        function addDigit(digit) {
            const pinInput = document.getElementById('pin');
            if (pinInput.value.length < 6) {
                pinInput.value += digit;
            }
        }
        
        function clearPin() {
            document.getElementById('pin').value = '';
        }
        
        function backspace() {
            const pinInput = document.getElementById('pin');
            pinInput.value = pinInput.value.slice(0, -1);
        }
        
        function showResult(message, isSuccess = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }
        
        async function testBackend() {
            try {
                showResult('🔄 Testing backend connection...', false);
                
                const response = await fetch(`${API_BASE}/api/admin/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ Backend is running!<br>
                        <strong>Status:</strong> ${data.status}<br>
                        <strong>Uptime:</strong> ${Math.round(data.uptime)}s<br>
                        <strong>Server:</strong> ${data.server}`, true);
                } else {
                    showResult(`❌ Backend error: ${response.status}`, false);
                }
            } catch (error) {
                showResult(`❌ Backend connection failed: ${error.message}`, false);
            }
        }
        
        async function testLogin() {
            const pin = document.getElementById('pin').value;
            const tenant = document.getElementById('tenant').value;
            
            if (!pin) {
                showResult('❌ Please enter a PIN', false);
                return;
            }
            
            if (pin.length !== 6) {
                showResult('❌ PIN must be 6 digits', false);
                return;
            }
            
            try {
                showResult('🔄 Testing login...', false);
                
                const requestBody = { 
                    pin: pin,
                    tenant_slug: tenant || undefined 
                };
                
                console.log('📤 Login request:', requestBody);
                
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                console.log('📥 Login response:', data);
                
                if (response.ok && data.success) {
                    const permissions = data.user.permissions || [];
                    const hasFullAccess = permissions.includes('all') || permissions.includes('tenant_management');
                    const privilegeLevel = data.user.role === 'super_admin' ? 'Platform Management' :
                                          (hasFullAccess ? 'Full Restaurant Operations' : 'Limited Access');

                    showResult(`✅ Login successful!<br>
                        <strong>User:</strong> ${data.user.name}<br>
                        <strong>Role:</strong> ${data.user.role}<br>
                        <strong>Privilege Level:</strong> ${privilegeLevel}<br>
                        <strong>Permissions:</strong> ${permissions.slice(0, 3).join(', ')}${permissions.length > 3 ? '...' : ''}<br>
                        <strong>Tenant:</strong> ${data.tenant ? data.tenant.name : data.user.tenant_name || 'N/A'}<br>
                        <strong>Token:</strong> ${data.token.substring(0, 20)}...`, true);
                } else {
                    showResult(`❌ Login failed: ${data.error || data.message || 'Unknown error'}`, false);
                }
            } catch (error) {
                showResult(`❌ Login request failed: ${error.message}`, false);
            }
        }
        
        async function quickTest(testPin) {
            document.getElementById('pin').value = testPin;
            await testLogin();
        }
        
        // Test backend on page load
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>
