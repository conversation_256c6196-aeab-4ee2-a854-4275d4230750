version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: restroflow-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: RESTROFLOW
      POSTGRES_USER: BARPOS
      POSTGRES_PASSWORD: Chaand@0319
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/backups:/backups
    ports:
      - "5432:5432"
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U BARPOS -d RESTROFLOW"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Session Management and Caching
  redis:
    image: redis:7-alpine
    container_name: restroflow-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass "RestroFlow2024!"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - restroflow-network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: restroflow-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 4000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: RESTROFLOW
      DB_USER: BARPOS
      DB_PASSWORD: Chaand@0319
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: RestroFlow2024!
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      RATE_LIMIT_WINDOW: 900000
      RATE_LIMIT_MAX_ATTEMPTS: 5
      BLOCK_DURATION: 1800000
      SECURITY_RISK_THRESHOLD: 70
      OTP_EXPIRY: 300000
      BACKUP_CODE_LENGTH: 8
      TOTP_WINDOW: 30
      AUDIT_RETENTION_DAYS: 365
      AUDIT_BATCH_SIZE: 100
      AUDIT_FLUSH_INTERVAL: 60000
      METRICS_RETENTION: 10000
      PERFORMANCE_BATCH_SIZE: 100
      MONITORING_INTERVAL: 30000
      EMAIL_SERVICE: ${EMAIL_SERVICE}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      SMS_SERVICE: ${SMS_SERVICE}
      SMS_API_KEY: ${SMS_API_KEY}
      SMS_API_SECRET: ${SMS_API_SECRET}
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backend/certificates:/app/certificates
    ports:
      - "4000:4000"
    networks:
      - restroflow-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./
      dockerfile: Dockerfile.frontend
    container_name: restroflow-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      REACT_APP_API_URL: http://backend:4000
      REACT_APP_WS_URL: ws://backend:4000
      REACT_APP_VERSION: ${APP_VERSION}
      REACT_APP_BUILD_DATE: ${BUILD_DATE}
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - restroflow-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Logging
  prometheus:
    image: prom/prometheus:latest
    container_name: restroflow-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - restroflow-network

  grafana:
    image: grafana/grafana:latest
    container_name: restroflow-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    networks:
      - restroflow-network
    depends_on:
      - prometheus

  # Log Management
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: restroflow-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - restroflow-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: restroflow-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
      - ./backend/logs:/logs
    ports:
      - "5044:5044"
    networks:
      - restroflow-network
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: restroflow-kibana
    restart: unless-stopped
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - restroflow-network
    depends_on:
      - elasticsearch

  # Backup Service
  backup:
    build:
      context: ./backup
      dockerfile: Dockerfile
    container_name: restroflow-backup
    restart: unless-stopped
    environment:
      BACKUP_SCHEDULE: "0 2 * * *"  # Daily at 2 AM
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: RESTROFLOW
      DB_USER: BARPOS
      DB_PASSWORD: Chaand@0319
      BACKUP_RETENTION_DAYS: 30
      S3_BUCKET: ${S3_BACKUP_BUCKET}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      S3_REGION: ${S3_REGION}
    volumes:
      - ./database/backups:/backups
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - restroflow-network
    depends_on:
      postgres:
        condition: service_healthy

  # Load Balancer (for production scaling)
  nginx-lb:
    image: nginx:alpine
    container_name: restroflow-loadbalancer
    restart: unless-stopped
    volumes:
      - ./nginx/nginx-lb.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "8080:80"
      - "8443:443"
    networks:
      - restroflow-network
    depends_on:
      - frontend
      - backend

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  restroflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
