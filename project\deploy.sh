#!/bin/bash

# RestroFlow POS - Production Deployment Script
# =============================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="restroflow"
ENVIRONMENT="${1:-production}"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking deployment requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f ".env.${ENVIRONMENT}" ]; then
        log_error "Environment file .env.${ENVIRONMENT} not found."
        exit 1
    fi
    
    log_success "All requirements met."
}

setup_environment() {
    log_info "Setting up environment for ${ENVIRONMENT}..."
    
    # Copy environment file
    cp ".env.${ENVIRONMENT}" .env
    
    # Generate secrets if not provided
    if grep -q "your-super-secure-jwt-secret" .env; then
        JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
        sed -i "s/your-super-secure-jwt-secret-key-change-this-in-production/${JWT_SECRET}/" .env
        log_info "Generated JWT secret"
    fi
    
    if grep -q "your-32-character-encryption-key" .env; then
        ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d '\n')
        sed -i "s/your-32-character-encryption-key-here/${ENCRYPTION_KEY}/" .env
        log_info "Generated encryption key"
    fi
    
    if grep -q "secure-grafana-password" .env; then
        GRAFANA_PASSWORD=$(openssl rand -base64 16 | tr -d '\n')
        sed -i "s/secure-grafana-password/${GRAFANA_PASSWORD}/" .env
        log_info "Generated Grafana password: ${GRAFANA_PASSWORD}"
    fi
    
    # Set build date
    BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    sed -i "s/BUILD_DATE=.*/BUILD_DATE=${BUILD_DATE}/" .env
    
    log_success "Environment setup completed."
}

create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p database/init
    mkdir -p database/backups
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p backend/certificates
    mkdir -p frontend/ssl
    mkdir -p monitoring/prometheus
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/logstash/pipeline
    mkdir -p monitoring/logstash/config
    mkdir -p nginx
    mkdir -p redis
    mkdir -p backup
    mkdir -p "${BACKUP_DIR}"
    
    log_success "Directories created."
}

generate_ssl_certificates() {
    log_info "Generating SSL certificates..."
    
    if [ ! -f "frontend/ssl/cert.pem" ]; then
        openssl req -x509 -newkey rsa:4096 -keyout frontend/ssl/key.pem -out frontend/ssl/cert.pem -days 365 -nodes \
            -subj "/C=US/ST=State/L=City/O=RestroFlow/CN=localhost"
        
        # Copy certificates for backend
        cp frontend/ssl/cert.pem backend/certificates/
        cp frontend/ssl/key.pem backend/certificates/
        
        log_success "SSL certificates generated."
    else
        log_info "SSL certificates already exist."
    fi
}

create_database_init_script() {
    log_info "Creating database initialization script..."
    
    cat > database/init/01-init.sql << 'EOF'
-- RestroFlow Database Initialization Script

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create database if not exists
SELECT 'CREATE DATABASE "RESTROFLOW"'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'RESTROFLOW');

-- Connect to the database
\c RESTROFLOW;

-- Create schemas
CREATE SCHEMA IF NOT EXISTS public;
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS security;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO BARPOS;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON TABLES TO BARPOS;
ALTER DEFAULT PRIVILEGES IN SCHEMA analytics GRANT ALL ON TABLES TO BARPOS;
ALTER DEFAULT PRIVILEGES IN SCHEMA security GRANT ALL ON TABLES TO BARPOS;

-- Create initial tables (basic structure)
-- Note: Full schema will be created by the application on first run

-- Create a health check table
CREATE TABLE IF NOT EXISTS health_check (
    id SERIAL PRIMARY KEY,
    status VARCHAR(20) DEFAULT 'healthy',
    last_check TIMESTAMP DEFAULT NOW()
);

INSERT INTO health_check (status) VALUES ('healthy') ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO BARPOS;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO BARPOS;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO BARPOS;
EOF
    
    log_success "Database initialization script created."
}

create_monitoring_configs() {
    log_info "Creating monitoring configurations..."
    
    # Prometheus configuration
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'restroflow-backend'
    static_configs:
      - targets: ['backend:4000']
    metrics_path: '/api/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'nginx'
    static_configs:
      - targets: ['frontend:80']
EOF
    
    # Grafana datasource configuration
    cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF
    
    log_success "Monitoring configurations created."
}

backup_existing_data() {
    log_info "Creating backup of existing data..."
    
    if docker-compose ps | grep -q "restroflow-db"; then
        log_info "Backing up existing database..."
        docker-compose exec -T postgres pg_dump -U BARPOS RESTROFLOW > "${BACKUP_DIR}/database_backup.sql"
        log_success "Database backup created: ${BACKUP_DIR}/database_backup.sql"
    fi
    
    # Backup volumes
    if docker volume ls | grep -q "${PROJECT_NAME}_postgres_data"; then
        log_info "Backing up PostgreSQL volume..."
        docker run --rm -v "${PROJECT_NAME}_postgres_data":/data -v "$(pwd)/${BACKUP_DIR}":/backup alpine tar czf /backup/postgres_volume.tar.gz -C /data .
    fi
    
    if docker volume ls | grep -q "${PROJECT_NAME}_redis_data"; then
        log_info "Backing up Redis volume..."
        docker run --rm -v "${PROJECT_NAME}_redis_data":/data -v "$(pwd)/${BACKUP_DIR}":/backup alpine tar czf /backup/redis_volume.tar.gz -C /data .
    fi
    
    log_success "Backup completed: ${BACKUP_DIR}"
}

build_images() {
    log_info "Building Docker images..."
    
    # Build backend image
    log_info "Building backend image..."
    docker build -t "${PROJECT_NAME}-backend:latest" -f backend/Dockerfile backend/
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build -t "${PROJECT_NAME}-frontend:latest" -f Dockerfile.frontend .
    
    log_success "Docker images built successfully."
}

deploy_services() {
    log_info "Deploying services..."
    
    # Stop existing services
    log_info "Stopping existing services..."
    docker-compose down --remove-orphans
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log_success "Services deployed successfully."
}

check_service_health() {
    log_info "Checking service health..."
    
    services=("postgres" "redis" "backend" "frontend")
    
    for service in "${services[@]}"; do
        log_info "Checking ${service}..."
        
        max_attempts=30
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if docker-compose ps | grep "${PROJECT_NAME}-${service}" | grep -q "healthy\|Up"; then
                log_success "${service} is healthy"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_error "${service} failed to become healthy"
                docker-compose logs "${service}"
                exit 1
            fi
            
            log_info "Waiting for ${service} to be healthy... (attempt ${attempt}/${max_attempts})"
            sleep 10
            ((attempt++))
        done
    done
    
    log_success "All services are healthy."
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations (if migration script exists)
    if [ -f "database/migrations/migrate.js" ]; then
        docker-compose exec backend node database/migrations/migrate.js
        log_success "Database migrations completed."
    else
        log_info "No migration script found, skipping migrations."
    fi
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Import Grafana dashboards (if they exist)
    if [ -d "monitoring/grafana/dashboards" ]; then
        log_info "Grafana dashboards will be automatically imported."
    fi
    
    log_success "Monitoring setup completed."
}

post_deployment_checks() {
    log_info "Running post-deployment checks..."
    
    # Check API health
    log_info "Checking API health..."
    if curl -f http://localhost:4000/api/health > /dev/null 2>&1; then
        log_success "API is responding"
    else
        log_error "API health check failed"
        exit 1
    fi
    
    # Check frontend
    log_info "Checking frontend..."
    if curl -f http://localhost:80/health > /dev/null 2>&1; then
        log_success "Frontend is responding"
    else
        log_error "Frontend health check failed"
        exit 1
    fi
    
    # Check database connection
    log_info "Checking database connection..."
    if docker-compose exec -T postgres psql -U BARPOS -d RESTROFLOW -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Database connection successful"
    else
        log_error "Database connection failed"
        exit 1
    fi
    
    log_success "All post-deployment checks passed."
}

cleanup() {
    log_info "Cleaning up..."
    
    # Remove unused Docker images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    log_success "Cleanup completed."
}

show_deployment_info() {
    log_success "Deployment completed successfully!"
    echo
    echo "=== RestroFlow POS Deployment Information ==="
    echo "Environment: ${ENVIRONMENT}"
    echo "Deployment Time: $(date)"
    echo
    echo "=== Service URLs ==="
    echo "Frontend: http://localhost:80 (HTTP) / https://localhost:443 (HTTPS)"
    echo "Backend API: http://localhost:4000"
    echo "Grafana: http://localhost:3000"
    echo "Prometheus: http://localhost:9090"
    echo "Kibana: http://localhost:5601"
    echo
    echo "=== Default Credentials ==="
    echo "Grafana Admin: admin / $(grep GRAFANA_PASSWORD .env | cut -d'=' -f2)"
    echo
    echo "=== Backup Location ==="
    echo "Backup Directory: ${BACKUP_DIR}"
    echo
    echo "=== Useful Commands ==="
    echo "View logs: docker-compose logs -f [service_name]"
    echo "Stop services: docker-compose down"
    echo "Restart services: docker-compose restart"
    echo "Update services: docker-compose pull && docker-compose up -d"
    echo
    log_success "RestroFlow POS is now running!"
}

# Main deployment process
main() {
    log_info "Starting RestroFlow POS deployment..."
    
    check_requirements
    setup_environment
    create_directories
    generate_ssl_certificates
    create_database_init_script
    create_monitoring_configs
    backup_existing_data
    build_images
    deploy_services
    run_database_migrations
    setup_monitoring
    post_deployment_checks
    cleanup
    show_deployment_info
}

# Run main function
main "$@"
