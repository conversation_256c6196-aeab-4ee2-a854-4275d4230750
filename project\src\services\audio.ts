class AudioNotificationService {
  private audioContext: AudioContext | null = null;
  private isEnabled = true;
  private volume = 0.5;

  constructor() {
    // Initialize audio context on first user interaction
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  private async ensureAudioContext() {
    if (!this.audioContext) {
      this.initializeAudioContext();
    }

    if (this.audioContext?.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        console.warn('Failed to resume audio context:', error);
      }
    }
  }

  // Generate notification sounds using Web Audio API
  private async playTone(frequency: number, duration: number, type: OscillatorType = 'sine') {
    if (!this.isEnabled || !this.audioContext) return;

    try {
      await this.ensureAudioContext();

      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(this.volume, this.audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + duration);
    } catch (error) {
      console.warn('Failed to play audio notification:', error);
    }
  }

  // Play notification for new order
  async playNewOrderNotification() {
    // Play a pleasant chime sequence
    await this.playTone(800, 0.2);
    setTimeout(() => this.playTone(1000, 0.2), 150);
    setTimeout(() => this.playTone(1200, 0.3), 300);
  }

  // Play notification for order ready
  async playOrderReadyNotification() {
    // Play a success sound
    await this.playTone(600, 0.15);
    setTimeout(() => this.playTone(800, 0.15), 100);
    setTimeout(() => this.playTone(1000, 0.2), 200);
  }

  // Play notification for urgent order (high priority)
  async playUrgentOrderNotification() {
    // Play an urgent beeping sound
    for (let i = 0; i < 3; i++) {
      setTimeout(() => this.playTone(1500, 0.1, 'square'), i * 200);
    }
  }

  // Play notification for order status change
  async playStatusChangeNotification() {
    // Play a subtle notification
    await this.playTone(700, 0.15);
  }

  // Play notification for order completed/served
  async playOrderCompletedNotification() {
    // Play a completion sound
    await this.playTone(500, 0.1);
    setTimeout(() => this.playTone(700, 0.1), 80);
    setTimeout(() => this.playTone(900, 0.15), 160);
  }

  // Enable/disable notifications
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  // Set volume (0-1)
  setVolume(volume: number) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  // Get current settings
  getSettings() {
    return {
      enabled: this.isEnabled,
      volume: this.volume,
    };
  }

  // Test audio functionality
  async testAudio() {
    await this.playTone(800, 0.3);
  }

  // Request audio permission (for browsers that require user interaction)
  async requestPermission(): Promise<boolean> {
    try {
      await this.ensureAudioContext();
      // Play a silent tone to test if audio is working
      await this.playTone(0, 0.001);
      return true;
    } catch (error) {
      console.warn('Audio permission denied or not available:', error);
      return false;
    }
  }
}

// Export singleton instance
export const audioService = new AudioNotificationService();
export default audioService;
