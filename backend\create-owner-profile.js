const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

/**
 * Create Owner Super Admin Profile
 * This script creates a comprehensive super admin profile for the system owner
 */
async function createOwnerProfile() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Creating BARPOS System Owner Profile...\n');
    
    // Step 1: Create or get the system tenant (tenant_id = 0 for system-wide access)
    console.log('📋 Step 1: Setting up system tenant...');
    
    let systemTenantId;
    const systemTenantCheck = await client.query(`
      SELECT id FROM tenants WHERE slug = 'barpos-system' LIMIT 1
    `);
    
    if (systemTenantCheck.rows.length === 0) {
      // Create system tenant
      const systemTenantResult = await client.query(`
        INSERT INTO tenants (
          name, slug, email, phone, address, status, 
          business_type, timezone, country, plan_type, 
          subscription_status, created_at, updated_at
        ) VALUES (
          'BARPOS System', 'barpos-system', '<EMAIL>', 
          '******-BARPOS', 'BARPOS Headquarters', 'active',
          'technology', 'America/New_York', 'US', 'enterprise',
          'active', NOW(), NOW()
        ) RETURNING id
      `);
      systemTenantId = systemTenantResult.rows[0].id;
      console.log(`✅ Created system tenant with ID: ${systemTenantId}`);
    } else {
      systemTenantId = systemTenantCheck.rows[0].id;
      console.log(`✅ Using existing system tenant with ID: ${systemTenantId}`);
    }
    
    // Step 2: Create system location
    console.log('\n📍 Step 2: Setting up system location...');
    
    let systemLocationId;
    const systemLocationCheck = await client.query(`
      SELECT id FROM locations WHERE tenant_id::text = $1 AND name = 'System Headquarters' LIMIT 1
    `, [systemTenantId.toString()]);
    
    if (systemLocationCheck.rows.length === 0) {
      const systemLocationResult = await client.query(`
        INSERT INTO locations (
          tenant_id, name, address, phone, email, 
          is_active, timezone, created_at, updated_at
        ) VALUES (
          $1, 'System Headquarters', 'BARPOS HQ', '******-BARPOS', 
          '<EMAIL>', true, 'America/New_York', NOW(), NOW()
        ) RETURNING id
      `, [systemTenantId]);
      systemLocationId = systemLocationResult.rows[0].id;
      console.log(`✅ Created system location with ID: ${systemLocationId}`);
    } else {
      systemLocationId = systemLocationCheck.rows[0].id;
      console.log(`✅ Using existing system location with ID: ${systemLocationId}`);
    }
    
    // Step 3: Create owner super admin profile
    console.log('\n👑 Step 3: Creating owner super admin profile...');
    
    // Check if owner already exists
    const ownerCheck = await client.query(`
      SELECT id, name, role FROM employees 
      WHERE email = '<EMAIL>' OR name = 'System Owner'
      LIMIT 1
    `);
    
    if (ownerCheck.rows.length > 0) {
      console.log(`⚠️  Owner profile already exists: ${ownerCheck.rows[0].name} (ID: ${ownerCheck.rows[0].id})`);
      console.log('Would you like to update the existing profile? (This script will continue with creation)');
    }
    
    // Create secure PIN hash
    const ownerPin = '000000'; // Default owner PIN - CHANGE THIS!
    const hashedPin = await bcrypt.hash(ownerPin, 12); // Higher salt rounds for owner
    
    // Create owner profile
    const ownerResult = await client.query(`
      INSERT INTO employees (
        tenant_id, location_id, name, email, pin, pin_hash, role, 
        is_active, permissions, hire_date, employment_type, 
        department, notes, created_at, updated_at
      ) VALUES (
        $1, $2, 'System Owner', '<EMAIL>', $3, $4, 'super_admin',
        true, $5, CURRENT_DATE, 'full_time', 'Executive', 
        'BARPOS System Owner - Full platform access', NOW(), NOW()
      ) RETURNING id, name, email, role
    `, [
      systemTenantId,
      systemLocationId, 
      ownerPin,
      hashedPin,
      JSON.stringify(['all', 'system:*', 'tenants:*', 'users:*', 'analytics:*', 'billing:*', 'security:*'])
    ]);
    
    const ownerId = ownerResult.rows[0].id;
    console.log(`✅ Created owner profile: ${ownerResult.rows[0].name} (ID: ${ownerId})`);
    
    // Step 4: Create additional admin profiles for development team
    console.log('\n👥 Step 4: Creating development team profiles...');
    
    const devTeam = [
      {
        name: 'Chaand Suhagiya',
        email: '<EMAIL>',
        pin: '123456',
        role: 'super_admin',
        department: 'Development',
        notes: 'Lead Developer & Co-Owner'
      },
      {
        name: 'Development Admin',
        email: '<EMAIL>', 
        pin: '999999',
        role: 'super_admin',
        department: 'Development',
        notes: 'Development Team Access'
      },
      {
        name: 'Support Admin',
        email: '<EMAIL>',
        pin: '888888', 
        role: 'tenant_admin',
        department: 'Support',
        notes: 'Customer Support Team Lead'
      }
    ];
    
    for (const dev of devTeam) {
      // Check if dev already exists
      const devCheck = await client.query(`
        SELECT id FROM employees WHERE email = $1 LIMIT 1
      `, [dev.email]);
      
      if (devCheck.rows.length === 0) {
        const devHashedPin = await bcrypt.hash(dev.pin, 10);
        const devPermissions = dev.role === 'super_admin' 
          ? ['all', 'system:*', 'tenants:*', 'users:*', 'analytics:*']
          : ['tenants:*', 'users:view', 'users:edit', 'analytics:view'];
          
        const devResult = await client.query(`
          INSERT INTO employees (
            tenant_id, location_id, name, email, pin, pin_hash, role,
            is_active, permissions, hire_date, employment_type,
            department, notes, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, true, $8, CURRENT_DATE, 
            'full_time', $9, $10, NOW(), NOW()
          ) RETURNING id, name, role
        `, [
          systemTenantId, systemLocationId, dev.name, dev.email,
          dev.pin, devHashedPin, dev.role, JSON.stringify(devPermissions),
          dev.department, dev.notes
        ]);
        
        console.log(`✅ Created ${dev.role}: ${dev.name} (ID: ${devResult.rows[0].id})`);
      } else {
        console.log(`⚠️  ${dev.name} already exists, skipping...`);
      }
    }
    
    // Step 5: Create system-wide permissions and roles
    console.log('\n🔐 Step 5: Setting up system permissions...');
    
    // Create admin roles if they don't exist
    const roles = [
      {
        name: 'system_owner',
        description: 'System Owner with absolute control',
        permissions: ['all', 'system:*', 'platform:*', 'billing:*', 'security:*'],
        is_system_role: true
      },
      {
        name: 'platform_admin', 
        description: 'Platform Administrator',
        permissions: ['tenants:*', 'users:*', 'analytics:*', 'support:*'],
        is_system_role: true
      }
    ];
    
    for (const role of roles) {
      await client.query(`
        INSERT INTO admin_roles (name, description, permissions, is_system_role)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          description = EXCLUDED.description,
          permissions = EXCLUDED.permissions,
          updated_at = NOW()
      `, [role.name, role.description, JSON.stringify(role.permissions), role.is_system_role]);
    }
    
    console.log('✅ System roles configured');

    // Step 6: Display access credentials
    console.log('\n🎯 Step 6: Access Credentials Summary');
    console.log('=' .repeat(60));
    console.log('🏢 BARPOS System Owner Access:');
    console.log(`   👤 Name: System Owner`);
    console.log(`   📧 Email: <EMAIL>`);
    console.log(`   🔑 PIN: 000000 (CHANGE THIS IMMEDIATELY!)`);
    console.log(`   🏛️ Tenant: BARPOS System (ID: ${systemTenantId})`);
    console.log(`   🎭 Role: super_admin`);
    console.log(`   ✅ Status: Active`);
    console.log('');
    console.log('👥 Development Team Access:');
    devTeam.forEach(dev => {
      console.log(`   👤 ${dev.name}: PIN ${dev.pin} (${dev.role})`);
    });
    console.log('');
    console.log('🌐 Access URLs:');
    console.log('   🖥️  Super Admin Dashboard: http://localhost:5175/super-admin.html');
    console.log('   🏪 POS System: http://localhost:5175/unified-pos.html');
    console.log('   🔗 API Base: http://localhost:4000/api');
    console.log('');
    console.log('⚠️  SECURITY RECOMMENDATIONS:');
    console.log('   1. Change the default PIN (000000) immediately');
    console.log('   2. Set up 2FA for production environments');
    console.log('   3. Review and audit permissions regularly');
    console.log('   4. Monitor access logs for suspicious activity');
    console.log('   5. Use strong, unique PINs for all admin accounts');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ Error creating owner profile:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Additional utility functions for owner management
async function updateOwnerPin(newPin) {
  const client = await pool.connect();
  try {
    const hashedPin = await bcrypt.hash(newPin, 12);
    await client.query(`
      UPDATE employees
      SET pin = $1, pin_hash = $2, updated_at = NOW()
      WHERE email = '<EMAIL>'
    `, [newPin, hashedPin]);
    console.log('✅ Owner PIN updated successfully');
  } catch (error) {
    console.error('❌ Error updating owner PIN:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function getOwnerProfile() {
  const client = await pool.connect();
  try {
    const result = await client.query(`
      SELECT e.*, t.name as tenant_name, l.name as location_name
      FROM employees e
      LEFT JOIN tenants t ON e.tenant_id = t.id
      LEFT JOIN locations l ON e.location_id = l.id
      WHERE e.email = '<EMAIL>'
    `);
    return result.rows[0];
  } catch (error) {
    console.error('❌ Error fetching owner profile:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the script
if (require.main === module) {
  createOwnerProfile()
    .then(() => {
      console.log('\n🎉 Owner profile creation completed successfully!');
      console.log('\n📝 Next Steps:');
      console.log('1. Run the server: cd backend && node working-server.js');
      console.log('2. Open Super Admin Dashboard: http://localhost:5175/super-admin.html');
      console.log('3. Login with PIN: 000000');
      console.log('4. IMMEDIATELY change your PIN in the profile settings');
      console.log('5. Review and configure system settings');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Failed to create owner profile:', error);
      process.exit(1);
    });
}

module.exports = { createOwnerProfile, updateOwnerPin, getOwnerProfile };
