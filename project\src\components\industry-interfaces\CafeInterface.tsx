import React, { useState, useEffect } from 'react';
import { 
  Coffee, 
  Clock, 
  User, 
  Star, 
  Calendar,
  Thermometer,
  Droplets,
  Plus,
  Minus,
  Heart,
  Gift,
  Leaf,
  Snowflake,
  Sun,
  CloudRain
} from 'lucide-react';

interface BeverageModifier {
  id: string;
  name: string;
  options: { value: string; price: number }[];
  required: boolean;
}

interface BeverageItem {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  category: 'coffee' | 'tea' | 'specialty' | 'cold' | 'food';
  modifiers: string[];
  seasonal?: boolean;
  popular?: boolean;
  caffeine?: number;
  calories?: number;
}

interface CustomerOrder {
  id: string;
  customerName: string;
  items: {
    item: BeverageItem;
    modifiers: Record<string, string>;
    quantity: number;
    totalPrice: number;
  }[];
  orderTime: Date;
  pickupTime?: Date;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  orderType: 'here' | 'togo' | 'pickup';
  loyaltyPoints?: number;
}

interface RegularCustomer {
  id: string;
  name: string;
  favoriteOrder: string;
  visitCount: number;
  loyaltyPoints: number;
  preferences: string[];
}

const CafeInterface: React.FC = () => {
  const [currentOrder, setCurrentOrder] = useState<any[]>([]);
  const [customerName, setCustomerName] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('coffee');
  const [selectedItem, setSelectedItem] = useState<BeverageItem | null>(null);
  const [itemModifiers, setItemModifiers] = useState<Record<string, string>>({});
  const [orderQueue, setOrderQueue] = useState<CustomerOrder[]>([]);
  const [orderType, setOrderType] = useState<'here' | 'togo' | 'pickup'>('here');
  const [regularCustomer, setRegularCustomer] = useState<RegularCustomer | null>(null);

  // Beverage modifiers
  const modifiers: Record<string, BeverageModifier> = {
    size: {
      id: 'size',
      name: 'Size',
      options: [
        { value: 'Small (8oz)', price: 0 },
        { value: 'Medium (12oz)', price: 0.75 },
        { value: 'Large (16oz)', price: 1.25 },
        { value: 'Extra Large (20oz)', price: 1.75 }
      ],
      required: true
    },
    milk: {
      id: 'milk',
      name: 'Milk',
      options: [
        { value: 'Whole Milk', price: 0 },
        { value: 'Skim Milk', price: 0 },
        { value: 'Oat Milk', price: 0.65 },
        { value: 'Almond Milk', price: 0.65 },
        { value: 'Soy Milk', price: 0.65 },
        { value: 'Coconut Milk', price: 0.65 }
      ],
      required: false
    },
    shots: {
      id: 'shots',
      name: 'Espresso Shots',
      options: [
        { value: '1 Shot', price: 0 },
        { value: '2 Shots', price: 0.75 },
        { value: '3 Shots', price: 1.50 },
        { value: 'Decaf', price: 0 }
      ],
      required: false
    },
    sweetener: {
      id: 'sweetener',
      name: 'Sweetener',
      options: [
        { value: 'None', price: 0 },
        { value: 'Sugar', price: 0 },
        { value: 'Honey', price: 0.25 },
        { value: 'Agave', price: 0.25 },
        { value: 'Stevia', price: 0 },
        { value: 'Vanilla Syrup', price: 0.50 },
        { value: 'Caramel Syrup', price: 0.50 },
        { value: 'Hazelnut Syrup', price: 0.50 }
      ],
      required: false
    },
    temperature: {
      id: 'temperature',
      name: 'Temperature',
      options: [
        { value: 'Hot', price: 0 },
        { value: 'Iced', price: 0 },
        { value: 'Extra Hot', price: 0 },
        { value: 'Lukewarm', price: 0 }
      ],
      required: true
    }
  };

  // Menu items
  const menuItems: Record<string, BeverageItem[]> = {
    coffee: [
      {
        id: '1',
        name: 'Americano',
        description: 'Rich espresso with hot water',
        basePrice: 3.25,
        category: 'coffee',
        modifiers: ['size', 'shots', 'temperature'],
        caffeine: 150,
        calories: 5
      },
      {
        id: '2',
        name: 'Latte',
        description: 'Espresso with steamed milk and light foam',
        basePrice: 4.75,
        category: 'coffee',
        modifiers: ['size', 'milk', 'shots', 'sweetener', 'temperature'],
        popular: true,
        caffeine: 150,
        calories: 190
      },
      {
        id: '3',
        name: 'Cappuccino',
        description: 'Equal parts espresso, steamed milk, and foam',
        basePrice: 4.25,
        category: 'coffee',
        modifiers: ['size', 'milk', 'shots', 'sweetener', 'temperature'],
        caffeine: 150,
        calories: 120
      },
      {
        id: '4',
        name: 'Mocha',
        description: 'Espresso with chocolate and steamed milk',
        basePrice: 5.25,
        category: 'coffee',
        modifiers: ['size', 'milk', 'shots', 'sweetener', 'temperature'],
        caffeine: 175,
        calories: 290
      }
    ],
    tea: [
      {
        id: '5',
        name: 'Earl Grey',
        description: 'Classic black tea with bergamot',
        basePrice: 2.75,
        category: 'tea',
        modifiers: ['size', 'sweetener', 'temperature'],
        caffeine: 40,
        calories: 0
      },
      {
        id: '6',
        name: 'Chamomile',
        description: 'Soothing herbal tea',
        basePrice: 2.75,
        category: 'tea',
        modifiers: ['size', 'sweetener', 'temperature'],
        caffeine: 0,
        calories: 0
      },
      {
        id: '7',
        name: 'Matcha Latte',
        description: 'Premium matcha with steamed milk',
        basePrice: 5.75,
        category: 'tea',
        modifiers: ['size', 'milk', 'sweetener', 'temperature'],
        popular: true,
        caffeine: 70,
        calories: 240
      }
    ],
    specialty: [
      {
        id: '8',
        name: 'Pumpkin Spice Latte',
        description: 'Seasonal favorite with pumpkin and spices',
        basePrice: 6.25,
        category: 'specialty',
        modifiers: ['size', 'milk', 'shots', 'sweetener', 'temperature'],
        seasonal: true,
        caffeine: 150,
        calories: 380
      },
      {
        id: '9',
        name: 'Lavender Honey Latte',
        description: 'Floral and sweet with local honey',
        basePrice: 5.95,
        category: 'specialty',
        modifiers: ['size', 'milk', 'shots', 'temperature'],
        caffeine: 150,
        calories: 220
      }
    ],
    cold: [
      {
        id: '10',
        name: 'Cold Brew',
        description: 'Smooth, slow-steeped coffee',
        basePrice: 3.75,
        category: 'cold',
        modifiers: ['size', 'milk', 'sweetener'],
        caffeine: 200,
        calories: 5
      },
      {
        id: '11',
        name: 'Iced Caramel Macchiato',
        description: 'Vanilla syrup, milk, espresso, and caramel drizzle',
        basePrice: 5.45,
        category: 'cold',
        modifiers: ['size', 'milk', 'shots'],
        popular: true,
        caffeine: 150,
        calories: 250
      }
    ],
    food: [
      {
        id: '12',
        name: 'Croissant',
        description: 'Buttery, flaky pastry',
        basePrice: 3.25,
        category: 'food',
        modifiers: [],
        calories: 280
      },
      {
        id: '13',
        name: 'Avocado Toast',
        description: 'Multigrain bread with fresh avocado',
        basePrice: 8.95,
        category: 'food',
        modifiers: [],
        calories: 320
      }
    ]
  };

  // Mock regular customers
  const regularCustomers: RegularCustomer[] = [
    {
      id: '1',
      name: 'Sarah',
      favoriteOrder: 'Large Oat Milk Latte, Extra Shot',
      visitCount: 47,
      loyaltyPoints: 235,
      preferences: ['Oat Milk', 'Extra Shot', 'No Sweetener']
    },
    {
      id: '2',
      name: 'Mike',
      favoriteOrder: 'Medium Americano, Hot',
      visitCount: 23,
      loyaltyPoints: 115,
      preferences: ['Black Coffee', 'Hot', 'No Milk']
    }
  ];

  const addItemToOrder = () => {
    if (!selectedItem) return;

    const modifierPrice = Object.entries(itemModifiers).reduce((sum, [modifierId, value]) => {
      const modifier = modifiers[modifierId];
      const option = modifier.options.find(opt => opt.value === value);
      return sum + (option?.price || 0);
    }, 0);

    const orderItem = {
      item: selectedItem,
      modifiers: { ...itemModifiers },
      quantity: 1,
      totalPrice: selectedItem.basePrice + modifierPrice
    };

    setCurrentOrder([...currentOrder, orderItem]);
    setSelectedItem(null);
    setItemModifiers({});
  };

  const submitOrder = () => {
    if (currentOrder.length === 0 || !customerName) return;

    const newOrder: CustomerOrder = {
      id: Date.now().toString(),
      customerName,
      items: [...currentOrder],
      orderTime: new Date(),
      pickupTime: orderType === 'pickup' ? new Date(Date.now() + 15 * 60000) : undefined,
      status: 'pending',
      orderType,
      loyaltyPoints: Math.floor(getOrderTotal() * 10)
    };

    setOrderQueue([...orderQueue, newOrder]);
    setCurrentOrder([]);
    setCustomerName('');
    setItemModifiers({});
  };

  const getOrderTotal = () => {
    return currentOrder.reduce((sum, item) => sum + item.totalPrice, 0);
  };

  const categories = [
    { id: 'coffee', name: 'Coffee', icon: Coffee, color: 'amber' },
    { id: 'tea', name: 'Tea', icon: Leaf, color: 'green' },
    { id: 'specialty', name: 'Specialty', icon: Star, color: 'purple' },
    { id: 'cold', name: 'Cold Drinks', icon: Snowflake, color: 'blue' },
    { id: 'food', name: 'Food', icon: Gift, color: 'orange' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-amber-700 to-orange-600 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Coffee className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Artisan Cafe</h1>
              <p className="text-amber-100">Handcrafted beverages & fresh pastries</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{orderQueue.filter(o => o.status !== 'completed').length}</div>
              <div className="text-amber-200">Active Orders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">☀️</div>
              <div className="text-amber-200">Seasonal Menu</div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Order Queue */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl shadow-lg border border-amber-200 p-6 h-full">
            <div className="flex items-center space-x-3 mb-4">
              <Clock className="w-6 h-6 text-amber-600" />
              <h2 className="text-xl font-bold text-gray-900">Order Queue</h2>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {orderQueue.map((order) => (
                <div key={order.id} className={`p-4 rounded-lg border-2 ${getStatusColor(order.status)}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-bold">{order.customerName}</span>
                    <span className="text-sm font-medium">
                      {order.orderType === 'pickup' ? '📱' : order.orderType === 'togo' ? '🥤' : '☕'}
                    </span>
                  </div>
                  
                  <div className="text-xs text-gray-600 mb-2">
                    {order.items.map(item => item.item.name).join(', ')}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span>{order.orderTime.toLocaleTimeString()}</span>
                    {order.pickupTime && (
                      <span className="font-medium">
                        Pickup: {order.pickupTime.toLocaleTimeString()}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Menu */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg border border-amber-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Coffee className="w-6 h-6 text-amber-600" />
              <h2 className="text-xl font-bold text-gray-900">Menu</h2>
            </div>
            
            {/* Category Tabs */}
            <div className="flex space-x-2 mb-6 overflow-x-auto">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
                      selectedCategory === category.id
                        ? 'bg-gradient-to-r from-amber-600 to-orange-500 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{category.name}</span>
                  </button>
                );
              })}
            </div>
            
            {/* Menu Items */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {menuItems[selectedCategory]?.map((item) => (
                <div
                  key={item.id}
                  onClick={() => setSelectedItem(item)}
                  className={`p-4 border-2 rounded-lg transition-all duration-200 cursor-pointer ${
                    selectedItem?.id === item.id
                      ? 'border-amber-500 bg-amber-50'
                      : 'border-gray-200 hover:border-amber-300 hover:shadow-md'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900">{item.name}</h3>
                        {item.popular && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                        {item.seasonal && <Sun className="w-4 h-4 text-orange-500" />}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                    </div>
                    <span className="text-lg font-bold text-amber-600">${item.basePrice}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    {item.caffeine !== undefined && <span>☕ {item.caffeine}mg caffeine</span>}
                    {item.calories !== undefined && <span>🔥 {item.calories} cal</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Order Customization & Checkout */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Customer Info */}
            <div className="bg-white rounded-2xl shadow-lg border border-amber-200 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <User className="w-6 h-6 text-amber-600" />
                <h2 className="text-xl font-bold text-gray-900">Customer</h2>
              </div>
              
              <input
                type="text"
                placeholder="Customer name"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent mb-4"
              />
              
              <div className="grid grid-cols-3 gap-2">
                {['here', 'togo', 'pickup'].map((type) => (
                  <button
                    key={type}
                    onClick={() => setOrderType(type as any)}
                    className={`py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      orderType === type
                        ? 'bg-amber-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {type === 'here' ? 'For Here' : type === 'togo' ? 'To Go' : 'Pickup'}
                  </button>
                ))}
              </div>
            </div>

            {/* Item Customization */}
            {selectedItem && (
              <div className="bg-white rounded-2xl shadow-lg border border-amber-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Customize {selectedItem.name}</h3>
                
                <div className="space-y-4">
                  {selectedItem.modifiers.map((modifierId) => {
                    const modifier = modifiers[modifierId];
                    return (
                      <div key={modifierId}>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {modifier.name} {modifier.required && <span className="text-red-500">*</span>}
                        </label>
                        <select
                          value={itemModifiers[modifierId] || ''}
                          onChange={(e) => setItemModifiers({
                            ...itemModifiers,
                            [modifierId]: e.target.value
                          })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                        >
                          <option value="">Select {modifier.name}</option>
                          {modifier.options.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.value} {option.price > 0 && `(+$${option.price})`}
                            </option>
                          ))}
                        </select>
                      </div>
                    );
                  })}
                </div>
                
                <button
                  onClick={addItemToOrder}
                  className="w-full mt-4 bg-gradient-to-r from-amber-600 to-orange-500 hover:from-amber-700 hover:to-orange-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200"
                >
                  Add to Order
                </button>
              </div>
            )}

            {/* Current Order */}
            <div className="bg-white rounded-2xl shadow-lg border border-amber-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Current Order</h3>
              
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {currentOrder.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No items in order</p>
                ) : (
                  currentOrder.map((item, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{item.item.name}</span>
                        <span className="font-bold text-amber-600">${item.totalPrice.toFixed(2)}</span>
                      </div>
                      <div className="text-xs text-gray-600">
                        {Object.values(item.modifiers).filter(Boolean).join(', ')}
                      </div>
                    </div>
                  ))
                )}
              </div>
              
              {currentOrder.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold">Total:</span>
                    <span className="text-xl font-bold text-amber-600">${getOrderTotal().toFixed(2)}</span>
                  </div>
                  
                  <button
                    onClick={submitOrder}
                    disabled={!customerName}
                    className="w-full bg-gradient-to-r from-amber-600 to-orange-500 hover:from-amber-700 hover:to-orange-600 disabled:from-gray-400 disabled:to-gray-500 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed"
                  >
                    Submit Order
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CafeInterface;
