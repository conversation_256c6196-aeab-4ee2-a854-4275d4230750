import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { AppState, AppAction, AppContextType, Employee, Product, Order, KitchenOrder, Tenant, KitchenMetrics, FloorLayout } from '../types';
import { io, Socket } from 'socket.io-client';

// Enhanced initial state with multi-tenancy
const initialState: AppState = {
  products: [],
  orders: [],
  currentOrder: null,
  employees: [],
  currentEmployee: null,
  isAuthenticated: false,
  categories: [],
  systemConfig: {
    tax_rate: 0.0825,
    receipt_header: 'Thank you for visiting!',
    receipt_footer: 'Please come again',
    business_name: 'Restaurant POS',
    business_address: '',
    business_phone: '',
    theme_primary_color: '#4f46e5',
    theme_secondary_color: '#10b981'
  },
  floorLayout: null,
  tables: [],
  schedules: [],
  customers: [],
  loyaltyRewards: [],
  kitchenOrders: [],
  selectedTable: null,
  kitchenMetrics: null,
  kitchenSettings: {
    audioEnabled: true,
    audioVolume: 0.7,
    autoRefresh: true,
    refreshInterval: 30,
    showMetrics: true,
    compactView: false,
    maxOrdersPerColumn: 10,
    overdueThreshold: 30,
    priorityColors: {
      low: '#10b981',
      normal: '#f59e0b',
      high: '#ef4444'
    }
  },
  kitchenStaff: [],
  // Enhanced state for multi-tenancy
  currentTenant: null,
  currentLocation: null,
  locations: [],
  tenantSettings: null,
  authToken: null,
  socket: null,
  realTimeUpdates: true
};

// Enhanced reducer with multi-tenancy actions
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_AUTH_TOKEN':
      return { ...state, authToken: action.payload };
    
    case 'SET_CURRENT_TENANT':
      return { ...state, currentTenant: action.payload };
    
    case 'SET_CURRENT_LOCATION':
      return { ...state, currentLocation: action.payload };
    
    case 'SET_LOCATIONS':
      return { ...state, locations: action.payload };
    
    case 'SET_TENANT_SETTINGS':
      return { ...state, tenantSettings: action.payload };
    
    case 'SET_SOCKET':
      return { ...state, socket: action.payload };
    
    case 'LOGIN':
      return {
        ...state,
        currentEmployee: action.payload.employee,
        currentTenant: action.payload.tenant,
        currentLocation: action.payload.location || null,
        authToken: action.payload.token,
        isAuthenticated: true
      };
    
    case 'LOGOUT':
      // Disconnect socket on logout
      if (state.socket) {
        state.socket.disconnect();
      }
      return {
        ...initialState,
        socket: null
      };
    
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload };
    
    case 'ADD_PRODUCT':
      return { ...state, products: [...state.products, action.payload] };
    
    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(p => p.id === action.payload.id ? action.payload : p)
      };
    
    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(p => p.id !== action.payload)
      };
    
    case 'SET_EMPLOYEES':
      return { ...state, employees: action.payload };
    
    case 'ADD_EMPLOYEE':
      return { ...state, employees: [...state.employees, action.payload] };
    
    case 'UPDATE_EMPLOYEE':
      return {
        ...state,
        employees: state.employees.map(e => e.id === action.payload.id ? action.payload : e)
      };
    
    case 'DELETE_EMPLOYEE':
      return {
        ...state,
        employees: state.employees.filter(e => e.id !== action.payload)
      };
    
    case 'SET_ORDERS':
      return { ...state, orders: action.payload };
    
    case 'SET_KITCHEN_ORDERS':
      return { ...state, kitchenOrders: action.payload };
    
    case 'ADD_KITCHEN_ORDER':
      return { ...state, kitchenOrders: [...state.kitchenOrders, action.payload] };
    
    case 'UPDATE_KITCHEN_ORDER':
      return {
        ...state,
        kitchenOrders: state.kitchenOrders.map(o => o.id === action.payload.id ? action.payload : o)
      };
    
    case 'SET_KITCHEN_METRICS':
      return { ...state, kitchenMetrics: action.payload };
    
    case 'UPDATE_KITCHEN_SETTINGS':
      return {
        ...state,
        kitchenSettings: { ...state.kitchenSettings, ...action.payload }
      };
    
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload };
    
    case 'ADD_CATEGORY':
      return { ...state, categories: [...state.categories, action.payload] };
    
    case 'UPDATE_SYSTEM_CONFIG':
      return {
        ...state,
        systemConfig: { ...state.systemConfig, ...action.payload }
      };
    
    // Order management actions
    case 'ADD_PRODUCT_TO_ORDER':
      const existingItem = state.currentOrder?.items.find(item => item.productId === action.payload.id);
      
      if (existingItem) {
        return {
          ...state,
          currentOrder: state.currentOrder ? {
            ...state.currentOrder,
            items: state.currentOrder.items.map(item =>
              item.productId === action.payload.id
                ? { ...item, quantity: item.quantity + 1 }
                : item
            )
          } : null
        };
      } else {
        const newItem = {
          id: `${action.payload.id}-${Date.now()}`,
          productId: action.payload.id,
          name: action.payload.name,
          price: action.payload.price,
          quantity: 1,
          notes: '',
          modifiers: []
        };
        
        return {
          ...state,
          currentOrder: state.currentOrder ? {
            ...state.currentOrder,
            items: [...state.currentOrder.items, newItem]
          } : {
            id: `order-${Date.now()}`,
            items: [newItem],
            timestamp: Date.now(),
            status: 'open' as const,
            total: 0,
            subtotal: 0,
            tax: 0,
            tenant_id: state.currentTenant?.id,
            location_id: state.currentLocation?.id
          }
        };
      }
    
    case 'REMOVE_ITEM_FROM_ORDER':
      return {
        ...state,
        currentOrder: state.currentOrder ? {
          ...state.currentOrder,
          items: state.currentOrder.items.filter(item => item.id !== action.payload)
        } : null
      };
    
    case 'UPDATE_ITEM_QUANTITY':
      return {
        ...state,
        currentOrder: state.currentOrder ? {
          ...state.currentOrder,
          items: state.currentOrder.items.map(item =>
            item.id === action.payload.id
              ? { ...item, quantity: action.payload.quantity }
              : item
          )
        } : null
      };
    
    case 'CLEAR_CURRENT_ORDER':
      return { ...state, currentOrder: null };
    
    case 'SET_CURRENT_ORDER':
      return { ...state, currentOrder: action.payload };
    
    case 'SET_TAB_NAME':
      return {
        ...state,
        currentOrder: state.currentOrder ? {
          ...state.currentOrder,
          tabName: action.payload
        } : null
      };

    case 'SELECT_TABLE':
      return {
        ...state,
        currentOrder: state.currentOrder ? {
          ...state.currentOrder,
          table_id: action.payload.tableId,
          tabName: action.payload.tableNumber
        } : {
          id: '',
          items: [],
          subtotal: 0,
          tax: 0,
          total: 0,
          table_id: action.payload.tableId,
          tabName: action.payload.tableNumber,
          status: 'open',
          timestamp: Date.now(),
          created_at: new Date().toISOString()
        }
      };

    case 'CLEAR_TABLE_SELECTION':
      return {
        ...state,
        currentOrder: state.currentOrder ? {
          ...state.currentOrder,
          table_id: undefined,
          tabName: undefined
        } : null
      };

    case 'SET_FLOOR_LAYOUT':
      return {
        ...state,
        floorLayout: action.payload
      };

    case 'UPDATE_TABLE_STATUS':
      return {
        ...state,
        floorLayout: state.floorLayout ? {
          ...state.floorLayout,
          tables: state.floorLayout.tables?.map(table =>
            table.id === action.payload.tableId
              ? { ...table, status: action.payload.status, substatus: action.payload.substatus }
              : table
          )
        } : state.floorLayout
      };

    case 'ADD_ORDER_TO_HISTORY':
      return {
        ...state,
        orders: [...state.orders, action.payload]
      };

    default:
      return state;
  }
};

// Enhanced context type with multi-tenancy
interface EnhancedAppContextType extends AppContextType {
  // Authentication
  login: (pin: string, tenantSlug?: string) => Promise<boolean>;
  logout: () => void;

  // Tenant management
  getCurrentTenant: () => Promise<void>;
  updateTenantSettings: (settings: any) => Promise<void>;

  // Location management
  fetchLocations: () => Promise<void>;
  addLocation: (location: any) => Promise<void>;

  // Real-time updates
  connectSocket: () => void;
  disconnectSocket: () => void;

  // Enhanced analytics
  fetchAdvancedAnalytics: (filters: any) => Promise<any>;

  // Multi-tenant API calls
  apiCall: (endpoint: string, options?: RequestInit) => Promise<Response>;

  // New health status fetch function
  fetchHealthStatus: () => Promise<{ status: string } | null>;

  // Product management
  addProduct: (product: Product) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  fetchProducts: () => Promise<Product[]>;
  fetchCategories: () => Promise<string[]>;
  addCategory: (category: string) => Promise<void>;
  importProducts: (file: File) => Promise<{ success: any[]; errors: any[] }>;
  exportProducts: () => Promise<void>;
  downloadTemplate: () => Promise<void>;

  // Employee management
  fetchEmployees: () => Promise<Employee[]>;
  addEmployee: (employee: Employee) => Promise<void>;
  updateEmployee: (employee: Employee) => Promise<void>;
  deleteEmployee: (employeeId: string) => Promise<void>;
  validateEmployeePin: (pin: string) => Promise<Employee | null>;

  // Settings
  fetchSettings: () => Promise<any>;
  updateSettings: (settings: any) => Promise<void>;

  // Kitchen management
  fetchKitchenOrders: () => Promise<KitchenOrder[]>;
  sendOrderToKitchen: (orderId: string) => Promise<void>;
  updateKitchenOrderStatus: (orderId: string, status: KitchenOrder['status']) => Promise<void>;
  fetchKitchenMetrics: () => Promise<KitchenMetrics | null>;
  updateKitchenSettings: (settings: any) => Promise<void>;
  fetchKitchenStaff: () => Promise<Employee[]>;
  assignOrderToStaff: (orderId: string, staffId: string) => Promise<void>;
  updateKitchenOrder: (orderId: string, updates: any) => Promise<void>;
  bulkUpdateKitchenOrders: (orderIds: string[], status: any) => Promise<void>;

  // Floor layout
  fetchFloorLayout: () => Promise<FloorLayout | null>;
  updateTableStatus: (tableId: string, status: any, orderId?: string) => Promise<void>;

  // Scheduling
  fetchSchedules: () => Promise<any[]>;
  addSchedule: (schedule: any) => Promise<void>;
  updateSchedule: (schedule: any) => Promise<void>;

  // Loyalty
  fetchCustomers: () => Promise<any[]>;
  addCustomer: (customer: any) => Promise<void>;
  updateCustomer: (customer: any) => Promise<void>;
  fetchLoyaltyRewards: () => Promise<any[]>;
}

const EnhancedAppContext = createContext<EnhancedAppContextType | undefined>(undefined);

export const EnhancedAppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Calculate order totals whenever current order changes
  useEffect(() => {
    if (state.currentOrder && state.currentOrder.items.length > 0) {
      const subtotal = state.currentOrder.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const tax = subtotal * (state.systemConfig.tax_rate || 0.08);
      const total = subtotal + tax;

      // Update order totals if they've changed
      if (state.currentOrder.subtotal !== subtotal || state.currentOrder.tax !== tax || state.currentOrder.total !== total) {
        dispatch({
          type: 'SET_CURRENT_ORDER',
          payload: {
            ...state.currentOrder,
            subtotal,
            tax,
            total
          }
        });
      }
    }
  }, [state.currentOrder?.items, state.systemConfig.tax_rate]);

  // API base URL
  const API_BASE = 'http://localhost:4000';

  // Mock API data for development
  const mockApiData = {
    products: [
      { id: '1', name: 'Coffee', price: 3.50, category: 'Beverages', image: null },
      { id: '2', name: 'Espresso', price: 2.75, category: 'Beverages', image: null },
      { id: '3', name: 'Sandwich', price: 8.99, category: 'Food', image: null },
      { id: '4', name: 'Salad', price: 7.50, category: 'Food', image: null },
      { id: '5', name: 'Burger', price: 12.99, category: 'Food', image: null },
      { id: '6', name: 'Tea', price: 2.50, category: 'Beverages', image: null }
    ],
    categories: [
      { id: '1', name: 'Beverages' },
      { id: '2', name: 'Food' },
      { id: '3', name: 'Desserts' }
    ]
  };

  // Enhanced API call with authentication and mock fallback
  const apiCall = async (endpoint: string, options: RequestInit = {}): Promise<Response> => {
    try {
      const headers = new Headers({
        'Content-Type': 'application/json',
        ...options.headers
      });

      if (state.authToken) {
        headers.set('Authorization', `Bearer ${state.authToken}`);
      }

      console.log(`🌐 Making API call: ${options.method || 'GET'} ${API_BASE}${endpoint}`);

      const response = await fetch(`${API_BASE}${endpoint}`, {
        ...options,
        headers
      });

      console.log(`📡 API response: ${response.status} ${response.statusText} for ${endpoint}`);

      if (response.status === 401) {
        // Token expired, logout
        dispatch({ type: 'LOGOUT' });
        throw new Error('Authentication expired');
      }

      // If we get here, the backend is responding (even if with an error)
      return response;
    } catch (error) {
      console.log(`🔧 API call failed (${error instanceof Error ? error.message : 'Unknown error'}), using mock data for:`, endpoint, options.method || 'GET');

      // Return mock data for common endpoints
      if (endpoint === '/api/products' || endpoint === '/products') {
        return new Response(JSON.stringify(mockApiData.products), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/categories' || endpoint === '/categories') {
        return new Response(JSON.stringify(mockApiData.categories), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/tenant/categories' && options.method === 'POST') {
        // Mock category creation
        const categoryData = JSON.parse(options.body as string);

        // Validate required fields
        if (!categoryData.name || categoryData.name.trim() === '') {
          console.error('🔧 Mock validation failed: category name is required');
          return new Response(JSON.stringify({
            error: 'Category name is required',
            field: 'name'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const newCategory = {
          id: `cat_${Date.now()}`,
          name: categoryData.name.trim(),
          description: categoryData.description || '',
          color: categoryData.color || '#3B82F6',
          icon: categoryData.icon || 'package',
          is_active: categoryData.is_active !== undefined ? categoryData.is_active : true,
          product_count: 0,
          sort_order: mockApiData.categories.length + 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Add to mock data
        mockApiData.categories.push(newCategory);

        console.log('✅ Mock category created:', newCategory);
        return new Response(JSON.stringify(newCategory), {
          status: 201,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint.startsWith('/api/tenant/categories/') && options.method === 'PUT') {
        // Mock category update
        const categoryId = endpoint.split('/').pop();
        const categoryData = JSON.parse(options.body as string);

        const categoryIndex = mockApiData.categories.findIndex(c => c.id === categoryId);
        if (categoryIndex === -1) {
          return new Response(JSON.stringify({ error: 'Category not found' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const updatedCategory = {
          ...mockApiData.categories[categoryIndex],
          ...categoryData,
          updated_at: new Date().toISOString()
        };

        mockApiData.categories[categoryIndex] = updatedCategory;

        console.log('✅ Mock category updated:', updatedCategory);
        return new Response(JSON.stringify(updatedCategory), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint.startsWith('/api/tenant/categories/') && options.method === 'DELETE') {
        // Mock category deletion
        const categoryId = endpoint.split('/').pop();

        const categoryIndex = mockApiData.categories.findIndex(c => c.id === categoryId);
        if (categoryIndex === -1) {
          return new Response(JSON.stringify({ error: 'Category not found' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        mockApiData.categories.splice(categoryIndex, 1);

        console.log('✅ Mock category deleted:', categoryId);
        return new Response(JSON.stringify({ success: true, message: 'Category deleted successfully' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/tenant/categories') {
        // Mock category list
        return new Response(JSON.stringify(mockApiData.categories), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/tenant/products' && options.method === 'POST') {
        // Mock product creation
        const productData = JSON.parse(options.body as string);
        const newProduct = {
          id: `prod_${Date.now()}`,
          ...productData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        console.log('🔧 Mock product created:', newProduct);
        return new Response(JSON.stringify(newProduct), {
          status: 201,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/tenant/products') {
        // Mock product list
        return new Response(JSON.stringify(mockApiData.products), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if ((endpoint === '/api/orders' || endpoint === '/orders') && options.method === 'POST') {
        const order = {
          id: 'mock-order-' + Date.now(),
          ...JSON.parse(options.body as string),
          timestamp: new Date().toISOString(),
          status: 'completed'
        };
        return new Response(JSON.stringify(order), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/orders' || endpoint === '/orders') {
        // Mock order history
        const mockOrders = [
          {
            id: 'order_1',
            items: [{ name: 'Coffee', quantity: 2, price: 3.50 }],
            total: 7.00,
            timestamp: new Date().toISOString(),
            status: 'completed'
          }
        ];
        return new Response(JSON.stringify(mockOrders), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/inventory') {
        // Mock inventory data
        const mockInventory = mockApiData.products.map(p => ({
          id: p.id,
          product_name: p.name,
          current_stock: Math.floor(Math.random() * 100) + 10,
          minimum_stock: 5,
          reorder_point: 15
        }));
        return new Response(JSON.stringify(mockInventory), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/employees') {
        // Mock employees data
        const mockEmployees = [
          { id: '1', name: 'Super Admin', role: 'super_admin', status: 'active' },
          { id: '2', name: 'Manager', role: 'manager', status: 'active' },
          { id: '3', name: 'Employee', role: 'employee', status: 'active' }
        ];
        return new Response(JSON.stringify(mockEmployees), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint.includes('/api/analytics/')) {
        // Mock analytics data
        const mockAnalytics = {
          total_sales: 2450.75,
          total_orders: 45,
          average_order_value: 54.46,
          top_products: [
            { name: 'Coffee', sales: 850.00, quantity: 170 },
            { name: 'Burger', sales: 649.50, quantity: 50 }
          ]
        };
        return new Response(JSON.stringify(mockAnalytics), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/kitchen/orders') {
        // Mock kitchen orders
        const mockKitchenOrders = [
          {
            id: 'ko_1',
            table_number: 5,
            items: [{ name: 'Burger', quantity: 2 }],
            status: 'preparing'
          }
        ];
        return new Response(JSON.stringify(mockKitchenOrders), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/floor/layout') {
        // Mock floor layout
        const mockLayout = {
          tables: [
            { id: 1, x: 100, y: 100, seats: 4, status: 'available' },
            { id: 2, x: 200, y: 100, seats: 2, status: 'occupied' }
          ]
        };
        return new Response(JSON.stringify(mockLayout), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/customers') {
        // Mock customers
        const mockCustomers = [
          { id: 'cust_1', name: 'John Doe', points: 250, tier: 'Gold' },
          { id: 'cust_2', name: 'Jane Smith', points: 150, tier: 'Silver' }
        ];
        return new Response(JSON.stringify(mockCustomers), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/menu/online') {
        // Mock online menu
        const mockMenu = {
          restaurant_info: { name: 'Demo Restaurant' },
          categories: mockApiData.categories,
          products: mockApiData.products
        };
        return new Response(JSON.stringify(mockMenu), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } else if (endpoint === '/api/qr/codes') {
        // Mock QR codes
        const mockQRCodes = [
          { id: 'qr_1', type: 'table_menu', table_id: 1, url: 'https://demo.com/table1' }
        ];
        return new Response(JSON.stringify(mockQRCodes), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Default mock response for unhandled endpoints
      console.warn(`🔧 Mock API: Unhandled endpoint ${options.method || 'GET'} ${endpoint}`);
      return new Response(JSON.stringify({
        error: 'Mock mode - endpoint not implemented',
        endpoint: endpoint,
        method: options.method || 'GET',
        message: 'This endpoint is not yet implemented in mock mode. Check the console for details.'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  };

  // New function to fetch health status from backend
  const fetchHealthStatus = async (): Promise<{ status: string } | null> => {
    try {
      const response = await apiCall('/api/health');
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching health status:', error);
      return null;
    }
  };

  // Mock data for development when backend is not available
  const mockLogin = (pin: string, _tenantSlug?: string): boolean => {
    console.log('🔧 Using mock login mode');

    // Mock credentials
    const mockCredentials = {
      '123456': { name: 'Super Admin', role: 'super_admin' },
      '567890': { name: 'Manager', role: 'manager' },
      '1234': { name: 'Employee', role: 'employee' }
    };

    const employee = mockCredentials[pin as keyof typeof mockCredentials];
    if (!employee) {
      console.log('❌ Mock login failed: Invalid PIN');
      return false;
    }

    const mockData = {
      employee: {
        id: '1',
        name: employee.name,
        role: employee.role as Employee['role'],
        permissions: (employee.role === 'super_admin' ? ['all'] : ['pos']) as unknown as Employee['permissions']
      },
      tenant: {
        id: '1',
        name: 'Demo Restaurant',
        slug: 'demo-restaurant',
        business_name: 'Demo Restaurant & Bar',
        email: '',
        subscription: 'active',
        settings: {
          business_name: 'Demo Restaurant',
          business_type: '',
          timezone: '',
          currency: '',
          locale: '',
          tax_rate: 0,
          features: {
            multi_location: false,
            kitchen_display: false,
            loyalty_program: false,
            inventory_management: false,
            sales_dashboard: false,
            staff_scheduling: false,
            online_menu: false,
            qr_codes: false,
            advanced_reporting: false,
            third_party_integrations: false,
            custom_branding: false
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as Tenant['settings'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'active' as 'active' // Added missing required property
      },
      location: {
        id: '1',
        tenant_id: '1',
        name: 'Main Location',
        address: '',
        timezone: '',
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        isActive: true
      },
      token: 'mock-jwt-token-' + Date.now()
    };

    dispatch({
      type: 'LOGIN',
      payload: mockData
    });

    // Store mock authentication data
    localStorage.setItem('authToken', mockData.token);
    localStorage.setItem('tenantSlug', mockData.tenant.slug);
    localStorage.setItem('currentEmployee', JSON.stringify(mockData.employee));
    localStorage.setItem('currentTenant', JSON.stringify(mockData.tenant));
    localStorage.setItem('currentLocation', JSON.stringify(mockData.location));

    console.log('✅ Mock login successful:', mockData);
    return true;
  };

  // Authentication functions
  const login = async (pin: string, tenantSlug?: string): Promise<boolean> => {
    try {
      console.log('🔐 Enhanced Login attempt:', { pin: pin.length + ' digits', tenantSlug, apiBase: API_BASE });

      const requestBody = { pin, tenant_slug: tenantSlug };
      console.log('📤 Request body:', requestBody);

      const response = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Login successful:', data);

        // Handle both old and new response formats
        const employee = data.employee || data.user;
        const tenant = data.tenant;
        const location = data.location || { id: 1, name: 'Main Location' };
        const token = data.token;

        dispatch({
          type: 'LOGIN',
          payload: {
            employee: employee,
            tenant: tenant,
            location: location,
            token: token
          }
        });

        // Store authentication data in localStorage
        localStorage.setItem('authToken', token);
        localStorage.setItem('tenantSlug', tenant.slug);
        localStorage.setItem('currentEmployee', JSON.stringify(employee));
        localStorage.setItem('currentTenant', JSON.stringify(tenant));
        localStorage.setItem('currentLocation', JSON.stringify(location));

        // Connect to socket
        connectSocket();

        return true;
      } else {
        const errorData = await response.text();
        console.log('❌ Login failed:', response.status, errorData);
        return false;
      }
    } catch (error) {
      console.error('💥 Login error:', error);
      console.log('🔧 Backend not available, trying mock login...');

      // Fallback to mock login when backend is not available
      return mockLogin(pin, tenantSlug);
    }
  };

  const logout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('tenantSlug');
    dispatch({ type: 'LOGOUT' });
  };

  // Enhanced socket connection with error handling and reconnection
  const connectSocket = () => {
    if (state.socket) {
      console.log('🔌 Socket already connected');
      return;
    }

    console.log('🔌 Connecting to socket server...');

    const socket: Socket = io(API_BASE, {
      auth: {
        token: state.authToken
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socket.on('connect', () => {
      console.log('✅ Socket connected successfully');
      if (state.currentTenant) {
        socket.emit('join-tenant', state.currentTenant.id);
        console.log('🏢 Joined tenant room:', state.currentTenant.id);
      }
      if (state.currentTenant && state.currentLocation) {
        socket.emit('join-kitchen', state.currentTenant.id, state.currentLocation.id);
        console.log('🍳 Joined kitchen room:', state.currentTenant.id, state.currentLocation.id);
      }
    });

    socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        socket.connect();
      }
    });

    socket.on('reconnect', (attemptNumber) => {
      console.log('🔄 Socket reconnected after', attemptNumber, 'attempts');
    });

    socket.on('reconnect_error', (error) => {
      console.error('❌ Socket reconnection error:', error);
    });

    // Real-time event handlers with error handling
    socket.on('kitchen-order-updated', (order: KitchenOrder) => {
      try {
        console.log('🍳 Kitchen order updated:', order.id);
        dispatch({ type: 'UPDATE_KITCHEN_ORDER', payload: order });
      } catch (error) {
        console.error('Error handling kitchen order update:', error);
      }
    });

    socket.on('order-created', (order: Order) => {
      try {
        console.log('📋 New order created:', order.id);
        dispatch({ type: 'SET_ORDERS', payload: [...state.orders, order] });
      } catch (error) {
        console.error('Error handling order creation:', error);
      }
    });

    socket.on('product-added', (product: Product) => {
      try {
        console.log('📦 Product added:', product.name);
        dispatch({ type: 'ADD_PRODUCT', payload: product });
      } catch (error) {
        console.error('Error handling product addition:', error);
      }
    });

    socket.on('employee-added', (employee: Employee) => {
      try {
        console.log('👤 Employee added:', employee.name);
        dispatch({ type: 'ADD_EMPLOYEE', payload: employee });
      } catch (error) {
        console.error('Error handling employee addition:', error);
      }
    });

    // Table status updates
    socket.on('table-status-updated', (tableUpdate: any) => {
      try {
        console.log('🏢 Table status updated:', tableUpdate);
        // Handle table status updates if needed
      } catch (error) {
        console.error('Error handling table status update:', error);
      }
    });

    dispatch({ type: 'SET_SOCKET', payload: socket });
  };

  const disconnectSocket = () => {
    if (state.socket) {
      state.socket.disconnect();
      dispatch({ type: 'SET_SOCKET', payload: null });
    }
  };

  // Tenant management
  const getCurrentTenant = async () => {
    try {
      const response = await apiCall('/tenants/current');
      if (response.ok) {
        const tenant = await response.json();
        dispatch({ type: 'SET_TENANT_SETTINGS', payload: tenant });
      }
    } catch (error) {
      console.error('Error fetching tenant:', error);
    }
  };

  const updateTenantSettings = async (settings: any) => {
    try {
      const response = await apiCall('/tenants/settings', {
        method: 'PUT',
        body: JSON.stringify(settings)
      });
      
      if (response.ok) {
        const updatedSettings = await response.json();
        dispatch({ type: 'SET_TENANT_SETTINGS', payload: updatedSettings });
      }
    } catch (error) {
      console.error('Error updating tenant settings:', error);
    }
  };

  // Location management
  const fetchLocations = async () => {
    try {
      const response = await apiCall('/locations');
      if (response.ok) {
        const locations = await response.json();
        dispatch({ type: 'SET_LOCATIONS', payload: locations });
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  const addLocation = async (location: any) => {
    try {
      const response = await apiCall('/locations', {
        method: 'POST',
        body: JSON.stringify(location)
      });
      
      if (response.ok) {
        const newLocation = await response.json();
        dispatch({ type: 'SET_LOCATIONS', payload: [...state.locations, newLocation] });
      }
    } catch (error) {
      console.error('Error adding location:', error);
    }
  };

  // Enhanced product management
  const fetchProducts = async (): Promise<Product[]> => {
    try {
      const response = await apiCall('/products');
      if (response.ok) {
        const products = await response.json();
        dispatch({ type: 'SET_PRODUCTS', payload: products });
        return products;
      }
      return [];
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  };

  const addProduct = async (product: Product): Promise<void> => {
    try {
      const response = await apiCall('/products', {
        method: 'POST',
        body: JSON.stringify(product)
      });
      
      if (response.ok) {
        const newProduct = await response.json();
        dispatch({ type: 'ADD_PRODUCT', payload: newProduct });
      }
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  };

  // Enhanced kitchen management
  const fetchKitchenOrders = async (): Promise<KitchenOrder[]> => {
    try {
      const response = await apiCall('/kitchen/orders');
      if (response.ok) {
        const orders = await response.json();
        dispatch({ type: 'SET_KITCHEN_ORDERS', payload: orders });
        return orders;
      }
      return [];
    } catch (error) {
      console.error('Error fetching kitchen orders:', error);
      return [];
    }
  };

  const fetchKitchenMetrics = async (): Promise<any> => {
    try {
      const response = await apiCall('/kitchen/metrics');
      if (response.ok) {
        const metrics = await response.json();
        dispatch({ type: 'SET_KITCHEN_METRICS', payload: metrics });
        return metrics;
      }
      return null;
    } catch (error) {
      console.error('Error fetching kitchen metrics:', error);
      return null;
    }
  };

  const updateKitchenOrderStatus = async (orderId: string, status: KitchenOrder['status']): Promise<void> => {
    try {
      const response = await apiCall(`/kitchen/orders/${orderId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status })
      });
      
      if (response.ok) {
        const updatedOrder = await response.json();
        dispatch({ type: 'UPDATE_KITCHEN_ORDER', payload: updatedOrder });
      }
    } catch (error) {
      console.error('Error updating kitchen order status:', error);
      throw error;
    }
  };

  // Enhanced analytics
  const fetchAdvancedAnalytics = async (filters: any): Promise<any> => {
    try {
      const queryParams = new URLSearchParams(filters).toString();
      const response = await apiCall(`/analytics/dashboard?${queryParams}`);
      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch (error) {
      console.error('Error fetching advanced analytics:', error);
      return null;
    }
  };

  // Auto-login on app start
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    
    if (token) {
      dispatch({ type: 'SET_AUTH_TOKEN', payload: token });
      // Validate token by fetching current tenant
      getCurrentTenant();
    }
  }, []);

  // Placeholder implementations for compatibility
  const updateProduct = async (_product: Product): Promise<void> => {
    // Implementation here
  };

  const deleteProduct = async (_productId: string): Promise<void> => {
    // Implementation here
  };

  const fetchCategories = async (): Promise<string[]> => {
    return [];
  };

  const addCategory = async (_category: string): Promise<void> => {
    // Implementation here
  };

  const fetchEmployees = async (): Promise<Employee[]> => {
    return [];
  };

  const addEmployee = async (_employee: Employee): Promise<void> => {
    // Implementation here
  };

  const updateEmployee = async (_employee: Employee): Promise<void> => {
    // Implementation here
  };

  const deleteEmployee = async (_employeeId: string): Promise<void> => {
    // Implementation here
  };

  const fetchSettings = async (): Promise<void> => {
    // Implementation here
  };

  const updateSettings = async (_settings: any): Promise<void> => {
    // Implementation here
  };

  const validateEmployeePin = async (_pin: string): Promise<Employee | null> => {
    return null;
  };

  const importProducts = async (_file: File): Promise<{ success: any[]; errors: any[] }> => {
    return { success: [], errors: [] };
  };

  const exportProducts = async (): Promise<void> => {
    // Implementation here
  };

  const downloadTemplate = async (): Promise<void> => {
    // Implementation here
  };

  const contextValue: EnhancedAppContextType = {
    state,
    dispatch,
    login,
    logout,
    getCurrentTenant,
    updateTenantSettings,
    fetchLocations,
    addLocation,
    connectSocket,
    disconnectSocket,
    fetchAdvancedAnalytics,
    apiCall,
    fetchHealthStatus,
    
    // Product management
    addProduct,
    updateProduct,
    deleteProduct,
    fetchProducts,
    fetchCategories,
    addCategory,
    importProducts,
    exportProducts,
    downloadTemplate,
    
    // Employee management
    fetchEmployees,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    validateEmployeePin,
    
    // Settings
    fetchSettings,
    updateSettings,
    
    // Kitchen management
    fetchKitchenOrders,
    sendOrderToKitchen: async (_orderId: string) => {},
    updateKitchenOrderStatus,
    fetchKitchenMetrics,
    updateKitchenSettings: async (_settings: any) => {},
    fetchKitchenStaff: async () => [],
    assignOrderToStaff: async (_orderId: string, _staffId: string) => {},
    updateKitchenOrder: async (_orderId: string, _updates: any) => {},
    bulkUpdateKitchenOrders: async (_orderIds: string[], _status: any) => {},
    
    // Floor layout (placeholder)
    fetchFloorLayout: async () => null,
    updateTableStatus: async (_tableId: string, _status: any, _orderId?: string) => {},

    // Scheduling (placeholder)
    fetchSchedules: async () => [],
    addSchedule: async (_schedule: any) => {},
    updateSchedule: async (_schedule: any) => {},

    // Loyalty (placeholder)
    fetchCustomers: async () => [],
    addCustomer: async (_customer: any) => {},
    updateCustomer: async (_customer: any) => {},
    fetchLoyaltyRewards: async () => []
  };

  return (
    <EnhancedAppContext.Provider value={contextValue}>
      {children}
    </EnhancedAppContext.Provider>
  );
};

export const useEnhancedAppContext = () => {
  const context = useContext(EnhancedAppContext);
  if (context === undefined) {
    throw new Error('useEnhancedAppContext must be used within an EnhancedAppProvider');
  }
  return context;
};

export default EnhancedAppContext;
