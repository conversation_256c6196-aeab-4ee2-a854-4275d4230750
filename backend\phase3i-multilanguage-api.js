const express = require('express');
const router = express.Router();

// Phase 3I: Multi-Language Localization System API

// Get translations for specific language
router.get('/translations/:language', async (req, res) => {
  try {
    const { language } = req.params;
    console.log(`🌍 Loading translations for language: ${language}`);

    // Mock translation data (in production, load from database)
    const translations = {
      en: {
        'common.welcome': 'Welcome',
        'common.login': 'Login',
        'common.logout': 'Logout',
        'pos.order': 'Order',
        'pos.payment': 'Payment',
        'pos.total': 'Total',
        'menu.appetizers': 'Appetizers',
        'menu.entrees': 'Main Courses',
        'menu.desserts': 'Desserts',
        'menu.beverages': 'Beverages'
      },
      es: {
        'common.welcome': 'Bienvenido',
        'common.login': 'Iniciar Sesión',
        'common.logout': 'Cerrar Sesión',
        'pos.order': 'Pedido',
        'pos.payment': 'Pago',
        'pos.total': 'Total',
        'menu.appetizers': 'Aperitivos',
        'menu.entrees': 'Platos Principales',
        'menu.desserts': 'Postres',
        'menu.beverages': 'Bebidas'
      },
      fr: {
        'common.welcome': 'Bienvenue',
        'common.login': 'Connexion',
        'common.logout': 'Déconnexion',
        'pos.order': 'Commande',
        'pos.payment': 'Paiement',
        'pos.total': 'Total',
        'menu.appetizers': 'Entrées',
        'menu.entrees': 'Plats Principaux',
        'menu.desserts': 'Desserts',
        'menu.beverages': 'Boissons'
      }
    };

    const languageTranslations = translations[language] || translations['en'];
    const totalKeys = Object.keys(languageTranslations).length;
    const coverage = language === 'en' ? 100 : Math.floor(Math.random() * 20) + 80;

    res.json({
      success: true,
      language,
      translations: languageTranslations,
      totalKeys,
      coverage: `${coverage}%`,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Translation loading error:', error);
    res.status(500).json({ error: 'Failed to load translations' });
  }
});

// Get cultural settings for specific language
router.get('/cultural/:language', async (req, res) => {
  try {
    const { language } = req.params;
    console.log(`🎭 Loading cultural settings for language: ${language}`);

    // Mock cultural data
    const culturalSettings = {
      en: {
        isRTL: false,
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        currency: 'USD',
        numberFormat: '1,234.56',
        greeting: 'Hello',
        culturalNorms: {
          tipping: true,
          bargaining: false,
          formalAddress: false
        }
      },
      ar: {
        isRTL: true,
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        currency: 'AED',
        numberFormat: '1.234,56',
        greeting: 'مرحبا',
        culturalNorms: {
          tipping: true,
          bargaining: true,
          formalAddress: true
        }
      },
      ja: {
        isRTL: false,
        dateFormat: 'YYYY/MM/DD',
        timeFormat: '24h',
        currency: 'JPY',
        numberFormat: '1,234',
        greeting: 'こんにちは',
        culturalNorms: {
          tipping: false,
          bargaining: false,
          formalAddress: true
        }
      }
    };

    const cultural = culturalSettings[language] || culturalSettings['en'];

    res.json({
      success: true,
      language,
      cultural,
      isRTL: cultural.isRTL,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Cultural settings error:', error);
    res.status(500).json({ error: 'Failed to load cultural settings' });
  }
});

// AI Translation service
router.post('/translate', async (req, res) => {
  try {
    const { text, to, context = 'general' } = req.body;
    console.log(`🤖 AI Translation: "${text}" to ${to} (context: ${context})`);

    // Mock AI translation (in production, use Google Translate API, Azure Translator, etc.)
    const translations = {
      es: {
        'Hello': 'Hola',
        'Welcome': 'Bienvenido',
        'Order': 'Pedido',
        'Payment': 'Pago',
        'Total': 'Total'
      },
      fr: {
        'Hello': 'Bonjour',
        'Welcome': 'Bienvenue',
        'Order': 'Commande',
        'Payment': 'Paiement',
        'Total': 'Total'
      }
    };

    const translatedText = translations[to]?.[text] || `[${to}] ${text}`;
    const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence

    res.json({
      success: true,
      originalText: text,
      translatedText,
      sourceLanguage: 'en',
      targetLanguage: to,
      confidence: Math.round(confidence * 100),
      context,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 AI Translation error:', error);
    res.status(500).json({ error: 'Translation failed' });
  }
});

// Get voice commands for specific language
router.get('/voice/:language', async (req, res) => {
  try {
    const { language } = req.params;
    console.log(`🎤 Loading voice commands for language: ${language}`);

    // Mock voice commands
    const voiceCommands = {
      en: {
        commands: [
          { phrase: 'add to order', action: 'ADD_ITEM', confidence: 0.95 },
          { phrase: 'remove item', action: 'REMOVE_ITEM', confidence: 0.92 },
          { phrase: 'process payment', action: 'PROCESS_PAYMENT', confidence: 0.98 },
          { phrase: 'show menu', action: 'SHOW_MENU', confidence: 0.90 },
          { phrase: 'table status', action: 'TABLE_STATUS', confidence: 0.88 }
        ],
        wakeWords: ['hey pos', 'restaurant assistant'],
        language: 'en-US'
      },
      es: {
        commands: [
          { phrase: 'agregar al pedido', action: 'ADD_ITEM', confidence: 0.93 },
          { phrase: 'quitar artículo', action: 'REMOVE_ITEM', confidence: 0.90 },
          { phrase: 'procesar pago', action: 'PROCESS_PAYMENT', confidence: 0.96 },
          { phrase: 'mostrar menú', action: 'SHOW_MENU', confidence: 0.88 },
          { phrase: 'estado de mesa', action: 'TABLE_STATUS', confidence: 0.85 }
        ],
        wakeWords: ['oye pos', 'asistente restaurante'],
        language: 'es-ES'
      }
    };

    const voice = voiceCommands[language] || voiceCommands['en'];

    res.json({
      success: true,
      language,
      voice,
      totalCommands: voice.commands.length,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Voice commands error:', error);
    res.status(500).json({ error: 'Failed to load voice commands' });
  }
});

module.exports = router;
