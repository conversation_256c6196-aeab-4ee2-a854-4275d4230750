# Enterprise POS Backend Server

## 🚀 Server Overview

This backend provides a comprehensive REST API for the Enterprise POS system with multi-tenant architecture, hardware integration, and advanced features.

## 📁 File Structure

```
backend/
├── working-server.js          # 🎯 MAIN SERVER - All features included
├── middleware/
│   └── auth.js               # Authentication middleware
├── migrations/               # Database migration files (future use)
├── __tests__/               # Test files
├── logs/                    # Server logs
├── package.json             # Dependencies and scripts
└── README.md               # This file
```

## 🎯 Main Server: `working-server.js`

**This is the ONLY server file you need!** It contains:

### ✅ Complete Feature Set
- **Phase 1**: MVP POS functionality
- **Phase 2**: KDS, loyalty, advanced features
- **Phase 3**: Multi-location, centralized management
- **Phase 4**: Payment terminals, digital wallets
- **Phase 5**: AI forecasting, automation
- **Hardware Integration**: Receipt printers, barcode scanners

### 🔧 Technical Features
- **Multi-tenant Architecture**: Secure tenant isolation
- **JWT Authentication**: Role-based access control
- **Socket.IO**: Real-time updates
- **Mock Data**: No database required for demo
- **CORS Support**: Frontend integration
- **Error Handling**: Comprehensive error management
- **Logging**: Request/response logging

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd backend
npm install
```

### 2. Start Server
```bash
# Development mode (auto-restart)
npm run dev

# Production mode
npm start
```

### 3. Server will run on:
- **Port**: 4000 (default)
- **Health Check**: http://localhost:4000/api/health

## 🔑 Test Credentials

| Role | PIN | Access Level |
|------|-----|-------------|
| **Super Admin** | `123456` | All 21 tabs, full system access |
| **Tenant Admin** | `567890` | 20 tabs, tenant management |
| **Manager** | `567890` | 11 tabs, operational management |
| **Employee** | `567890` | 3 tabs, basic POS functions |

## 📡 API Endpoints

### 🔐 Authentication
- `POST /api/auth/login` - User login with PIN

### 🏪 Core POS
- `GET /api/products` - Get products
- `GET /api/categories` - Get categories
- `POST /api/orders` - Create order
- `GET /api/inventory` - Get inventory

### 🍳 Kitchen & Operations
- `GET /api/kitchen/orders` - Kitchen display orders
- `POST /api/kitchen/orders/:id/update` - Update order status
- `GET /api/staff/schedules` - Staff schedules
- `POST /api/loyalty/points` - Loyalty points

### 🏢 Enterprise Features
- `GET /api/enterprise/locations` - Multi-location data
- `GET /api/enterprise/inventory` - Centralized inventory
- `GET /api/enterprise/analytics` - Advanced analytics

### 💳 Payment & Hardware
- `GET /api/payments/terminals` - Payment terminals
- `GET /api/payments/digital-wallets` - Digital wallets
- `GET /api/hardware/printers` - Receipt printers
- `GET /api/hardware/scanners` - Barcode scanners

### 🤖 AI Features
- `GET /api/ai/forecasting` - AI demand forecasting
- `POST /api/ai/recommendations` - AI recommendations

## 🔧 Configuration

### Environment Variables
```bash
PORT=4000                    # Server port
JWT_SECRET=your-secret-key   # JWT signing key
NODE_ENV=development         # Environment mode
```

### CORS Origins
The server accepts requests from:
- `http://localhost:5173` (Vite dev server)
- `http://localhost:5174` (Alternative port)
- `http://localhost:5175` (Alternative port)
- `http://localhost:5176` (Alternative port)
- `http://localhost:3000` (React dev server)

## 📊 Data Storage

**Current**: In-memory mock data (perfect for demo/development)
**Future**: PostgreSQL database with migrations

### Mock Data Includes:
- **Tenants**: Demo restaurant data
- **Employees**: Test users with different roles
- **Products**: Sample menu items
- **Categories**: Food, beverages, etc.
- **Orders**: Transaction history
- **Hardware**: Simulated devices

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 🔄 Real-time Features

The server uses Socket.IO for real-time updates:
- **Kitchen orders**: Live order status updates
- **Inventory**: Real-time stock changes
- **Analytics**: Live dashboard updates
- **Multi-location**: Cross-location synchronization

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based auth
- **Role-based Access**: Granular permissions
- **CORS Protection**: Controlled origin access
- **Input Validation**: Request data validation
- **Error Handling**: Secure error responses

## 📈 Performance

- **In-memory Data**: Fast response times
- **Efficient Routing**: Optimized endpoint structure
- **Connection Pooling**: Socket.IO optimization
- **Request Logging**: Performance monitoring

## 🔮 Future Enhancements

- **Database Integration**: PostgreSQL with migrations
- **Redis Caching**: Performance optimization
- **Rate Limiting**: API protection
- **Monitoring**: Health checks and metrics
- **Deployment**: Docker containerization

## 🆘 Troubleshooting

### Common Issues:

1. **Port Already in Use**
   ```bash
   # Kill process on port 4000
   npx kill-port 4000
   ```

2. **CORS Errors**
   - Check frontend URL in CORS origins
   - Verify request headers

3. **Authentication Errors**
   - Check JWT_SECRET configuration
   - Verify token format

4. **Module Not Found**
   ```bash
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📞 Support

For issues or questions:
1. Check server logs in console
2. Verify API endpoints with health check
3. Test with provided credentials
4. Review error messages for details

---

**🎉 The backend is now streamlined with ONE comprehensive server file containing ALL features!**
