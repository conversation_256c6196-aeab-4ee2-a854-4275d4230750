<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enable Restructured POS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #2563EB;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: 600;
        }
        .enabled {
            background: rgba(34, 197, 94, 0.2);
            border: 2px solid #22C55E;
        }
        .disabled {
            background: rgba(239, 68, 68, 0.2);
            border: 2px solid #EF4444;
        }
        .info {
            background: rgba(59, 130, 246, 0.2);
            border: 2px solid #3B82F6;
            margin-top: 20px;
            text-align: left;
        }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🚀 Restructured POS System</h1>
        <p>Enable or disable the restructured Industry Standard POS interface</p>
        
        <div id="status" class="status disabled">
            ❌ Restructured Mode: DISABLED
        </div>
        
        <div>
            <button onclick="enableRestructured()">✅ Enable Restructured Mode</button>
            <button onclick="disableRestructured()">❌ Disable Restructured Mode</button>
        </div>
        
        <div>
            <button onclick="openPOS()">🏪 Open POS System</button>
            <button onclick="openRestructuredPOS()">🚀 Open Restructured POS</button>
        </div>
        
        <div class="info">
            <h3>📋 Access Methods:</h3>
            <ul>
                <li><strong>URL Parameter:</strong> <code>?restructured=true</code></li>
                <li><strong>localStorage:</strong> <code>useRestructuredPOS = 'true'</code></li>
                <li><strong>Direct URL:</strong> <code>?industry=true&restructured=true</code></li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🔧 Debug Information:</h3>
            <div id="debugInfo">
                <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
                <p><strong>localStorage Flag:</strong> <span id="localStorageFlag"></span></p>
                <p><strong>URL Parameters:</strong> <span id="urlParams"></span></p>
            </div>
        </div>
    </div>

    <script>
        function updateStatus() {
            const isEnabled = localStorage.getItem('useRestructuredPOS') === 'true';
            const statusEl = document.getElementById('status');
            
            if (isEnabled) {
                statusEl.className = 'status enabled';
                statusEl.innerHTML = '✅ Restructured Mode: ENABLED';
            } else {
                statusEl.className = 'status disabled';
                statusEl.innerHTML = '❌ Restructured Mode: DISABLED';
            }
            
            updateDebugInfo();
        }
        
        function updateDebugInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('localStorageFlag').textContent = localStorage.getItem('useRestructuredPOS') || 'not set';
            document.getElementById('urlParams').textContent = window.location.search || 'none';
        }
        
        function enableRestructured() {
            localStorage.setItem('useRestructuredPOS', 'true');
            console.log('🚀 Restructured mode ENABLED');
            updateStatus();
            alert('✅ Restructured mode enabled! The POS system will now use the new interface.');
        }
        
        function disableRestructured() {
            localStorage.removeItem('useRestructuredPOS');
            console.log('❌ Restructured mode DISABLED');
            updateStatus();
            alert('❌ Restructured mode disabled! The POS system will use the original interface.');
        }
        
        function openPOS() {
            window.open('http://localhost:5173/?industry=true', '_blank');
        }

        function openRestructuredPOS() {
            // Enable restructured mode first
            localStorage.setItem('useRestructuredPOS', 'true');
            updateStatus();

            // Open with both flags
            window.open('http://localhost:5173/?industry=true&restructured=true', '_blank');
        }
        
        // Initialize on page load
        updateStatus();
        
        // Update debug info every 2 seconds
        setInterval(updateDebugInfo, 2000);
        
        // Log current state
        console.log('🔧 Debug Information:');
        console.log('localStorage.useRestructuredPOS:', localStorage.getItem('useRestructuredPOS'));
        console.log('URL search params:', window.location.search);
        console.log('Current URL:', window.location.href);
    </script>
</body>
</html>
