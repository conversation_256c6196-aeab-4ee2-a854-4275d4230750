import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ign,
  CreditCard,
  Wifi,
  WifiOff,
  <PERSON>tings,
  Plus,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  Monitor,
  Bluetooth,
  Usb,
  Network,
  Zap,
  TrendingUp
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface HardwareDevice {
  id: string;
  device_type: string;
  device_name: string;
  manufacturer?: string;
  model?: string;
  connection_type: string;
  is_active: boolean;
  is_connected: boolean;
  is_online: boolean;
  status: 'connected' | 'disconnected' | 'error' | 'inactive';
  last_heartbeat?: string;
  error_count: number;
  total_jobs_processed: number;
  uptime_percentage: number;
}

interface DeviceRegistration {
  device_type: string;
  device_name: string;
  manufacturer: string;
  model: string;
  connection_type: string;
  connection_string: string;
  ip_address?: string;
  port?: number;
}

const Phase4HardwareManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  
  const [devices, setDevices] = useState<HardwareDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [showRegistration, setShowRegistration] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<HardwareDevice | null>(null);
  const [deviceStats, setDeviceStats] = useState({
    total: 0,
    connected: 0,
    offline: 0,
    errors: 0
  });

  // Device registration form
  const [newDevice, setNewDevice] = useState<DeviceRegistration>({
    device_type: 'receipt_printer',
    device_name: '',
    manufacturer: '',
    model: '',
    connection_type: 'network',
    connection_string: '',
    ip_address: '',
    port: 9100
  });

  useEffect(() => {
    loadDevices();
    const interval = setInterval(loadDevices, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDevices = async () => {
    try {
      const response = await apiCall('/api/hardware/devices/enhanced');
      if (response.ok) {
        const data = await response.json();
        setDevices(data.devices || []);
        updateDeviceStats(data.devices || []);
      }
    } catch (error) {
      console.error('Failed to load devices:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateDeviceStats = (deviceList: HardwareDevice[]) => {
    const stats = {
      total: deviceList.length,
      connected: deviceList.filter(d => d.status === 'connected').length,
      offline: deviceList.filter(d => d.status === 'disconnected').length,
      errors: deviceList.filter(d => d.status === 'error').length
    };
    setDeviceStats(stats);
  };

  const registerDevice = async () => {
    try {
      const response = await apiCall('/api/hardware/devices/register', {
        method: 'POST',
        body: JSON.stringify(newDevice)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Device registered:', result);
        setShowRegistration(false);
        setNewDevice({
          device_type: 'receipt_printer',
          device_name: '',
          manufacturer: '',
          model: '',
          connection_type: 'network',
          connection_string: '',
          ip_address: '',
          port: 9100
        });
        loadDevices();
      } else {
        const error = await response.json();
        alert(`Registration failed: ${error.error}`);
      }
    } catch (error) {
      console.error('Device registration error:', error);
      alert('Device registration failed');
    }
  };

  const testDevice = async (deviceId: string, deviceType: string) => {
    try {
      let response;
      
      switch (deviceType) {
        case 'receipt_printer':
          response = await apiCall('/api/hardware/printers/print-receipt', {
            method: 'POST',
            body: JSON.stringify({
              device_id: deviceId,
              receipt_data: {
                receipt_number: `TEST_${Date.now()}`,
                header: 'TEST RECEIPT',
                business_info: {
                  name: 'Test Restaurant',
                  address: '123 Test St',
                  phone: '555-0123'
                },
                items: [
                  { name: 'Test Item', quantity: 1, total: 1.00 }
                ],
                subtotal: 1.00,
                tax: 0.08,
                total: 1.08,
                payment_method: 'Test',
                footer: 'Thank you for testing!'
              },
              options: { copies: 1 }
            })
          });
          break;
          
        case 'barcode_scanner':
          response = await apiCall('/api/hardware/scanners/scan-barcode', {
            method: 'POST',
            body: JSON.stringify({
              device_id: deviceId,
              timeout: 5000
            })
          });
          break;
          
        case 'cash_drawer':
          response = await apiCall('/api/hardware/cash-drawer/open', {
            method: 'POST',
            body: JSON.stringify({
              device_id: deviceId,
              reason: 'test'
            })
          });
          break;
          
        default:
          alert('Test not available for this device type');
          return;
      }

      if (response.ok) {
        const result = await response.json();
        alert(`Device test successful! Processing time: ${result.processing_time || 'N/A'}ms`);
      } else {
        const error = await response.json();
        alert(`Device test failed: ${error.error}`);
      }
    } catch (error) {
      console.error('Device test error:', error);
      alert('Device test failed');
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'receipt_printer':
        return <Printer className="h-5 w-5" />;
      case 'barcode_scanner':
        return <Scan className="h-5 w-5" />;
      case 'cash_drawer':
        return <DollarSign className="h-5 w-5" />;
      case 'card_reader':
        return <CreditCard className="h-5 w-5" />;
      default:
        return <Monitor className="h-5 w-5" />;
    }
  };

  const getConnectionIcon = (connectionType: string) => {
    switch (connectionType) {
      case 'network':
      case 'wifi':
        return <Network className="h-4 w-4" />;
      case 'bluetooth':
        return <Bluetooth className="h-4 w-4" />;
      case 'usb':
        return <Usb className="h-4 w-4" />;
      default:
        return <Wifi className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-600 bg-green-100';
      case 'disconnected':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'inactive':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4" />;
      case 'disconnected':
        return <WifiOff className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'inactive':
        return <Clock className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Loading hardware devices...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <TrendingUp className="h-6 w-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-900">Hardware Manager</h2>
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
            Phase 4
          </span>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadDevices}
            className="flex items-center px-3 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowRegistration(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Device
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Monitor className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Devices</p>
              <p className="text-2xl font-semibold text-gray-900">{deviceStats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Connected</p>
              <p className="text-2xl font-semibold text-gray-900">{deviceStats.connected}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <WifiOff className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Offline</p>
              <p className="text-2xl font-semibold text-gray-900">{deviceStats.offline}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Errors</p>
              <p className="text-2xl font-semibold text-gray-900">{deviceStats.errors}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Devices List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Connected Devices</h3>
        </div>
        
        {devices.length === 0 ? (
          <div className="p-6 text-center">
            <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No devices registered</h3>
            <p className="text-gray-600 mb-4">Get started by adding your first hardware device</p>
            <button
              onClick={() => setShowRegistration(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Device
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {devices.map((device) => (
              <div key={device.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-lg ${getStatusColor(device.status)}`}>
                      {getDeviceIcon(device.device_type)}
                    </div>
                    
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{device.device_name}</h4>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-sm text-gray-600 capitalize">
                          {device.device_type.replace('_', ' ')}
                        </span>
                        <div className="flex items-center space-x-1">
                          {getConnectionIcon(device.connection_type)}
                          <span className="text-sm text-gray-600 capitalize">{device.connection_type}</span>
                        </div>
                        {device.manufacturer && (
                          <span className="text-sm text-gray-600">{device.manufacturer} {device.model}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    {/* Status Badge */}
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(device.status)}`}>
                      {getStatusIcon(device.status)}
                      <span className="capitalize">{device.status}</span>
                    </div>

                    {/* Device Stats */}
                    <div className="text-right text-sm text-gray-600">
                      <div>Jobs: {device.total_jobs_processed}</div>
                      <div>Uptime: {device.uptime_percentage.toFixed(1)}%</div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <button
                        onClick={() => testDevice(device.id, device.device_type)}
                        disabled={device.status !== 'connected'}
                        className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                      >
                        <Zap className="h-3 w-3 mr-1" />
                        Test
                      </button>
                      <button
                        onClick={() => setSelectedDevice(device)}
                        className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        Settings
                      </button>
                    </div>
                  </div>
                </div>

                {/* Error Information */}
                {device.error_count > 0 && (
                  <div className="mt-3 p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                      <span className="text-sm text-red-700">
                        {device.error_count} error(s) detected. Last heartbeat: {
                          device.last_heartbeat 
                            ? new Date(device.last_heartbeat).toLocaleString()
                            : 'Never'
                        }
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Device Registration Modal */}
      {showRegistration && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Register New Device</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Device Type</label>
                  <select
                    value={newDevice.device_type}
                    onChange={(e) => setNewDevice({...newDevice, device_type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="receipt_printer">Receipt Printer</option>
                    <option value="barcode_scanner">Barcode Scanner</option>
                    <option value="cash_drawer">Cash Drawer</option>
                    <option value="card_reader">Card Reader</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Device Name</label>
                  <input
                    type="text"
                    value={newDevice.device_name}
                    onChange={(e) => setNewDevice({...newDevice, device_name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Main Counter Printer"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Manufacturer</label>
                    <input
                      type="text"
                      value={newDevice.manufacturer}
                      onChange={(e) => setNewDevice({...newDevice, manufacturer: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Epson"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
                    <input
                      type="text"
                      value={newDevice.model}
                      onChange={(e) => setNewDevice({...newDevice, model: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., TM-T88VI"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Connection Type</label>
                  <select
                    value={newDevice.connection_type}
                    onChange={(e) => setNewDevice({...newDevice, connection_type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="network">Network (Ethernet/WiFi)</option>
                    <option value="usb">USB</option>
                    <option value="bluetooth">Bluetooth</option>
                    <option value="serial">Serial</option>
                  </select>
                </div>

                {newDevice.connection_type === 'network' && (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                      <input
                        type="text"
                        value={newDevice.ip_address}
                        onChange={(e) => setNewDevice({...newDevice, ip_address: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="*************"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Port</label>
                      <input
                        type="number"
                        value={newDevice.port}
                        onChange={(e) => setNewDevice({...newDevice, port: parseInt(e.target.value)})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="9100"
                      />
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Connection String</label>
                  <input
                    type="text"
                    value={newDevice.connection_string}
                    onChange={(e) => setNewDevice({...newDevice, connection_string: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Device-specific connection string"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowRegistration(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={registerDevice}
                  disabled={!newDevice.device_name || !newDevice.connection_string}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Register Device
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Phase4HardwareManager;
