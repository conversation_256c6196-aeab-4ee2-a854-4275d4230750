import React, { useState, useEffect } from 'react';
import { EnhancedAppProvider, useEnhancedAppContext } from './context/EnhancedAppContext';
import EnhancedLogin from './components/EnhancedLogin';
import ProductGrid from './components/ProductGrid';
import Tabs from './components/Tabs';
import Settings from './components/Settings';
import FloorLayout from './components/FloorLayout';
import StaffScheduling from './components/StaffScheduling';
import LoyaltyProgram from './components/LoyaltyProgram';
import SalesDashboard from './components/SalesDashboard';
import AdvancedAnalytics from './components/AdvancedAnalytics';
import Inventory from './components/Inventory';

// Enhanced Header Component with Multi-Tenant Info
const EnhancedHeader: React.FC = () => {
  const { state, logout } = useEnhancedAppContext();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h1 className="text-xl font-bold text-gray-900">
              {state.currentTenant?.name || 'Restaurant POS'}
            </h1>
          </div>
          
          {/* Tenant & Location Info */}
          {state.currentTenant && (
            <div className="flex items-center space-x-3 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
                </svg>
                <span>{state.currentTenant.slug}</span>
              </div>
              
              {state.currentLocation && (
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>{state.currentLocation.name}</span>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {/* Real-time Status Indicator */}
          {state.socket && (
            <div className="flex items-center space-x-2 text-sm text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Live</span>
            </div>
          )}

          {/* Current Employee */}
          {state.currentEmployee && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>{state.currentEmployee.name}</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                {state.currentEmployee.role}
              </span>
            </div>
          )}

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </header>
  );
};

// Enhanced Main App Component
const EnhancedMainApp: React.FC = () => {
  const { state } = useEnhancedAppContext();
  const [activeTab, setActiveTab] = useState('pos');

  // Auto-connect socket when authenticated
  useEffect(() => {
    if (state.isAuthenticated && state.currentTenant && !state.socket) {
      // Socket connection is handled in the context
    }
  }, [state.isAuthenticated, state.currentTenant, state.socket]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'pos':
        return (
          <div className="flex h-full">
            <div className="flex-1">
              <ProductGrid />
            </div>
            <div className="w-96 border-l border-gray-200">
              <Tabs activeTab={activeTab} setActiveTab={setActiveTab} />
            </div>
          </div>
        );
      case 'floor':
        return <FloorLayout />;
      case 'inventory':
        return <Inventory />;
      case 'staff':
        return <StaffScheduling />;
      case 'loyalty':
        return <LoyaltyProgram />;
      case 'analytics':
        return <AdvancedAnalytics />;
      case 'settings':
        return <Settings />;
      default:
        return (
          <div className="flex h-full">
            <div className="flex-1">
              <ProductGrid />
            </div>
            <div className="w-96 border-l border-gray-200">
              <Tabs activeTab={activeTab} setActiveTab={setActiveTab} />
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <EnhancedHeader />
      
      {/* Navigation Tabs */}
      <nav className="bg-white border-b border-gray-200 px-6">
        <div className="flex space-x-8">
          {[
            { id: 'pos', label: 'POS', icon: '🛒' },
            { id: 'floor', label: 'Floor Plan', icon: '🏢' },
            { id: 'inventory', label: 'Inventory', icon: '📦' },
            { id: 'staff', label: 'Staff', icon: '👥' },
            { id: 'loyalty', label: 'Loyalty', icon: '⭐' },
            { id: 'analytics', label: 'Analytics', icon: '📊' },
            { id: 'settings', label: 'Settings', icon: '⚙️' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        {renderTabContent()}
      </main>

      {/* Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Orders: {state.orders.length}</span>
            <span>Products: {state.products.length}</span>
            {state.currentOrder && (
              <span className="text-blue-600 font-medium">
                Current Order: ${state.currentOrder.total.toFixed(2)}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            {state.tenantSettings && (
              <span>Tax Rate: {(state.tenantSettings.tax_rate * 100).toFixed(1)}%</span>
            )}
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Main Enhanced App Wrapper
const EnhancedApp: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <EnhancedAppProvider>
      <EnhancedAppContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
    </EnhancedAppProvider>
  );
};

// Content component that uses the context
const EnhancedAppContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  const { state } = useEnhancedAppContext();

  // Update login state based on authentication
  useEffect(() => {
    setIsLoggedIn(state.isAuthenticated);
  }, [state.isAuthenticated, setIsLoggedIn]);

  if (!isLoggedIn) {
    return <EnhancedLogin onLogin={setIsLoggedIn} />;
  }

  return <EnhancedMainApp />;
};

export default EnhancedApp;
