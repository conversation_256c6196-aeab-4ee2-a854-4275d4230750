import React, { useState, useEffect, useMemo } from 'react';
import { useAppContext } from '../context/AppContext';
import { KitchenOrder, Order } from '../types';
import { 
  Clock, ChefHat, CheckCircle, AlertCircle, Play, RotateCcw, 
  Settings, Filter, BarChart3, Search, Timer, User, AlertTriangle, RefreshCw
} from 'lucide-react';

interface KitchenFilters {
  status?: KitchenOrder['status'][];
  priority?: KitchenOrder['priority'][];
  searchTerm?: string;
  showCompleted?: boolean;
}

const KitchenDisplaySystemSimple: React.FC = () => {
  const { 
    state, 
    fetchKitchenOrders, 
    updateKitchenOrderStatus, 
    sendOrderToKitchen
  } = useAppContext();

  const [selectedOrder, setSelectedOrder] = useState<KitchenOrder | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showFilters, setShowFilters] = useState(false);
  const [showMetrics, setShowMetrics] = useState(true);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<KitchenFilters>({
    searchTerm: '',
    showCompleted: false
  });

  // Initialize and set up intervals
  useEffect(() => {
    // Fetch initial data
    fetchKitchenOrders();

    // Set up auto-refresh
    const refreshInterval = setInterval(() => {
      fetchKitchenOrders();
    }, 30000); // 30 seconds

    // Set up time update
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearInterval(refreshInterval);
      clearInterval(timeInterval);
    };
  }, []);

  // Filter orders based on current filters
  const filteredOrders = useMemo(() => {
    let orders = state.kitchenOrders;

    if (filters.status && filters.status.length > 0) {
      orders = orders.filter(order => filters.status!.includes(order.status));
    }

    if (filters.priority && filters.priority.length > 0) {
      orders = orders.filter(order => filters.priority!.includes(order.priority));
    }

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      orders = orders.filter(order =>
        order.orderNumber.toString().includes(searchLower) ||
        order.items.some(item => item.name.toLowerCase().includes(searchLower)) ||
        (order.notes && order.notes.toLowerCase().includes(searchLower))
      );
    }

    if (!filters.showCompleted) {
      orders = orders.filter(order => order.status !== 'served');
    }

    return orders;
  }, [state.kitchenOrders, filters]);

  // Group filtered orders by status
  const ordersByStatus = useMemo(() => ({
    new: filteredOrders.filter(order => order.status === 'new'),
    preparing: filteredOrders.filter(order => order.status === 'preparing'),
    ready: filteredOrders.filter(order => order.status === 'ready'),
    served: filteredOrders.filter(order => order.status === 'served')
  }), [filteredOrders]);

  // Calculate overdue orders (15 minutes threshold)
  const overdueOrders = useMemo(() => {
    const overdueThreshold = 15 * 60 * 1000; // 15 minutes in ms
    return filteredOrders.filter(order => {
      const elapsed = currentTime.getTime() - order.timestamp;
      return elapsed > overdueThreshold && order.status !== 'served';
    });
  }, [filteredOrders, currentTime]);

  const getStatusColor = (status: KitchenOrder['status']) => {
    switch (status) {
      case 'new':
        return 'bg-red-600 border-red-500';
      case 'preparing':
        return 'bg-yellow-600 border-yellow-500';
      case 'ready':
        return 'bg-green-600 border-green-500';
      case 'served':
        return 'bg-gray-600 border-gray-500';
      default:
        return 'bg-gray-600 border-gray-500';
    }
  };

  const getPriorityColor = (priority: KitchenOrder['priority']) => {
    switch (priority) {
      case 'high':
        return '#ef4444';
      case 'normal':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getStatusIcon = (status: KitchenOrder['status']) => {
    switch (status) {
      case 'new':
        return <AlertCircle className="h-5 w-5" />;
      case 'preparing':
        return <Play className="h-5 w-5" />;
      case 'ready':
        return <CheckCircle className="h-5 w-5" />;
      case 'served':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getElapsedTime = (timestamp: number) => {
    const elapsed = Math.floor((currentTime.getTime() - timestamp) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const isOrderOverdue = (order: KitchenOrder) => {
    const elapsed = currentTime.getTime() - order.timestamp;
    const overdueThreshold = 15 * 60 * 1000; // 15 minutes
    return elapsed > overdueThreshold && order.status !== 'served';
  };

  const handleStatusUpdate = async (orderId: string, newStatus: KitchenOrder['status']) => {
    try {
      await updateKitchenOrderStatus(orderId, newStatus);
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  const handleSendToKitchen = async (order: Order) => {
    try {
      await sendOrderToKitchen(order.id);
    } catch (error) {
      console.error('Failed to send order to kitchen:', error);
    }
  };

  const toggleOrderSelection = (orderId: string) => {
    const newSelection = new Set(selectedOrders);
    if (newSelection.has(orderId)) {
      newSelection.delete(orderId);
    } else {
      newSelection.add(orderId);
    }
    setSelectedOrders(newSelection);
  };

  const OrderCard: React.FC<{ order: KitchenOrder }> = ({ order }) => {
    const isOverdue = isOrderOverdue(order);
    const isSelected = selectedOrders.has(order.id);
    
    return (
      <div
        className={`${getStatusColor(order.status)} border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-lg ${
          selectedOrder?.id === order.id ? 'ring-2 ring-purple-400' : ''
        } ${isSelected ? 'ring-2 ring-blue-400' : ''} ${isOverdue ? 'animate-pulse' : ''}`}
        onClick={() => setSelectedOrder(order)}
      >
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation();
                toggleOrderSelection(order.id);
              }}
              className="rounded"
            />
            {getStatusIcon(order.status)}
            <span className="text-white font-bold text-lg">#{order.orderNumber}</span>
            {order.tableNumber && (
              <span className="text-white text-sm">Table {order.tableNumber}</span>
            )}
            {isOverdue && (
              <AlertTriangle className="h-4 w-4 text-red-300" />
            )}
          </div>
          <div className="text-right">
            <div className={`text-white font-medium ${isOverdue ? 'text-red-300' : ''}`}>
              {getElapsedTime(order.timestamp)}
            </div>
            <div 
              className="text-xs font-semibold"
              style={{ color: getPriorityColor(order.priority) }}
            >
              {order.priority.toUpperCase()}
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {order.items.map((item, index) => (
            <div key={index} className="flex justify-between text-white">
              <span>{item.quantity}x {item.name}</span>
              {item.notes && (
                <span className="text-yellow-300 text-sm italic">*{item.notes}</span>
              )}
            </div>
          ))}
        </div>

        {order.notes && (
          <div className="mt-3 p-2 bg-black bg-opacity-30 rounded text-yellow-300 text-sm">
            <strong>Notes:</strong> {order.notes}
          </div>
        )}

        <div className="mt-2 flex justify-between items-center text-sm">
          {order.estimatedTime && (
            <div className="text-white">
              <Timer className="h-3 w-3 inline mr-1" />
              Est. {order.estimatedTime} min
            </div>
          )}
          
          {order.assignedTo && (
            <div className="text-gray-300">
              <User className="h-3 w-3 inline mr-1" />
              {order.assignedTo}
            </div>
          )}
        </div>
      </div>
    );
  };

  const StatusColumn: React.FC<{ 
    title: string; 
    orders: KitchenOrder[]; 
    status: KitchenOrder['status'];
  }> = ({ title, orders, status }) => {
    return (
      <div className="flex-1 bg-gray-800 rounded-lg p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">{title}</h3>
          <span className="bg-gray-700 text-white px-2 py-1 rounded-full text-sm">
            {orders.length}
          </span>
        </div>
        
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {orders.map(order => (
            <OrderCard key={order.id} order={order} />
          ))}
          
          {orders.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              <ChefHat className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No {title.toLowerCase()} orders</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const MetricsPanel: React.FC = () => {
    if (!showMetrics) return null;
    
    return (
      <div className="bg-gray-800 rounded-lg p-4 mb-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">Performance Metrics</h3>
          <button
            onClick={() => setShowMetrics(false)}
            className="text-gray-400 hover:text-white"
          >
            ×
          </button>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{state.kitchenOrders.length}</div>
            <div className="text-gray-300 text-sm">Total Orders</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{ordersByStatus.served.length}</div>
            <div className="text-gray-300 text-sm">Completed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              {ordersByStatus.new.length + ordersByStatus.preparing.length}
            </div>
            <div className="text-gray-300 text-sm">Active</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-400">{overdueOrders.length}</div>
            <div className="text-gray-300 text-sm">Overdue</div>
          </div>
        </div>
        
        {overdueOrders.length > 0 && (
          <div className="mt-4 p-3 bg-red-900 border border-red-600 rounded">
            <div className="flex items-center space-x-2 text-red-300">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">
                {overdueOrders.length} overdue order{overdueOrders.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  };

  const FiltersPanel: React.FC = () => {
    if (!showFilters) return null;

    return (
      <div className="bg-gray-800 rounded-lg p-4 mb-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">Filters</h3>
          <button
            onClick={() => setShowFilters(false)}
            className="text-gray-400 hover:text-white"
          >
            ×
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filters.searchTerm || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                placeholder="Order #, item name..."
                className="w-full pl-10 pr-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Status</label>
            <select
              multiple
              value={filters.status || []}
              onChange={(e) => {
                const values = Array.from(e.target.selectedOptions, option => option.value) as KitchenOrder['status'][];
                setFilters(prev => ({ ...prev, status: values }));
              }}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              <option value="new">New</option>
              <option value="preparing">Preparing</option>
              <option value="ready">Ready</option>
              <option value="served">Served</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Priority</label>
            <select
              multiple
              value={filters.priority || []}
              onChange={(e) => {
                const values = Array.from(e.target.selectedOptions, option => option.value) as KitchenOrder['priority'][];
                setFilters(prev => ({ ...prev, priority: values }));
              }}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            >
              <option value="low">Low</option>
              <option value="normal">Normal</option>
              <option value="high">High</option>
            </select>
          </div>
        </div>
        
        <div className="mt-4 flex items-center space-x-4">
          <label className="flex items-center space-x-2 text-gray-300">
            <input
              type="checkbox"
              checked={filters.showCompleted || false}
              onChange={(e) => setFilters(prev => ({ ...prev, showCompleted: e.target.checked }))}
              className="rounded"
            />
            <span>Show completed orders</span>
          </label>
          
          <button
            onClick={() => setFilters({
              searchTerm: '',
              showCompleted: false
            })}
            className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Clear Filters
          </button>
        </div>
      </div>
    );
  };

  // Get pending orders that can be sent to kitchen
  const pendingOrders = state.orders.filter(order => 
    order.status === 'open' && order.items.length > 0
  );

  return (
    <div className="h-screen flex flex-col p-4">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-gray-800 rounded-lg mb-4">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-white">Kitchen Display System</h1>
          <div className="text-gray-300">
            {currentTime.toLocaleTimeString()}
          </div>
          {overdueOrders.length > 0 && (
            <div className="flex items-center space-x-1 text-red-400">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">{overdueOrders.length} overdue</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-white">
            Active: {ordersByStatus.new.length + ordersByStatus.preparing.length}
          </div>
          
          <button
            onClick={() => fetchKitchenOrders()}
            className="p-2 rounded-md bg-blue-600 text-white hover:bg-blue-700"
            title="Refresh Orders"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => setShowMetrics(!showMetrics)}
            className={`p-2 rounded-md ${showMetrics ? 'bg-blue-600' : 'bg-gray-600'} text-white hover:opacity-80`}
            title="Toggle Metrics"
          >
            <BarChart3 className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-md ${showFilters ? 'bg-blue-600' : 'bg-gray-600'} text-white hover:opacity-80`}
            title="Toggle Filters"
          >
            <Filter className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Metrics Panel */}
      <MetricsPanel />

      {/* Filters Panel */}
      <FiltersPanel />

      {/* Pending Orders to Send */}
      {pendingOrders.length > 0 && (
        <div className="bg-blue-800 rounded-lg p-4 mb-4">
          <h3 className="text-white font-semibold mb-3">Orders Ready to Send to Kitchen</h3>
          <div className="flex flex-wrap gap-2">
            {pendingOrders.map(order => (
              <button
                key={order.id}
                onClick={() => handleSendToKitchen(order)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Send Order {order.tabName ? `(${order.tabName})` : `#${order.id.slice(-4)}`}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="flex-grow flex gap-4">
        {/* Order Columns */}
        <div className="flex-grow flex gap-4">
          <StatusColumn 
            title="New Orders" 
            orders={ordersByStatus.new} 
            status="new"
          />
          <StatusColumn 
            title="Preparing" 
            orders={ordersByStatus.preparing} 
            status="preparing"
          />
          <StatusColumn 
            title="Ready" 
            orders={ordersByStatus.ready} 
            status="ready"
          />
          {filters.showCompleted && (
            <StatusColumn 
              title="Served" 
              orders={ordersByStatus.served} 
              status="served"
            />
          )}
        </div>

        {/* Order Details Panel */}
        {selectedOrder && (
          <div className="w-80">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-white">Order #{selectedOrder.orderNumber}</h3>
                  {selectedOrder.tableNumber && (
                    <p className="text-gray-300">Table {selectedOrder.tableNumber}</p>
                  )}
                </div>
                <div className="text-right">
                  <div className={`text-white font-medium text-lg ${isOrderOverdue(selectedOrder) ? 'text-red-300' : ''}`}>
                    {getElapsedTime(selectedOrder.timestamp)}
                  </div>
                  <div 
                    className="text-sm font-semibold"
                    style={{ color: getPriorityColor(selectedOrder.priority) }}
                  >
                    {selectedOrder.priority.toUpperCase()} PRIORITY
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-white font-medium mb-3">Items:</h4>
                <div className="space-y-2">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-start bg-gray-700 p-3 rounded">
                      <div>
                        <span className="text-white font-medium">{item.quantity}x {item.name}</span>
                        {item.modifiers && item.modifiers.length > 0 && (
                          <div className="text-gray-300 text-sm mt-1">
                            Modifiers: {item.modifiers.join(', ')}
                          </div>
                        )}
                        {item.notes && (
                          <div className="text-yellow-300 text-sm mt-1 italic">
                            Notes: {item.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {selectedOrder.notes && (
                <div className="mb-6">
                  <h4 className="text-white font-medium mb-2">Order Notes:</h4>
                  <div className="bg-yellow-900 border border-yellow-600 p-3 rounded text-yellow-100">
                    {selectedOrder.notes}
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="flex space-x-2">
                  {selectedOrder.status === 'new' && (
                    <button
                      onClick={() => handleStatusUpdate(selectedOrder.id, 'preparing')}
                      className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
                    >
                      <Play className="h-4 w-4" />
                      <span>Start Preparing</span>
                    </button>
                  )}
                  
                  {selectedOrder.status === 'preparing' && (
                    <button
                      onClick={() => handleStatusUpdate(selectedOrder.id, 'ready')}
                      className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    >
                      <CheckCircle className="h-4 w-4" />
                      <span>Mark Ready</span>
                    </button>
                  )}
                  
                  {selectedOrder.status === 'ready' && (
                    <button
                      onClick={() => handleStatusUpdate(selectedOrder.id, 'served')}
                      className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                    >
                      <CheckCircle className="h-4 w-4" />
                      <span>Mark Served</span>
                    </button>
                  )}
                </div>

                {selectedOrder.status !== 'served' && (
                  <button
                    onClick={() => handleStatusUpdate(selectedOrder.id, 'new')}
                    className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    <RotateCcw className="h-4 w-4" />
                    <span>Reset</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="mt-4 grid grid-cols-4 gap-4">
        <div className="bg-red-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.new.length}</div>
          <div className="text-red-100 text-sm">New</div>
        </div>
        <div className="bg-yellow-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.preparing.length}</div>
          <div className="text-yellow-100 text-sm">Preparing</div>
        </div>
        <div className="bg-green-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.ready.length}</div>
          <div className="text-green-100 text-sm">Ready</div>
        </div>
        <div className="bg-gray-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.served.length}</div>
          <div className="text-gray-100 text-sm">Served</div>
        </div>
      </div>
    </div>
  );
};

export default KitchenDisplaySystemSimple;
