import React, { useState } from 'react';

// Simple Super Admin Login Component
const SimpleLogin: React.FC<{ onLogin: (success: boolean) => void }> = ({ onLogin }) => {
  const [pin, setPin] = useState('');

  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(pin + digit);
    }
  };

  const handleSubmit = () => {
    if (pin === '888888' || pin === '999999') {
      onLogin(true);
    } else {
      alert('Invalid PIN');
      setPin('');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-pink-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-6">Super Admin Login</h1>
        
        <div className="mb-6">
          <div className="flex justify-center space-x-2 mb-4">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className={`w-8 h-8 border-2 rounded ${
                  i < pin.length ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                } flex items-center justify-center`}
              >
                {i < pin.length ? '●' : ''}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-3 gap-2 mb-4">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
            <button
              key={digit}
              onClick={() => handlePinInput(digit.toString())}
              className="h-12 bg-gray-100 hover:bg-gray-200 rounded text-lg font-semibold"
            >
              {digit}
            </button>
          ))}
          <button
            onClick={() => setPin('')}
            className="h-12 bg-red-100 hover:bg-red-200 rounded text-sm font-semibold text-red-600"
          >
            Clear
          </button>
          <button
            onClick={() => handlePinInput('0')}
            className="h-12 bg-gray-100 hover:bg-gray-200 rounded text-lg font-semibold"
          >
            0
          </button>
          <button
            onClick={() => setPin(pin.slice(0, -1))}
            className="h-12 bg-yellow-100 hover:bg-yellow-200 rounded text-sm font-semibold text-yellow-600"
          >
            ⌫
          </button>
        </div>

        <button
          onClick={handleSubmit}
          disabled={pin.length !== 6}
          className="w-full py-3 bg-blue-600 text-white rounded font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Sign In
        </button>

        <div className="mt-4 p-3 bg-blue-50 rounded text-sm">
          <p className="font-semibold text-blue-800 mb-1">Test Credentials:</p>
          <p className="text-blue-700">Super Admin: 888888 or 999999</p>
        </div>
      </div>
    </div>
  );
};

// Simple Dashboard Component
const SimpleDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow p-4">
        <h1 className="text-xl font-bold">Super Admin Dashboard</h1>
      </header>
      <main className="p-4">
        <div className="bg-white rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">Dashboard Overview</h2>
          <p>Welcome to the Super Admin Dashboard!</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded"
          >
            Logout
          </button>
        </div>
      </main>
    </div>
  );
};

// Main Simple Super Admin System
const SuperAdminSystemSimple: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  console.log('🔐 Simple SuperAdmin: isLoggedIn =', isLoggedIn);

  if (!isLoggedIn) {
    return <SimpleLogin onLogin={setIsLoggedIn} />;
  }

  return <SimpleDashboard />;
};

export default SuperAdminSystemSimple;
