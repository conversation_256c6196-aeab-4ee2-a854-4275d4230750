import React, { useState, useEffect } from 'react';
import { 
  Package, 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp, 
  RefreshCw, 
  Plus, 
  Search, 
  Filter,
  Download,
  Upload,
  Truck,
  Clock,
  DollarSign,
  BarChart3,
  Zap,
  Target,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  ShoppingCart,
  Calendar,
  MapPin,
  Users
} from 'lucide-react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  sku: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  location: string;
  expiryDate?: string;
  lastRestocked: string;
  usageRate: number; // units per day
  predictedDemand: number;
  reorderPoint: number;
  status: 'in-stock' | 'low-stock' | 'out-of-stock' | 'overstocked';
  tags: string[];
  nutritionalInfo?: {
    calories: number;
    allergens: string[];
  };
}

interface Supplier {
  id: string;
  name: string;
  contact: {
    email: string;
    phone: string;
    address: string;
  };
  reliability: number;
  averageDeliveryTime: number;
  products: string[];
  paymentTerms: string;
  minimumOrder: number;
}

interface PurchaseOrder {
  id: string;
  supplierId: string;
  supplierName: string;
  items: Array<{
    itemId: string;
    itemName: string;
    quantity: number;
    unitCost: number;
    total: number;
  }>;
  totalAmount: number;
  status: 'draft' | 'sent' | 'confirmed' | 'delivered' | 'cancelled';
  orderDate: string;
  expectedDelivery: string;
  actualDelivery?: string;
  notes?: string;
}

interface WasteRecord {
  id: string;
  itemId: string;
  itemName: string;
  quantity: number;
  reason: 'expired' | 'damaged' | 'overproduction' | 'quality-issue' | 'other';
  cost: number;
  date: string;
  reportedBy: string;
  notes?: string;
}

const Phase3InventoryManagement: React.FC = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [wasteRecords, setWasteRecords] = useState<WasteRecord[]>([]);
  const [activeTab, setActiveTab] = useState<'inventory' | 'orders' | 'suppliers' | 'waste' | 'analytics'>('inventory');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

  useEffect(() => {
    loadInventoryData();
  }, []);

  const loadInventoryData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - in production this would come from API
      const mockInventory: InventoryItem[] = [
        {
          id: '1',
          name: 'Tomatoes (Fresh)',
          category: 'Vegetables',
          sku: 'VEG-TOM-001',
          currentStock: 45,
          minStock: 20,
          maxStock: 100,
          unit: 'lbs',
          costPerUnit: 2.50,
          supplier: 'Fresh Farm Co.',
          location: 'Walk-in Cooler A',
          expiryDate: '2024-06-10',
          lastRestocked: '2024-06-01',
          usageRate: 12,
          predictedDemand: 84,
          reorderPoint: 30,
          status: 'in-stock',
          tags: ['fresh', 'organic', 'local'],
          nutritionalInfo: {
            calories: 18,
            allergens: []
          }
        },
        {
          id: '2',
          name: 'Mozzarella Cheese',
          category: 'Dairy',
          sku: 'DAI-MOZ-001',
          currentStock: 8,
          minStock: 15,
          maxStock: 50,
          unit: 'lbs',
          costPerUnit: 6.75,
          supplier: 'Dairy Fresh Inc.',
          location: 'Walk-in Cooler B',
          expiryDate: '2024-06-15',
          lastRestocked: '2024-05-28',
          usageRate: 6,
          predictedDemand: 42,
          reorderPoint: 15,
          status: 'low-stock',
          tags: ['dairy', 'cheese'],
          nutritionalInfo: {
            calories: 300,
            allergens: ['milk']
          }
        },
        {
          id: '3',
          name: 'Olive Oil (Extra Virgin)',
          category: 'Oils & Condiments',
          sku: 'OIL-OLI-001',
          currentStock: 0,
          minStock: 5,
          maxStock: 25,
          unit: 'bottles',
          costPerUnit: 12.99,
          supplier: 'Mediterranean Imports',
          location: 'Dry Storage',
          lastRestocked: '2024-05-20',
          usageRate: 2,
          predictedDemand: 14,
          reorderPoint: 5,
          status: 'out-of-stock',
          tags: ['oil', 'premium', 'imported']
        },
        {
          id: '4',
          name: 'Chicken Breast',
          category: 'Meat',
          sku: 'MEA-CHI-001',
          currentStock: 85,
          minStock: 30,
          maxStock: 60,
          unit: 'lbs',
          costPerUnit: 4.25,
          supplier: 'Premium Meats Ltd.',
          location: 'Walk-in Freezer',
          expiryDate: '2024-06-08',
          lastRestocked: '2024-06-02',
          usageRate: 8,
          predictedDemand: 56,
          reorderPoint: 30,
          status: 'overstocked',
          tags: ['meat', 'protein', 'fresh']
        }
      ];

      const mockSuppliers: Supplier[] = [
        {
          id: '1',
          name: 'Fresh Farm Co.',
          contact: {
            email: '<EMAIL>',
            phone: '(*************',
            address: '123 Farm Road, Green Valley, CA 90210'
          },
          reliability: 95,
          averageDeliveryTime: 2,
          products: ['Vegetables', 'Fruits', 'Herbs'],
          paymentTerms: 'Net 30',
          minimumOrder: 100
        },
        {
          id: '2',
          name: 'Dairy Fresh Inc.',
          contact: {
            email: '<EMAIL>',
            phone: '(*************',
            address: '456 Dairy Lane, Milk Valley, CA 90211'
          },
          reliability: 92,
          averageDeliveryTime: 1,
          products: ['Dairy', 'Cheese', 'Eggs'],
          paymentTerms: 'Net 15',
          minimumOrder: 200
        }
      ];

      const mockPurchaseOrders: PurchaseOrder[] = [
        {
          id: 'PO-001',
          supplierId: '1',
          supplierName: 'Fresh Farm Co.',
          items: [
            {
              itemId: '1',
              itemName: 'Tomatoes (Fresh)',
              quantity: 50,
              unitCost: 2.50,
              total: 125.00
            }
          ],
          totalAmount: 125.00,
          status: 'confirmed',
          orderDate: '2024-06-03',
          expectedDelivery: '2024-06-05',
          notes: 'Urgent order for weekend rush'
        }
      ];

      const mockWasteRecords: WasteRecord[] = [
        {
          id: 'W-001',
          itemId: '1',
          itemName: 'Tomatoes (Fresh)',
          quantity: 5,
          reason: 'expired',
          cost: 12.50,
          date: '2024-06-01',
          reportedBy: 'Kitchen Staff',
          notes: 'Found during morning prep'
        }
      ];

      setInventory(mockInventory);
      setSuppliers(mockSuppliers);
      setPurchaseOrders(mockPurchaseOrders);
      setWasteRecords(mockWasteRecords);
    } catch (error) {
      console.error('Error loading inventory data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in-stock': return 'bg-green-100 text-green-800';
      case 'low-stock': return 'bg-yellow-100 text-yellow-800';
      case 'out-of-stock': return 'bg-red-100 text-red-800';
      case 'overstocked': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in-stock': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'low-stock': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'out-of-stock': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'overstocked': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      default: return <Package className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatCurrency = (amount: number) => `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`;

  const calculateInventoryValue = () => {
    return inventory.reduce((total, item) => total + (item.currentStock * item.costPerUnit), 0);
  };

  const getInventoryStats = () => {
    const totalItems = inventory.length;
    const lowStockItems = inventory.filter(item => item.status === 'low-stock').length;
    const outOfStockItems = inventory.filter(item => item.status === 'out-of-stock').length;
    const overstockedItems = inventory.filter(item => item.status === 'overstocked').length;
    
    return { totalItems, lowStockItems, outOfStockItems, overstockedItems };
  };

  const stats = getInventoryStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading inventory management...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Package className="h-6 w-6 mr-2 text-blue-600" />
              Advanced Inventory Management
            </h1>
            <p className="text-gray-600 mt-1">
              Real-time inventory tracking with AI-powered predictions and automation
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Item</span>
            </button>
            
            <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2">
              <Upload className="h-4 w-4" />
              <span>Import</span>
            </button>
            
            <button className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {[
            { id: 'inventory', label: 'Inventory', icon: Package },
            { id: 'orders', label: 'Purchase Orders', icon: ShoppingCart },
            { id: 'suppliers', label: 'Suppliers', icon: Truck },
            { id: 'waste', label: 'Waste Tracking', icon: Trash2 },
            { id: 'analytics', label: 'Analytics', icon: BarChart3 }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeTab === 'inventory' && (
          <div className="space-y-6">
            {/* Inventory Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Items</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(calculateInventoryValue())}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Low Stock</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.lowStockItems}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Out of Stock</p>
                    <p className="text-2xl font-bold text-red-600">{stats.outOfStockItems}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Overstocked</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.overstockedItems}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-blue-500" />
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search inventory by name, SKU, or supplier..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Categories</option>
                  <option value="Vegetables">Vegetables</option>
                  <option value="Dairy">Dairy</option>
                  <option value="Meat">Meat</option>
                  <option value="Oils & Condiments">Oils & Condiments</option>
                </select>
                
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="in-stock">In Stock</option>
                  <option value="low-stock">Low Stock</option>
                  <option value="out-of-stock">Out of Stock</option>
                  <option value="overstocked">Overstocked</option>
                </select>
              </div>
            </div>

            {/* Inventory Table */}
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Inventory Items</h3>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Usage Rate
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Supplier
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredInventory.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                                <Package className="h-5 w-5 text-gray-600" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{item.name}</div>
                              <div className="text-sm text-gray-500">{item.sku} • {item.category}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getStatusIcon(item.status)}
                            <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>
                              {item.status.replace('-', ' ')}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {item.currentStock} {item.unit}
                          </div>
                          <div className="text-sm text-gray-500">
                            Min: {item.minStock} • Max: {item.maxStock}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.usageRate} {item.unit}/day
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(item.currentStock * item.costPerUnit)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.supplier}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => setSelectedItem(item)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="text-purple-600 hover:text-purple-900">
                              <ShoppingCart className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs content would be implemented here */}
        {activeTab !== 'inventory' && (
          <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Management
              </h3>
              <p className="text-gray-600 mb-4">
                Advanced {activeTab} management system is being implemented with comprehensive tracking and automation.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">
                  This section will include full {activeTab} management capabilities with real-time updates and AI predictions.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3InventoryManagement;
