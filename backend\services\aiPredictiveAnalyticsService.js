// AI Predictive Analytics Service for Phase 5
// Sales forecasting, demand prediction, and intelligent insights

const { Pool } = require('pg');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class AIPredictiveAnalyticsService {
  constructor() {
    this.predictionTypes = {
      SALES_FORECAST: 'sales_forecast',
      DEMAND_PREDICTION: 'demand_prediction',
      INVENTORY_OPTIMIZATION: 'inventory_optimization',
      STAFFING_FORECAST: 'staffing_forecast',
      CUSTOMER_BEHAVIOR: 'customer_behavior'
    };
    
    this.timeHorizons = {
      HOURLY: 'hourly',
      DAILY: 'daily',
      WEEKLY: 'weekly',
      MONTHLY: 'monthly'
    };
  }

  // =====================================================
  // SALES FORECASTING
  // =====================================================

  async generateSalesForecast(tenantId, timeframe = 'daily', daysAhead = 7) {
    const startTime = Date.now();
    
    try {
      console.log(`📈 Generating sales forecast for tenant ${tenantId}, ${timeframe}, ${daysAhead} days ahead`);

      // Get historical sales data
      const historicalData = await this.getHistoricalSalesData(tenantId, timeframe, 90);
      
      if (historicalData.length < 14) {
        throw new Error('Insufficient historical data for accurate forecasting');
      }

      // Get prediction model
      const model = await this.getPredictionModel(tenantId, this.predictionTypes.SALES_FORECAST, timeframe);
      
      // Generate predictions using time series analysis
      const predictions = await this.generateTimeSeriesPredictions(
        historicalData, 
        daysAhead, 
        timeframe,
        model
      );

      // Calculate confidence intervals
      const predictionsWithConfidence = await this.calculateConfidenceIntervals(predictions, historicalData);

      // Store predictions
      await this.storePredictions(tenantId, model.id, predictionsWithConfidence, 'total_sales');

      // Update model metrics
      await this.updateModelMetrics(model.id, predictions.length, Date.now() - startTime);

      return {
        success: true,
        predictions: predictionsWithConfidence,
        model_accuracy: model.validation_score,
        confidence_level: 0.95,
        processing_time: Date.now() - startTime,
        data_points_used: historicalData.length
      };

    } catch (error) {
      console.error('❌ Sales forecast error:', error);
      return {
        success: false,
        error: error.message,
        processing_time: Date.now() - startTime
      };
    }
  }

  async getHistoricalSalesData(tenantId, timeframe, daysBack) {
    try {
      const client = await pool.connect();
      
      let groupBy, dateFormat;
      switch (timeframe) {
        case 'hourly':
          groupBy = 'DATE(created_at), EXTRACT(hour FROM created_at)';
          dateFormat = 'YYYY-MM-DD HH24:00:00';
          break;
        case 'daily':
          groupBy = 'DATE(created_at)';
          dateFormat = 'YYYY-MM-DD';
          break;
        case 'weekly':
          groupBy = 'DATE_TRUNC(\'week\', created_at)';
          dateFormat = 'YYYY-MM-DD';
          break;
        case 'monthly':
          groupBy = 'DATE_TRUNC(\'month\', created_at)';
          dateFormat = 'YYYY-MM-DD';
          break;
        default:
          groupBy = 'DATE(created_at)';
          dateFormat = 'YYYY-MM-DD';
      }

      let query, params;

      if (timeframe === 'hourly') {
        query = `
          SELECT
            TO_CHAR(created_at, 'YYYY-MM-DD HH24:00:00') as period,
            DATE(created_at) as date,
            EXTRACT(hour FROM created_at)::INTEGER as hour,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as avg_order_value,
            COUNT(DISTINCT COALESCE(customer_info->>'email', 'anonymous')) as unique_customers
          FROM payment_transactions
          WHERE tenant_id = $1
            AND status = 'completed'
            AND created_at >= NOW() - INTERVAL '${daysBack} days'
          GROUP BY DATE(created_at), EXTRACT(hour FROM created_at)
          ORDER BY DATE(created_at), EXTRACT(hour FROM created_at)
        `;
      } else {
        query = `
          SELECT
            TO_CHAR(created_at, 'YYYY-MM-DD') as period,
            DATE(created_at) as date,
            NULL as hour,
            COUNT(*) as transaction_count,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as avg_order_value,
            COUNT(DISTINCT COALESCE(customer_info->>'email', 'anonymous')) as unique_customers
          FROM payment_transactions
          WHERE tenant_id = $1
            AND status = 'completed'
            AND created_at >= NOW() - INTERVAL '${daysBack} days'
          GROUP BY DATE(created_at)
          ORDER BY DATE(created_at)
        `;
      }

      params = [tenantId];
      const result = await client.query(query, params);
      
      client.release();
      
      return result.rows.map(row => ({
        period: row.period,
        date: row.date,
        hour: row.hour || null,
        sales: parseFloat(row.total_sales || 0),
        transactions: parseInt(row.transaction_count),
        avg_order_value: parseFloat(row.avg_order_value || 0),
        unique_customers: parseInt(row.unique_customers)
      }));

    } catch (error) {
      console.error('❌ Error getting historical sales data:', error);
      return [];
    }
  }

  async generateTimeSeriesPredictions(historicalData, daysAhead, timeframe, model) {
    // Simple moving average with trend analysis for demonstration
    // In production, this would use more sophisticated ML algorithms
    
    const predictions = [];
    const windowSize = Math.min(7, Math.floor(historicalData.length / 3));
    
    // Calculate trend
    const recentData = historicalData.slice(-windowSize);
    const olderData = historicalData.slice(-windowSize * 2, -windowSize);
    
    const recentAvg = recentData.reduce((sum, d) => sum + d.sales, 0) / recentData.length;
    const olderAvg = olderData.reduce((sum, d) => sum + d.sales, 0) / olderData.length;
    
    const trendFactor = olderData.length > 0 ? (recentAvg - olderAvg) / olderAvg : 0;
    const seasonalityFactor = this.calculateSeasonality(historicalData, timeframe);

    // Generate predictions
    for (let i = 1; i <= daysAhead; i++) {
      const baseValue = recentAvg;
      const trendAdjustment = baseValue * trendFactor * (i / daysAhead);
      const seasonalAdjustment = baseValue * seasonalityFactor * Math.sin((i * Math.PI) / 7); // Weekly seasonality
      const randomVariation = baseValue * 0.1 * (Math.random() - 0.5); // ±5% random variation
      
      const predictedValue = Math.max(0, baseValue + trendAdjustment + seasonalAdjustment + randomVariation);
      
      const predictionDate = new Date();
      predictionDate.setDate(predictionDate.getDate() + i);
      
      predictions.push({
        date: predictionDate.toISOString().split('T')[0],
        predicted_value: Math.round(predictedValue * 100) / 100,
        base_value: Math.round(baseValue * 100) / 100,
        trend_factor: Math.round(trendFactor * 10000) / 10000,
        seasonal_factor: Math.round(seasonalityFactor * 10000) / 10000
      });
    }

    return predictions;
  }

  calculateSeasonality(historicalData, timeframe) {
    // Simple seasonality calculation based on day of week patterns
    if (timeframe !== 'daily' || historicalData.length < 14) {
      return 0;
    }

    const dayOfWeekSales = {};
    historicalData.forEach(data => {
      const dayOfWeek = new Date(data.date).getDay();
      if (!dayOfWeekSales[dayOfWeek]) {
        dayOfWeekSales[dayOfWeek] = [];
      }
      dayOfWeekSales[dayOfWeek].push(data.sales);
    });

    // Calculate average sales by day of week
    const avgByDay = {};
    Object.keys(dayOfWeekSales).forEach(day => {
      const sales = dayOfWeekSales[day];
      avgByDay[day] = sales.reduce((sum, s) => sum + s, 0) / sales.length;
    });

    // Calculate overall average
    const overallAvg = Object.values(avgByDay).reduce((sum, avg) => sum + avg, 0) / Object.keys(avgByDay).length;

    // Return seasonality factor (deviation from average)
    const today = new Date().getDay();
    return avgByDay[today] ? (avgByDay[today] - overallAvg) / overallAvg : 0;
  }

  async calculateConfidenceIntervals(predictions, historicalData) {
    // Calculate prediction intervals based on historical variance
    const historicalValues = historicalData.map(d => d.sales);
    const mean = historicalValues.reduce((sum, val) => sum + val, 0) / historicalValues.length;
    const variance = historicalValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / historicalValues.length;
    const stdDev = Math.sqrt(variance);

    return predictions.map(prediction => ({
      ...prediction,
      confidence_interval: {
        lower: Math.max(0, prediction.predicted_value - 1.96 * stdDev), // 95% confidence
        upper: prediction.predicted_value + 1.96 * stdDev,
        confidence_level: 0.95
      }
    }));
  }

  // =====================================================
  // DEMAND PREDICTION
  // =====================================================

  async generateDemandForecast(tenantId, productIds = null, daysAhead = 7) {
    const startTime = Date.now();
    
    try {
      console.log(`📊 Generating demand forecast for tenant ${tenantId}`);

      // Get product sales history
      const demandData = await this.getProductDemandHistory(tenantId, productIds, 60);
      
      if (demandData.length === 0) {
        throw new Error('No product demand data available');
      }

      // Group by product and generate predictions
      const productDemands = {};
      demandData.forEach(item => {
        if (!productDemands[item.product_id]) {
          productDemands[item.product_id] = [];
        }
        productDemands[item.product_id].push(item);
      });

      const predictions = {};
      for (const [productId, history] of Object.entries(productDemands)) {
        if (history.length >= 7) { // Minimum data requirement
          predictions[productId] = await this.predictProductDemand(history, daysAhead);
        }
      }

      // Store demand predictions
      const model = await this.getPredictionModel(tenantId, this.predictionTypes.DEMAND_PREDICTION, 'daily');
      await this.storeDemandPredictions(tenantId, model.id, predictions);

      return {
        success: true,
        predictions: predictions,
        products_analyzed: Object.keys(predictions).length,
        processing_time: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Demand forecast error:', error);
      return {
        success: false,
        error: error.message,
        processing_time: Date.now() - startTime
      };
    }
  }

  async getProductDemandHistory(tenantId, productIds, daysBack) {
    try {
      const client = await pool.connect();
      
      let productFilter = '';
      let params = [tenantId, daysBack];
      
      if (productIds && productIds.length > 0) {
        productFilter = 'AND oi.product_id = ANY($3)';
        params.push(productIds);
      }

      // This is a simplified query - in a real system, you'd have order_items table
      const result = await client.query(`
        SELECT
          'mock_product_' || (RANDOM() * 10)::INTEGER as product_id,
          DATE(pt.created_at) as date,
          COUNT(*) as quantity_sold,
          SUM(pt.total_amount) as revenue
        FROM payment_transactions pt
        WHERE pt.tenant_id = $1
          AND pt.status = 'completed'
          AND pt.created_at >= NOW() - INTERVAL '${daysBack} days'
        GROUP BY DATE(pt.created_at), 1
        ORDER BY date, product_id
      `, [tenantId, daysBack]);
      
      client.release();
      
      return result.rows.map(row => ({
        product_id: row.product_id,
        date: row.date,
        quantity_sold: parseInt(row.quantity_sold),
        revenue: parseFloat(row.revenue)
      }));

    } catch (error) {
      console.error('❌ Error getting product demand history:', error);
      return [];
    }
  }

  async predictProductDemand(history, daysAhead) {
    // Simple exponential smoothing for demand prediction
    const alpha = 0.3; // Smoothing parameter
    let smoothedValue = history[0].quantity_sold;
    
    // Calculate smoothed values
    for (let i = 1; i < history.length; i++) {
      smoothedValue = alpha * history[i].quantity_sold + (1 - alpha) * smoothedValue;
    }

    // Generate future predictions
    const predictions = [];
    for (let i = 1; i <= daysAhead; i++) {
      const predictionDate = new Date();
      predictionDate.setDate(predictionDate.getDate() + i);
      
      // Add some trend and seasonality
      const trendFactor = 1 + (Math.random() - 0.5) * 0.1; // ±5% trend
      const seasonalFactor = 1 + 0.2 * Math.sin((i * Math.PI) / 7); // Weekly pattern
      
      const predictedDemand = Math.round(smoothedValue * trendFactor * seasonalFactor);
      
      predictions.push({
        date: predictionDate.toISOString().split('T')[0],
        predicted_demand: Math.max(0, predictedDemand),
        confidence: 0.8 - (i * 0.05) // Confidence decreases with time
      });
    }

    return predictions;
  }

  // =====================================================
  // INVENTORY OPTIMIZATION
  // =====================================================

  async generateInventoryRecommendations(tenantId) {
    const startTime = Date.now();
    
    try {
      console.log(`📦 Generating inventory recommendations for tenant ${tenantId}`);

      // Get current inventory levels (mock data for demonstration)
      const inventoryData = await this.getCurrentInventoryLevels(tenantId);
      
      // Get demand forecasts
      const demandForecast = await this.generateDemandForecast(tenantId, null, 14);
      
      if (!demandForecast.success) {
        throw new Error('Failed to generate demand forecast for inventory optimization');
      }

      // Generate reorder recommendations
      const recommendations = await this.calculateReorderRecommendations(
        inventoryData, 
        demandForecast.predictions
      );

      // Store recommendations
      await this.storeInventoryRecommendations(tenantId, recommendations);

      return {
        success: true,
        recommendations: recommendations,
        items_analyzed: recommendations.length,
        processing_time: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Inventory optimization error:', error);
      return {
        success: false,
        error: error.message,
        processing_time: Date.now() - startTime
      };
    }
  }

  async getCurrentInventoryLevels(tenantId) {
    // Mock inventory data - in real system, this would come from inventory management
    return [
      { product_id: 'mock_product_1', current_stock: 25, min_stock: 10, max_stock: 100, unit_cost: 5.50 },
      { product_id: 'mock_product_2', current_stock: 8, min_stock: 15, max_stock: 80, unit_cost: 3.25 },
      { product_id: 'mock_product_3', current_stock: 45, min_stock: 20, max_stock: 120, unit_cost: 7.75 },
      { product_id: 'mock_product_4', current_stock: 12, min_stock: 8, max_stock: 60, unit_cost: 2.10 },
      { product_id: 'mock_product_5', current_stock: 33, min_stock: 25, max_stock: 150, unit_cost: 4.80 }
    ];
  }

  async calculateReorderRecommendations(inventoryData, demandPredictions) {
    const recommendations = [];

    inventoryData.forEach(item => {
      const productDemand = demandPredictions[item.product_id];
      
      if (!productDemand) {
        // No demand data available
        if (item.current_stock <= item.min_stock) {
          recommendations.push({
            product_id: item.product_id,
            current_stock: item.current_stock,
            recommended_order_quantity: item.max_stock - item.current_stock,
            reason: 'Below minimum stock level',
            urgency: 'high',
            estimated_cost: (item.max_stock - item.current_stock) * item.unit_cost,
            days_until_stockout: Math.floor(item.current_stock / 2) // Rough estimate
          });
        }
        return;
      }

      // Calculate total predicted demand for next 14 days
      const totalDemand = productDemand.reduce((sum, pred) => sum + pred.predicted_demand, 0);
      const avgDailyDemand = totalDemand / productDemand.length;
      
      // Calculate days until stockout
      const daysUntilStockout = avgDailyDemand > 0 ? item.current_stock / avgDailyDemand : 999;
      
      // Determine if reorder is needed
      if (daysUntilStockout <= 7 || item.current_stock <= item.min_stock) {
        const safetyStock = avgDailyDemand * 3; // 3 days safety stock
        const reorderQuantity = Math.max(
          totalDemand + safetyStock - item.current_stock,
          item.min_stock - item.current_stock
        );

        let urgency = 'low';
        if (daysUntilStockout <= 2) urgency = 'critical';
        else if (daysUntilStockout <= 5) urgency = 'high';
        else if (daysUntilStockout <= 7) urgency = 'medium';

        recommendations.push({
          product_id: item.product_id,
          current_stock: item.current_stock,
          recommended_order_quantity: Math.round(reorderQuantity),
          reason: `Predicted stockout in ${Math.round(daysUntilStockout)} days`,
          urgency: urgency,
          estimated_cost: Math.round(reorderQuantity * item.unit_cost * 100) / 100,
          days_until_stockout: Math.round(daysUntilStockout),
          predicted_demand_14d: Math.round(totalDemand),
          avg_daily_demand: Math.round(avgDailyDemand * 100) / 100
        });
      }
    });

    return recommendations.sort((a, b) => {
      const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
    });
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async getPredictionModel(tenantId, modelType, horizon) {
    try {
      const client = await pool.connect();
      
      let result = await client.query(`
        SELECT * FROM ai_prediction_models 
        WHERE tenant_id = $1 AND model_type = $2 AND prediction_horizon = $3 AND is_active = true
        ORDER BY created_at DESC 
        LIMIT 1
      `, [tenantId, modelType, horizon]);
      
      if (result.rows.length === 0) {
        // Create default model
        result = await client.query(`
          INSERT INTO ai_prediction_models (
            tenant_id, model_type, model_name, prediction_horizon, 
            algorithm_type, validation_score, is_active
          ) VALUES ($1, $2, $3, $4, $5, $6, true)
          RETURNING *
        `, [
          tenantId, 
          modelType, 
          `${modelType.replace('_', ' ')} Model`,
          horizon,
          'time_series',
          0.85
        ]);
      }
      
      client.release();
      return result.rows[0];

    } catch (error) {
      console.error('❌ Error getting prediction model:', error);
      return null;
    }
  }

  async storePredictions(tenantId, modelId, predictions, category) {
    try {
      const client = await pool.connect();
      
      for (const prediction of predictions) {
        await client.query(`
          INSERT INTO ai_predictions (
            model_id, prediction_date, prediction_category,
            predicted_value, confidence_interval, prediction_factors
          ) VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          modelId,
          prediction.date,
          category,
          prediction.predicted_value,
          JSON.stringify(prediction.confidence_interval || {}),
          JSON.stringify({
            base_value: prediction.base_value,
            trend_factor: prediction.trend_factor,
            seasonal_factor: prediction.seasonal_factor
          })
        ]);
      }
      
      client.release();

    } catch (error) {
      console.error('❌ Error storing predictions:', error);
    }
  }

  async storeDemandPredictions(tenantId, modelId, predictions) {
    try {
      const client = await pool.connect();
      
      for (const [productId, productPredictions] of Object.entries(predictions)) {
        for (const prediction of productPredictions) {
          await client.query(`
            INSERT INTO ai_predictions (
              model_id, prediction_date, prediction_category, target_identifier,
              predicted_value, confidence_interval
            ) VALUES ($1, $2, 'product_demand', $3, $4, $5)
          `, [
            modelId,
            prediction.date,
            productId,
            prediction.predicted_demand,
            JSON.stringify({ confidence: prediction.confidence })
          ]);
        }
      }
      
      client.release();

    } catch (error) {
      console.error('❌ Error storing demand predictions:', error);
    }
  }

  async storeInventoryRecommendations(tenantId, recommendations) {
    try {
      const client = await pool.connect();
      
      for (const rec of recommendations) {
        await client.query(`
          INSERT INTO ai_recommendations (
            tenant_id, recommendation_type, target_entity_type, target_entity_id,
            recommendation_title, recommendation_description, recommendation_data,
            confidence_score, expected_impact, priority_level, expires_at
          ) VALUES ($1, 'inventory_reorder', 'product', $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
          tenantId,
          rec.product_id,
          `Reorder ${rec.recommended_order_quantity} units`,
          rec.reason,
          JSON.stringify(rec),
          0.8,
          JSON.stringify({ cost_savings: rec.estimated_cost * 0.1 }),
          rec.urgency,
          new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days
        ]);
      }
      
      client.release();

    } catch (error) {
      console.error('❌ Error storing inventory recommendations:', error);
    }
  }

  async updateModelMetrics(modelId, predictionCount, processingTime) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        UPDATE ai_prediction_models 
        SET 
          last_prediction = CURRENT_TIMESTAMP,
          next_training_scheduled = CURRENT_TIMESTAMP + INTERVAL '1 week',
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [modelId]);
      
      client.release();

    } catch (error) {
      console.error('❌ Error updating model metrics:', error);
    }
  }
}

module.exports = AIPredictiveAnalyticsService;
