import { io, Socket } from 'socket.io-client';
import { KitchenOrder } from '../types';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      this.socket = io('http://localhost:4000', {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        resolve(this.socket!);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(new Error('Failed to connect to WebSocket server'));
        }
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log('WebSocket reconnected after', attemptNumber, 'attempts');
      });

      this.socket.on('reconnect_failed', () => {
        console.error('WebSocket reconnection failed');
        reject(new Error('WebSocket reconnection failed'));
      });
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Kitchen-specific event listeners
  onKitchenOrderUpdate(callback: (order: KitchenOrder) => void) {
    this.socket?.on('kitchen:order:updated', callback);
  }

  onKitchenOrderCreated(callback: (order: KitchenOrder) => void) {
    this.socket?.on('kitchen:order:created', callback);
  }

  onKitchenOrderDeleted(callback: (orderId: string) => void) {
    this.socket?.on('kitchen:order:deleted', callback);
  }

  // Join kitchen room for real-time updates
  joinKitchenRoom() {
    this.socket?.emit('join:kitchen');
  }

  leaveKitchenRoom() {
    this.socket?.emit('leave:kitchen');
  }

  // Remove event listeners
  removeKitchenListeners() {
    this.socket?.off('kitchen:order:updated');
    this.socket?.off('kitchen:order:created');
    this.socket?.off('kitchen:order:deleted');
  }

  // Get connection status
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Get socket instance for custom events
  getSocket(): Socket | null {
    return this.socket;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
