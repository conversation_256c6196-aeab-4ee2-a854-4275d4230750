import React from 'react';
import App from './App';
import EnhancedApp from './EnhancedApp';

// This component merges Basic POS and Enhanced POS into a single interface.
// For simplicity, it can render EnhancedApp as the unified interface,
// or you can merge features from both as needed.

const UnifiedPOS: React.FC = () => {
  // For now, render EnhancedApp as the unified POS interface.
  // You can extend this component to merge features from App and EnhancedApp.
  return <EnhancedApp />;
};

export default UnifiedPOS;
