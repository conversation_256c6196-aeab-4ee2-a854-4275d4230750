const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'BARPOS',
  password: '<PERSON><PERSON>@0319',
  port: 5432,
});

async function migrateTenantSchema() {
  const client = await pool.connect();
  
  try {
    console.log('Starting tenant schema migration...');
    
    // Check if tenants table exists
    const tenantsTableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tenants'
      );
    `);
    
    if (!tenantsTableExists.rows[0].exists) {
      console.log('Creating tenants table...');
      await client.query(`
        CREATE TABLE tenants (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          slug VARCHAR(100) UNIQUE NOT NULL,
          email VARCHAR(255),
          phone VARCHAR(50),
          address TEXT,
          status VARCHAR(20) DEFAULT 'active',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
    } else {
      // Alter email column to be nullable
      console.log('Altering email column to be nullable...');
      await client.query(`
        ALTER TABLE tenants
        ALTER COLUMN email DROP NOT NULL;
      `);
    }
    
    // Check if tenant_settings table exists
    const tenantSettingsExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tenant_settings'
      );
    `);
    
    if (!tenantSettingsExists.rows[0].exists) {
      console.log('Creating tenant_settings table...');
      await client.query(`
        CREATE TABLE tenant_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
          business_name VARCHAR(255),
          subscription_plan VARCHAR(50) DEFAULT 'starter',
          tax_rate DECIMAL(5,4) DEFAULT 0.08,
          currency VARCHAR(3) DEFAULT 'USD',
          features JSONB DEFAULT '{}',
          theme_primary_color VARCHAR(7) DEFAULT '#3B82F6',
          theme_secondary_color VARCHAR(7) DEFAULT '#6B7280',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
    } else {
      // Check if subscription_plan column exists
      const subscriptionPlanExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'tenant_settings' 
          AND column_name = 'subscription_plan'
        );
      `);
      
      if (!subscriptionPlanExists.rows[0].exists) {
        console.log('Adding subscription_plan column to tenant_settings...');
        await client.query(`
          ALTER TABLE tenant_settings 
          ADD COLUMN subscription_plan VARCHAR(50) DEFAULT 'starter';
        `);
      }
    }
    
    // Check if locations table exists
    const locationsExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'locations'
      );
    `);
    
    if (!locationsExists.rows[0].exists) {
      console.log('Creating locations table...');
      await client.query(`
        CREATE TABLE locations (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          address TEXT,
          phone VARCHAR(50),
          email VARCHAR(255),
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
    }
    
    // Update employees table to include tenant_id if it doesn't exist
    const employeesTenantIdExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'employees' 
        AND column_name = 'tenant_id'
      );
    `);
    
    if (!employeesTenantIdExists.rows[0].exists) {
      console.log('Adding tenant_id to employees table...');
      await client.query(`
        ALTER TABLE employees 
        ADD COLUMN tenant_id UUID,
        ADD COLUMN location_id UUID;
      `);
      
      // Create a default tenant for existing employees
      const defaultTenant = await client.query(`
        INSERT INTO tenants (name, slug, email, status)
        VALUES ('Default Restaurant', 'default', '<EMAIL>', 'active')
        RETURNING id;
      `);
      
      const tenantId = defaultTenant.rows[0].id;
      
      // Create default tenant settings
      await client.query(`
        INSERT INTO tenant_settings (
          tenant_id, business_name, subscription_plan, features
        )
        VALUES ($1, 'Default Restaurant', 'enterprise', $2);
      `, [tenantId, JSON.stringify({
        multi_location: true,
        kitchen_display: true,
        loyalty_program: true,
        inventory_management: true,
        advanced_reporting: true,
        third_party_integrations: true,
        custom_branding: true
      })]);
      
      // Update existing employees to belong to default tenant
      await client.query(`
        UPDATE employees SET tenant_id = $1 WHERE tenant_id IS NULL;
      `, [tenantId]);
      
      // Add foreign key constraints
      await client.query(`
        ALTER TABLE employees 
        ADD CONSTRAINT fk_employees_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(id);
      `);
      
      await client.query(`
        ALTER TABLE employees 
        ADD CONSTRAINT fk_employees_location 
        FOREIGN KEY (location_id) REFERENCES locations(id);
      `);
    }
    
    // Create audit_logs table if it doesn't exist
    const auditLogsExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'audit_logs'
      );
    `);
    
    if (!auditLogsExists.rows[0].exists) {
      console.log('Creating audit_logs table...');
      await client.query(`
        CREATE TABLE audit_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID REFERENCES tenants(id),
          user_id UUID,
          action VARCHAR(100) NOT NULL,
          resource_type VARCHAR(100),
          resource_id UUID,
          ip_address INET,
          user_agent TEXT,
          risk_level VARCHAR(20) DEFAULT 'low',
          details JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
    }
    
    console.log('Tenant schema migration completed successfully!');
    
  } catch (error) {
    console.error('Migration error:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

migrateTenantSchema().catch(console.error);
