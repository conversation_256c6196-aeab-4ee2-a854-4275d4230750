services:
  js-backend:
    build:
      context: ./backend
      dockerfile: ../backendDockerfile
    container_name: js-backend
    restart: unless-stopped
    init: true
    # env_file: ./backend/.env  # Uncomment if .env file exists
    ports:
      - "4000:4000"  # Expose backend API port
    networks:
      - appnet
    # The backend currently uses in-memory data, but is designed for future PostgreSQL integration
    # If you add a database, add 'depends_on' here

  ts-project:
    build:
      context: ./project
      dockerfile: ../projectDockerfile
    container_name: ts-project
    restart: unless-stopped
    init: true
    # env_file: ./project/.env  # Uncomment if .env file exists
    ports:
      - "4001:4000"  # Expose project app on a different host port if needed
    networks:
      - appnet
    # Add 'depends_on' if this service requires the backend or a database

# Shared network for inter-service communication
networks:
  appnet:
    driver: bridge
