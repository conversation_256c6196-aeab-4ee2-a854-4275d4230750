// 💰 BARPOS Billing API Routes
// Phase 7B: Customer Systems & Billing
// Comprehensive billing and subscription management endpoints

const express = require('express');
const router = express.Router();
const billingService = require('../services/billingService');
const { authenticateToken, requireRole } = require('../middleware/auth');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// ================================
// SUBSCRIPTION MANAGEMENT
// ================================

// Get available subscription plans
router.get('/plans', async (req, res) => {
    try {
        const plans = billingService.getPlans();
        res.json(plans);
    } catch (error) {
        console.error('Error getting plans:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get subscription plans',
            error: error.message 
        });
    }
});

// Create customer and start trial
router.post('/customers', authenticateToken, requireRole(['super_admin', 'tenant_admin']), async (req, res) => {
    try {
        const { tenantId, plan = 'starter' } = req.body;
        
        // Get tenant information
        const { Pool } = require('pg');
        const pool = new Pool({
            user: process.env.POSTGRES_USER || 'BARPOS',
            host: process.env.POSTGRES_HOST || 'localhost',
            database: process.env.POSTGRES_DB || 'BARPOS',
            password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
            port: process.env.POSTGRES_PORT || 5432,
        });

        const tenantResult = await pool.query('SELECT * FROM tenants WHERE id = $1', [tenantId]);
        if (tenantResult.rows.length === 0) {
            return res.status(404).json({ success: false, message: 'Tenant not found' });
        }

        const tenant = tenantResult.rows[0];
        const tenantData = {
            id: tenant.id,
            name: tenant.name,
            email: tenant.email,
            phone: tenant.phone,
            address: tenant.address,
            plan: plan
        };

        const result = await billingService.createCustomer(tenantData);
        
        res.json({
            success: true,
            message: 'Customer created successfully',
            customer: result.customer,
            trial_end: result.trial_end
        });

    } catch (error) {
        console.error('Error creating customer:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to create customer',
            error: error.message 
        });
    }
});

// Create subscription
router.post('/subscriptions', authenticateToken, requireRole(['super_admin', 'tenant_admin']), async (req, res) => {
    try {
        const { tenantId, planId, paymentMethodId } = req.body;

        if (!tenantId || !planId || !paymentMethodId) {
            return res.status(400).json({ 
                success: false, 
                message: 'Missing required fields: tenantId, planId, paymentMethodId' 
            });
        }

        const result = await billingService.createSubscription(tenantId, planId, paymentMethodId);
        
        res.json({
            success: true,
            message: 'Subscription created successfully',
            subscription: result.subscription,
            client_secret: result.client_secret
        });

    } catch (error) {
        console.error('Error creating subscription:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to create subscription',
            error: error.message 
        });
    }
});

// Update subscription
router.put('/subscriptions/:tenantId', authenticateToken, requireRole(['super_admin', 'tenant_admin']), async (req, res) => {
    try {
        const { tenantId } = req.params;
        const { planId } = req.body;

        if (!planId) {
            return res.status(400).json({ 
                success: false, 
                message: 'Missing required field: planId' 
            });
        }

        const result = await billingService.updateSubscription(parseInt(tenantId), planId);
        
        res.json({
            success: true,
            message: 'Subscription updated successfully',
            subscription: result.subscription
        });

    } catch (error) {
        console.error('Error updating subscription:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to update subscription',
            error: error.message 
        });
    }
});

// Cancel subscription
router.delete('/subscriptions/:tenantId', authenticateToken, requireRole(['super_admin', 'tenant_admin']), async (req, res) => {
    try {
        const { tenantId } = req.params;
        const { reason = 'customer_request' } = req.body;

        const result = await billingService.cancelSubscription(parseInt(tenantId), reason);
        
        res.json({
            success: true,
            message: 'Subscription cancelled successfully',
            subscription: result.subscription
        });

    } catch (error) {
        console.error('Error cancelling subscription:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to cancel subscription',
            error: error.message 
        });
    }
});

// ================================
// BILLING INFORMATION
// ================================

// Get billing information for tenant
router.get('/info/:tenantId', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.params;
        
        // Check if user has access to this tenant
        if (req.user.role !== 'super_admin' && req.user.tenantId !== parseInt(tenantId)) {
            return res.status(403).json({ success: false, message: 'Access denied' });
        }

        const result = await billingService.getBillingInfo(parseInt(tenantId));
        
        if (!result.success) {
            return res.status(404).json(result);
        }

        res.json({
            success: true,
            billing: result.billing
        });

    } catch (error) {
        console.error('Error getting billing info:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get billing information',
            error: error.message 
        });
    }
});

// Get usage statistics
router.get('/usage/:tenantId', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.params;
        
        // Check if user has access to this tenant
        if (req.user.role !== 'super_admin' && req.user.tenantId !== parseInt(tenantId)) {
            return res.status(403).json({ success: false, message: 'Access denied' });
        }

        const result = await billingService.getUsageStats(parseInt(tenantId));
        
        res.json({
            success: true,
            usage: result.usage
        });

    } catch (error) {
        console.error('Error getting usage stats:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get usage statistics',
            error: error.message 
        });
    }
});

// ================================
// PAYMENT METHODS
// ================================

// Create setup intent for payment method
router.post('/setup-intent', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.body;

        // Get customer ID
        const billingInfo = await billingService.getBillingInfo(tenantId);
        if (!billingInfo.success) {
            return res.status(404).json({ success: false, message: 'Customer not found' });
        }

        const setupIntent = await stripe.setupIntents.create({
            customer: billingInfo.billing.stripe_customer_id,
            payment_method_types: ['card'],
            usage: 'off_session'
        });

        res.json({
            success: true,
            client_secret: setupIntent.client_secret
        });

    } catch (error) {
        console.error('Error creating setup intent:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to create setup intent',
            error: error.message 
        });
    }
});

// Get payment methods for customer
router.get('/payment-methods/:tenantId', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.params;

        // Get customer ID
        const billingInfo = await billingService.getBillingInfo(parseInt(tenantId));
        if (!billingInfo.success) {
            return res.status(404).json({ success: false, message: 'Customer not found' });
        }

        const paymentMethods = await stripe.paymentMethods.list({
            customer: billingInfo.billing.stripe_customer_id,
            type: 'card',
        });

        res.json({
            success: true,
            payment_methods: paymentMethods.data
        });

    } catch (error) {
        console.error('Error getting payment methods:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get payment methods',
            error: error.message 
        });
    }
});

// ================================
// INVOICES
// ================================

// Get invoices for tenant
router.get('/invoices/:tenantId', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.params;
        const { limit = 10, starting_after } = req.query;

        // Check access
        if (req.user.role !== 'super_admin' && req.user.tenantId !== parseInt(tenantId)) {
            return res.status(403).json({ success: false, message: 'Access denied' });
        }

        // Get customer ID
        const billingInfo = await billingService.getBillingInfo(parseInt(tenantId));
        if (!billingInfo.success) {
            return res.status(404).json({ success: false, message: 'Customer not found' });
        }

        const invoices = await stripe.invoices.list({
            customer: billingInfo.billing.stripe_customer_id,
            limit: parseInt(limit),
            starting_after: starting_after
        });

        res.json({
            success: true,
            invoices: invoices.data,
            has_more: invoices.has_more
        });

    } catch (error) {
        console.error('Error getting invoices:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get invoices',
            error: error.message 
        });
    }
});

// ================================
// WEBHOOKS
// ================================

// Stripe webhook endpoint
router.post('/webhooks/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        await billingService.handleWebhook(event);
        res.json({ received: true });
    } catch (error) {
        console.error('Error handling webhook:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to handle webhook',
            error: error.message 
        });
    }
});

// ================================
// ADMIN ENDPOINTS
// ================================

// Get all customers (super admin only)
router.get('/admin/customers', authenticateToken, requireRole(['super_admin']), async (req, res) => {
    try {
        const { Pool } = require('pg');
        const pool = new Pool({
            user: process.env.POSTGRES_USER || 'BARPOS',
            host: process.env.POSTGRES_HOST || 'localhost',
            database: process.env.POSTGRES_DB || 'BARPOS',
            password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
            port: process.env.POSTGRES_PORT || 5432,
        });

        const result = await pool.query(`
            SELECT 
                bc.*,
                t.name as tenant_name,
                t.email as tenant_email,
                sp.name as plan_name,
                sp.price as plan_price
            FROM billing_customers bc
            JOIN tenants t ON bc.tenant_id = t.id
            JOIN subscription_plans sp ON bc.plan_id = sp.id
            ORDER BY bc.created_at DESC
        `);

        res.json({
            success: true,
            customers: result.rows
        });

    } catch (error) {
        console.error('Error getting customers:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get customers',
            error: error.message 
        });
    }
});

// Get revenue analytics (super admin only)
router.get('/admin/analytics', authenticateToken, requireRole(['super_admin']), async (req, res) => {
    try {
        const { Pool } = require('pg');
        const pool = new Pool({
            user: process.env.POSTGRES_USER || 'BARPOS',
            host: process.env.POSTGRES_HOST || 'localhost',
            database: process.env.POSTGRES_DB || 'BARPOS',
            password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
            port: process.env.POSTGRES_PORT || 5432,
        });

        // Get monthly revenue
        const revenueResult = await pool.query(`
            SELECT * FROM monthly_revenue 
            ORDER BY month DESC 
            LIMIT 12
        `);

        // Get current metrics
        const metricsResult = await pool.query(`
            SELECT 
                COUNT(*) as total_customers,
                COUNT(*) FILTER (WHERE status = 'active') as active_customers,
                COUNT(*) FILTER (WHERE status = 'trialing') as trial_customers,
                COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_customers,
                AVG(CASE WHEN plan_id = 'starter' THEN 99 
                         WHEN plan_id = 'professional' THEN 299 
                         WHEN plan_id = 'enterprise' THEN 799 
                         ELSE 0 END) as average_plan_value
            FROM billing_customers
        `);

        res.json({
            success: true,
            analytics: {
                monthly_revenue: revenueResult.rows,
                current_metrics: metricsResult.rows[0]
            }
        });

    } catch (error) {
        console.error('Error getting analytics:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Failed to get analytics',
            error: error.message 
        });
    }
});

module.exports = router;
