#!/bin/bash

# 🚀 PHASE 7 PRODUCTION DEPLOYMENT SETUP SCRIPT
# Multi-Tenant Restaurant POS System - Production Launch
# Version: 1.0.0
# Date: 2025-06-14

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Configuration
PROJECT_NAME="barpos-production"
ENVIRONMENT="production"
NODE_VERSION="18"
POSTGRES_VERSION="15"

log "🚀 Starting Phase 7 Production Deployment Setup"
log "Project: $PROJECT_NAME"
log "Environment: $ENVIRONMENT"

# Check prerequisites
check_prerequisites() {
    log "📋 Checking prerequisites..."
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root"
    fi
    
    # Check required commands
    command -v node >/dev/null 2>&1 || error "Node.js is required but not installed"
    command -v npm >/dev/null 2>&1 || error "npm is required but not installed"
    command -v git >/dev/null 2>&1 || error "git is required but not installed"
    command -v docker >/dev/null 2>&1 || error "Docker is required but not installed"
    
    log "✅ Prerequisites check passed"
}

# Setup production environment
setup_production_env() {
    log "🏗️ Setting up production environment..."
    
    # Create production directory structure
    mkdir -p production/{backend,frontend,database,nginx,ssl,logs,backups}
    mkdir -p production/scripts/{deploy,backup,monitoring}
    mkdir -p production/config/{nginx,ssl,env}
    
    log "✅ Production directory structure created"
}

# Setup database for production
setup_production_database() {
    log "🗄️ Setting up production database..."
    
    # Create production database configuration
    cat > production/database/docker-compose.yml << EOF
version: '3.8'
services:
  postgres:
    image: postgres:${POSTGRES_VERSION}
    container_name: barpos-postgres-prod
    environment:
      POSTGRES_DB: BARPOS_PROD
      POSTGRES_USER: barpos_prod
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d
      - ../backups:/backups
    restart: unless-stopped
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB

  redis:
    image: redis:7-alpine
    container_name: barpos-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass \${REDIS_PASSWORD}

volumes:
  postgres_data:
  redis_data:
EOF

    # Create database initialization script
    cat > production/database/init/01-init-database.sql << EOF
-- Production Database Initialization
-- BARPOS Multi-Tenant Restaurant POS System

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create production-specific indexes for performance
-- (All Phase 1-6 tables are already created, adding production optimizations)

-- Performance indexes for high-traffic queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_created 
ON orders(tenant_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_tenant_status 
ON payments(tenant_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_tenant_active 
ON employees(tenant_id, is_active) WHERE is_active = true;

-- Analytics indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_analytics 
ON orders(tenant_id, status, created_at, total_amount);

-- AI service indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fraud_detection_realtime 
ON ai_fraud_detection(tenant_id, created_at DESC, risk_score DESC);

-- Production monitoring views
CREATE OR REPLACE VIEW production_health AS
SELECT 
    'database' as component,
    'healthy' as status,
    NOW() as last_check,
    (SELECT COUNT(*) FROM tenants) as tenant_count,
    (SELECT COUNT(*) FROM employees WHERE is_active = true) as active_users,
    (SELECT COUNT(*) FROM orders WHERE created_at > NOW() - INTERVAL '24 hours') as orders_24h;

-- Production performance monitoring
CREATE OR REPLACE VIEW production_performance AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY tablename, attname;

COMMENT ON VIEW production_health IS 'Production system health monitoring';
COMMENT ON VIEW production_performance IS 'Production database performance metrics';
EOF

    log "✅ Production database configuration created"
}

# Setup backend for production
setup_production_backend() {
    log "⚙️ Setting up production backend..."
    
    # Create production backend Dockerfile
    cat > production/backend/Dockerfile << EOF
FROM node:${NODE_VERSION}-alpine

# Install production dependencies
RUN apk add --no-cache \
    postgresql-client \
    curl \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S barpos -u 1001 -G nodejs

# Change ownership
RUN chown -R barpos:nodejs /app
USER barpos

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:4000/health || exit 1

# Expose port
EXPOSE 4000

# Start application
CMD ["node", "working-server.js"]
EOF

    # Create production environment template
    cat > production/config/env/production.env.template << EOF
# 🚀 BARPOS Production Environment Configuration
# Copy to production.env and fill in actual values

# Application
NODE_ENV=production
PORT=4000
APP_NAME=BARPOS Production
VERSION=1.0.0

# Database
DATABASE_URL=***********************************************/BARPOS_PROD
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=BARPOS_PROD
POSTGRES_USER=barpos_prod
POSTGRES_PASSWORD=CHANGE_THIS_PASSWORD

# Redis
REDIS_URL=redis://:PASSWORD@redis:6379
REDIS_PASSWORD=CHANGE_THIS_PASSWORD

# JWT
JWT_SECRET=CHANGE_THIS_TO_SECURE_SECRET_KEY
JWT_EXPIRES_IN=24h

# Encryption
ENCRYPTION_KEY=CHANGE_THIS_TO_32_CHAR_SECRET_KEY

# Payment Gateways
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Storage
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_REGION=us-east-1
AWS_S3_BUCKET=barpos-production-files

# Monitoring
SENTRY_DSN=https://...
NEW_RELIC_LICENSE_KEY=...

# Security
CORS_ORIGIN=https://yourapp.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Features
ENABLE_AI_FEATURES=true
ENABLE_GLOBAL_FEATURES=true
ENABLE_ANALYTICS=true
EOF

    log "✅ Production backend configuration created"
}

# Setup frontend for production
setup_production_frontend() {
    log "🎨 Setting up production frontend..."
    
    # Create production frontend Dockerfile
    cat > production/frontend/Dockerfile << EOF
# Multi-stage build for production
FROM node:${NODE_VERSION}-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build for production
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
EOF

    # Create production nginx configuration
    cat > production/frontend/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript 
               application/javascript application/xml+rss 
               application/json application/xml;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Handle React Router
        location / {
            try_files \$uri \$uri/ /index.html;
        }
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # API proxy (if needed)
        location /api/ {
            proxy_pass http://backend:4000;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF

    log "✅ Production frontend configuration created"
}

# Setup monitoring and logging
setup_monitoring() {
    log "📊 Setting up monitoring and logging..."
    
    # Create monitoring docker-compose
    cat > production/monitoring/docker-compose.yml << EOF
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: barpos-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: barpos-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=\${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped

volumes:
  prometheus_data:
  grafana_data:
EOF

    # Create Prometheus configuration
    cat > production/monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'barpos-backend'
    static_configs:
      - targets: ['backend:4000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['frontend:80']
    scrape_interval: 30s
EOF

    log "✅ Monitoring configuration created"
}

# Create deployment scripts
create_deployment_scripts() {
    log "📜 Creating deployment scripts..."
    
    # Main deployment script
    cat > production/scripts/deploy/deploy.sh << 'EOF'
#!/bin/bash
# Production deployment script

set -e

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "🚀 Starting production deployment..."

# Pull latest code
git pull origin main

# Build and deploy backend
log "📦 Building backend..."
cd backend
docker build -t barpos-backend:latest .
cd ..

# Build and deploy frontend
log "🎨 Building frontend..."
cd frontend
docker build -t barpos-frontend:latest .
cd ..

# Deploy with docker-compose
log "🚀 Deploying services..."
docker-compose -f production/docker-compose.yml up -d

# Run database migrations
log "🗄️ Running database migrations..."
docker exec barpos-backend-prod npm run migrate

# Health check
log "🏥 Running health checks..."
sleep 30
curl -f http://localhost:4000/health || exit 1
curl -f http://localhost:80/health || exit 1

log "✅ Deployment completed successfully!"
EOF

    chmod +x production/scripts/deploy/deploy.sh

    # Backup script
    cat > production/scripts/backup/backup.sh << 'EOF'
#!/bin/bash
# Production backup script

set -e

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "💾 Starting production backup..."

# Database backup
log "🗄️ Backing up database..."
docker exec barpos-postgres-prod pg_dump -U barpos_prod BARPOS_PROD > "$BACKUP_DIR/database_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/database_$DATE.sql"

# Upload to cloud storage (AWS S3 example)
if [ ! -z "$AWS_S3_BUCKET" ]; then
    log "☁️ Uploading to cloud storage..."
    aws s3 cp "$BACKUP_DIR/database_$DATE.sql.gz" "s3://$AWS_S3_BUCKET/backups/"
fi

# Clean old backups (keep last 30 days)
find $BACKUP_DIR -name "database_*.sql.gz" -mtime +30 -delete

log "✅ Backup completed successfully!"
EOF

    chmod +x production/scripts/backup/backup.sh

    log "✅ Deployment scripts created"
}

# Main execution
main() {
    log "🚀 Phase 7 Production Deployment Setup Starting..."
    
    check_prerequisites
    setup_production_env
    setup_production_database
    setup_production_backend
    setup_production_frontend
    setup_monitoring
    create_deployment_scripts
    
    log "✅ Phase 7 Production Setup Complete!"
    log ""
    log "📋 Next Steps:"
    log "1. Copy production/config/env/production.env.template to production.env"
    log "2. Fill in all environment variables with production values"
    log "3. Set up SSL certificates in production/ssl/"
    log "4. Configure your domain and DNS settings"
    log "5. Run: cd production && docker-compose up -d"
    log "6. Set up monitoring dashboards in Grafana"
    log "7. Configure automated backups with cron"
    log ""
    log "🎉 Ready for Phase 7 Production Launch!"
}

# Run main function
main "$@"
