<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug POS Routing</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 600;
        }
        .success { background: rgba(34, 197, 94, 0.2); border: 2px solid #22C55E; }
        .error { background: rgba(239, 68, 68, 0.2); border: 2px solid #EF4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border: 2px solid #F59E0B; }
        .info { background: rgba(59, 130, 246, 0.2); border: 2px solid #3B82F6; }
        
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #2563EB;
            transform: translateY(-1px);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
        
        .log {
            background: #1e293b;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 POS Routing Debug Center</h1>
            <p>This page will help diagnose why the restructured interface isn't loading</p>
            
            <div id="current-status">
                <!-- Status will be populated by JavaScript -->
            </div>
            
            <div class="test-grid">
                <div class="test-card">
                    <h3>🚀 Quick Tests</h3>
                    <button onclick="testDirect()">Test Direct Route</button>
                    <button onclick="testWithParams()">Test With Parameters</button>
                    <button onclick="testLocalStorage()">Test localStorage</button>
                    <button onclick="clearAll()">Clear Everything</button>
                </div>
                
                <div class="test-card">
                    <h3>🔍 Diagnostics</h3>
                    <button onclick="checkRouting()">Check Routing Logic</button>
                    <button onclick="checkComponents()">Check Components</button>
                    <button onclick="checkConsole()">Check Console</button>
                    <button onclick="exportDebug()">Export Debug Info</button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>📊 Current System State</h2>
            <div id="system-state">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
        
        <div class="card">
            <h2>🧪 Test Results</h2>
            <div id="test-results" class="log">
                Initializing debug session...\n
            </div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="card">
            <h2>🎯 Recommended Actions</h2>
            <div id="recommendations">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        let logContainer;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            
            if (!logContainer) {
                logContainer = document.getElementById('test-results');
            }
            
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }
        
        function updateCurrentStatus() {
            const container = document.getElementById('current-status');
            
            // Check all the conditions
            const conditions = {
                'URL has industry param': window.location.search.includes('industry=true'),
                'URL has restructured param': window.location.search.includes('restructured=true'),
                'localStorage useRestructuredPOS': localStorage.getItem('useRestructuredPOS') === 'true',
                'localStorage useIndustryStandardPOS': localStorage.getItem('useIndustryStandardPOS') === 'true',
                'URL has test param': window.location.search.includes('test=true'),
                'Pathname includes industry-standard': window.location.pathname.includes('industry-standard')
            };
            
            let statusHTML = '<h3>🔍 Current Conditions</h3>';
            
            for (const [condition, result] of Object.entries(conditions)) {
                const statusClass = result ? 'success' : 'error';
                const icon = result ? '✅' : '❌';
                statusHTML += `<div class="status ${statusClass}">${icon} ${condition}: ${result}</div>`;
            }
            
            // Calculate what should happen
            const shouldUseIndustry = conditions['URL has industry param'] || 
                                    conditions['URL has restructured param'] || 
                                    conditions['localStorage useIndustryStandardPOS'] || 
                                    conditions['localStorage useRestructuredPOS'] ||
                                    conditions['Pathname includes industry-standard'];
                                    
            const shouldUseRestructured = conditions['localStorage useRestructuredPOS'] || 
                                        conditions['URL has restructured param'];
            
            statusHTML += '<h3>🎯 Expected Behavior</h3>';
            statusHTML += `<div class="status ${shouldUseIndustry ? 'success' : 'error'}">
                ${shouldUseIndustry ? '✅' : '❌'} Should load Industry Standard POS: ${shouldUseIndustry}
            </div>`;
            statusHTML += `<div class="status ${shouldUseRestructured ? 'success' : 'error'}">
                ${shouldUseRestructured ? '✅' : '❌'} Should load Restructured Interface: ${shouldUseRestructured}
            </div>`;
            
            container.innerHTML = statusHTML;
            
            log(`Status updated - Industry: ${shouldUseIndustry}, Restructured: ${shouldUseRestructured}`);
        }
        
        function updateSystemState() {
            const container = document.getElementById('system-state');
            
            const state = {
                'Current URL': window.location.href,
                'Pathname': window.location.pathname,
                'Search Params': window.location.search || 'none',
                'Hash': window.location.hash || 'none',
                'User Agent': navigator.userAgent.split(' ').slice(-2).join(' '),
                'Viewport': `${window.innerWidth}x${window.innerHeight}`,
                'localStorage Keys': Object.keys(localStorage).join(', ') || 'none',
                'sessionStorage Keys': Object.keys(sessionStorage).join(', ') || 'none'
            };
            
            let stateHTML = '<table style="width: 100%; border-collapse: collapse;">';
            for (const [key, value] of Object.entries(state)) {
                stateHTML += `
                    <tr style="border-bottom: 1px solid rgba(255,255,255,0.2);">
                        <td style="padding: 8px; font-weight: 600; width: 200px;">${key}:</td>
                        <td style="padding: 8px; font-family: monospace; word-break: break-all; font-size: 12px;">${value}</td>
                    </tr>
                `;
            }
            stateHTML += '</table>';
            
            container.innerHTML = stateHTML;
        }
        
        function updateRecommendations() {
            const container = document.getElementById('recommendations');
            
            const hasIndustry = window.location.search.includes('industry=true');
            const hasRestructured = window.location.search.includes('restructured=true');
            const hasLocalStorage = localStorage.getItem('useRestructuredPOS') === 'true';
            
            let recommendations = [];
            
            if (!hasIndustry && !hasRestructured && !hasLocalStorage) {
                recommendations.push({
                    priority: 'high',
                    action: 'Enable Restructured Mode',
                    description: 'No restructured flags detected. Use the quick test buttons above.',
                    button: 'testWithParams()'
                });
            }
            
            if (hasLocalStorage && !hasIndustry) {
                recommendations.push({
                    priority: 'medium',
                    action: 'Add URL Parameters',
                    description: 'localStorage flag is set but URL parameters are missing.',
                    button: 'testWithParams()'
                });
            }
            
            recommendations.push({
                priority: 'low',
                action: 'Test Direct Route',
                description: 'Try the direct test route to bypass routing logic.',
                button: 'testDirect()'
            });
            
            let recHTML = '';
            recommendations.forEach((rec, index) => {
                const priorityClass = rec.priority === 'high' ? 'error' : rec.priority === 'medium' ? 'warning' : 'info';
                recHTML += `
                    <div class="status ${priorityClass}">
                        <strong>${index + 1}. ${rec.action}</strong><br>
                        ${rec.description}<br>
                        <button onclick="${rec.button}" style="margin-top: 8px;">Try This</button>
                    </div>
                `;
            });
            
            container.innerHTML = recHTML;
        }
        
        // Test functions
        function testDirect() {
            log('Testing direct route...');
            const url = 'http://localhost:5173/?test=true';
            log(`Opening: ${url}`);
            window.open(url, '_blank');
        }
        
        function testWithParams() {
            log('Testing with all parameters...');
            localStorage.setItem('useRestructuredPOS', 'true');
            localStorage.setItem('useIndustryStandardPOS', 'true');
            const url = 'http://localhost:5173/?industry=true&restructured=true';
            log(`Opening: ${url}`);
            window.open(url, '_blank');
        }
        
        function testLocalStorage() {
            log('Setting localStorage flags...');
            localStorage.setItem('useRestructuredPOS', 'true');
            localStorage.setItem('useIndustryStandardPOS', 'true');
            log('Flags set, refreshing current page...');
            updateCurrentStatus();
            updateSystemState();
            updateRecommendations();
        }
        
        function clearAll() {
            log('Clearing all storage and cache...');
            localStorage.clear();
            sessionStorage.clear();
            log('Storage cleared');
            updateCurrentStatus();
            updateSystemState();
            updateRecommendations();
        }
        
        function checkRouting() {
            log('Checking routing logic...');
            
            // Simulate the main.tsx logic
            const isTestRoute = window.location.pathname.includes('test-restructured') ||
                               window.location.search.includes('test=true');
            
            const useIndustryStandard = window.location.pathname.includes('industry-standard') ||
                                       window.location.search.includes('industry=true') ||
                                       window.location.search.includes('restructured=true') ||
                                       localStorage.getItem('useIndustryStandardPOS') === 'true' ||
                                       localStorage.getItem('useRestructuredPOS') === 'true';
            
            log(`Routing check results:`);
            log(`  isTestRoute: ${isTestRoute}`);
            log(`  useIndustryStandard: ${useIndustryStandard}`);
            
            if (isTestRoute) {
                log('✅ Should load TestRestructuredPOS component');
            } else if (useIndustryStandard) {
                log('✅ Should load IndustryStandardPOSSystem');
                
                // Check restructured logic within IndustryStandardPOSSystem
                const useRestructured = localStorage.getItem('useRestructuredPOS') === 'true' ||
                                       window.location.search.includes('restructured=true');
                log(`  Within IndustryStandardPOSSystem, useRestructured: ${useRestructured}`);
                
                if (useRestructured) {
                    log('✅ Should render RestructuredIndustryPOS component');
                } else {
                    log('❌ Should render standard IndustryStandardPOSLayout');
                }
            } else {
                log('❌ Should load UnifiedPOSSystem (legacy)');
            }
        }
        
        function checkComponents() {
            log('Checking component availability...');
            
            // This is a simplified check - in a real app you'd need to actually import
            const components = [
                'IndustryStandardPOSSystem',
                'RestructuredIndustryPOS',
                'TestRestructuredPOS',
                'UnifiedPOSSystem'
            ];
            
            components.forEach(comp => {
                log(`  ${comp}: Available (would need actual import test)`);
            });
        }
        
        function checkConsole() {
            log('Opening browser console for manual inspection...');
            log('Look for these messages in the main console:');
            log('  - "🚀 Loading Industry Standard POS System"');
            log('  - "🔧 RESTRUCTURED CHECK:"');
            log('  - "🚀 RENDERING RESTRUCTURED INTERFACE"');
            log('  - "🚀 RESTRUCTURED INDUSTRY POS LOADING!"');
            
            // Try to open console (this doesn't actually work, but shows intent)
            console.log('🔧 DEBUG: Check these console messages in the main app');
        }
        
        function exportDebug() {
            const debugInfo = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                localStorage: Object.fromEntries(Object.entries(localStorage)),
                sessionStorage: Object.fromEntries(Object.entries(sessionStorage)),
                userAgent: navigator.userAgent,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                conditions: {
                    hasIndustryParam: window.location.search.includes('industry=true'),
                    hasRestructuredParam: window.location.search.includes('restructured=true'),
                    hasTestParam: window.location.search.includes('test=true'),
                    hasLocalStorageRestructured: localStorage.getItem('useRestructuredPOS') === 'true',
                    hasLocalStorageIndustry: localStorage.getItem('useIndustryStandardPOS') === 'true'
                }
            };
            
            const debugText = JSON.stringify(debugInfo, null, 2);
            
            // Copy to clipboard
            navigator.clipboard.writeText(debugText).then(() => {
                log('Debug info copied to clipboard');
                alert('Debug information copied to clipboard!');
            }).catch(() => {
                log('Failed to copy to clipboard, showing in console');
                console.log('DEBUG INFO:', debugInfo);
            });
        }
        
        function clearLog() {
            document.getElementById('test-results').textContent = 'Log cleared...\n';
            log('Debug log cleared');
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('test-results');
            log('Debug routing page loaded');
            updateCurrentStatus();
            updateSystemState();
            updateRecommendations();
            
            // Auto-refresh every 10 seconds
            setInterval(() => {
                updateCurrentStatus();
                updateSystemState();
            }, 10000);
        });
        
        log('Debug routing system initialized');
    </script>
</body>
</html>
