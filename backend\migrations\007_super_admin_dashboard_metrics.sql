-- Super Admin Dashboard Metrics Migration
-- This migration adds tables for system metrics, analytics, and monitoring

-- System metrics table for real-time monitoring
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(50) NOT NULL, -- 'performance', 'usage', 'revenue', etc.
    metric_name VARCHAR(100) NOT NULL, -- 'cpu_usage', 'active_users', 'total_revenue', etc.
    value DECIMAL(15,4) NOT NULL,
    unit VARCHAR(20), -- '%', 'ms', 'USD', 'count', etc.
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE, -- NULL for system-wide metrics
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE, -- NULL for tenant-wide metrics
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}', -- Additional context data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_system_metrics_type_time ON system_metrics(metric_type, recorded_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_tenant_time ON system_metrics(tenant_id, recorded_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time ON system_metrics(metric_name, recorded_at);

-- System activity log for audit trail
CREATE TABLE IF NOT EXISTS system_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_type VARCHAR(50) NOT NULL, -- 'tenant_created', 'user_login', 'system_maintenance', etc.
    activity_description TEXT NOT NULL,
    actor_type VARCHAR(20) NOT NULL, -- 'user', 'system', 'admin'
    actor_id UUID, -- employee_id or system identifier
    actor_name VARCHAR(255),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE, -- NULL for system-wide activities
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'success')),
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for activity log
CREATE INDEX IF NOT EXISTS idx_activity_log_type_time ON system_activity_log(activity_type, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_log_tenant_time ON system_activity_log(tenant_id, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_log_actor ON system_activity_log(actor_id, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_log_severity ON system_activity_log(severity, created_at);

-- Daily tenant analytics summary
CREATE TABLE IF NOT EXISTS tenant_daily_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_orders INTEGER DEFAULT 0,
    total_revenue DECIMAL(15,2) DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    avg_order_value DECIMAL(10,2) DEFAULT 0,
    peak_hour INTEGER CHECK (peak_hour BETWEEN 0 AND 23),
    new_customers INTEGER DEFAULT 0,
    returning_customers INTEGER DEFAULT 0,
    payment_methods JSONB DEFAULT '{}', -- {"card": 50, "cash": 30, "mobile": 20}
    top_products JSONB DEFAULT '[]', -- [{"id": "1", "name": "Coffee", "quantity": 100}]
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, date)
);

-- Index for daily analytics
CREATE INDEX IF NOT EXISTS idx_tenant_analytics_date ON tenant_daily_analytics(tenant_id, date);

-- System performance snapshots
CREATE TABLE IF NOT EXISTS system_performance_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cpu_usage DECIMAL(5,2), -- Percentage
    memory_usage DECIMAL(5,2), -- Percentage
    disk_usage DECIMAL(5,2), -- Percentage
    active_connections INTEGER,
    database_connections INTEGER,
    response_time_avg DECIMAL(8,3), -- Milliseconds
    response_time_p95 DECIMAL(8,3), -- 95th percentile response time
    error_rate DECIMAL(5,4), -- Percentage
    throughput INTEGER, -- Requests per minute
    tenant_count INTEGER,
    active_tenant_count INTEGER,
    total_users_online INTEGER,
    queue_size INTEGER, -- Background job queue size
    cache_hit_rate DECIMAL(5,2), -- Percentage
    metadata JSONB DEFAULT '{}'
);

-- Index for performance snapshots
CREATE INDEX IF NOT EXISTS idx_performance_snapshots_time ON system_performance_snapshots(timestamp);

-- API usage tracking
CREATE TABLE IF NOT EXISTS api_usage_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time DECIMAL(8,3), -- Milliseconds
    request_size INTEGER, -- Bytes
    response_size INTEGER, -- Bytes
    user_agent TEXT,
    ip_address INET,
    api_key_id UUID, -- Reference to API keys if applicable
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for API usage
CREATE INDEX IF NOT EXISTS idx_api_usage_tenant_time ON api_usage_log(tenant_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint_time ON api_usage_log(endpoint, timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_status ON api_usage_log(status_code, timestamp);

-- Tenant subscription and billing tracking
CREATE TABLE IF NOT EXISTS tenant_billing_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    plan_type VARCHAR(50) NOT NULL, -- 'starter', 'professional', 'enterprise'
    base_amount DECIMAL(10,2) NOT NULL,
    usage_charges DECIMAL(10,2) DEFAULT 0,
    discounts DECIMAL(10,2) DEFAULT 0,
    taxes DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    payment_method VARCHAR(50),
    payment_date TIMESTAMP,
    invoice_number VARCHAR(100) UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for billing history
CREATE INDEX IF NOT EXISTS idx_billing_history_tenant ON tenant_billing_history(tenant_id, billing_period_start);
CREATE INDEX IF NOT EXISTS idx_billing_history_status ON tenant_billing_history(status, billing_period_end);

-- System alerts and notifications
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_type VARCHAR(50) NOT NULL, -- 'performance', 'security', 'billing', 'maintenance'
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE, -- NULL for system-wide alerts
    affected_component VARCHAR(100), -- 'database', 'api', 'payment_processor', etc.
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'dismissed')),
    triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acknowledged_at TIMESTAMP,
    acknowledged_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP,
    resolved_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    resolution_notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for alerts
CREATE INDEX IF NOT EXISTS idx_alerts_status_severity ON system_alerts(status, severity, triggered_at);
CREATE INDEX IF NOT EXISTS idx_alerts_tenant ON system_alerts(tenant_id, triggered_at);
CREATE INDEX IF NOT EXISTS idx_alerts_type ON system_alerts(alert_type, triggered_at);

-- Insert some initial system metrics for demo purposes
INSERT INTO system_metrics (metric_type, metric_name, value, unit, recorded_at) VALUES
('performance', 'system_uptime', 99.9, '%', NOW()),
('performance', 'avg_response_time', 245, 'ms', NOW()),
('performance', 'error_rate', 0.12, '%', NOW()),
('usage', 'active_connections', 150, 'count', NOW()),
('usage', 'database_connections', 25, 'count', NOW())
ON CONFLICT DO NOTHING;

-- Insert some sample activity log entries
INSERT INTO system_activity_log (activity_type, activity_description, actor_type, actor_name, severity) VALUES
('system_startup', 'System started successfully', 'system', 'BARPOS System', 'success'),
('maintenance', 'Database maintenance completed', 'system', 'BARPOS System', 'info'),
('backup', 'Daily backup completed successfully', 'system', 'BARPOS System', 'success')
ON CONFLICT DO NOTHING;

-- Create a view for real-time dashboard metrics
CREATE OR REPLACE VIEW dashboard_metrics_view AS
SELECT 
    (SELECT COUNT(*) FROM tenants) as total_tenants,
    (SELECT COUNT(*) FROM tenants WHERE status = 'active') as active_tenants,
    (SELECT COALESCE(SUM(total), 0) FROM orders WHERE created_at >= NOW() - INTERVAL '30 days') as total_revenue_30d,
    (SELECT COUNT(*) FROM orders WHERE DATE(created_at) = CURRENT_DATE) as transactions_today,
    (SELECT COUNT(DISTINCT id) FROM employees WHERE last_login >= NOW() - INTERVAL '24 hours') as active_users_24h,
    (SELECT value FROM system_metrics WHERE metric_name = 'system_uptime' ORDER BY recorded_at DESC LIMIT 1) as system_uptime,
    (SELECT value FROM system_metrics WHERE metric_name = 'avg_response_time' ORDER BY recorded_at DESC LIMIT 1) as avg_response_time,
    (SELECT value FROM system_metrics WHERE metric_name = 'error_rate' ORDER BY recorded_at DESC LIMIT 1) as error_rate;

-- Create a function to update system metrics
CREATE OR REPLACE FUNCTION update_system_metric(
    p_metric_type VARCHAR(50),
    p_metric_name VARCHAR(100),
    p_value DECIMAL(15,4),
    p_unit VARCHAR(20) DEFAULT NULL,
    p_tenant_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    metric_id UUID;
BEGIN
    INSERT INTO system_metrics (metric_type, metric_name, value, unit, tenant_id, metadata)
    VALUES (p_metric_type, p_metric_name, p_value, p_unit, p_tenant_id, p_metadata)
    RETURNING id INTO metric_id;
    
    RETURN metric_id;
END;
$$ LANGUAGE plpgsql;

-- Create a function to log system activity
CREATE OR REPLACE FUNCTION log_system_activity(
    p_activity_type VARCHAR(50),
    p_description TEXT,
    p_actor_type VARCHAR(20),
    p_actor_id UUID DEFAULT NULL,
    p_actor_name VARCHAR(255) DEFAULT NULL,
    p_tenant_id UUID DEFAULT NULL,
    p_severity VARCHAR(20) DEFAULT 'info',
    p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO system_activity_log (
        activity_type, activity_description, actor_type, actor_id, 
        actor_name, tenant_id, severity, metadata
    )
    VALUES (
        p_activity_type, p_description, p_actor_type, p_actor_id,
        p_actor_name, p_tenant_id, p_severity, p_metadata
    )
    RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql;
