// Mock Super Admin API Server for Testing
// Provides mock data without requiring PostgreSQL

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockTenants = [
  {
    id: 1,
    name: 'Coffee Shop Pro',
    slug: 'coffee-shop-pro',
    status: 'active',
    plan: 'pro',
    createdAt: '2024-01-15T10:00:00Z',
    lastLogin: '2024-06-07T20:00:00Z',
    userCount: 8,
    monthlyRevenue: 15420,
    locations: 2,
    features: ['basic_pos', 'advanced_analytics', 'api_access']
  },
  {
    id: 2,
    name: 'Restaurant Chain',
    slug: 'restaurant-chain',
    status: 'active',
    plan: 'enterprise',
    createdAt: '2024-02-01T09:00:00Z',
    lastLogin: '2024-06-07T19:30:00Z',
    userCount: 25,
    monthlyRevenue: 45680,
    locations: 5,
    features: ['basic_pos', 'advanced_analytics', 'api_access', 'ai_features', 'multi_location']
  },
  {
    id: 3,
    name: 'Bakery Delights',
    slug: 'bakery-delights',
    status: 'active',
    plan: 'basic',
    createdAt: '2024-03-10T11:00:00Z',
    lastLogin: '2024-06-06T16:00:00Z',
    userCount: 4,
    monthlyRevenue: 8950,
    locations: 1,
    features: ['basic_pos']
  }
];

const mockUsers = [
  {
    id: 1,
    name: 'Super Administrator',
    email: '<EMAIL>',
    role: 'super_admin',
    tenantId: null,
    tenantName: 'System',
    status: 'active',
    lastLogin: '2024-06-07T21:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    permissions: ['all']
  },
  {
    id: 2,
    name: 'John Manager',
    email: '<EMAIL>',
    role: 'tenant_admin',
    tenantId: 1,
    tenantName: 'Coffee Shop Pro',
    status: 'active',
    lastLogin: '2024-06-07T20:30:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    permissions: ['tenant_management', 'user_management', 'pos_access']
  },
  {
    id: 3,
    name: 'Sarah Barista',
    email: '<EMAIL>',
    role: 'employee',
    tenantId: 1,
    tenantName: 'Coffee Shop Pro',
    status: 'active',
    lastLogin: '2024-06-07T18:00:00Z',
    createdAt: '2024-01-20T14:00:00Z',
    permissions: ['pos_access']
  }
];

const mockSecurityAudits = [
  {
    id: 1,
    timestamp: '2024-06-07T21:00:00Z',
    event: 'Super admin login',
    severity: 'low',
    userId: 1,
    userEmail: '<EMAIL>',
    ipAddress: '*************',
    details: 'Successful super admin authentication',
    status: 'resolved'
  },
  {
    id: 2,
    timestamp: '2024-06-07T20:45:00Z',
    event: 'Failed login attempt',
    severity: 'medium',
    userId: null,
    userEmail: '<EMAIL>',
    ipAddress: '*************',
    details: 'Multiple failed login attempts detected',
    status: 'investigating'
  },
  {
    id: 3,
    timestamp: '2024-06-07T19:30:00Z',
    event: 'Password changed',
    severity: 'low',
    userId: 2,
    userEmail: '<EMAIL>',
    ipAddress: '*************',
    details: 'User password successfully updated',
    status: 'resolved'
  }
];

const mockActivities = [
  {
    id: 1,
    timestamp: '2024-06-07T21:00:00Z',
    action: 'System backup completed',
    user: 'System',
    tenant: 'System',
    type: 'success',
    details: 'Daily automated backup completed successfully'
  },
  {
    id: 2,
    timestamp: '2024-06-07T20:30:00Z',
    action: 'New user created',
    user: 'John Manager',
    tenant: 'Coffee Shop Pro',
    type: 'info',
    details: 'New employee account created'
  },
  {
    id: 3,
    timestamp: '2024-06-07T19:45:00Z',
    action: 'Menu updated',
    user: 'Mike Owner',
    tenant: 'Restaurant Chain',
    type: 'info',
    details: 'Restaurant menu items updated'
  }
];

// Database connection test
app.get('/api/admin/health/database', (req, res) => {
  res.json({
    connected: true,
    timestamp: new Date().toISOString(),
    database: 'BARPOS (Mock)',
    host: 'localhost:5432'
  });
});

// System Metrics
app.get('/api/admin/metrics/system', (req, res) => {
  const metrics = {
    totalTenants: mockTenants.length,
    activeTenants: mockTenants.filter(t => t.status === 'active').length,
    totalUsers: mockUsers.length,
    activeUsers: mockUsers.filter(u => u.status === 'active').length,
    monthlyRevenue: mockTenants.reduce((sum, t) => sum + t.monthlyRevenue, 0),
    systemUptime: 99.8,
    databaseConnections: Math.floor(Math.random() * 15) + 5,
    apiRequests: Math.floor(Math.random() * 10000) + 50000,
    errorRate: Math.random() * 0.5,
    responseTime: Math.floor(Math.random() * 50) + 120,
    memoryUsage: Math.floor(Math.random() * 30) + 45,
    cpuUsage: Math.floor(Math.random() * 40) + 20,
    diskUsage: Math.floor(Math.random() * 20) + 60,
    lastUpdated: new Date().toISOString()
  };
  
  res.json(metrics);
});

// Tenants
app.get('/api/admin/tenants', (req, res) => {
  res.json(mockTenants);
});

// Users
app.get('/api/admin/users', (req, res) => {
  res.json(mockUsers);
});

// System Analytics
app.get('/api/admin/analytics/system', (req, res) => {
  const analytics = {
    dailyActiveUsers: Math.floor(Math.random() * 50) + 100,
    monthlyActiveUsers: Math.floor(Math.random() * 200) + 500,
    totalTransactions: Math.floor(Math.random() * 1000) + 5000,
    totalRevenue: Math.floor(Math.random() * 50000) + 100000,
    averageOrderValue: Math.floor(Math.random() * 20) + 25,
    conversionRate: Math.random() * 10 + 15,
    churnRate: Math.random() * 5 + 2,
    growthRate: Math.random() * 20 + 10,
    popularFeatures: [
      { name: 'POS System', usage: 95 },
      { name: 'Inventory Management', usage: 78 },
      { name: 'Analytics Dashboard', usage: 65 },
      { name: 'Staff Management', usage: 52 }
    ],
    performanceMetrics: {
      avgResponseTime: Math.floor(Math.random() * 50) + 120,
      uptime: 99.8,
      errorRate: Math.random() * 0.5,
      throughput: Math.floor(Math.random() * 100) + 200
    }
  };
  
  res.json(analytics);
});

// Security Audits
app.get('/api/admin/security/audits', (req, res) => {
  res.json(mockSecurityAudits);
});

// AI Analytics
app.get('/api/admin/ai/analytics', (req, res) => {
  const aiAnalytics = {
    predictedRevenue: Math.floor(Math.random() * 50000) + 100000,
    forecastAccuracy: Math.floor(Math.random() * 15) + 85,
    inventoryOptimization: {
      overstockedItems: Math.floor(Math.random() * 20) + 5,
      understockedItems: Math.floor(Math.random() * 15) + 3,
      optimizationSavings: Math.floor(Math.random() * 5000) + 2000
    },
    customerBehavior: {
      segmentCount: Math.floor(Math.random() * 5) + 8,
      churnPrediction: Math.floor(Math.random() * 10) + 5,
      lifetimeValue: Math.floor(Math.random() * 500) + 1200
    },
    pricingOptimization: {
      recommendedChanges: Math.floor(Math.random() * 20) + 10,
      potentialIncrease: Math.floor(Math.random() * 10) + 8,
      competitiveAnalysis: 'Competitive pricing position maintained'
    },
    staffOptimization: {
      optimalScheduling: Math.floor(Math.random() * 15) + 85,
      laborCostSavings: Math.floor(Math.random() * 10) + 12,
      productivityIncrease: Math.floor(Math.random() * 20) + 15
    }
  };
  
  res.json(aiAnalytics);
});

// System Activity
app.get('/api/admin/activity', (req, res) => {
  res.json(mockActivities);
});

// Error handling
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mock Super Admin API Server running on port ${PORT}`);
  console.log(`📊 Database: BARPOS (Mock Data)`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/admin/health/database`);
  console.log(`📈 System metrics: http://localhost:${PORT}/api/admin/metrics/system`);
  console.log(`🏢 Tenants: http://localhost:${PORT}/api/admin/tenants`);
  console.log(`👥 Users: http://localhost:${PORT}/api/admin/users`);
  console.log(`🤖 AI Analytics: http://localhost:${PORT}/api/admin/ai/analytics`);
});

module.exports = app;
