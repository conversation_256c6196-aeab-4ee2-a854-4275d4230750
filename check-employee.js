const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: '<PERSON>and@0319',
  port: 5432,
});

async function checkEmployees() {
  try {
    const result = await pool.query('SELECT * FROM employees');
    console.log('Current employees in database:', result.rows);
  } catch (error) {
    console.error('Error checking employees:', error);
  } finally {
    await pool.end();
  }
}

checkEmployees();
