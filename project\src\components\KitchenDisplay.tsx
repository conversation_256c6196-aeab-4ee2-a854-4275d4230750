import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { KitchenOrder, Order } from '../types';
import { Clock, ChefHat, CheckCircle, AlertCircle, Play, Pause, RotateCcw } from 'lucide-react';

const KitchenDisplay: React.FC = () => {
  const { state, fetchKitchenOrders, updateKitchenOrderStatus, sendOrderToKitchen } = useAppContext();
  const [selectedOrder, setSelectedOrder] = useState<KitchenOrder | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    fetchKitchenOrders();
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: KitchenOrder['status']) => {
    switch (status) {
      case 'new':
        return 'bg-red-600 border-red-500';
      case 'preparing':
        return 'bg-yellow-600 border-yellow-500';
      case 'ready':
        return 'bg-green-600 border-green-500';
      case 'served':
        return 'bg-gray-600 border-gray-500';
      default:
        return 'bg-gray-600 border-gray-500';
    }
  };

  const getStatusIcon = (status: KitchenOrder['status']) => {
    switch (status) {
      case 'new':
        return <AlertCircle className="h-5 w-5" />;
      case 'preparing':
        return <Play className="h-5 w-5" />;
      case 'ready':
        return <CheckCircle className="h-5 w-5" />;
      case 'served':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getElapsedTime = (timestamp: number) => {
    const elapsed = Math.floor((currentTime.getTime() - timestamp) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getPriorityColor = (priority: KitchenOrder['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'normal':
        return 'text-yellow-400';
      case 'low':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  const handleStatusUpdate = async (orderId: string, newStatus: KitchenOrder['status']) => {
    try {
      await updateKitchenOrderStatus(orderId, newStatus);
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  const handleSendToKitchen = async (order: Order) => {
    try {
      await sendOrderToKitchen(order.id);
    } catch (error) {
      console.error('Failed to send order to kitchen:', error);
    }
  };

  // Group orders by status
  const ordersByStatus = {
    new: state.kitchenOrders.filter(order => order.status === 'new'),
    preparing: state.kitchenOrders.filter(order => order.status === 'preparing'),
    ready: state.kitchenOrders.filter(order => order.status === 'ready'),
    served: state.kitchenOrders.filter(order => order.status === 'served')
  };

  // Get pending orders that can be sent to kitchen
  const pendingOrders = state.orders.filter(order => 
    order.status === 'open' && order.items.length > 0
  );

  const OrderCard: React.FC<{ order: KitchenOrder }> = ({ order }) => (
    <div
      className={`${getStatusColor(order.status)} border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-lg ${
        selectedOrder?.id === order.id ? 'ring-2 ring-purple-400' : ''
      }`}
      onClick={() => setSelectedOrder(order)}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon(order.status)}
          <span className="text-white font-bold text-lg">#{order.orderNumber}</span>
          {order.tableNumber && (
            <span className="text-white text-sm">Table {order.tableNumber}</span>
          )}
        </div>
        <div className="text-right">
          <div className="text-white font-medium">{getElapsedTime(order.timestamp)}</div>
          <div className={`text-xs ${getPriorityColor(order.priority)}`}>
            {order.priority.toUpperCase()}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        {order.items.map((item, index) => (
          <div key={index} className="flex justify-between text-white">
            <span>{item.quantity}x {item.name}</span>
            {item.notes && (
              <span className="text-yellow-300 text-sm italic">*{item.notes}</span>
            )}
          </div>
        ))}
      </div>

      {order.notes && (
        <div className="mt-3 p-2 bg-black bg-opacity-30 rounded text-yellow-300 text-sm">
          <strong>Notes:</strong> {order.notes}
        </div>
      )}

      {order.estimatedTime && (
        <div className="mt-2 text-white text-sm">
          Est. {order.estimatedTime} min
        </div>
      )}

      {order.assignedTo && (
        <div className="mt-2 text-gray-300 text-sm">
          Assigned to: {order.assignedTo}
        </div>
      )}
    </div>
  );

  const StatusColumn: React.FC<{ 
    title: string; 
    orders: KitchenOrder[]; 
    status: KitchenOrder['status'];
    nextStatus?: KitchenOrder['status'];
  }> = ({ title, orders, status, nextStatus }) => (
    <div className="flex-1 bg-gray-800 rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <span className="bg-gray-700 text-white px-2 py-1 rounded-full text-sm">
          {orders.length}
        </span>
      </div>
      
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {orders.map(order => (
          <OrderCard key={order.id} order={order} />
        ))}
        
        {orders.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <ChefHat className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No {title.toLowerCase()} orders</p>
          </div>
        )}
      </div>
    </div>
  );

  const OrderDetails: React.FC<{ order: KitchenOrder }> = ({ order }) => (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-semibold text-white">Order #{order.orderNumber}</h3>
          {order.tableNumber && (
            <p className="text-gray-300">Table {order.tableNumber}</p>
          )}
        </div>
        <div className="text-right">
          <div className="text-white font-medium text-lg">{getElapsedTime(order.timestamp)}</div>
          <div className={`text-sm ${getPriorityColor(order.priority)}`}>
            {order.priority.toUpperCase()} PRIORITY
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h4 className="text-white font-medium mb-3">Items:</h4>
        <div className="space-y-2">
          {order.items.map((item, index) => (
            <div key={index} className="flex justify-between items-start bg-gray-700 p-3 rounded">
              <div>
                <span className="text-white font-medium">{item.quantity}x {item.name}</span>
                {item.modifiers && item.modifiers.length > 0 && (
                  <div className="text-gray-300 text-sm mt-1">
                    Modifiers: {item.modifiers.join(', ')}
                  </div>
                )}
                {item.notes && (
                  <div className="text-yellow-300 text-sm mt-1 italic">
                    Notes: {item.notes}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {order.notes && (
        <div className="mb-6">
          <h4 className="text-white font-medium mb-2">Order Notes:</h4>
          <div className="bg-yellow-900 border border-yellow-600 p-3 rounded text-yellow-100">
            {order.notes}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          {order.status === 'new' && (
            <button
              onClick={() => handleStatusUpdate(order.id, 'preparing')}
              className="flex items-center space-x-2 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
            >
              <Play className="h-4 w-4" />
              <span>Start Preparing</span>
            </button>
          )}
          
          {order.status === 'preparing' && (
            <button
              onClick={() => handleStatusUpdate(order.id, 'ready')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Mark Ready</span>
            </button>
          )}
          
          {order.status === 'ready' && (
            <button
              onClick={() => handleStatusUpdate(order.id, 'served')}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Mark Served</span>
            </button>
          )}
        </div>

        {order.status !== 'served' && (
          <button
            onClick={() => handleStatusUpdate(order.id, 'new')}
            className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Reset</span>
          </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-gray-800 rounded-lg mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-white">Kitchen Display</h2>
          <div className="text-gray-300">
            {currentTime.toLocaleTimeString()}
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="text-white">
            Active Orders: {ordersByStatus.new.length + ordersByStatus.preparing.length}
          </div>
        </div>
      </div>

      {/* Pending Orders to Send */}
      {pendingOrders.length > 0 && (
        <div className="bg-blue-800 rounded-lg p-4 mb-4">
          <h3 className="text-white font-semibold mb-3">Orders Ready to Send to Kitchen</h3>
          <div className="flex flex-wrap gap-2">
            {pendingOrders.map(order => (
              <button
                key={order.id}
                onClick={() => handleSendToKitchen(order)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
              >
                Send Order {order.tabName ? `(${order.tabName})` : `#${order.id.slice(-4)}`}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="flex-grow flex gap-4">
        {/* Order Columns */}
        <div className="flex-grow flex gap-4">
          <StatusColumn 
            title="New Orders" 
            orders={ordersByStatus.new} 
            status="new"
            nextStatus="preparing"
          />
          <StatusColumn 
            title="Preparing" 
            orders={ordersByStatus.preparing} 
            status="preparing"
            nextStatus="ready"
          />
          <StatusColumn 
            title="Ready" 
            orders={ordersByStatus.ready} 
            status="ready"
            nextStatus="served"
          />
          <StatusColumn 
            title="Served" 
            orders={ordersByStatus.served} 
            status="served"
          />
        </div>

        {/* Order Details Panel */}
        {selectedOrder && (
          <div className="w-80">
            <OrderDetails order={selectedOrder} />
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="mt-4 grid grid-cols-4 gap-4">
        <div className="bg-red-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.new.length}</div>
          <div className="text-red-100 text-sm">New</div>
        </div>
        <div className="bg-yellow-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.preparing.length}</div>
          <div className="text-yellow-100 text-sm">Preparing</div>
        </div>
        <div className="bg-green-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.ready.length}</div>
          <div className="text-green-100 text-sm">Ready</div>
        </div>
        <div className="bg-gray-600 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{ordersByStatus.served.length}</div>
          <div className="text-gray-100 text-sm">Served</div>
        </div>
      </div>
    </div>
  );
};

export default KitchenDisplay;
