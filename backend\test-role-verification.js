const fetch = require('node-fetch');

async function testRoleVerification() {
  try {
    console.log('🚀 Testing Role Verification Fix...');
    console.log('='.repeat(60));
    
    // Test with Enhanced Admin (PIN: 999999)
    console.log('\n📋 Testing Enhanced Admin (PIN: 999999)');
    console.log('-'.repeat(40));
    
    const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '999999',
        tenant_slug: 'demo-restaurant'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginResult = await loginResponse.json();
    console.log(`✅ Login successful!`);
    console.log(`👤 Employee: ${loginResult.employee.name}`);
    console.log(`🔑 Role: ${loginResult.employee.role}`);
    console.log(`🏢 Tenant: ${loginResult.tenant.name}`);
    console.log(`🔗 Token: ${loginResult.token ? 'Present' : 'Missing'}`);

    // Simulate what the frontend should store in localStorage
    console.log('\n📋 Simulating Frontend localStorage Storage');
    console.log('-'.repeat(40));
    
    const employeeData = JSON.stringify(loginResult.employee);
    const tenantData = JSON.stringify(loginResult.tenant);
    const locationData = JSON.stringify(loginResult.location);
    
    console.log('📦 Employee data to store:', employeeData);
    console.log('📦 Tenant data to store:', tenantData);
    console.log('📦 Location data to store:', locationData);
    
    // Simulate role verification
    console.log('\n📋 Simulating Role Verification');
    console.log('-'.repeat(40));
    
    const storedEmployee = JSON.parse(employeeData);
    console.log('👤 Parsed employee from storage:', storedEmployee);
    console.log('🔑 Employee role:', storedEmployee.role);
    console.log('✅ Role verification:', storedEmployee.role === 'super_admin' ? 'PASS' : 'FAIL');
    
    if (storedEmployee.role === 'super_admin') {
      console.log('🎉 Super Admin access should be granted!');
    } else {
      console.log('❌ Super Admin access should be denied');
    }
    
    // Test with Main Admin (PIN: 888888)
    console.log('\n📋 Testing Main Admin (PIN: 888888)');
    console.log('-'.repeat(40));
    
    const loginResponse2 = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '888888',
        tenant_slug: 'demo-restaurant'
      })
    });

    if (loginResponse2.ok) {
      const loginResult2 = await loginResponse2.json();
      console.log(`✅ Login successful!`);
      console.log(`👤 Employee: ${loginResult2.employee.name}`);
      console.log(`🔑 Role: ${loginResult2.employee.role}`);
      console.log('✅ Role verification:', loginResult2.employee.role === 'super_admin' ? 'PASS' : 'FAIL');
    }
    
    // Test with regular employee (PIN: 123456)
    console.log('\n📋 Testing Regular Employee (PIN: 123456)');
    console.log('-'.repeat(40));
    
    const loginResponse3 = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '123456',
        tenant_slug: 'demo-restaurant'
      })
    });

    if (loginResponse3.ok) {
      const loginResult3 = await loginResponse3.json();
      console.log(`✅ Login successful!`);
      console.log(`👤 Employee: ${loginResult3.employee.name}`);
      console.log(`🔑 Role: ${loginResult3.employee.role}`);
      console.log('✅ Role verification:', loginResult3.employee.role === 'super_admin' ? 'PASS (should be FAIL)' : 'FAIL (correct)');
      
      if (loginResult3.employee.role !== 'super_admin') {
        console.log('🎉 Access denial should work correctly for non-super-admin users');
      }
    }
    
    console.log('\n🏁 Role Verification Test Completed!');
    console.log('='.repeat(60));
    
    console.log('\n📋 SUMMARY:');
    console.log('✅ Backend authentication working');
    console.log('✅ Role data available in API response');
    console.log('✅ localStorage storage format correct');
    console.log('✅ Role verification logic should work');
    
    console.log('\n🎯 FRONTEND FIXES APPLIED:');
    console.log('✅ Added employee data storage to localStorage');
    console.log('✅ Added role verification debugging');
    console.log('✅ Added timeout for localStorage update');
    console.log('✅ Added detailed error messages');
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Test login at: http://localhost:5174/super-admin.html');
    console.log('2. Use PIN: 999999 or 888888');
    console.log('3. Check browser console for detailed logs');
    console.log('4. Should now proceed to dashboard successfully');
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testRoleVerification();
