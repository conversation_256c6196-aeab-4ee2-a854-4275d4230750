// 🌐 RestroFlow Marketing Website JavaScript
// Phase 7C: Marketing Website & Sales Launch
// Lead generation and customer acquisition functionality

// Configuration
const API_BASE_URL = 'http://localhost:4000/api';
const MARKETING_API_URL = 'http://localhost:4000/api/marketing';

// Global variables
let selectedPlan = 'professional';

// ================================
// LEAD CAPTURE FUNCTIONALITY
// ================================

// Start free trial modal
function startFreeTrial() {
    document.getElementById('trialModal').classList.remove('hidden');
    
    // Track event
    trackEvent('trial_modal_opened', {
        source: 'hero_cta',
        timestamp: new Date().toISOString()
    });
}

// Close trial modal
function closeTrialModal() {
    document.getElementById('trialModal').classList.add('hidden');
}

// Select pricing plan
function selectPlan(plan) {
    selectedPlan = plan;
    
    // Update form
    const planSelect = document.querySelector('select[name="plan"]');
    if (planSelect) {
        planSelect.value = plan;
    }
    
    // Open trial modal
    startFreeTrial();
    
    // Track event
    trackEvent('plan_selected', {
        plan: plan,
        source: 'pricing_section',
        timestamp: new Date().toISOString()
    });
}

// Submit trial form
async function submitTrial(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Show loading state
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Creating Account...';
    submitButton.disabled = true;
    
    try {
        // Submit to onboarding API
        const response = await fetch(`${API_BASE_URL}/onboarding/signup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Success - show confirmation
            showSuccessMessage(result);
            closeTrialModal();
            
            // Track conversion
            trackEvent('trial_signup_success', {
                plan: data.plan,
                restaurantName: data.restaurantName,
                timestamp: new Date().toISOString()
            });
            
        } else {
            throw new Error(result.message || 'Failed to create account');
        }
        
    } catch (error) {
        console.error('Trial signup error:', error);
        showErrorMessage(error.message);
        
        // Track error
        trackEvent('trial_signup_error', {
            error: error.message,
            plan: data.plan,
            timestamp: new Date().toISOString()
        });
        
    } finally {
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
}

// Schedule demo
function scheduleDemo() {
    // Open Calendly or demo scheduling
    window.open('https://calendly.com/barpos-demo', '_blank');
    
    // Track event
    trackEvent('demo_scheduled', {
        source: 'hero_cta',
        timestamp: new Date().toISOString()
    });
}

// ================================
// SUCCESS/ERROR MESSAGING
// ================================

// Show success message
function showSuccessMessage(result) {
    const message = `
        <div class="fixed top-4 right-4 bg-green-500 text-white p-6 rounded-lg shadow-lg z-50 max-w-md">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <span class="text-2xl">🎉</span>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-semibold">Account Created Successfully!</h3>
                    <p class="mt-1 text-sm">Welcome to BARPOS! Check your email for login credentials.</p>
                    <div class="mt-3 text-sm">
                        <p><strong>Restaurant Code:</strong> ${result.tenant.restaurantCode}</p>
                        <p><strong>Login PIN:</strong> ${result.credentials.pin}</p>
                    </div>
                    <div class="mt-4">
                        <a href="${result.loginUrl}" target="_blank" class="bg-white text-green-500 px-4 py-2 rounded text-sm font-semibold hover:bg-gray-100">
                            Login Now →
                        </a>
                    </div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    ×
                </button>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', message);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
        const notification = document.querySelector('.fixed.top-4.right-4');
        if (notification) notification.remove();
    }, 10000);
}

// Show error message
function showErrorMessage(message) {
    const errorDiv = `
        <div class="fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-md">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <span class="text-xl">❌</span>
                </div>
                <div class="ml-3">
                    <h3 class="font-semibold">Error</h3>
                    <p class="text-sm">${message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    ×
                </button>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', errorDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const notification = document.querySelector('.fixed.top-4.right-4.bg-red-500');
        if (notification) notification.remove();
    }, 5000);
}

// ================================
// ANALYTICS & TRACKING
// ================================

// Track events for analytics
function trackEvent(eventName, properties = {}) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
    
    // Mixpanel
    if (typeof mixpanel !== 'undefined') {
        mixpanel.track(eventName, properties);
    }
    
    // Custom analytics
    const eventData = {
        event: eventName,
        properties: properties,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
    };
    
    // Send to backend
    fetch(`${MARKETING_API_URL}/events`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData)
    }).catch(error => {
        console.log('Analytics tracking error:', error);
    });
    
    console.log('Event tracked:', eventName, properties);
}

// Track page views
function trackPageView() {
    trackEvent('page_view', {
        page: window.location.pathname,
        referrer: document.referrer,
        timestamp: new Date().toISOString()
    });
}

// ================================
// SMOOTH SCROLLING
// ================================

// Smooth scroll for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Track page view
    trackPageView();
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Close modal when clicking outside
    document.getElementById('trialModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeTrialModal();
        }
    });
    
    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            
            // Track scroll milestones
            if (maxScroll >= 25 && maxScroll < 50) {
                trackEvent('scroll_25_percent');
            } else if (maxScroll >= 50 && maxScroll < 75) {
                trackEvent('scroll_50_percent');
            } else if (maxScroll >= 75 && maxScroll < 100) {
                trackEvent('scroll_75_percent');
            } else if (maxScroll >= 100) {
                trackEvent('scroll_100_percent');
            }
        }
    });
});

// ================================
// FEATURE INTERACTIONS
// ================================

// Feature card hover effects
document.addEventListener('DOMContentLoaded', function() {
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const title = this.querySelector('h3').textContent;
            trackEvent('feature_card_hover', { feature: title });
        });
    });
});

// ================================
// LEAD SCORING
// ================================

// Calculate lead score based on interactions
let leadScore = 0;
const leadActions = {
    'page_view': 1,
    'scroll_25_percent': 2,
    'scroll_50_percent': 3,
    'scroll_75_percent': 4,
    'feature_card_hover': 2,
    'pricing_view': 5,
    'plan_selected': 10,
    'trial_modal_opened': 15,
    'demo_scheduled': 20,
    'trial_signup_success': 50
};

// Update lead score
function updateLeadScore(eventName) {
    if (leadActions[eventName]) {
        leadScore += leadActions[eventName];
        
        // Store in localStorage
        localStorage.setItem('restroflow_lead_score', leadScore);
        
        // Send high-value leads to sales team
        if (leadScore >= 30) {
            notifyHighValueLead();
        }
    }
}

// Notify sales team of high-value lead
function notifyHighValueLead() {
    fetch(`${MARKETING_API_URL}/high-value-lead`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            score: leadScore,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            actions: Object.keys(leadActions)
        })
    }).catch(error => {
        console.log('High-value lead notification error:', error);
    });
}

// ================================
// CHAT WIDGET INTEGRATION
// ================================

// Initialize chat widget (placeholder for Intercom, Zendesk, etc.)
function initializeChatWidget() {
    // This would integrate with your chosen chat platform
    console.log('Chat widget initialized');
    
    // Example: Intercom
    // window.Intercom('boot', {
    //     app_id: 'your_app_id'
    // });
}

// ================================
// A/B TESTING
// ================================

// Simple A/B testing framework
function initializeABTesting() {
    const tests = {
        'hero_cta_text': {
            variants: ['Start Free Trial', 'Try BARPOS Free', 'Get Started Free'],
            weights: [0.4, 0.3, 0.3]
        },
        'pricing_highlight': {
            variants: ['most_popular', 'best_value', 'recommended'],
            weights: [0.5, 0.25, 0.25]
        }
    };
    
    Object.keys(tests).forEach(testName => {
        const variant = selectVariant(tests[testName]);
        applyVariant(testName, variant);
        
        // Track variant assignment
        trackEvent('ab_test_assigned', {
            test: testName,
            variant: variant
        });
    });
}

// Select variant based on weights
function selectVariant(test) {
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < test.variants.length; i++) {
        cumulative += test.weights[i];
        if (random <= cumulative) {
            return test.variants[i];
        }
    }
    
    return test.variants[0]; // fallback
}

// Apply variant to page
function applyVariant(testName, variant) {
    switch (testName) {
        case 'hero_cta_text':
            const ctaButtons = document.querySelectorAll('.cta-button');
            ctaButtons.forEach(button => {
                if (button.textContent.includes('Start')) {
                    button.textContent = variant;
                }
            });
            break;
            
        case 'pricing_highlight':
            // Apply different highlighting styles
            break;
    }
}

// ================================
// INITIALIZATION
// ================================

// Initialize all marketing features
document.addEventListener('DOMContentLoaded', function() {
    initializeChatWidget();
    initializeABTesting();
    
    // Override trackEvent to update lead score
    const originalTrackEvent = trackEvent;
    trackEvent = function(eventName, properties) {
        originalTrackEvent(eventName, properties);
        updateLeadScore(eventName);
    };
});

// ================================
// UTILITY FUNCTIONS
// ================================

// Format phone number
function formatPhoneNumber(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
        return '(' + match[1] + ') ' + match[2] + '-' + match[3];
    }
    return phoneNumber;
}

// Validate email
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Get URL parameters
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Handle UTM parameters for attribution
function handleUTMParameters() {
    const utmParams = {
        source: getUrlParameter('utm_source'),
        medium: getUrlParameter('utm_medium'),
        campaign: getUrlParameter('utm_campaign'),
        term: getUrlParameter('utm_term'),
        content: getUrlParameter('utm_content')
    };
    
    // Store UTM parameters
    if (Object.values(utmParams).some(param => param)) {
        localStorage.setItem('restroflow_utm_params', JSON.stringify(utmParams));
        
        // Track attribution
        trackEvent('utm_attribution', utmParams);
    }
}

// Initialize UTM tracking
document.addEventListener('DOMContentLoaded', handleUTMParameters);
