// Comprehensive Test Suite for RestroFlow Advanced Authentication System
// Covers unit tests, integration tests, security tests, and performance tests

const { describe, it, before, after, beforeEach, afterEach } = require('mocha');
const { expect } = require('chai');
const request = require('supertest');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

// Test runner script
const runTests = async () => {
  console.log('🧪 Starting RestroFlow Authentication Test Suite');
  console.log('================================================');

  const startTime = Date.now();

  try {
    // Run the test suite
    const Mocha = require('mocha');
    const mocha = new Mocha({
      timeout: 30000,
      reporter: 'spec',
      color: true
    });

    mocha.addFile(__filename);

    return new Promise((resolve, reject) => {
      mocha.run((failures) => {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;

        console.log('\n================================================');
        console.log(`🏁 Test suite completed in ${duration.toFixed(2)} seconds`);

        if (failures) {
          console.log(`❌ ${failures} test(s) failed`);
          reject(new Error(`${failures} test(s) failed`));
        } else {
          console.log('✅ All tests passed!');
          resolve();
        }
      });
    });
  } catch (error) {
    console.error('❌ Test suite error:', error);
    throw error;
  }
};

// Export test runner for external use
if (require.main === module) {
  runTests().catch(console.error);
}

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:4000',
  dbConfig: {
    user: 'BARPOS',
    host: 'localhost',
    database: 'RESTROFLOW_TEST',
    password: 'Chaand@0319',
    port: 5432
  },
  jwtSecret: 'test-jwt-secret',
  testTimeout: 30000
};

// Test data
const TEST_DATA = {
  tenant: {
    name: 'Test Restaurant',
    slug: 'test-restaurant',
    business_name: 'Test Restaurant LLC',
    email: '<EMAIL>',
    status: 'active'
  },
  employee: {
    name: 'Test Employee',
    email: '<EMAIL>',
    role: 'manager',
    pin: '123456',
    is_active: true
  },
  superAdmin: {
    name: 'Super Admin',
    email: '<EMAIL>',
    role: 'super_admin',
    pin: '123456',
    is_active: true
  }
};

// Database connection
let db;
let app;
let server;

describe('RestroFlow Advanced Authentication System', function() {
  this.timeout(TEST_CONFIG.testTimeout);

  before(async function() {
    console.log('🚀 Starting comprehensive test suite...');
    
    // Setup test database
    db = new Pool(TEST_CONFIG.dbConfig);
    
    // Import the app
    app = require('../backend/working-server.js');
    
    // Setup test data
    await setupTestData();
    
    console.log('✅ Test environment initialized');
  });

  after(async function() {
    console.log('🧹 Cleaning up test environment...');
    
    // Cleanup test data
    await cleanupTestData();
    
    // Close database connection
    if (db) {
      await db.end();
    }
    
    // Close server if running
    if (server) {
      server.close();
    }
    
    console.log('✅ Test cleanup completed');
  });

  // Unit Tests
  describe('Unit Tests', function() {
    
    describe('Password Hashing', function() {
      it('should hash PIN correctly', async function() {
        const pin = '123456';
        const hashedPin = await bcrypt.hash(pin, 10);
        
        expect(hashedPin).to.be.a('string');
        expect(hashedPin).to.not.equal(pin);
        
        const isValid = await bcrypt.compare(pin, hashedPin);
        expect(isValid).to.be.true;
      });

      it('should reject invalid PIN', async function() {
        const pin = '123456';
        const wrongPin = '654321';
        const hashedPin = await bcrypt.hash(pin, 10);
        
        const isValid = await bcrypt.compare(wrongPin, hashedPin);
        expect(isValid).to.be.false;
      });
    });

    describe('JWT Token Management', function() {
      it('should generate valid JWT token', function() {
        const payload = { employeeId: 1, tenantId: 1, role: 'manager' };
        const token = jwt.sign(payload, TEST_CONFIG.jwtSecret, { expiresIn: '24h' });
        
        expect(token).to.be.a('string');
        expect(token.split('.')).to.have.length(3);
      });

      it('should verify JWT token correctly', function() {
        const payload = { employeeId: 1, tenantId: 1, role: 'manager' };
        const token = jwt.sign(payload, TEST_CONFIG.jwtSecret, { expiresIn: '24h' });
        
        const decoded = jwt.verify(token, TEST_CONFIG.jwtSecret);
        expect(decoded.employeeId).to.equal(payload.employeeId);
        expect(decoded.tenantId).to.equal(payload.tenantId);
        expect(decoded.role).to.equal(payload.role);
      });

      it('should reject invalid JWT token', function() {
        const invalidToken = 'invalid.jwt.token';
        
        expect(() => {
          jwt.verify(invalidToken, TEST_CONFIG.jwtSecret);
        }).to.throw();
      });
    });
  });

  // Integration Tests
  describe('Integration Tests', function() {
    
    describe('Authentication API', function() {
      it('should authenticate employee successfully', async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.employee.pin,
            tenant_slug: TEST_DATA.tenant.slug
          });

        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('token');
        expect(response.body).to.have.property('employee');
        expect(response.body).to.have.property('tenant');
        expect(response.body.employee.role).to.equal(TEST_DATA.employee.role);
      });

      it('should authenticate super admin successfully', async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.superAdmin.pin,
            admin_access: true
          });

        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('token');
        expect(response.body.employee.role).to.equal('super_admin');
      });

      it('should reject invalid PIN', async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: '999999',
            tenant_slug: TEST_DATA.tenant.slug
          });

        expect(response.status).to.equal(401);
        expect(response.body).to.have.property('error');
      });

      it('should reject invalid tenant', async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.employee.pin,
            tenant_slug: 'invalid-tenant'
          });

        expect(response.status).to.equal(404);
        expect(response.body).to.have.property('error');
      });
    });

    describe('Token Verification', function() {
      let authToken;

      beforeEach(async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.employee.pin,
            tenant_slug: TEST_DATA.tenant.slug
          });
        
        authToken = response.body.token;
      });

      it('should verify valid token', async function() {
        const response = await request(app)
          .get('/api/auth/verify')
          .set('Authorization', `Bearer ${authToken}`);

        expect(response.status).to.equal(200);
        expect(response.body.valid).to.be.true;
      });

      it('should reject invalid token', async function() {
        const response = await request(app)
          .get('/api/auth/verify')
          .set('Authorization', 'Bearer invalid-token');

        expect(response.status).to.equal(401);
      });

      it('should reject missing token', async function() {
        const response = await request(app)
          .get('/api/auth/verify');

        expect(response.status).to.equal(401);
      });
    });
  });

  // Security Tests
  describe('Security Tests', function() {
    
    describe('Rate Limiting', function() {
      it('should enforce login rate limits', async function() {
        const promises = [];
        
        // Make 6 rapid login attempts (limit is 5)
        for (let i = 0; i < 6; i++) {
          promises.push(
            request(app)
              .post('/api/auth/login')
              .send({
                pin: '999999',
                tenant_slug: TEST_DATA.tenant.slug
              })
          );
        }

        const responses = await Promise.all(promises);
        
        // First 5 should be 401 (unauthorized), 6th should be 429 (rate limited)
        const rateLimitedResponses = responses.filter(r => r.status === 429);
        expect(rateLimitedResponses.length).to.be.at.least(1);
      });

      it('should enforce API rate limits', async function() {
        // Get auth token first
        const loginResponse = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.employee.pin,
            tenant_slug: TEST_DATA.tenant.slug
          });

        const token = loginResponse.body.token;
        const promises = [];
        
        // Make 101 rapid API requests (limit is 100 per minute)
        for (let i = 0; i < 101; i++) {
          promises.push(
            request(app)
              .get('/api/auth/verify')
              .set('Authorization', `Bearer ${token}`)
          );
        }

        const responses = await Promise.all(promises);
        
        // Some should be rate limited
        const rateLimitedResponses = responses.filter(r => r.status === 429);
        expect(rateLimitedResponses.length).to.be.at.least(1);
      });
    });

    describe('SQL Injection Protection', function() {
      it('should prevent SQL injection in login', async function() {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: "'; DROP TABLE employees; --",
            tenant_slug: TEST_DATA.tenant.slug
          });

        expect(response.status).to.equal(401);
        
        // Verify table still exists
        const result = await db.query('SELECT COUNT(*) FROM employees');
        expect(parseInt(result.rows[0].count)).to.be.greaterThan(0);
      });
    });

    describe('XSS Protection', function() {
      it('should sanitize input data', async function() {
        const maliciousInput = '<script>alert("xss")</script>';
        
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: maliciousInput,
            tenant_slug: TEST_DATA.tenant.slug
          });

        expect(response.status).to.equal(400);
        expect(response.body.error).to.not.include('<script>');
      });
    });
  });

  // Performance Tests
  describe('Performance Tests', function() {
    
    describe('Login Performance', function() {
      it('should authenticate within acceptable time', async function() {
        const startTime = Date.now();
        
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            pin: TEST_DATA.employee.pin,
            tenant_slug: TEST_DATA.tenant.slug
          });

        const endTime = Date.now();
        const responseTime = endTime - startTime;

        expect(response.status).to.equal(200);
        expect(responseTime).to.be.below(2000); // Should complete within 2 seconds
      });

      it('should handle concurrent logins', async function() {
        const concurrentRequests = 10;
        const promises = [];

        for (let i = 0; i < concurrentRequests; i++) {
          promises.push(
            request(app)
              .post('/api/auth/login')
              .send({
                pin: TEST_DATA.employee.pin,
                tenant_slug: TEST_DATA.tenant.slug
              })
          );
        }

        const responses = await Promise.all(promises);
        
        // All should succeed
        responses.forEach(response => {
          expect(response.status).to.equal(200);
          expect(response.body).to.have.property('token');
        });
      });
    });

    describe('Database Performance', function() {
      it('should query database efficiently', async function() {
        const startTime = Date.now();
        
        const result = await db.query(`
          SELECT e.*, t.name as tenant_name 
          FROM employees e 
          JOIN tenants t ON e.tenant_id = t.id 
          WHERE e.is_active = true
        `);

        const endTime = Date.now();
        const queryTime = endTime - startTime;

        expect(result.rows.length).to.be.greaterThan(0);
        expect(queryTime).to.be.below(100); // Should complete within 100ms
      });
    });
  });

  // Multi-Factor Authentication Tests
  describe('Multi-Factor Authentication Tests', function() {
    let authToken;

    beforeEach(async function() {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          pin: TEST_DATA.employee.pin,
          tenant_slug: TEST_DATA.tenant.slug
        });
      
      authToken = response.body.token;
    });

    describe('OTP Generation', function() {
      it('should send SMS OTP', async function() {
        const response = await request(app)
          .post('/api/auth/mfa/send-otp')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            employeeId: 1,
            tenantId: 1,
            method: 'sms'
          });

        expect(response.status).to.equal(200);
        expect(response.body.success).to.be.true;
      });

      it('should send email OTP', async function() {
        const response = await request(app)
          .post('/api/auth/mfa/send-otp')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            employeeId: 1,
            tenantId: 1,
            method: 'email'
          });

        expect(response.status).to.equal(200);
        expect(response.body.success).to.be.true;
      });
    });
  });

  // Audit Trail Tests
  describe('Audit Trail Tests', function() {
    let authToken;

    beforeEach(async function() {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          pin: TEST_DATA.employee.pin,
          tenant_slug: TEST_DATA.tenant.slug
        });
      
      authToken = response.body.token;
    });

    describe('Event Logging', function() {
      it('should log authentication events', async function() {
        const response = await request(app)
          .post('/api/audit/events')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            events: [{
              eventType: 'authentication',
              action: 'login_success',
              userId: 1,
              userName: 'Test User',
              tenantId: 1,
              success: true,
              riskLevel: 'low',
              metadata: { method: 'pin' }
            }]
          });

        expect(response.status).to.equal(200);
        expect(response.body.success).to.be.true;
      });

      it('should retrieve audit events', async function() {
        const response = await request(app)
          .get('/api/audit/events')
          .set('Authorization', `Bearer ${authToken}`)
          .query({
            limit: 10,
            offset: 0
          });

        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('events');
        expect(response.body.events).to.be.an('array');
      });
    });
  });

  // Emergency Access Tests
  describe('Emergency Access Tests', function() {
    
    describe('Master Key Access', function() {
      it('should grant emergency access with valid master key', async function() {
        const response = await request(app)
          .post('/api/auth/emergency/master-key')
          .send({
            masterKey: 'EMERGENCY_MASTER_KEY_2024',
            tenantSlug: TEST_DATA.tenant.slug,
            reason: 'System testing emergency access',
            contactInfo: {
              name: 'Test User',
              phone: '+1234567890',
              email: '<EMAIL>'
            }
          });

        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('token');
      });

      it('should reject invalid master key', async function() {
        const response = await request(app)
          .post('/api/auth/emergency/master-key')
          .send({
            masterKey: 'INVALID_KEY',
            tenantSlug: TEST_DATA.tenant.slug,
            reason: 'Testing invalid key',
            contactInfo: {
              name: 'Test User',
              phone: '+1234567890',
              email: '<EMAIL>'
            }
          });

        expect(response.status).to.equal(401);
        expect(response.body).to.have.property('error');
      });
    });
  });
});

// Helper functions
async function setupTestData() {
  try {
    // Create test tenant
    const tenantResult = await db.query(`
      INSERT INTO tenants (name, slug, business_name, email, status)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (slug) DO UPDATE SET
        name = EXCLUDED.name,
        business_name = EXCLUDED.business_name,
        email = EXCLUDED.email,
        status = EXCLUDED.status
      RETURNING id
    `, [
      TEST_DATA.tenant.name,
      TEST_DATA.tenant.slug,
      TEST_DATA.tenant.business_name,
      TEST_DATA.tenant.email,
      TEST_DATA.tenant.status
    ]);

    const tenantId = tenantResult.rows[0].id;

    // Create test employee
    const hashedPin = await bcrypt.hash(TEST_DATA.employee.pin, 10);
    await db.query(`
      INSERT INTO employees (tenant_id, name, email, role, pin_hash, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO UPDATE SET
        name = EXCLUDED.name,
        role = EXCLUDED.role,
        pin_hash = EXCLUDED.pin_hash,
        is_active = EXCLUDED.is_active
    `, [
      tenantId,
      TEST_DATA.employee.name,
      TEST_DATA.employee.email,
      TEST_DATA.employee.role,
      hashedPin,
      TEST_DATA.employee.is_active
    ]);

    // Create super admin
    const adminHashedPin = await bcrypt.hash(TEST_DATA.superAdmin.pin, 10);
    await db.query(`
      INSERT INTO employees (tenant_id, name, email, role, pin_hash, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO UPDATE SET
        name = EXCLUDED.name,
        role = EXCLUDED.role,
        pin_hash = EXCLUDED.pin_hash,
        is_active = EXCLUDED.is_active
    `, [
      tenantId,
      TEST_DATA.superAdmin.name,
      TEST_DATA.superAdmin.email,
      TEST_DATA.superAdmin.role,
      adminHashedPin,
      TEST_DATA.superAdmin.is_active
    ]);

    console.log('✅ Test data setup completed');
  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
}

async function cleanupTestData() {
  try {
    // Clean up in reverse order due to foreign key constraints
    await db.query('DELETE FROM employees WHERE email IN ($1, $2)', [
      TEST_DATA.employee.email,
      TEST_DATA.superAdmin.email
    ]);
    
    await db.query('DELETE FROM tenants WHERE slug = $1', [TEST_DATA.tenant.slug]);
    
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

module.exports = {
  TEST_CONFIG,
  TEST_DATA,
  setupTestData,
  cleanupTestData
};
