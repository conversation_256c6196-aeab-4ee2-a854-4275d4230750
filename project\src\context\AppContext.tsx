import React, { createContext, useContext, useReducer } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { AppState, AppAction, Product, OrderItem, Category, Employee, AppContextType, SystemConfig, FloorLayout, Table, Schedule, Customer, LoyaltyReward, KitchenOrder, KitchenMetrics, KitchenSettings, KitchenStaff } from '../types';

// Initialize with empty state
const initialState: AppState = {
  products: [],
  orders: [],
  currentOrder: null,
  employees: [],
  currentEmployee: null,
  isAuthenticated: false,
  categories: ['beer', 'wine', 'cocktails', 'spirits', 'non-alcoholic', 'food'],
  systemConfig: {
    tax_rate: 0.0825,
    receipt_header: 'Thank you for visiting!',
    receipt_footer: 'Please come again',
    business_name: 'Bar POS',
    business_address: '',
    business_phone: '',
    theme_primary_color: '#4f46e5',
    theme_secondary_color: '#10b981'
  },
  floorLayout: null,
  tables: [],
  schedules: [],
  customers: [],
  loyaltyRewards: [],
  kitchenOrders: [],
  selectedTable: null,
  kitchenMetrics: null,
  kitchenSettings: {
    audioEnabled: true,
    audioVolume: 0.5,
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
    showMetrics: true,
    compactView: false,
    maxOrdersPerColumn: 10,
    overdueThreshold: 15, // 15 minutes
    priorityColors: {
      low: '#10b981',
      normal: '#f59e0b',
      high: '#ef4444'
    }
  },
  kitchenStaff: []
};

// Helper function to calculate subtotal
const calculateSubtotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

// Helper function for API calls
const AppContext = createContext<AppContextType | null>(null);

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  const headers = {
    ...options.headers,
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };
  const response = await fetch(`http://localhost:4000/api${endpoint}`, {
    ...options,
    headers,
  });
  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }
  return response.json();
};

// Reducer function to handle state updates
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'ADD_PRODUCT_TO_ORDER': {
      const product = action.payload;
      let currentOrder = state.currentOrder;

      if (!currentOrder) {
        currentOrder = {
          id: uuidv4(),
          items: [],
          timestamp: Date.now(),
          status: 'open',
          total: 0,
          subtotal: 0,
          tax: 0,
          tableId: state.selectedTable?.id
        };
      }

      const existingItemIndex = currentOrder.items.findIndex(
        item => item.productId === product.id
      );

      if (existingItemIndex >= 0) {
        const updatedItems = [...currentOrder.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + 1
        };

        const subtotal = calculateSubtotal(updatedItems);
        const tax = subtotal * state.systemConfig.tax_rate;

        return {
          ...state,
          currentOrder: {
            ...currentOrder,
            items: updatedItems,
            subtotal,
            tax,
            total: subtotal + tax
          }
        };
      } else {
        const newItem: OrderItem = {
          id: uuidv4(),
          productId: product.id,
          name: product.name,
          price: product.price,
          quantity: 1
        };

        const updatedItems = [...currentOrder.items, newItem];
        const subtotal = calculateSubtotal(updatedItems);
        const tax = subtotal * state.systemConfig.tax_rate;

        return {
          ...state,
          currentOrder: {
            ...currentOrder,
            items: updatedItems,
            subtotal,
            tax,
            total: subtotal + tax
          }
        };
      }
    }

    case 'REMOVE_ITEM_FROM_ORDER': {
      if (!state.currentOrder) return state;

      const updatedItems = state.currentOrder.items.filter(
        item => item.id !== action.payload
      );

      if (updatedItems.length === 0) {
        return {
          ...state,
          currentOrder: null
        };
      }

      const subtotal = calculateSubtotal(updatedItems);
      const tax = subtotal * state.systemConfig.tax_rate;

      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          items: updatedItems,
          subtotal,
          tax,
          total: subtotal + tax
        }
      };
    }

    case 'UPDATE_ITEM_QUANTITY': {
      if (!state.currentOrder) return state;

      const { id, quantity } = action.payload;

      if (quantity <= 0) {
        return appReducer(state, { type: 'REMOVE_ITEM_FROM_ORDER', payload: id });
      }

      const updatedItems = state.currentOrder.items.map(item =>
        item.id === id ? { ...item, quantity } : item
      );

      const subtotal = calculateSubtotal(updatedItems);
      const tax = subtotal * state.systemConfig.tax_rate;

      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          items: updatedItems,
          subtotal,
          tax,
          total: subtotal + tax
        }
      };
    }

    case 'CLEAR_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: null
      };

    case 'SET_CURRENT_ORDER':
      return {
        ...state,
        currentOrder: action.payload
      };

    case 'COMPLETE_ORDER': {
      if (!state.currentOrder) return state;

      const { paymentMethod, tip = 0 } = action.payload;
      const completedOrder = {
        ...state.currentOrder,
        status: 'paid' as const,
        paymentMethod,
        tip,
        total: state.currentOrder.subtotal + state.currentOrder.tax + tip,
        employee_id: state.currentEmployee?.id
      };

      return {
        ...state,
        orders: [...state.orders, completedOrder],
        currentOrder: null,
        tables: state.tables.map(table =>
          table.id === state.currentOrder?.tableId
            ? { ...table, status: 'available' as const, currentOrderId: undefined }
            : table
        )
      };
    }

    case 'SET_TAB_NAME': {
      if (!state.currentOrder) return state;

      return {
        ...state,
        currentOrder: {
          ...state.currentOrder,
          tabName: action.payload
        }
      };
    }

    case 'LOGIN':
      return {
        ...state,
        currentEmployee: action.payload,
        isAuthenticated: true
      };

    case 'LOGOUT':
      return {
        ...state,
        currentEmployee: null,
        isAuthenticated: false,
        currentOrder: null
      };

    case 'SET_PRODUCTS':
      return {
        ...state,
        products: action.payload
      };

    case 'ADD_PRODUCT':
      return {
        ...state,
        products: [...state.products, action.payload]
      };

    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? action.payload : product
        )
      };

    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload)
      };

    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload
      };

    case 'ADD_CATEGORY':
      if (state.categories.includes(action.payload)) {
        return state;
      }
      return {
        ...state,
        categories: [...state.categories, action.payload]
      };

    case 'SET_EMPLOYEES':
      return {
        ...state,
        employees: action.payload
      };

    case 'UPDATE_SYSTEM_CONFIG':
      return {
        ...state,
        systemConfig: {
          ...state.systemConfig,
          ...action.payload
        }
      };

    case 'SET_FLOOR_LAYOUT':
      return {
        ...state,
        floorLayout: action.payload
      };

    case 'SET_TABLES':
      return {
        ...state,
        tables: action.payload
      };

    case 'UPDATE_TABLE_STATUS':
      return {
        ...state,
        tables: state.tables.map(table =>
          table.id === action.payload.tableId
            ? { ...table, status: action.payload.status, currentOrderId: action.payload.orderId }
            : table
        )
      };

    case 'SELECT_TABLE':
      return {
        ...state,
        selectedTable: action.payload
      };

    case 'SET_SCHEDULES':
      return {
        ...state,
        schedules: action.payload
      };

    case 'ADD_SCHEDULE':
      return {
        ...state,
        schedules: [...state.schedules, action.payload]
      };

    case 'UPDATE_SCHEDULE':
      return {
        ...state,
        schedules: state.schedules.map(schedule =>
          schedule.id === action.payload.id ? action.payload : schedule
        )
      };

    case 'SET_CUSTOMERS':
      return {
        ...state,
        customers: action.payload
      };

    case 'ADD_CUSTOMER':
      return {
        ...state,
        customers: [...state.customers, action.payload]
      };

    case 'UPDATE_CUSTOMER':
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? action.payload : customer
        )
      };

    case 'SET_LOYALTY_REWARDS':
      return {
        ...state,
        loyaltyRewards: action.payload
      };

    case 'SET_KITCHEN_ORDERS':
      return {
        ...state,
        kitchenOrders: action.payload
      };

    case 'ADD_KITCHEN_ORDER':
      return {
        ...state,
        kitchenOrders: [...state.kitchenOrders, action.payload]
      };

    case 'UPDATE_KITCHEN_ORDER':
      return {
        ...state,
        kitchenOrders: state.kitchenOrders.map(order =>
          order.id === action.payload.id ? action.payload : order
        )
      };

    case 'SEND_ORDER_TO_KITCHEN':
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload ? { ...order, status: 'kitchen' as const } : order
        )
      };

    case 'SET_KITCHEN_METRICS':
      return {
        ...state,
        kitchenMetrics: action.payload
      };

    case 'UPDATE_KITCHEN_SETTINGS':
      return {
        ...state,
        kitchenSettings: {
          ...state.kitchenSettings,
          ...action.payload
        }
      };

    case 'SET_KITCHEN_STAFF':
      return {
        ...state,
        kitchenStaff: action.payload
      };

    case 'ASSIGN_ORDER_TO_STAFF':
      return {
        ...state,
        kitchenOrders: state.kitchenOrders.map(order =>
          order.id === action.payload.id ? action.payload : order
        )
      };

    default:
      return state;
  }
};

// Provider component
function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Product management functions
  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/products/template');
      if (!response.ok) throw new Error('Failed to download template');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'product_template.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to download template:', error);
      throw error;
    }
  };

  const exportProducts = async () => {
    try {
      const response = await fetch('/api/products/export');
      if (!response.ok) throw new Error('Failed to export products');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'products.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Failed to export products:', error);
      throw error;
    }
  };

  const importProducts = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/products/bulk', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to import products');
      }

      const result = await response.json();
      await fetchProducts();
      return result;
    } catch (error) {
      console.error('Failed to import products:', error);
      throw error;
    }
  };

  const addProduct = async (product: Product) => {
    try {
      await apiCall('/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(product),
      });
      await fetchProducts();
    } catch (error) {
      console.error('Failed to add product:', error);
      throw error;
    }
  };

  const updateProduct = async (product: Product) => {
    try {
      const updatedProduct = await apiCall(`/products/${product.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(product),
      });
      dispatch({ type: 'UPDATE_PRODUCT', payload: updatedProduct });
    } catch (error) {
      console.error('Failed to update product:', error);
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      await apiCall(`/products/${productId}`, {
        method: 'DELETE',
      });
      dispatch({ type: 'DELETE_PRODUCT', payload: productId });
    } catch (error) {
      console.error('Failed to delete product:', error);
      throw error;
    }
  };

  const addCategory = async (category: Category) => {
    try {
      await apiCall('/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: category }),
      });
      dispatch({ type: 'ADD_CATEGORY', payload: category });
    } catch (error) {
      console.error('Failed to add category:', error);
      throw error;
    }
  };

  // Employee management functions
  const addEmployee = async (employee: Employee) => {
    try {
      const newEmployee = await apiCall('/employees', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: employee.name,
          pin: employee.pin,
          role: employee.role
        }),
      });
      dispatch({ type: 'ADD_EMPLOYEE', payload: newEmployee });
      return newEmployee;
    } catch (error) {
      console.error('Failed to add employee:', error);
      throw error;
    }
  };

  const updateEmployee = async (employee: Employee) => {
    if (!employee.id) {
      throw new Error('Employee ID is required for update');
    }

    try {
      const updatedEmployee = await apiCall(`/employees/${employee.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: employee.name,
          ...(employee.pin && { pin: employee.pin }),
          role: employee.role
        }),
      });
      dispatch({ type: 'UPDATE_EMPLOYEE', payload: updatedEmployee });
      return updatedEmployee;
    } catch (error) {
      console.error('Failed to update employee:', error);
      throw error;
    }
  };

  const deleteEmployee = async (employeeId: string) => {
    try {
      await apiCall(`/employees/${employeeId}`, {
        method: 'DELETE',
      });
      dispatch({ type: 'DELETE_EMPLOYEE', payload: employeeId });
    } catch (error) {
      console.error('Failed to delete employee:', error);
      throw error;
    }
  };

  const validateEmployeePin = async (pin: string) => {
    try {
      const response = await fetch('/api/employees/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pin }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          return null;
        }
        const error = await response.json();
        throw new Error(error.error || 'Failed to validate employee');
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to validate employee:', error);
      return null;
    }
  };

  // Fetch functions
  const fetchProducts = async () => {
    try {
      const products = await apiCall('/products');
      dispatch({ type: 'SET_PRODUCTS', payload: products });
      return products;
    } catch (error) {
      console.error('Failed to fetch products:', error);
      throw error;
    }
  };

  const fetchCategories = async () => {
    try {
      const categories = await apiCall('/categories');
      const normalizedCategories = [...new Set(categories.map((c: { name: string }) => c.name.toLowerCase()))] as string[];
      dispatch({ type: 'SET_CATEGORIES', payload: normalizedCategories });
      return categories;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      throw error;
    }
  };

  const fetchEmployees = async () => {
    try {
      const employees = await apiCall('/employees');
      dispatch({ type: 'SET_EMPLOYEES', payload: employees });
      return employees;
    } catch (error) {
      console.error('Failed to fetch employees:', error);
      throw error;
    }
  };

  // Settings functions
  const updateSettings = async (settings: Partial<SystemConfig>) => {
    try {
      const updatedSettings = await apiCall('/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });
      dispatch({ type: 'UPDATE_SYSTEM_CONFIG', payload: updatedSettings });
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  };

  const fetchSettings = async () => {
    try {
      const settings = await apiCall('/settings');
      dispatch({ type: 'UPDATE_SYSTEM_CONFIG', payload: settings });
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      throw error;
    }
  };

  // Floor Layout functions
  const fetchFloorLayout = async (): Promise<FloorLayout | null> => {
    try {
      const layout = await apiCall('/floor-layout');
      dispatch({ type: 'SET_FLOOR_LAYOUT', payload: layout });
      dispatch({ type: 'SET_TABLES', payload: layout.tables });
      return layout;
    } catch (error) {
      console.error('Failed to fetch floor layout:', error);
      return null;
    }
  };

  const updateTableStatus = async (tableId: string, status: Table['status'], orderId?: string) => {
    try {
      await apiCall(`/tables/${tableId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status, orderId }),
      });
      dispatch({ type: 'UPDATE_TABLE_STATUS', payload: { tableId, status, orderId } });
    } catch (error) {
      console.error('Failed to update table status:', error);
      throw error;
    }
  };

  // Scheduling functions
  const fetchSchedules = async (): Promise<Schedule[]> => {
    try {
      const schedules = await apiCall('/schedules');
      dispatch({ type: 'SET_SCHEDULES', payload: schedules });
      return schedules;
    } catch (error) {
      console.error('Failed to fetch schedules:', error);
      throw error;
    }
  };

  const addSchedule = async (schedule: Schedule) => {
    try {
      const newSchedule = await apiCall('/schedules', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(schedule),
      });
      dispatch({ type: 'ADD_SCHEDULE', payload: newSchedule });
    } catch (error) {
      console.error('Failed to add schedule:', error);
      throw error;
    }
  };

  const updateSchedule = async (schedule: Schedule) => {
    try {
      const updatedSchedule = await apiCall(`/schedules/${schedule.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(schedule),
      });
      dispatch({ type: 'UPDATE_SCHEDULE', payload: updatedSchedule });
    } catch (error) {
      console.error('Failed to update schedule:', error);
      throw error;
    }
  };

  // Loyalty functions
  const fetchCustomers = async (): Promise<Customer[]> => {
    try {
      const customers = await apiCall('/customers');
      dispatch({ type: 'SET_CUSTOMERS', payload: customers });
      return customers;
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      throw error;
    }
  };

  const addCustomer = async (customer: Customer) => {
    try {
      const newCustomer = await apiCall('/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customer),
      });
      dispatch({ type: 'ADD_CUSTOMER', payload: newCustomer });
    } catch (error) {
      console.error('Failed to add customer:', error);
      throw error;
    }
  };

  const updateCustomer = async (customer: Customer) => {
    try {
      const updatedCustomer = await apiCall(`/customers/${customer.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customer),
      });
      dispatch({ type: 'UPDATE_CUSTOMER', payload: updatedCustomer });
    } catch (error) {
      console.error('Failed to update customer:', error);
      throw error;
    }
  };

  // Loyalty rewards
  const fetchLoyaltyRewards = async (): Promise<LoyaltyReward[]> => {
    try {
      const rewards = await apiCall('/loyalty/rewards');
      dispatch({ type: 'SET_LOYALTY_REWARDS', payload: rewards });
      return rewards;
    } catch (error) {
      console.error('Failed to fetch loyalty rewards:', error);
      throw error;
    }
  };

  // Kitchen functions
  const fetchKitchenOrders = async (): Promise<KitchenOrder[]> => {
    try {
      const orders = await apiCall('/kitchen/orders');
      dispatch({ type: 'SET_KITCHEN_ORDERS', payload: orders });
      return orders;
    } catch (error) {
      console.error('Failed to fetch kitchen orders:', error);
      throw error;
    }
  };

  const sendOrderToKitchen = async (orderId: string) => {
    try {
      await apiCall(`/kitchen/orders/${orderId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      dispatch({ type: 'SEND_ORDER_TO_KITCHEN', payload: orderId });
    } catch (error) {
      console.error('Failed to send order to kitchen:', error);
      throw error;
    }
  };

  const updateKitchenOrderStatus = async (orderId: string, status: KitchenOrder['status']) => {
    try {
      const updatedOrder = await apiCall(`/kitchen/orders/${orderId}/status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status }),
      });
      dispatch({ type: 'UPDATE_KITCHEN_ORDER', payload: updatedOrder });
    } catch (error) {
      console.error('Failed to update kitchen order status:', error);
      throw error;
    }
  };

  const fetchKitchenMetrics = async (): Promise<KitchenMetrics> => {
    try {
      const metrics = await apiCall('/kitchen/metrics');
      dispatch({ type: 'SET_KITCHEN_METRICS', payload: metrics });
      return metrics;
    } catch (error) {
      console.error('Failed to fetch kitchen metrics:', error);
      throw error;
    }
  };

  const updateKitchenSettings = async (settings: Partial<KitchenSettings>) => {
    try {
      const updatedSettings = await apiCall('/kitchen/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings),
      });
      dispatch({ type: 'UPDATE_KITCHEN_SETTINGS', payload: updatedSettings });
    } catch (error) {
      console.error('Failed to update kitchen settings:', error);
      throw error;
    }
  };

  const fetchKitchenStaff = async (): Promise<KitchenStaff[]> => {
    try {
      const staff = await apiCall('/kitchen/staff');
      dispatch({ type: 'SET_KITCHEN_STAFF', payload: staff });
      return staff;
    } catch (error) {
      console.error('Failed to fetch kitchen staff:', error);
      throw error;
    }
  };

  const assignOrderToStaff = async (orderId: string, staffId: string) => {
    try {
      const updatedOrder = await apiCall(`/kitchen/orders/${orderId}/assign`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ staffId }),
      });
      dispatch({ type: 'ASSIGN_ORDER_TO_STAFF', payload: updatedOrder });
    } catch (error) {
      console.error('Failed to assign order to staff:', error);
      throw error;
    }
  };

  const updateKitchenOrder = async (orderId: string, updates: Partial<KitchenOrder>) => {
    try {
      const updatedOrder = await apiCall(`/kitchen/orders/${orderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      dispatch({ type: 'UPDATE_KITCHEN_ORDER', payload: updatedOrder });
    } catch (error) {
      console.error('Failed to update kitchen order:', error);
      throw error;
    }
  };

  const bulkUpdateKitchenOrders = async (orderIds: string[], status: KitchenOrder['status']) => {
    try {
      const updatedOrders = await apiCall('/kitchen/orders/bulk-update', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderIds, status }),
      });
      updatedOrders.forEach((order: KitchenOrder) => {
        dispatch({ type: 'UPDATE_KITCHEN_ORDER', payload: order });
      });
    } catch (error) {
      console.error('Failed to bulk update kitchen orders:', error);
      throw error;
    }
  };

  // Fetch initial data
  React.useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          fetchEmployees(),
          fetchSettings(),
          fetchProducts(),
          fetchCategories(),
          fetchFloorLayout()
        ]);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      }
    };
    loadInitialData();
  }, []);

  const contextValue: AppContextType = {
    state,
    dispatch,
    addProduct,
    updateProduct,
    deleteProduct,
    addCategory,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateSettings,
    fetchEmployees,
    fetchSettings,
    validateEmployeePin,
    fetchProducts,
    fetchCategories,
    importProducts,
    exportProducts,
    downloadTemplate,
    // Floor Layout functions
    fetchFloorLayout,
    updateTableStatus,
    // Scheduling functions
    fetchSchedules,
    addSchedule,
    updateSchedule,
    // Loyalty functions
    fetchCustomers,
    addCustomer,
    updateCustomer,
    fetchLoyaltyRewards,
    // Kitchen functions
    fetchKitchenOrders,
    sendOrderToKitchen,
    updateKitchenOrderStatus,
    fetchKitchenMetrics,
    updateKitchenSettings,
    fetchKitchenStaff,
    assignOrderToStaff,
    updateKitchenOrder,
    bulkUpdateKitchenOrders
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook to use the app context
function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

export { AppContext, AppProvider, useAppContext };
