const http = require('http');

// First get current config
http.get('http://localhost:4000/config', (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('GET Config Status:', res.statusCode);
    console.log('Current Config:', JSON.parse(data));

    // Then update tax rate
    const updateData = JSON.stringify({
      value: "0.095"
    });

    const updateOptions = {
      hostname: 'localhost',
      port: 4000,
      path: '/config/tax_rate',
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': updateData.length
      }
    };

    const updateReq = http.request(updateOptions, updateRes => {
      console.log(`\nUpdate Config Status: ${updateRes.statusCode}`);
      updateRes.on('data', d => {
        process.stdout.write(d);
      });
    });

    updateReq.on('error', error => {
      console.error('Update Error:', error);
    });

    updateReq.write(updateData);
    updateReq.end();
  });
}).on('error', (err) => {
  console.error('Error:', err.message);
});
