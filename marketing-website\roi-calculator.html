<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow ROI Calculator - See Your Potential Savings</title>
    <meta name="description" content="Calculate how much RestroFlow can save your restaurant. See potential ROI, cost savings, and revenue increases.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .calculator-card { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .input-group { transition: all 0.3s ease; }
        .input-group:focus-within { transform: translateY(-2px); }
        .result-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .savings-highlight { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-purple-600">RestroFlow</h1>
                    <span class="ml-4 text-gray-600">ROI Calculator</span>
                </div>
                <a href="index.html" class="text-purple-600 hover:text-purple-700 font-medium">← Back to Home</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Calculate Your RestroFlow ROI</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                See how much RestroFlow can save your restaurant and increase your profits.
                Get a personalized ROI analysis based on your current operations.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Calculator Form -->
            <div class="calculator-card bg-white rounded-xl p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Restaurant Information</h2>
                
                <form id="roiCalculator" class="space-y-6">
                    <!-- Basic Info -->
                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Restaurant Type</label>
                        <select id="restaurantType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <option value="fast_casual">Fast Casual</option>
                            <option value="full_service">Full Service</option>
                            <option value="quick_service">Quick Service</option>
                            <option value="fine_dining">Fine Dining</option>
                            <option value="cafe">Café/Coffee Shop</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number of Locations</label>
                        <input type="number" id="locations" min="1" max="100" value="1" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Number of Employees</label>
                        <input type="number" id="employees" min="1" max="500" value="10" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <!-- Financial Info -->
                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Monthly Revenue ($)</label>
                        <input type="number" id="monthlyRevenue" min="1000" max="10000000" value="50000" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Average Transaction Value ($)</label>
                        <input type="number" id="avgTransaction" min="1" max="1000" value="25" step="0.01" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Daily Transactions</label>
                        <input type="number" id="dailyTransactions" min="1" max="10000" value="100" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <!-- Current Costs -->
                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current POS System Cost ($/month)</label>
                        <input type="number" id="currentPosCost" min="0" max="10000" value="200" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Administrative Hours per Week</label>
                        <input type="number" id="adminHours" min="0" max="168" value="20" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="input-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Average Hourly Wage ($)</label>
                        <input type="number" id="hourlyWage" min="7" max="100" value="15" step="0.01" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <button type="button" onclick="calculateROI()" class="w-full bg-purple-600 text-white py-4 rounded-lg text-lg font-semibold hover:bg-purple-700 transition-colors">
                        Calculate My ROI
                    </button>
                </form>
            </div>

            <!-- Results Panel -->
            <div id="resultsPanel" class="space-y-6" style="display: none;">
                <!-- ROI Summary -->
                <div class="result-card text-white rounded-xl p-8">
                    <h3 class="text-2xl font-bold mb-4">Your BARPOS ROI</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold" id="roiPercentage">0%</div>
                            <div class="text-sm opacity-90">Annual ROI</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold" id="paybackPeriod">0</div>
                            <div class="text-sm opacity-90">Months to Payback</div>
                        </div>
                    </div>
                </div>

                <!-- Savings Breakdown -->
                <div class="savings-highlight text-white rounded-xl p-8">
                    <h3 class="text-2xl font-bold mb-4">Annual Savings</h3>
                    <div class="text-4xl font-bold mb-2" id="totalSavings">$0</div>
                    <div class="text-sm opacity-90">Total cost savings per year</div>
                </div>

                <!-- Detailed Breakdown -->
                <div class="bg-white rounded-xl p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Savings Breakdown</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Labor Cost Reduction</span>
                            <span class="font-semibold" id="laborSavings">$0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Operational Efficiency</span>
                            <span class="font-semibold" id="operationalSavings">$0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Inventory Optimization</span>
                            <span class="font-semibold" id="inventorySavings">$0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Revenue Increase</span>
                            <span class="font-semibold" id="revenueIncrease">$0</span>
                        </div>
                        <hr class="my-3">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total Annual Benefit</span>
                            <span id="totalBenefit">$0</span>
                        </div>
                    </div>
                </div>

                <!-- Cost Comparison -->
                <div class="bg-white rounded-xl p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Cost Comparison</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Current System (Annual)</span>
                            <span class="font-semibold" id="currentAnnualCost">$0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">BARPOS (Annual)</span>
                            <span class="font-semibold" id="barposAnnualCost">$0</span>
                        </div>
                        <hr class="my-3">
                        <div class="flex justify-between text-lg font-bold text-green-600">
                            <span>Net Savings</span>
                            <span id="netSavings">$0</span>
                        </div>
                    </div>
                </div>

                <!-- Chart -->
                <div class="bg-white rounded-xl p-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">5-Year Projection</h3>
                    <canvas id="projectionChart" width="400" height="200"></canvas>
                </div>

                <!-- CTA -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Ready to Start Saving?</h3>
                    <p class="text-gray-600 mb-4">Based on your numbers, BARPOS could save your restaurant significant money. Start your free trial today!</p>
                    <div class="flex gap-4">
                        <button onclick="startFreeTrial()" class="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700">
                            Start Free Trial
                        </button>
                        <button onclick="scheduleDemo()" class="bg-white border border-purple-600 text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50">
                            Schedule Demo
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- How We Calculate -->
        <div class="mt-16 bg-white rounded-xl p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">How We Calculate Your ROI</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">⏱️</span>
                    </div>
                    <h3 class="font-semibold mb-2">Time Savings</h3>
                    <p class="text-sm text-gray-600">Automation reduces administrative work by 60-80%</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">📈</span>
                    </div>
                    <h3 class="font-semibold mb-2">Revenue Growth</h3>
                    <p class="text-sm text-gray-600">AI insights typically increase revenue by 15-35%</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">💰</span>
                    </div>
                    <h3 class="font-semibold mb-2">Cost Reduction</h3>
                    <p class="text-sm text-gray-600">Streamlined operations reduce costs by 20-30%</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-2xl">📊</span>
                    </div>
                    <h3 class="font-semibold mb-2">Data-Driven</h3>
                    <p class="text-sm text-gray-600">Based on real customer results and industry benchmarks</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        let projectionChart = null;

        function calculateROI() {
            // Get form values
            const restaurantType = document.getElementById('restaurantType').value;
            const locations = parseInt(document.getElementById('locations').value);
            const employees = parseInt(document.getElementById('employees').value);
            const monthlyRevenue = parseFloat(document.getElementById('monthlyRevenue').value);
            const avgTransaction = parseFloat(document.getElementById('avgTransaction').value);
            const dailyTransactions = parseInt(document.getElementById('dailyTransactions').value);
            const currentPosCost = parseFloat(document.getElementById('currentPosCost').value);
            const adminHours = parseFloat(document.getElementById('adminHours').value);
            const hourlyWage = parseFloat(document.getElementById('hourlyWage').value);

            // Calculate BARPOS cost based on locations and employees
            let barposMonthlyCost;
            if (locations === 1 && employees <= 5) {
                barposMonthlyCost = 99;
            } else if (locations <= 3 && employees <= 25) {
                barposMonthlyCost = 299;
            } else {
                barposMonthlyCost = 799;
            }

            // Calculate savings
            const annualRevenue = monthlyRevenue * 12;
            const currentAnnualPosCost = currentPosCost * 12;
            const barposAnnualCost = barposMonthlyCost * 12;

            // Labor savings (60% reduction in admin time)
            const laborSavings = adminHours * 52 * hourlyWage * 0.6;

            // Operational efficiency (5% of revenue)
            const operationalSavings = annualRevenue * 0.05;

            // Inventory optimization (3% of revenue)
            const inventorySavings = annualRevenue * 0.03;

            // Revenue increase (15% average)
            const revenueIncrease = annualRevenue * 0.15;

            // Total benefits
            const totalBenefit = laborSavings + operationalSavings + inventorySavings + revenueIncrease;
            const netSavings = totalBenefit - (barposAnnualCost - currentAnnualPosCost);
            const totalSavings = netSavings;

            // ROI calculation
            const investment = barposAnnualCost;
            const roiPercentage = ((totalBenefit - investment) / investment * 100).toFixed(0);
            const paybackPeriod = Math.ceil(investment / (totalBenefit / 12));

            // Update UI
            document.getElementById('roiPercentage').textContent = roiPercentage + '%';
            document.getElementById('paybackPeriod').textContent = paybackPeriod;
            document.getElementById('totalSavings').textContent = '$' + totalSavings.toLocaleString();
            document.getElementById('laborSavings').textContent = '$' + laborSavings.toLocaleString();
            document.getElementById('operationalSavings').textContent = '$' + operationalSavings.toLocaleString();
            document.getElementById('inventorySavings').textContent = '$' + inventorySavings.toLocaleString();
            document.getElementById('revenueIncrease').textContent = '$' + revenueIncrease.toLocaleString();
            document.getElementById('totalBenefit').textContent = '$' + totalBenefit.toLocaleString();
            document.getElementById('currentAnnualCost').textContent = '$' + currentAnnualPosCost.toLocaleString();
            document.getElementById('barposAnnualCost').textContent = '$' + barposAnnualCost.toLocaleString();
            document.getElementById('netSavings').textContent = '$' + netSavings.toLocaleString();

            // Show results
            document.getElementById('resultsPanel').style.display = 'block';
            document.getElementById('resultsPanel').scrollIntoView({ behavior: 'smooth' });

            // Create chart
            createProjectionChart(totalBenefit, investment);

            // Track event
            if (typeof trackEvent === 'function') {
                trackEvent('roi_calculated', {
                    restaurantType,
                    locations,
                    employees,
                    monthlyRevenue,
                    roiPercentage: parseFloat(roiPercentage),
                    totalSavings
                });
            }
        }

        function createProjectionChart(annualBenefit, annualCost) {
            const ctx = document.getElementById('projectionChart').getContext('2d');
            
            if (projectionChart) {
                projectionChart.destroy();
            }

            const years = [1, 2, 3, 4, 5];
            const cumulativeSavings = years.map(year => (annualBenefit - annualCost) * year);
            const cumulativeInvestment = years.map(year => annualCost * year);

            projectionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: years.map(year => `Year ${year}`),
                    datasets: [{
                        label: 'Cumulative Savings',
                        data: cumulativeSavings,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Cumulative Investment',
                        data: cumulativeInvestment,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        function startFreeTrial() {
            // Redirect to signup with ROI data
            window.location.href = 'index.html#trial';
        }

        function scheduleDemo() {
            // Open demo scheduling
            window.open('https://calendly.com/barpos-demo', '_blank');
        }

        // Auto-calculate on input change
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('#roiCalculator input, #roiCalculator select');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (document.getElementById('resultsPanel').style.display !== 'none') {
                        calculateROI();
                    }
                });
            });
        });
    </script>
</body>
</html>
