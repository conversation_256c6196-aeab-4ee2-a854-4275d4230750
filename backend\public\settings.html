<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Bar POS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4f46e5;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .color-input {
            height: 50px !important;
            cursor: pointer;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .success-message {
            background: #10b981;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .error-message {
            background: #ef4444;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ Settings</h1>
            <p>Configure your Bar POS system</p>
        </div>
        
        <div class="content">
            <div id="successMessage" class="success-message">
                Settings updated successfully!
            </div>
            
            <div id="errorMessage" class="error-message">
                Failed to update settings. Please try again.
            </div>
            
            <form id="settingsForm">
                <div class="form-group">
                    <label for="businessName">Business Name</label>
                    <input type="text" id="businessName" name="business_name" required>
                </div>
                
                <div class="form-group">
                    <label for="businessAddress">Business Address</label>
                    <textarea id="businessAddress" name="business_address" placeholder="Enter your business address"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="businessPhone">Business Phone</label>
                    <input type="tel" id="businessPhone" name="business_phone" placeholder="(*************">
                </div>
                
                <div class="form-group">
                    <label for="taxRate">Tax Rate (%)</label>
                    <input type="number" id="taxRate" name="tax_rate" step="0.0001" min="0" max="1" required>
                </div>
                
                <div class="form-group">
                    <label for="receiptHeader">Receipt Header</label>
                    <textarea id="receiptHeader" name="receipt_header" placeholder="Thank you for visiting!"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="receiptFooter">Receipt Footer</label>
                    <textarea id="receiptFooter" name="receipt_footer" placeholder="Please come again"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="primaryColor">Primary Color</label>
                        <input type="color" id="primaryColor" name="theme_primary_color" class="color-input">
                    </div>
                    
                    <div class="form-group">
                        <label for="secondaryColor">Secondary Color</label>
                        <input type="color" id="secondaryColor" name="theme_secondary_color" class="color-input">
                    </div>
                </div>
                
                <button type="submit" class="btn" id="saveBtn">
                    💾 Save Settings
                </button>
            </form>
        </div>
    </div>

    <script src="js/settings.js"></script>
</body>
</html>
