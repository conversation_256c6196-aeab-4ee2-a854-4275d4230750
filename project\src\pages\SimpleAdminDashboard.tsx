import React, { useState, useEffect } from 'react';

// Simple Admin Dashboard Component
export function SimpleAdminDashboard() {
  const [currentView, setCurrentView] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Simple navigation configuration
  const navigationConfig = {
    'dashboard': { label: 'Dashboard Overview', icon: '📊', color: 'blue' },
    'tenant-management': { label: 'Tenant Management', icon: '🏢', color: 'green' },
    'user-management': { label: 'User Management', icon: '👥', color: 'purple' },
    'analytics': { label: 'Advanced Analytics', icon: '📈', color: 'indigo' },
    'billing': { label: 'Billing Management', icon: '💳', color: 'emerald' },
    'system-health': { label: 'System Health', icon: '🔧', color: 'orange' },
    'security': { label: 'Security Audit', icon: '🛡️', color: 'red' },
  };

  const handleViewChange = (newView: string) => {
    setCurrentView(newView);
    window.location.hash = newView;
  };

  const renderContent = () => {
    switch (currentView) {
      case 'tenant-management':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Tenant Management</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>Tenant management functionality will be implemented here.</p>
            </div>
          </div>
        );
      case 'user-management':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">User Management</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>User management functionality will be implemented here.</p>
            </div>
          </div>
        );
      case 'analytics':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Advanced Analytics</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>Analytics dashboard will be implemented here.</p>
            </div>
          </div>
        );
      case 'billing':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Billing Management</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>Billing management functionality will be implemented here.</p>
            </div>
          </div>
        );
      case 'system-health':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">System Health</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>System health monitoring will be implemented here.</p>
            </div>
          </div>
        );
      case 'security':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Security Audit</h2>
            <div className="bg-white rounded-lg border p-6">
              <p>Security audit functionality will be implemented here.</p>
            </div>
          </div>
        );
      case 'dashboard':
      default:
        return (
          <div className="p-6 space-y-6">
            {/* Dashboard Overview */}
            <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-xl p-6 text-white">
              <h2 className="text-3xl font-bold mb-2">Super Admin Overview</h2>
              <p className="text-red-100">
                Monitor your multi-tenant POS ecosystem performance and key metrics
              </p>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Tenants</p>
                    <p className="text-2xl font-bold">12</p>
                  </div>
                  <div className="text-blue-600">🏢</div>
                </div>
              </div>
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Users</p>
                    <p className="text-2xl font-bold">248</p>
                  </div>
                  <div className="text-green-600">👥</div>
                </div>
              </div>
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Monthly Revenue</p>
                    <p className="text-2xl font-bold">$24,580</p>
                  </div>
                  <div className="text-emerald-600">💰</div>
                </div>
              </div>
              <div className="bg-white rounded-lg border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">System Uptime</p>
                    <p className="text-2xl font-bold">99.9%</p>
                  </div>
                  <div className="text-orange-600">⚡</div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => handleViewChange('tenant-management')}
                  className="p-4 border rounded-lg hover:bg-gray-50 text-left"
                >
                  <div className="text-2xl mb-2">🏢</div>
                  <div className="font-semibold">Manage Tenants</div>
                  <div className="text-sm text-gray-600">Add, edit, or view tenant information</div>
                </button>
                <button
                  onClick={() => handleViewChange('analytics')}
                  className="p-4 border rounded-lg hover:bg-gray-50 text-left"
                >
                  <div className="text-2xl mb-2">📈</div>
                  <div className="font-semibold">View Analytics</div>
                  <div className="text-sm text-gray-600">System analytics and insights</div>
                </button>
                <button
                  onClick={() => handleViewChange('system-health')}
                  className="p-4 border rounded-lg hover:bg-gray-50 text-left"
                >
                  <div className="text-2xl mb-2">🔧</div>
                  <div className="font-semibold">System Health</div>
                  <div className="text-sm text-gray-600">Monitor system performance</div>
                </button>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-red-600 to-pink-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">SA</span>
            </div>
            <div>
              <h1 className="text-xl font-bold">Super Admin Dashboard</h1>
              <p className="text-sm text-gray-600">Multi-Tenant System Management</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Logout
          </button>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="px-6">
          <div className="flex space-x-1 overflow-x-auto">
            {Object.entries(navigationConfig).map(([key, config]) => (
              <button
                key={key}
                onClick={() => handleViewChange(key)}
                className={`flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm whitespace-nowrap ${
                  currentView === key
                    ? 'border-red-500 text-red-600 bg-red-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{config.icon}</span>
                <span>{config.label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        {error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => setError(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : (
          renderContent()
        )}
      </main>

      {/* Status Bar */}
      <footer className="bg-white border-t px-6 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div>Super Admin Dashboard v2.0.0</div>
          <div>{new Date().toLocaleString()}</div>
        </div>
      </footer>
    </div>
  );
}
