import React, { useState, useEffect } from 'react';
import {
  Store,
  Package,
  Users,
  BarChart3,
  Settings,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Bell,
  Menu,
  X,
  ChevronRight,
  Plus,
  Search,
  Filter,
  LogOut,
  User,
  Shield,
  Globe,
  CreditCard,
  FileText,
  Clock,
  Target,
  Zap,
  Building
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import TenantAdminDashboard from './TenantAdminDashboard';
import ProductManagementInterface from './ProductManagementInterface';
import MultiLocationBackendManager from './MultiLocationBackendManager';
import APIConnectivityTest from './APIConnectivityTest';
import CategoryCreationTest from './CategoryCreationTest';
import BusinessRegistrationTest from './BusinessRegistrationTest';

interface EnhancedTenantAdminLandingPageProps {
  onSwitchToPOS: () => void;
  onLogout: () => void;
}

const EnhancedTenantAdminLandingPage: React.FC<EnhancedTenantAdminLandingPageProps> = ({ onSwitchToPOS, onLogout }) => {
  const { user } = useEnhancedAppContext();
  const [currentTab, setCurrentTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);

  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and quick actions'
    },
    {
      id: 'product-management',
      name: 'Product Management',
      icon: Package,
      description: 'Manage menu items and categories'
    },
    {
      id: 'location-management',
      name: 'Location Management',
      icon: MapPin,
      description: 'Multi-location backend management'
    },
    {
      id: 'staff-management',
      name: 'Staff Management',
      icon: Users,
      description: 'Employee management across locations'
    },
    {
      id: 'financial-reports',
      name: 'Financial Reports',
      icon: DollarSign,
      description: 'Revenue and financial analytics'
    },
    {
      id: 'inventory-management',
      name: 'Inventory Management',
      icon: Package,
      description: 'Stock management and transfers'
    },
    {
      id: 'tenant-settings',
      name: 'Settings',
      icon: Settings,
      description: 'Business configuration and preferences'
    },
    {
      id: 'api-debug',
      name: 'API Debug',
      icon: Zap,
      description: 'API connectivity and debugging tools'
    },
    {
      id: 'category-test',
      name: 'Category Test',
      icon: Package,
      description: 'Category creation testing interface'
    },
    {
      id: 'business-test',
      name: 'Business Test',
      icon: Building,
      description: 'Business registration and authentication testing'
    }
  ];

  useEffect(() => {
    // Fetch notifications
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    // Mock notifications - in production, fetch from API
    setNotifications([
      {
        id: 1,
        title: 'Low Stock Alert',
        message: 'Chicken Breast is running low at Downtown location',
        type: 'warning',
        time: '5 minutes ago',
        unread: true
      },
      {
        id: 2,
        title: 'New Staff Member',
        message: 'John Doe has been added to Airport location',
        type: 'info',
        time: '1 hour ago',
        unread: true
      },
      {
        id: 3,
        title: 'Daily Report Ready',
        message: 'Yesterday\'s financial report is available',
        type: 'success',
        time: '2 hours ago',
        unread: false
      }
    ]);
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 'dashboard':
        return <TenantAdminDashboard onNavigate={setCurrentTab} currentTab={currentTab} />;
      case 'product-management':
        return <ProductManagementInterface />;
      case 'location-management':
        return <MultiLocationBackendManager />;
      case 'staff-management':
        return (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Staff Management</h3>
            <p className="mt-1 text-sm text-gray-500">Comprehensive employee management system coming soon</p>
          </div>
        );
      case 'financial-reports':
        return (
          <div className="text-center py-12">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Financial Reports</h3>
            <p className="mt-1 text-sm text-gray-500">Advanced financial reporting and analytics coming soon</p>
          </div>
        );
      case 'inventory-management':
        return (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Inventory Management</h3>
            <p className="mt-1 text-sm text-gray-500">Advanced inventory tracking and management coming soon</p>
          </div>
        );
      case 'tenant-settings':
        return (
          <div className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Tenant Settings</h3>
            <p className="mt-1 text-sm text-gray-500">Business configuration and preferences coming soon</p>
          </div>
        );
      case 'api-debug':
        return <APIConnectivityTest />;
      case 'category-test':
        return <CategoryCreationTest />;
      case 'business-test':
        return <BusinessRegistrationTest />;
      default:
        return <TenantAdminDashboard onNavigate={setCurrentTab} currentTab={currentTab} />;
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'success': return <Target className="h-4 w-4 text-green-500" />;
      case 'info': return <Bell className="h-4 w-4 text-blue-500" />;
      default: return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              >
                {sidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
              <div className="flex-shrink-0 flex items-center ml-4 md:ml-0">
                <Store className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">Restaurant Admin</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Quick Actions */}
              <button
                onClick={onSwitchToPOS}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Zap className="h-4 w-4 mr-2" />
                Switch to POS
              </button>

              {/* Notifications */}
              <div className="relative">
                <button
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="p-2 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Bell className="h-6 w-6" />
                  {notifications.filter(n => n.unread).length > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {notifications.filter(n => n.unread).length}
                    </span>
                  )}
                </button>

                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="text-lg font-medium text-gray-900">Notifications</h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      {notifications.map((notification) => (
                        <div key={notification.id} className={`p-4 border-b border-gray-100 hover:bg-gray-50 ${notification.unread ? 'bg-blue-50' : ''}`}>
                          <div className="flex items-start space-x-3">
                            {getNotificationIcon(notification.type)}
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                              <p className="text-sm text-gray-500">{notification.message}</p>
                              <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                            </div>
                            {notification.unread && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-4 border-t border-gray-200">
                      <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                        View all notifications
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* User Menu */}
              <div className="relative">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <User className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="hidden md:block">
                    <div className="text-sm font-medium text-gray-900">{user?.name || 'Admin User'}</div>
                    <div className="text-xs text-gray-500">{user?.role || 'Tenant Admin'}</div>
                  </div>
                  <button
                    onClick={onLogout}
                    className="p-2 rounded-full text-gray-400 hover:text-gray-500"
                  >
                    <LogOut className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'block' : 'hidden'} md:block md:w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen`}>
          <div className="p-4">
            <nav className="space-y-2">
              {navigationItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    setCurrentTab(item.id);
                    setSidebarOpen(false);
                  }}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    currentTab === item.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  <div className="text-left">
                    <div>{item.name}</div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default EnhancedTenantAdminLandingPage;
