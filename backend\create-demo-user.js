const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function createDemoUser() {
  const client = await pool.connect();
  
  try {
    console.log('Creating demo user and tenant...');
    
    // Check if default tenant exists
    const tenantCheck = await client.query(`
      SELECT id FROM tenants WHERE slug = 'demo-restaurant'
    `);
    
    let tenantId;
    
    if (tenantCheck.rows.length === 0) {
      // Create demo tenant
      console.log('Creating demo tenant...');
      const tenantResult = await client.query(`
        INSERT INTO tenants (id, name, slug, email, status)
        VALUES ($1, 'Demo Restaurant', 'demo-restaurant', '<EMAIL>', 'active')
        RETURNING id
      `, [uuidv4()]);
      
      tenantId = tenantResult.rows[0].id;
      
      // Create tenant settings
      await client.query(`
        INSERT INTO tenant_settings (
          tenant_id, business_name, subscription_plan, tax_rate, currency,
          features, theme_primary_color, theme_secondary_color
        )
        VALUES ($1, 'Demo Restaurant', 'enterprise', 0.08, 'USD', $2, '#3B82F6', '#6B7280')
      `, [
        tenantId,
        JSON.stringify({
          multi_location: true,
          kitchen_display: true,
          loyalty_program: true,
          inventory_management: true,
          advanced_reporting: true,
          third_party_integrations: true,
          custom_branding: true
        })
      ]);
      
      console.log('Demo tenant created successfully!');
    } else {
      tenantId = tenantCheck.rows[0].id;
      console.log('Demo tenant already exists, using existing tenant.');
    }
    
    // Check if demo user exists
    const userCheck = await client.query(`
      SELECT id FROM employees WHERE name = 'Demo Admin' AND tenant_id = $1
    `, [tenantId]);
    
    if (userCheck.rows.length === 0) {
      // Create demo user
      console.log('Creating demo user...');
      const hashedPin = await bcrypt.hash('1234', 10);
      
      const userResult = await client.query(`
        INSERT INTO employees (
          id, name, pin, role, tenant_id, permissions, is_active
        )
        VALUES ($1, $2, $3, $4, $5, $6, true)
        RETURNING id, name, role
      `, [
        uuidv4(),
        'Demo Admin',
        hashedPin,
        'super_admin',
        tenantId,
        JSON.stringify(['all'])
      ]);
      
      console.log('Demo user created successfully!');
      console.log('User details:', userResult.rows[0]);
    } else {
      console.log('Demo user already exists.');
    }
    
    // Create a regular employee as well
    const employeeCheck = await client.query(`
      SELECT id FROM employees WHERE name = 'Demo Employee' AND tenant_id = $1
    `, [tenantId]);
    
    if (employeeCheck.rows.length === 0) {
      console.log('Creating demo employee...');
      const hashedPin = await bcrypt.hash('5678', 10);
      
      await client.query(`
        INSERT INTO employees (
          id, name, pin, role, tenant_id, permissions, is_active
        )
        VALUES ($1, $2, $3, $4, $5, $6, true)
      `, [
        uuidv4(),
        'Demo Employee',
        hashedPin,
        'employee',
        tenantId,
        JSON.stringify([])
      ]);
      
      console.log('Demo employee created successfully!');
    } else {
      console.log('Demo employee already exists.');
    }
    
    console.log('\n=== LOGIN CREDENTIALS ===');
    console.log('🔑 Admin Access:');
    console.log('   PIN: 1234');
    console.log('   Role: Super Admin');
    console.log('   Tenant: demo-restaurant');
    console.log('');
    console.log('🔑 Employee Access:');
    console.log('   PIN: 5678');
    console.log('   Role: Employee');
    console.log('   Tenant: demo-restaurant');
    console.log('');
    console.log('🌐 Access URLs:');
    console.log('   Enhanced Interface: http://localhost:5175/enhanced.html');
    console.log('   Combined Interface: http://localhost:5175/combined.html');
    console.log('   Main Interface: http://localhost:5175/');
    console.log('========================');
    
  } catch (error) {
    console.error('Error creating demo user:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

createDemoUser().catch(console.error);
