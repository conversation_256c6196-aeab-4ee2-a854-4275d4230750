// Backend Integration Test for Super Admin Dashboard
// Tests all endpoints to ensure no mock data is used

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:4000';
let authToken = '';

// Test credentials
const TEST_PIN = '123456';

async function testLogin() {
  console.log('🔐 Testing Super Admin Login...');

  try {
    // Try with tenant slug first
    let response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: TEST_PIN,
        tenantSlug: 'demo-restaurant'
      })
    });

    let data = await response.json();

    // If that fails, try without tenant slug
    if (!response.ok) {
      console.log('   Trying without tenant slug...');
      response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pin: TEST_PIN
        })
      });
      data = await response.json();
    }


    
    if (response.ok && data.token && data.employee?.role === 'super_admin') {
      authToken = data.token;
      console.log('✅ Super Admin login successful');
      console.log(`   Employee: ${data.employee.name} (${data.employee.role})`);
      return true;
    } else {
      console.log('❌ Super Admin login failed:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return false;
  }
}

async function testDatabaseHealth() {
  console.log('🏥 Testing Database Health...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/health/database`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (response.ok && data.database?.connected) {
      console.log('✅ Database connection healthy');
      console.log(`   Database version: ${data.database.version?.substring(0, 50)}...`);
      console.log(`   Pool status: Total=${data.database.pool_total}, Idle=${data.database.pool_idle}`);
      return true;
    } else {
      console.log('❌ Database health check failed:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Database health error:', error.message);
    return false;
  }
}

async function testSystemMetrics() {
  console.log('📊 Testing System Metrics (No Mock Data)...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/metrics/system`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 500) {
      const errorData = await response.json();
      if (errorData.error === 'Database connection failed') {
        console.log('✅ Correctly rejecting mock data - Database connection required');
        return true;
      }
    }

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ System metrics from database');
      console.log(`   Total Tenants: ${data.totalTenants}`);
      console.log(`   Active Tenants: ${data.activeTenants}`);
      console.log(`   Total Revenue: $${data.totalRevenue}`);
      console.log(`   System Uptime: ${data.systemUptime}%`);
      
      // Check if this looks like real data (not the fallback mock values)
      if (data.totalTenants === 156 && data.activeTenants === 142 && data.totalRevenue === 2847392) {
        console.log('❌ WARNING: This appears to be mock fallback data!');
        return false;
      }
      
      return true;
    } else {
      console.log('❌ System metrics failed:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ System metrics error:', error.message);
    return false;
  }
}

async function testTenantData() {
  console.log('🏢 Testing Tenant Data (No Mock Data)...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/tenants`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 500) {
      const errorData = await response.json();
      if (errorData.error === 'Database connection failed') {
        console.log('✅ Correctly rejecting mock data - Database connection required');
        return true;
      }
    }

    const data = await response.json();
    
    if (response.ok && Array.isArray(data)) {
      console.log('✅ Tenant data from database');
      console.log(`   Found ${data.length} tenants`);
      
      if (data.length > 0) {
        console.log(`   Sample tenant: ${data[0].name || data[0].business_name}`);
      }
      
      return true;
    } else {
      console.log('❌ Tenant data failed:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Tenant data error:', error.message);
    return false;
  }
}

async function testUserData() {
  console.log('👥 Testing User Data (No Mock Data)...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/users`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 500) {
      const errorData = await response.json();
      if (errorData.error === 'Database connection failed') {
        console.log('✅ Correctly rejecting mock data - Database connection required');
        return true;
      }
    }

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ User data from database');
      
      if (data.users && Array.isArray(data.users)) {
        console.log(`   Found ${data.users.length} users`);
        console.log(`   Total count: ${data.total}`);
      } else if (Array.isArray(data)) {
        console.log(`   Found ${data.length} users`);
      }
      
      return true;
    } else {
      console.log('❌ User data failed:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ User data error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Backend Integration Tests');
  console.log('=====================================');
  
  const results = {
    login: false,
    database: false,
    metrics: false,
    tenants: false,
    users: false
  };

  // Test login first
  results.login = await testLogin();
  
  if (!results.login) {
    console.log('❌ Cannot proceed without authentication');
    return results;
  }

  // Test database connection
  results.database = await testDatabaseHealth();
  
  // Test all endpoints
  results.metrics = await testSystemMetrics();
  results.tenants = await testTenantData();
  results.users = await testUserData();

  console.log('\n📋 Test Results Summary');
  console.log('========================');
  console.log(`Login Authentication: ${results.login ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Database Connection:  ${results.database ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`System Metrics:       ${results.metrics ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Tenant Data:          ${results.tenants ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`User Data:            ${results.users ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('🎉 Backend integration is working correctly with PostgreSQL database!');
    console.log('🚫 No mock data fallbacks are being used.');
  } else {
    console.log('⚠️ Some issues detected. Check the logs above for details.');
  }

  return results;
}

// Run the tests
runAllTests().catch(console.error);
