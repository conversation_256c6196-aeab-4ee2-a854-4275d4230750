import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Brain,
  Target,
  TrendingUp,
  TrendingDown,
  Heart,
  Star,
  ShoppingCart,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  Eye,
  Zap,
  BarChart3,
  PieChart,
  Activity,
  RefreshCw,
  Filter,
  Search,
  UserCheck,
  UserX,
  Gift,
  MessageSquare
} from 'lucide-react';

interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  customerCount: number;
  avgOrderValue: number;
  frequency: number;
  churnRisk: 'low' | 'medium' | 'high';
  lifetimeValue: number;
  characteristics: string[];
  color: string;
  growthRate: number;
}

interface CustomerBehaviorInsight {
  id: string;
  type: 'pattern' | 'anomaly' | 'opportunity' | 'risk';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  affectedCustomers: number;
  potentialValue: number;
  actionRecommendation: string;
  timestamp: Date;
}

interface CustomerProfile {
  id: string;
  name: string;
  email: string;
  segment: string;
  totalOrders: number;
  totalSpent: number;
  avgOrderValue: number;
  lastVisit: Date;
  churnProbability: number;
  lifetimeValue: number;
  preferredItems: string[];
  visitPattern: string;
  satisfactionScore: number;
  loyaltyTier: 'bronze' | 'silver' | 'gold' | 'platinum';
}

export function Phase3DAICustomerBehaviorAnalysis() {
  const [customerSegments, setCustomerSegments] = useState<CustomerSegment[]>([]);
  const [behaviorInsights, setBehaviorInsights] = useState<CustomerBehaviorInsight[]>([]);
  const [customerProfiles, setCustomerProfiles] = useState<CustomerProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedView, setSelectedView] = useState<'segments' | 'insights' | 'profiles'>('segments');

  const mockCustomerSegments: CustomerSegment[] = [
    {
      id: 'vip-customers',
      name: 'VIP Customers',
      description: 'High-value customers with frequent visits and large orders',
      customerCount: 156,
      avgOrderValue: 89.50,
      frequency: 3.2,
      churnRisk: 'low',
      lifetimeValue: 2850.00,
      characteristics: ['High AOV', 'Frequent visits', 'Premium items', 'Low price sensitivity'],
      color: 'bg-purple-500',
      growthRate: 12.5
    },
    {
      id: 'regular-customers',
      name: 'Regular Customers',
      description: 'Consistent customers with moderate spending and regular visits',
      customerCount: 423,
      avgOrderValue: 45.20,
      frequency: 2.1,
      churnRisk: 'low',
      lifetimeValue: 1250.00,
      characteristics: ['Consistent visits', 'Moderate AOV', 'Loyal to favorites', 'Price conscious'],
      color: 'bg-green-500',
      growthRate: 8.3
    },
    {
      id: 'occasional-customers',
      name: 'Occasional Customers',
      description: 'Infrequent visitors with variable spending patterns',
      customerCount: 789,
      avgOrderValue: 32.80,
      frequency: 0.8,
      churnRisk: 'medium',
      lifetimeValue: 420.00,
      characteristics: ['Infrequent visits', 'Variable AOV', 'Event-driven', 'Promotion sensitive'],
      color: 'bg-yellow-500',
      growthRate: -2.1
    },
    {
      id: 'at-risk-customers',
      name: 'At-Risk Customers',
      description: 'Previously active customers showing declining engagement',
      customerCount: 234,
      avgOrderValue: 38.90,
      frequency: 0.3,
      churnRisk: 'high',
      lifetimeValue: 680.00,
      characteristics: ['Declining visits', 'Reduced AOV', 'Long gaps', 'Needs re-engagement'],
      color: 'bg-red-500',
      growthRate: -15.7
    },
    {
      id: 'new-customers',
      name: 'New Customers',
      description: 'Recent customers in their first 30 days',
      customerCount: 167,
      avgOrderValue: 28.60,
      frequency: 1.4,
      churnRisk: 'medium',
      lifetimeValue: 180.00,
      characteristics: ['Recent signup', 'Exploring menu', 'First impressions', 'Conversion critical'],
      color: 'bg-blue-500',
      growthRate: 25.8
    }
  ];

  const mockBehaviorInsights: CustomerBehaviorInsight[] = [
    {
      id: 'insight-1',
      type: 'opportunity',
      title: 'Weekend Dinner Upselling Opportunity',
      description: 'VIP customers show 34% higher willingness to order premium items on weekend evenings',
      confidence: 92.4,
      impact: 'high',
      affectedCustomers: 156,
      potentialValue: 2840.00,
      actionRecommendation: 'Implement targeted premium menu recommendations for VIP customers on weekends',
      timestamp: new Date(Date.now() - 1800000)
    },
    {
      id: 'insight-2',
      type: 'risk',
      title: 'Churn Risk Pattern Detected',
      description: 'Regular customers with 3+ week gaps between visits have 67% churn probability',
      confidence: 89.7,
      impact: 'high',
      affectedCustomers: 89,
      potentialValue: -5670.00,
      actionRecommendation: 'Send personalized re-engagement offers to customers with 2+ week gaps',
      timestamp: new Date(Date.now() - 3600000)
    },
    {
      id: 'insight-3',
      type: 'pattern',
      title: 'Lunch Rush Behavior Shift',
      description: 'Office workers now prefer quick-service items during 12-1 PM peak hours',
      confidence: 94.1,
      impact: 'medium',
      affectedCustomers: 312,
      potentialValue: 1890.00,
      actionRecommendation: 'Optimize lunch menu for speed and introduce express ordering options',
      timestamp: new Date(Date.now() - 7200000)
    },
    {
      id: 'insight-4',
      type: 'anomaly',
      title: 'Unusual Ordering Pattern',
      description: 'Significant increase in vegetarian orders among 25-35 age group (+45% this month)',
      confidence: 87.3,
      impact: 'medium',
      affectedCustomers: 198,
      potentialValue: 1240.00,
      actionRecommendation: 'Expand vegetarian menu options and promote plant-based alternatives',
      timestamp: new Date(Date.now() - 10800000)
    }
  ];

  const mockCustomerProfiles: CustomerProfile[] = [
    {
      id: 'cust-001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      segment: 'VIP Customers',
      totalOrders: 47,
      totalSpent: 2890.50,
      avgOrderValue: 61.50,
      lastVisit: new Date(Date.now() - 172800000), // 2 days ago
      churnProbability: 8.2,
      lifetimeValue: 3450.00,
      preferredItems: ['Truffle Pasta', 'Premium Wine', 'Chocolate Dessert'],
      visitPattern: 'Weekend evenings',
      satisfactionScore: 4.8,
      loyaltyTier: 'platinum'
    },
    {
      id: 'cust-002',
      name: 'Mike Chen',
      email: '<EMAIL>',
      segment: 'Regular Customers',
      totalOrders: 23,
      totalSpent: 1240.80,
      avgOrderValue: 54.00,
      lastVisit: new Date(Date.now() - 432000000), // 5 days ago
      churnProbability: 15.7,
      lifetimeValue: 1890.00,
      preferredItems: ['Burger', 'Craft Beer', 'Fries'],
      visitPattern: 'Weekday lunch',
      satisfactionScore: 4.3,
      loyaltyTier: 'gold'
    },
    {
      id: 'cust-003',
      name: 'Emma Davis',
      email: '<EMAIL>',
      segment: 'At-Risk Customers',
      totalOrders: 12,
      totalSpent: 456.70,
      avgOrderValue: 38.00,
      lastVisit: new Date(Date.now() - 1814400000), // 21 days ago
      churnProbability: 73.4,
      lifetimeValue: 680.00,
      preferredItems: ['Salad', 'Smoothie', 'Soup'],
      visitPattern: 'Irregular',
      satisfactionScore: 3.9,
      loyaltyTier: 'silver'
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setCustomerSegments(mockCustomerSegments);
      setBehaviorInsights(mockBehaviorInsights);
      setCustomerProfiles(mockCustomerProfiles);
      setLoading(false);
    }, 1000);
  }, []);

  const getInsightTypeColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'bg-green-100 text-green-800';
      case 'risk': return 'bg-red-100 text-red-800';
      case 'pattern': return 'bg-blue-100 text-blue-800';
      case 'anomaly': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightTypeIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="h-4 w-4" />;
      case 'risk': return <AlertTriangle className="h-4 w-4" />;
      case 'pattern': return <BarChart3 className="h-4 w-4" />;
      case 'anomaly': return <Zap className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getChurnRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getLoyaltyTierColor = (tier: string) => {
    switch (tier) {
      case 'platinum': return 'bg-purple-100 text-purple-800';
      case 'gold': return 'bg-yellow-100 text-yellow-800';
      case 'silver': return 'bg-gray-100 text-gray-800';
      case 'bronze': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">AI Customer Behavior Analysis</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3D AI Customer Behavior Analysis</h2>
          <p className="text-gray-600">Advanced customer segmentation and behavioral insights</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Analyze Behavior
          </Button>
          <Button variant="outline" size="sm">
            <Target className="h-4 w-4 mr-2" />
            Create Campaign
          </Button>
        </div>
      </div>

      {/* View Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={selectedView === 'segments' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('segments')}
        >
          <Users className="h-4 w-4 mr-2" />
          Customer Segments
        </Button>
        <Button
          variant={selectedView === 'insights' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('insights')}
        >
          <Brain className="h-4 w-4 mr-2" />
          Behavior Insights
        </Button>
        <Button
          variant={selectedView === 'profiles' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('profiles')}
        >
          <UserCheck className="h-4 w-4 mr-2" />
          Customer Profiles
        </Button>
      </div>

      {/* Customer Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold text-blue-600">
                  {customerSegments.reduce((sum, segment) => sum + segment.customerCount, 0).toLocaleString()}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Customer LTV</p>
                <p className="text-2xl font-bold text-green-600">
                  ${Math.round(customerSegments.reduce((sum, segment) => sum + (segment.lifetimeValue * segment.customerCount), 0) / customerSegments.reduce((sum, segment) => sum + segment.customerCount, 0)).toLocaleString()}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">At-Risk Customers</p>
                <p className="text-2xl font-bold text-red-600">
                  {customerSegments.find(s => s.id === 'at-risk-customers')?.customerCount || 0}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">New Insights</p>
                <p className="text-2xl font-bold text-purple-600">
                  {behaviorInsights.length}
                </p>
              </div>
              <Brain className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {selectedView === 'segments' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {customerSegments.map((segment) => (
            <Card key={segment.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${segment.color}`}></div>
                    <div>
                      <CardTitle className="text-lg">{segment.name}</CardTitle>
                      <p className="text-sm text-gray-600">{segment.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${segment.growthRate > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {segment.growthRate > 0 ? '+' : ''}{segment.growthRate}%
                    </div>
                    <div className="text-xs text-gray-500">growth</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Customers</p>
                      <p className="text-xl font-bold">{segment.customerCount}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Avg Order Value</p>
                      <p className="text-xl font-bold">${segment.avgOrderValue}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Visit Frequency</p>
                      <p className="text-xl font-bold">{segment.frequency}/month</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Lifetime Value</p>
                      <p className="text-xl font-bold">${segment.lifetimeValue}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">Churn Risk</p>
                    <div className={`text-sm font-medium ${getChurnRiskColor(segment.churnRisk)} capitalize`}>
                      {segment.churnRisk}
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">Key Characteristics</p>
                    <div className="flex flex-wrap gap-1">
                      {segment.characteristics.map((char, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {char}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="h-3 w-3 mr-1" />
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <Target className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedView === 'insights' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">AI-Generated Behavior Insights</h3>
          {behaviorInsights.map((insight) => (
            <Card key={insight.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {getInsightTypeIcon(insight.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{insight.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getInsightTypeColor(insight.type)}>
                          {insight.type}
                        </Badge>
                        <span className={`text-sm font-medium ${
                          insight.impact === 'high' ? 'text-red-600' : 
                          insight.impact === 'medium' ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {insight.impact} impact
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{insight.confidence}%</div>
                    <div className="text-xs text-gray-500">confidence</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600">{insight.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Affected Customers</p>
                      <p className="font-semibold">{insight.affectedCustomers.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Potential Value</p>
                      <p className={`font-semibold ${insight.potentialValue > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {insight.potentialValue > 0 ? '+' : ''}${Math.abs(insight.potentialValue).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                    <p className="text-sm font-medium text-blue-900">Recommended Action:</p>
                    <p className="text-sm text-blue-800">{insight.actionRecommendation}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {insight.timestamp.toLocaleString()}
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        Details
                      </Button>
                      <Button size="sm">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Implement
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedView === 'profiles' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Customer Profiles</h3>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            {customerProfiles.map((profile) => (
              <Card key={profile.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-6 w-6 text-gray-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg">{profile.name}</h4>
                        <p className="text-sm text-gray-600">{profile.email}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline">{profile.segment}</Badge>
                          <Badge className={getLoyaltyTierColor(profile.loyaltyTier)}>
                            {profile.loyaltyTier}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${
                        profile.churnProbability < 20 ? 'text-green-600' : 
                        profile.churnProbability < 50 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {profile.churnProbability.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-500">churn risk</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">Total Orders</p>
                      <p className="font-semibold">{profile.totalOrders}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Total Spent</p>
                      <p className="font-semibold">${profile.totalSpent.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Avg Order Value</p>
                      <p className="font-semibold">${profile.avgOrderValue}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Lifetime Value</p>
                      <p className="font-semibold">${profile.lifetimeValue.toLocaleString()}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Preferred Items</p>
                      <div className="flex flex-wrap gap-1">
                        {profile.preferredItems.map((item, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {item}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Visit Pattern</p>
                      <p className="text-sm font-medium">{profile.visitPattern}</p>
                      <p className="text-sm text-gray-600">Last Visit</p>
                      <p className="text-sm font-medium">{profile.lastVisit.toLocaleDateString()}</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium">{profile.satisfactionScore}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Contact
                      </Button>
                      <Button size="sm" variant="outline">
                        <Gift className="h-3 w-3 mr-1" />
                        Offer
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
