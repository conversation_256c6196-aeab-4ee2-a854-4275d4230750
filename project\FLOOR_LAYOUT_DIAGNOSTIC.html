<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 Floor Layout & Restaurant Admin Diagnostic Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .diagnostic-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .diagnostic-card h3 {
            margin-bottom: 15px;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .test-button.danger {
            background: linear-gradient(45deg, #f44336, #d32f2f);
        }
        
        .test-button.warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }
        
        .test-button.info {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.3);
            border-left: 4px solid #4CAF50;
        }
        
        .error {
            background: rgba(244, 67, 54, 0.3);
            border-left: 4px solid #f44336;
        }
        
        .warning {
            background: rgba(255, 152, 0, 0.3);
            border-left: 4px solid #ff9800;
        }
        
        .logs {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info {
            background: rgba(33, 150, 243, 0.2);
        }
        
        .log-success {
            background: rgba(76, 175, 80, 0.2);
        }
        
        .log-error {
            background: rgba(244, 67, 54, 0.2);
        }
        
        .log-warning {
            background: rgba(255, 152, 0, 0.2);
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .quick-action {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px 25px;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .quick-action:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Floor Layout & Restaurant Admin Diagnostic Tool</h1>
            <p>Comprehensive testing for POS system critical functionality</p>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="http://localhost:5173" target="_blank" class="quick-action">
                🖥️ Open POS System
            </a>
            <a href="http://localhost:4000/api/floor/layout" target="_blank" class="quick-action">
                🏢 Floor Layout API
            </a>
            <a href="http://localhost:4000/api/floor/tables" target="_blank" class="quick-action">
                🪑 Tables API
            </a>
            <a href="#" onclick="runFullDiagnostic()" class="quick-action">
                🚀 Run Full Diagnostic
            </a>
        </div>

        <!-- Diagnostic Categories -->
        <div class="diagnostic-grid">
            <!-- Authentication & Access -->
            <div class="diagnostic-card">
                <h3>🔐 Authentication & Access</h3>
                <button class="test-button" onclick="testAdminLogin()">Admin Login Test</button>
                <button class="test-button" onclick="testPermissions()">Permission Check</button>
                <button class="test-button info" onclick="testTokenValidation()">Token Validation</button>
                <div id="auth-result" class="result" style="display: none;"></div>
            </div>

            <!-- Floor Layout API Tests -->
            <div class="diagnostic-card">
                <h3>🏢 Floor Layout API</h3>
                <button class="test-button" onclick="testFloorLayoutAPI()">Layout API Test</button>
                <button class="test-button" onclick="testTablesAPI()">Tables API Test</button>
                <button class="test-button" onclick="testTableCRUD()">Table CRUD Test</button>
                <button class="test-button warning" onclick="testTableStatusFlow()">Status Flow Test</button>
                <div id="floor-api-result" class="result" style="display: none;"></div>
            </div>

            <!-- Frontend Integration -->
            <div class="diagnostic-card">
                <h3>🖥️ Frontend Integration</h3>
                <button class="test-button" onclick="testFloorLayoutComponent()">Floor Component Test</button>
                <button class="test-button" onclick="testOrderIntegration()">Order Integration</button>
                <button class="test-button" onclick="testRealTimeUpdates()">Real-time Updates</button>
                <button class="test-button info" onclick="testUIResponsiveness()">UI Responsiveness</button>
                <div id="frontend-result" class="result" style="display: none;"></div>
            </div>

            <!-- Restaurant Admin Features -->
            <div class="diagnostic-card">
                <h3>👑 Restaurant Admin</h3>
                <button class="test-button" onclick="testAdminTabs()">Admin Tabs Access</button>
                <button class="test-button" onclick="testAdminCRUD()">Admin CRUD Ops</button>
                <button class="test-button" onclick="testRoleBasedAccess()">Role-Based Access</button>
                <button class="test-button warning" onclick="testAdminNavigation()">Navigation Test</button>
                <div id="admin-result" class="result" style="display: none;"></div>
            </div>

            <!-- Dine-in Workflow -->
            <div class="diagnostic-card">
                <h3>🍽️ Dine-in Workflow</h3>
                <button class="test-button" onclick="testTableSelection()">Table Selection</button>
                <button class="test-button" onclick="testOrderCreation()">Order Creation</button>
                <button class="test-button" onclick="testPaymentFlow()">Payment Flow</button>
                <button class="test-button danger" onclick="testCompleteWorkflow()">Complete Workflow</button>
                <div id="workflow-result" class="result" style="display: none;"></div>
            </div>

            <!-- Error Detection -->
            <div class="diagnostic-card">
                <h3>🐛 Error Detection</h3>
                <button class="test-button" onclick="checkConsoleErrors()">Console Errors</button>
                <button class="test-button" onclick="checkNetworkErrors()">Network Errors</button>
                <button class="test-button" onclick="checkAPIEndpoints()">API Endpoints</button>
                <button class="test-button warning" onclick="checkDependencies()">Dependencies</button>
                <div id="error-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- Diagnostic Logs -->
        <div class="logs" id="diagnostic-logs">
            <h4>📋 Diagnostic Execution Logs</h4>
            <div id="log-entries">
                <div class="log-entry log-info">
                    [INFO] Floor Layout & Restaurant Admin Diagnostic Tool initialized
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000';
        const FRONTEND_BASE = 'http://localhost:5173';
        let authToken = null;
        
        // Logging function
        function addLog(message, type = 'info') {
            const logEntries = document.getElementById('log-entries');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntries.appendChild(logEntry);
            logEntries.scrollTop = logEntries.scrollHeight;
        }
        
        // Show result function
        function showResult(elementId, message, isSuccess = false) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
        }
        
        // Authentication Tests
        async function testAdminLogin() {
            addLog('Testing admin login with enhanced privileges...', 'info');
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ pin: '123456', tenant_slug: 'demo-restaurant' })
                });
                
                const data = await response.json();
                if (response.ok && data.success) {
                    authToken = data.token;
                    const hasFullAccess = data.user.permissions?.includes('all') || data.user.permissions?.includes('tenant_management');
                    
                    showResult('auth-result', `✅ Admin login successful!<br>Role: ${data.user.role}<br>Full Access: ${hasFullAccess ? 'YES' : 'NO'}<br>Token: ${authToken ? 'Generated' : 'Missing'}<br>Permissions: ${data.user.permissions?.length || 0} total`, true);
                    addLog('Admin login test PASSED', 'success');
                } else {
                    showResult('auth-result', `❌ Admin login failed: ${data.error}`, false);
                    addLog('Admin login test FAILED', 'error');
                }
            } catch (error) {
                showResult('auth-result', `❌ Admin login error: ${error.message}`, false);
                addLog(`Admin login test ERROR: ${error.message}`, 'error');
            }
        }
        
        async function testPermissions() {
            if (!authToken) {
                showResult('auth-result', `⚠️ Please run Admin Login Test first`, false);
                return;
            }
            
            addLog('Testing admin permissions on various endpoints...', 'info');
            try {
                const testEndpoints = [
                    { url: '/api/products', name: 'Products' },
                    { url: '/api/categories', name: 'Categories' },
                    { url: '/api/floor/layout', name: 'Floor Layout' },
                    { url: '/api/floor/tables', name: 'Tables' },
                    { url: '/api/employees', name: 'Employees' }
                ];
                
                let results = [];
                for (const endpoint of testEndpoints) {
                    try {
                        const response = await fetch(`${API_BASE}${endpoint.url}`, {
                            headers: { 'Authorization': `Bearer ${authToken}` }
                        });
                        results.push(`${endpoint.name}: ${response.ok ? '✅ Accessible' : '❌ Denied'}`);
                    } catch (error) {
                        results.push(`${endpoint.name}: ❌ Error`);
                    }
                }
                
                showResult('auth-result', `✅ Permission test complete!<br><br>${results.join('<br>')}`, true);
                addLog('Permission test PASSED', 'success');
            } catch (error) {
                showResult('auth-result', `❌ Permission test error: ${error.message}`, false);
                addLog(`Permission test ERROR: ${error.message}`, 'error');
            }
        }
        
        // Floor Layout API Tests
        async function testFloorLayoutAPI() {
            addLog('Testing floor layout API endpoint...', 'info');
            try {
                const response = await fetch(`${API_BASE}/api/floor/layout`);
                if (response.ok) {
                    const layout = await response.json();
                    const tableCount = layout.tables ? layout.tables.length : 0;
                    
                    showResult('floor-api-result', `✅ Floor Layout API working!<br>Layout: ${layout.name}<br>Tables: ${tableCount}<br>Dimensions: ${layout.width}x${layout.height}<br>Sections: ${layout.sections?.length || 0}`, true);
                    addLog(`Floor Layout API test PASSED - ${tableCount} tables`, 'success');
                } else {
                    showResult('floor-api-result', `❌ Floor Layout API error: ${response.status}`, false);
                    addLog('Floor Layout API test FAILED', 'error');
                }
            } catch (error) {
                showResult('floor-api-result', `❌ Floor Layout API error: ${error.message}`, false);
                addLog(`Floor Layout API test ERROR: ${error.message}`, 'error');
            }
        }
        
        async function testTablesAPI() {
            addLog('Testing tables API endpoint...', 'info');
            try {
                const response = await fetch(`${API_BASE}/api/floor/tables`);
                if (response.ok) {
                    const tables = await response.json();
                    const statusCounts = tables.reduce((acc, table) => {
                        acc[table.status] = (acc[table.status] || 0) + 1;
                        return acc;
                    }, {});
                    
                    const statusSummary = Object.entries(statusCounts)
                        .map(([status, count]) => `${status}: ${count}`)
                        .join('<br>');
                    
                    showResult('floor-api-result', `✅ Tables API working!<br>Total Tables: ${tables.length}<br><br>Status Breakdown:<br>${statusSummary}`, true);
                    addLog(`Tables API test PASSED - ${tables.length} tables`, 'success');
                } else {
                    showResult('floor-api-result', `❌ Tables API error: ${response.status}`, false);
                    addLog('Tables API test FAILED', 'error');
                }
            } catch (error) {
                showResult('floor-api-result', `❌ Tables API error: ${error.message}`, false);
                addLog(`Tables API test ERROR: ${error.message}`, 'error');
            }
        }
        
        // Placeholder functions for remaining tests
        function testTokenValidation() { addLog('Token validation test - Manual verification required', 'warning'); }
        function testTableCRUD() { addLog('Table CRUD test - Implementation needed', 'warning'); }
        function testTableStatusFlow() { addLog('Table status flow test - Implementation needed', 'warning'); }
        function testFloorLayoutComponent() { addLog('Floor layout component test - Manual verification required', 'warning'); }
        function testOrderIntegration() { addLog('Order integration test - Implementation needed', 'warning'); }
        function testRealTimeUpdates() { addLog('Real-time updates test - Implementation needed', 'warning'); }
        function testUIResponsiveness() { addLog('UI responsiveness test - Manual verification required', 'warning'); }
        function testAdminTabs() { addLog('Admin tabs test - Manual verification required', 'warning'); }
        function testAdminCRUD() { addLog('Admin CRUD test - Implementation needed', 'warning'); }
        function testRoleBasedAccess() { addLog('Role-based access test - Implementation needed', 'warning'); }
        function testAdminNavigation() { addLog('Admin navigation test - Manual verification required', 'warning'); }
        function testTableSelection() { addLog('Table selection test - Implementation needed', 'warning'); }
        function testOrderCreation() { addLog('Order creation test - Implementation needed', 'warning'); }
        function testPaymentFlow() { addLog('Payment flow test - Implementation needed', 'warning'); }
        function testCompleteWorkflow() { addLog('Complete workflow test - Implementation needed', 'warning'); }
        function checkConsoleErrors() { addLog('Console errors check - Manual verification required', 'warning'); }
        function checkNetworkErrors() { addLog('Network errors check - Implementation needed', 'warning'); }
        function checkAPIEndpoints() { addLog('API endpoints check - Implementation needed', 'warning'); }
        function checkDependencies() { addLog('Dependencies check - Implementation needed', 'warning'); }
        
        // Run full diagnostic
        async function runFullDiagnostic() {
            addLog('🚀 Starting full diagnostic suite...', 'info');
            
            await testAdminLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (authToken) {
                await testPermissions();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            await testFloorLayoutAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTablesAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            addLog('✅ Full diagnostic suite completed!', 'success');
        }
        
        // Initialize on page load
        window.onload = function() {
            addLog('🏢 Floor Layout & Restaurant Admin Diagnostic Tool ready', 'info');
        };
    </script>
</body>
</html>
