openapi: 3.0.3
info:
  title: RestroFlow POS - Advanced Authentication API
  description: |
    Comprehensive API documentation for RestroFlow POS Advanced Authentication System.
    
    This API provides enterprise-grade authentication with advanced security features including:
    - Multi-factor authentication (MFA)
    - Biometric authentication
    - QR code login
    - Emergency access
    - Comprehensive audit trails
    - Performance monitoring
    - Real-time security analytics
    
    ## Authentication
    Most endpoints require JWT token authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API endpoints are rate limited to prevent abuse:
    - Login endpoints: 5 attempts per 15 minutes
    - Admin endpoints: 3 attempts per 10 minutes
    - General API: 100 requests per minute
    
    ## Error Handling
    The API uses standard HTTP status codes and returns detailed error information:
    ```json
    {
      "error": "Error message",
      "code": "ERROR_CODE",
      "details": {},
      "timestamp": "2024-01-01T00:00:00Z"
    }
    ```
  version: 2.0.0
  contact:
    name: RestroFlow Support
    email: <EMAIL>
    url: https://restroflow.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:4000/api
    description: Development server
  - url: https://api.restroflow.com/api
    description: Production server

tags:
  - name: Authentication
    description: Core authentication endpoints
  - name: Multi-Factor Authentication
    description: MFA setup and verification
  - name: Mobile Authentication
    description: QR code and biometric authentication
  - name: Emergency Access
    description: Emergency authentication methods
  - name: Security
    description: Security monitoring and management
  - name: Audit
    description: Audit trail and compliance
  - name: Analytics
    description: Performance and usage analytics
  - name: Admin
    description: Administrative functions
  - name: Health
    description: System health and monitoring

paths:
  /health:
    get:
      tags: [Health]
      summary: System health check
      description: Check the overall health of the authentication system
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "2.0.0"
                  uptime:
                    type: number
                    example: 86400
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                        example: connected
                      redis:
                        type: string
                        example: connected
                      email:
                        type: string
                        example: operational

  /auth/login:
    post:
      tags: [Authentication]
      summary: Employee/Admin login
      description: |
        Authenticate users with PIN and optional tenant selection.
        Supports both employee and super admin authentication.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [pin]
              properties:
                pin:
                  type: string
                  description: User PIN (4-6 digits)
                  example: "123456"
                tenant_slug:
                  type: string
                  description: Tenant identifier (required for employee login)
                  example: "demo-restaurant"
                admin_access:
                  type: boolean
                  description: Set to true for super admin login
                  example: false
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT authentication token
                  employee:
                    $ref: '#/components/schemas/Employee'
                  tenant:
                    $ref: '#/components/schemas/Tenant'
                  mfa_required:
                    type: boolean
                    description: Whether MFA is required
                  mfa_methods:
                    type: array
                    items:
                      type: string
                    example: ["sms", "email", "authenticator"]
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'

  /auth/verify:
    get:
      tags: [Authentication]
      summary: Verify JWT token
      description: Validate the current JWT token and return user information
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Token is valid
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    example: true
                  employee:
                    $ref: '#/components/schemas/Employee'
                  tenant:
                    $ref: '#/components/schemas/Tenant'
                  expires_at:
                    type: string
                    format: date-time
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/mfa/send-otp:
    post:
      tags: [Multi-Factor Authentication]
      summary: Send OTP for MFA
      description: Send one-time password via SMS or email
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [employeeId, tenantId, method]
              properties:
                employeeId:
                  type: integer
                  example: 123
                tenantId:
                  type: integer
                  example: 456
                method:
                  type: string
                  enum: [sms, email]
                  example: sms
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "OTP sent to your phone"
                  expires_in:
                    type: integer
                    description: OTP expiry time in seconds
                    example: 300
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/mfa/verify-otp:
    post:
      tags: [Multi-Factor Authentication]
      summary: Verify OTP
      description: Verify the OTP code for multi-factor authentication
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [employeeId, tenantId, code, method]
              properties:
                employeeId:
                  type: integer
                  example: 123
                tenantId:
                  type: integer
                  example: 456
                code:
                  type: string
                  description: 6-digit OTP code
                  example: "123456"
                method:
                  type: string
                  enum: [sms, email]
                  example: sms
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                    description: Updated JWT token with MFA completion
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/qr/register:
    post:
      tags: [Mobile Authentication]
      summary: Register QR code session
      description: Register a new QR code session for mobile authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [sessionId, tenantSlug, expiresAt]
              properties:
                sessionId:
                  type: string
                  example: "qr_1234567890_abcdef"
                tenantSlug:
                  type: string
                  example: "demo-restaurant"
                timestamp:
                  type: integer
                  example: 1640995200000
                expiresAt:
                  type: integer
                  example: 1640995500000
                serverUrl:
                  type: string
                  example: "http://localhost:4000"
      responses:
        '200':
          description: QR session registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  sessionId:
                    type: string
                    example: "qr_1234567890_abcdef"

  /auth/qr/status/{sessionId}:
    get:
      tags: [Mobile Authentication]
      summary: Check QR code status
      description: Check the status of a QR code authentication session
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "qr_1234567890_abcdef"
      responses:
        '200':
          description: QR session status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum: [waiting, scanned, authenticated, expired]
                    example: "waiting"
                  token:
                    type: string
                    description: JWT token (only when status is authenticated)
                  employee:
                    $ref: '#/components/schemas/Employee'
                  tenant:
                    $ref: '#/components/schemas/Tenant'

  /auth/biometric:
    post:
      tags: [Mobile Authentication]
      summary: Biometric authentication
      description: Authenticate using biometric data (WebAuthn)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [credentialId, authenticatorData, signature]
              properties:
                credentialId:
                  type: string
                  description: WebAuthn credential ID
                authenticatorData:
                  type: array
                  items:
                    type: integer
                  description: Authenticator data bytes
                signature:
                  type: array
                  items:
                    type: integer
                  description: Authentication signature bytes
                tenantSlug:
                  type: string
                  example: "demo-restaurant"
                biometricType:
                  type: string
                  enum: [fingerprint, faceId]
                  example: "fingerprint"
      responses:
        '200':
          description: Biometric authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                  employee:
                    $ref: '#/components/schemas/Employee'
                  tenant:
                    $ref: '#/components/schemas/Tenant'

  /auth/emergency/master-key:
    post:
      tags: [Emergency Access]
      summary: Master key emergency access
      description: Authenticate using master emergency key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [masterKey, reason, contactInfo]
              properties:
                masterKey:
                  type: string
                  description: Master emergency access key
                tenantSlug:
                  type: string
                  example: "demo-restaurant"
                reason:
                  type: string
                  description: Emergency reason
                  example: "System lockout during peak hours"
                contactInfo:
                  type: object
                  properties:
                    name:
                      type: string
                      example: "John Doe"
                    phone:
                      type: string
                      example: "+1234567890"
                    email:
                      type: string
                      example: "<EMAIL>"
      responses:
        '200':
          description: Emergency access granted
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  token:
                    type: string
                  access_level:
                    type: string
                    example: "emergency"
                  expires_at:
                    type: string
                    format: date-time

  /security/events:
    post:
      tags: [Security]
      summary: Log security event
      description: Log a security event for monitoring and analysis
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [events]
              properties:
                events:
                  type: array
                  items:
                    $ref: '#/components/schemas/SecurityEvent'
      responses:
        '200':
          description: Security events logged successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  events_logged:
                    type: integer
                    example: 5

  /audit/events:
    get:
      tags: [Audit]
      summary: Get audit events
      description: Retrieve audit events with optional filtering
      security:
        - bearerAuth: []
      parameters:
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-01-01"
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          example: "2024-12-31"
        - name: eventType
          in: query
          schema:
            type: string
            enum: [authentication, authorization, data_access, system_change, security_incident]
        - name: userId
          in: query
          schema:
            type: integer
        - name: tenantId
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Audit events retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  events:
                    type: array
                    items:
                      $ref: '#/components/schemas/AuditEvent'
                  total:
                    type: integer
                    example: 1500
                  limit:
                    type: integer
                    example: 100
                  offset:
                    type: integer
                    example: 0

  /analytics/login-analytics:
    get:
      tags: [Analytics]
      summary: Get login analytics
      description: Retrieve comprehensive login analytics and statistics
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          schema:
            type: string
            format: date
        - name: end
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Login analytics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginAnalytics'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Employee:
      type: object
      properties:
        id:
          type: integer
          example: 123
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          example: "<EMAIL>"
        role:
          type: string
          enum: [employee, manager, tenant_admin, super_admin]
          example: "manager"
        permissions:
          type: array
          items:
            type: string
          example: ["pos_access", "reports_view"]
        is_active:
          type: boolean
          example: true

    Tenant:
      type: object
      properties:
        id:
          type: integer
          example: 456
        name:
          type: string
          example: "Demo Restaurant"
        slug:
          type: string
          example: "demo-restaurant"
        business_name:
          type: string
          example: "Demo Restaurant LLC"
        business_type:
          type: string
          example: "restaurant"
        status:
          type: string
          enum: [active, inactive, suspended]
          example: "active"

    SecurityEvent:
      type: object
      properties:
        id:
          type: string
          example: "sec_1234567890_abcdef"
        timestamp:
          type: string
          format: date-time
        type:
          type: string
          enum: [login_attempt, login_success, login_failure, suspicious_activity, account_locked]
        userId:
          type: integer
          example: 123
        tenantId:
          type: integer
          example: 456
        ipAddress:
          type: string
          example: "*************"
        userAgent:
          type: string
          example: "Mozilla/5.0..."
        riskScore:
          type: integer
          minimum: 0
          maximum: 100
          example: 25
        blocked:
          type: boolean
          example: false
        details:
          type: object

    AuditEvent:
      type: object
      properties:
        id:
          type: string
          example: "audit_1234567890_abcdef"
        timestamp:
          type: string
          format: date-time
        eventType:
          type: string
          enum: [authentication, authorization, data_access, system_change, security_incident]
        action:
          type: string
          example: "login_success"
        userId:
          type: integer
          example: 123
        userName:
          type: string
          example: "John Doe"
        tenantId:
          type: integer
          example: 456
        resource:
          type: string
          example: "orders"
        success:
          type: boolean
          example: true
        riskLevel:
          type: string
          enum: [low, medium, high, critical]
          example: "low"

    LoginAnalytics:
      type: object
      properties:
        totalLogins:
          type: integer
          example: 1500
        successfulLogins:
          type: integer
          example: 1450
        failedLogins:
          type: integer
          example: 50
        averageLoginTime:
          type: number
          example: 1250.5
        peakLoginHours:
          type: array
          items:
            type: integer
          example: [9, 12, 18]
        loginsByMethod:
          type: object
          properties:
            pin:
              type: integer
              example: 1200
            qr_code:
              type: integer
              example: 200
            biometric:
              type: integer
              example: 100
        uniqueUsers:
          type: integer
          example: 45

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Invalid request parameters"
              code:
                type: string
                example: "BAD_REQUEST"

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Invalid credentials"
              code:
                type: string
                example: "UNAUTHORIZED"

    RateLimited:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: "Rate limit exceeded"
              code:
                type: string
                example: "RATE_LIMITED"
              retry_after:
                type: integer
                example: 900
