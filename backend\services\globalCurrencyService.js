// Global Currency Service for Phase 6
// Multi-currency support, exchange rates, and international pricing

const { Pool } = require('pg');
const axios = require('axios');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class GlobalCurrencyService {
  constructor() {
    this.exchangeRateProviders = {
      xe: 'https://api.xe.com/v1/convert_from',
      fixer: 'https://api.fixer.io/latest',
      openexchangerates: 'https://openexchangerates.org/api/latest.json',
      ecb: 'https://api.exchangerate-api.com/v4/latest'
    };
    
    this.baseCurrency = 'USD';
    this.updateInterval = 300000; // 5 minutes
    this.rateCache = new Map();
    
    // Start automatic rate updates
    this.startRateUpdates();
  }

  // =====================================================
  // CURRENCY MANAGEMENT
  // =====================================================

  async getSupportedCurrencies() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          currency_code,
          currency_name,
          currency_symbol,
          decimal_places,
          is_crypto,
          supported_regions,
          display_format,
          rounding_method,
          is_active
        FROM global_currencies 
        WHERE is_active = true
        ORDER BY currency_code
      `);
      
      client.release();
      
      return {
        success: true,
        currencies: result.rows,
        count: result.rows.length
      };

    } catch (error) {
      console.error('❌ Error getting supported currencies:', error);
      return {
        success: false,
        error: error.message,
        currencies: []
      };
    }
  }

  async addCurrency(currencyData) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        INSERT INTO global_currencies (
          currency_code, currency_name, currency_symbol, decimal_places,
          is_crypto, supported_regions, display_format, rounding_method
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `, [
        currencyData.currency_code.toUpperCase(),
        currencyData.currency_name,
        currencyData.currency_symbol,
        currencyData.decimal_places || 2,
        currencyData.is_crypto || false,
        currencyData.supported_regions || [],
        currencyData.display_format || 'symbol_amount',
        currencyData.rounding_method || 'round'
      ]);
      
      client.release();
      
      console.log(`✅ Currency added: ${currencyData.currency_code}`);
      
      return {
        success: true,
        currency: result.rows[0]
      };

    } catch (error) {
      console.error('❌ Error adding currency:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // EXCHANGE RATE MANAGEMENT
  // =====================================================

  async getExchangeRate(fromCurrency, toCurrency, rateType = 'spot') {
    try {
      // Check cache first
      const cacheKey = `${fromCurrency}_${toCurrency}_${rateType}`;
      if (this.rateCache.has(cacheKey)) {
        const cached = this.rateCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 300000) { // 5 minutes
          return {
            success: true,
            rate: cached.rate,
            source: 'cache',
            last_updated: new Date(cached.timestamp)
          };
        }
      }

      // If same currency, return 1
      if (fromCurrency === toCurrency) {
        return {
          success: true,
          rate: 1.0,
          source: 'direct',
          last_updated: new Date()
        };
      }

      const client = await pool.connect();
      
      // Try to get from database first
      const dbResult = await client.query(`
        SELECT exchange_rate, rate_source, last_updated
        FROM global_exchange_rates 
        WHERE base_currency = $1 
          AND target_currency = $2 
          AND rate_type = $3 
          AND is_active = true
          AND last_updated > NOW() - INTERVAL '1 hour'
        ORDER BY last_updated DESC
        LIMIT 1
      `, [fromCurrency, toCurrency, rateType]);
      
      if (dbResult.rows.length > 0) {
        const rate = dbResult.rows[0];
        
        // Cache the result
        this.rateCache.set(cacheKey, {
          rate: parseFloat(rate.exchange_rate),
          timestamp: Date.now()
        });
        
        client.release();
        
        return {
          success: true,
          rate: parseFloat(rate.exchange_rate),
          source: rate.rate_source,
          last_updated: rate.last_updated
        };
      }
      
      client.release();
      
      // If not in database or stale, fetch from external API
      const freshRate = await this.fetchExchangeRateFromAPI(fromCurrency, toCurrency);
      
      if (freshRate.success) {
        // Store in database
        await this.storeExchangeRate(fromCurrency, toCurrency, freshRate.rate, freshRate.source, rateType);
        
        // Cache the result
        this.rateCache.set(cacheKey, {
          rate: freshRate.rate,
          timestamp: Date.now()
        });
        
        return freshRate;
      }
      
      // Fallback to any available rate
      const fallbackResult = await pool.query(`
        SELECT exchange_rate, rate_source, last_updated
        FROM global_exchange_rates 
        WHERE base_currency = $1 
          AND target_currency = $2 
          AND rate_type = $3 
          AND is_active = true
        ORDER BY last_updated DESC
        LIMIT 1
      `, [fromCurrency, toCurrency, rateType]);
      
      if (fallbackResult.rows.length > 0) {
        const rate = fallbackResult.rows[0];
        return {
          success: true,
          rate: parseFloat(rate.exchange_rate),
          source: rate.rate_source + '_stale',
          last_updated: rate.last_updated,
          warning: 'Using stale exchange rate'
        };
      }
      
      return {
        success: false,
        error: 'Exchange rate not available'
      };

    } catch (error) {
      console.error('❌ Error getting exchange rate:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async fetchExchangeRateFromAPI(fromCurrency, toCurrency) {
    try {
      // Try multiple providers for redundancy
      const providers = ['ecb', 'fixer', 'openexchangerates'];
      
      for (const provider of providers) {
        try {
          const rate = await this.fetchFromProvider(provider, fromCurrency, toCurrency);
          if (rate) {
            return {
              success: true,
              rate: rate,
              source: provider,
              last_updated: new Date()
            };
          }
        } catch (providerError) {
          console.warn(`⚠️ Provider ${provider} failed:`, providerError.message);
          continue;
        }
      }
      
      // If all providers fail, use mock rate for demo
      const mockRate = this.generateMockExchangeRate(fromCurrency, toCurrency);
      return {
        success: true,
        rate: mockRate,
        source: 'mock',
        last_updated: new Date(),
        warning: 'Using mock exchange rate for demonstration'
      };

    } catch (error) {
      console.error('❌ Error fetching exchange rate from API:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async fetchFromProvider(provider, fromCurrency, toCurrency) {
    // Mock implementation for demonstration
    // In production, this would make actual API calls to exchange rate providers
    
    const mockRates = {
      'USD_EUR': 0.85,
      'USD_GBP': 0.73,
      'USD_JPY': 110.0,
      'USD_CNY': 6.45,
      'USD_CAD': 1.25,
      'USD_AUD': 1.35,
      'EUR_USD': 1.18,
      'GBP_USD': 1.37,
      'JPY_USD': 0.0091,
      'CNY_USD': 0.155,
      'CAD_USD': 0.80,
      'AUD_USD': 0.74
    };
    
    const key = `${fromCurrency}_${toCurrency}`;
    const reverseKey = `${toCurrency}_${fromCurrency}`;
    
    if (mockRates[key]) {
      return mockRates[key];
    } else if (mockRates[reverseKey]) {
      return 1 / mockRates[reverseKey];
    }
    
    // Generate a reasonable mock rate
    return this.generateMockExchangeRate(fromCurrency, toCurrency);
  }

  generateMockExchangeRate(fromCurrency, toCurrency) {
    // Generate a mock exchange rate based on currency codes
    // This is for demonstration purposes only
    
    const currencyValues = {
      'USD': 1.0,
      'EUR': 1.18,
      'GBP': 1.37,
      'JPY': 0.0091,
      'CNY': 0.155,
      'CAD': 0.80,
      'AUD': 0.74,
      'CHF': 1.09,
      'SEK': 0.12,
      'NOK': 0.11,
      'DKK': 0.16,
      'SGD': 0.74,
      'HKD': 0.13,
      'NZD': 0.70,
      'MXN': 0.05
    };
    
    const fromValue = currencyValues[fromCurrency] || 1.0;
    const toValue = currencyValues[toCurrency] || 1.0;
    
    return parseFloat((toValue / fromValue).toFixed(8));
  }

  async storeExchangeRate(baseCurrency, targetCurrency, rate, source, rateType = 'spot') {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO global_exchange_rates (
          base_currency, target_currency, exchange_rate, rate_source, rate_type
        ) VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (base_currency, target_currency, rate_type, rate_source)
        DO UPDATE SET
          exchange_rate = EXCLUDED.exchange_rate,
          last_updated = CURRENT_TIMESTAMP
      `, [baseCurrency, targetCurrency, rate, source, rateType]);
      
      client.release();

    } catch (error) {
      console.error('❌ Error storing exchange rate:', error);
    }
  }

  // =====================================================
  // CURRENCY CONVERSION
  // =====================================================

  async convertCurrency(amount, fromCurrency, toCurrency, rateType = 'spot') {
    try {
      if (fromCurrency === toCurrency) {
        return {
          success: true,
          original_amount: amount,
          converted_amount: amount,
          exchange_rate: 1.0,
          from_currency: fromCurrency,
          to_currency: toCurrency,
          conversion_fee: 0,
          total_amount: amount
        };
      }

      const rateResult = await this.getExchangeRate(fromCurrency, toCurrency, rateType);
      
      if (!rateResult.success) {
        return {
          success: false,
          error: 'Exchange rate not available'
        };
      }

      const convertedAmount = amount * rateResult.rate;
      const conversionFee = this.calculateConversionFee(amount, fromCurrency, toCurrency);
      const totalAmount = convertedAmount + conversionFee;

      return {
        success: true,
        original_amount: amount,
        converted_amount: convertedAmount,
        exchange_rate: rateResult.rate,
        from_currency: fromCurrency,
        to_currency: toCurrency,
        conversion_fee: conversionFee,
        total_amount: totalAmount,
        rate_source: rateResult.source,
        last_updated: rateResult.last_updated
      };

    } catch (error) {
      console.error('❌ Error converting currency:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  calculateConversionFee(amount, fromCurrency, toCurrency) {
    // Simple fee calculation - in production this would be more sophisticated
    const feePercentage = 0.01; // 1% conversion fee
    const minimumFee = 0.50;
    
    const calculatedFee = amount * feePercentage;
    return Math.max(calculatedFee, minimumFee);
  }

  // =====================================================
  // MULTI-CURRENCY PRICING
  // =====================================================

  async getProductPricing(tenantId, productId, currencyCode) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          p.*,
          c.currency_symbol,
          c.decimal_places,
          c.display_format
        FROM global_product_pricing p
        JOIN global_currencies c ON p.currency_code = c.currency_code
        WHERE p.tenant_id = $1 
          AND p.product_id = $2 
          AND p.currency_code = $3
      `, [tenantId, productId, currencyCode]);
      
      if (result.rows.length > 0) {
        client.release();
        return {
          success: true,
          pricing: result.rows[0]
        };
      }
      
      // If no specific pricing exists, try to convert from base currency
      const basePricing = await client.query(`
        SELECT * FROM global_product_pricing 
        WHERE tenant_id = $1 AND product_id = $2 AND currency_code = 'USD'
        LIMIT 1
      `, [tenantId, productId]);
      
      client.release();
      
      if (basePricing.rows.length > 0) {
        const basePrice = basePricing.rows[0].base_price;
        const conversion = await this.convertCurrency(basePrice, 'USD', currencyCode);
        
        if (conversion.success) {
          return {
            success: true,
            pricing: {
              ...basePricing.rows[0],
              currency_code: currencyCode,
              base_price: conversion.converted_amount,
              auto_converted: true,
              exchange_rate: conversion.exchange_rate
            }
          };
        }
      }
      
      return {
        success: false,
        error: 'Pricing not available for this currency'
      };

    } catch (error) {
      console.error('❌ Error getting product pricing:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async setProductPricing(tenantId, productId, currencyCode, pricingData) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        INSERT INTO global_product_pricing (
          tenant_id, product_id, currency_code, base_price, local_price,
          auto_convert, price_strategy, markup_percentage, price_tier
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (tenant_id, product_id, currency_code)
        DO UPDATE SET
          base_price = EXCLUDED.base_price,
          local_price = EXCLUDED.local_price,
          auto_convert = EXCLUDED.auto_convert,
          price_strategy = EXCLUDED.price_strategy,
          markup_percentage = EXCLUDED.markup_percentage,
          price_tier = EXCLUDED.price_tier,
          last_updated = CURRENT_TIMESTAMP
        RETURNING *
      `, [
        tenantId,
        productId,
        currencyCode,
        pricingData.base_price,
        pricingData.local_price || null,
        pricingData.auto_convert !== false,
        pricingData.price_strategy || 'auto_convert',
        pricingData.markup_percentage || 0,
        pricingData.price_tier || 'standard'
      ]);
      
      client.release();
      
      return {
        success: true,
        pricing: result.rows[0]
      };

    } catch (error) {
      console.error('❌ Error setting product pricing:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // AUTOMATIC RATE UPDATES
  // =====================================================

  startRateUpdates() {
    console.log('🔄 Starting automatic exchange rate updates');
    
    // Update rates immediately
    this.updateAllExchangeRates();
    
    // Set up periodic updates
    this.updateInterval = setInterval(() => {
      this.updateAllExchangeRates();
    }, 300000); // 5 minutes
  }

  stopRateUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      console.log('🛑 Stopped automatic exchange rate updates');
    }
  }

  async updateAllExchangeRates() {
    try {
      console.log('🔄 Updating exchange rates...');
      
      const currencies = await this.getSupportedCurrencies();
      if (!currencies.success) return;
      
      const baseCurrencies = ['USD', 'EUR', 'GBP'];
      const targetCurrencies = currencies.currencies.map(c => c.currency_code);
      
      let updatedCount = 0;
      
      for (const base of baseCurrencies) {
        for (const target of targetCurrencies) {
          if (base !== target) {
            try {
              const rate = await this.fetchExchangeRateFromAPI(base, target);
              if (rate.success) {
                await this.storeExchangeRate(base, target, rate.rate, rate.source);
                updatedCount++;
              }
            } catch (error) {
              console.warn(`⚠️ Failed to update ${base}/${target}:`, error.message);
            }
          }
        }
      }
      
      console.log(`✅ Updated ${updatedCount} exchange rates`);

    } catch (error) {
      console.error('❌ Error updating exchange rates:', error);
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  formatCurrency(amount, currencyCode, locale = 'en-US') {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode
      }).format(amount);
    } catch (error) {
      // Fallback formatting
      return `${currencyCode} ${amount.toFixed(2)}`;
    }
  }

  async getHistoricalRates(baseCurrency, targetCurrency, days = 30) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          DATE(last_updated) as date,
          AVG(exchange_rate) as avg_rate,
          MIN(exchange_rate) as min_rate,
          MAX(exchange_rate) as max_rate,
          COUNT(*) as data_points
        FROM global_exchange_rates 
        WHERE base_currency = $1 
          AND target_currency = $2 
          AND last_updated >= NOW() - INTERVAL '${days} days'
        GROUP BY DATE(last_updated)
        ORDER BY date DESC
      `, [baseCurrency, targetCurrency]);
      
      client.release();
      
      return {
        success: true,
        historical_rates: result.rows,
        period_days: days,
        base_currency: baseCurrency,
        target_currency: targetCurrency
      };

    } catch (error) {
      console.error('❌ Error getting historical rates:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = GlobalCurrencyService;
