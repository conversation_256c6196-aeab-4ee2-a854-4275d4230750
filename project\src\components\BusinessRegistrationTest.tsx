import React, { useState } from 'react';
import { AlertTriangle, CheckCircle, X, Building, Users, Database } from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

const BusinessRegistrationTest: React.FC = () => {
  const { apiCall, state } = useEnhancedAppContext();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [tenants, setTenants] = useState<any[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  // Test business registration
  const testBusinessRegistration = async () => {
    try {
      clearMessages();
      setIsLoading(true);
      
      const testBusiness = {
        business_name: `Test Business ${Date.now()}`,
        business_type: 'restaurant',
        address: '123 Test Street',
        city: 'Test City',
        state: 'ON',
        zip_code: 'K1A 0A6',
        phone: '************',
        email: `test${Date.now()}@example.com`,
        admin_name: 'Test Admin',
        admin_email: `admin${Date.now()}@example.com`,
        admin_phone: '************',
        admin_pin: '123456',
        plan_type: 'starter',
        payment_method: 'credit_card'
      };

      addTestResult(`🚀 Testing business registration: ${testBusiness.business_name}`);
      addTestResult(`📧 Email: ${testBusiness.email}`);
      addTestResult(`👤 Admin: ${testBusiness.admin_name} (PIN: ${testBusiness.admin_pin})`);

      const response = await apiCall('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(testBusiness)
      });

      addTestResult(`📡 Registration response: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const result = await response.json();
        addTestResult(`✅ Business registered successfully!`);
        addTestResult(`🏢 Tenant ID: ${result.tenant.id}`);
        addTestResult(`👤 Admin ID: ${result.admin.id}`);
        addTestResult(`📍 Location ID: ${result.location.id}`);
        addTestResult(`🔑 Token received: ${result.token ? 'Yes' : 'No'}`);
        
        setSuccess(`Business "${testBusiness.business_name}" registered successfully!`);
        
        // Test immediate login with the new PIN
        await testLoginWithNewBusiness(testBusiness.admin_pin, result.tenant.slug);
        
        return result;
      } else {
        const errorText = await response.text();
        addTestResult(`❌ Registration failed: ${errorText}`);
        setError(`Registration failed: ${response.status} ${response.statusText}`);
        return null;
      }
    } catch (error) {
      addTestResult(`💥 Registration error: ${error}`);
      setError(`Registration error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Test login with newly created business
  const testLoginWithNewBusiness = async (pin: string, tenantSlug: string) => {
    try {
      addTestResult(`🔐 Testing login with new business PIN: ${pin}, Tenant: ${tenantSlug}`);
      
      const loginResponse = await apiCall('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          pin: pin,
          tenant_slug: tenantSlug
        })
      });

      addTestResult(`📡 Login response: ${loginResponse.status} ${loginResponse.statusText}`);

      if (loginResponse.ok) {
        const loginResult = await loginResponse.json();
        addTestResult(`✅ Login successful!`);
        addTestResult(`👤 Employee: ${loginResult.employee.name} (${loginResult.employee.role})`);
        addTestResult(`🏢 Tenant: ${loginResult.tenant.name}`);
        addTestResult(`📍 Location: ${loginResult.location.name}`);
        return loginResult;
      } else {
        const errorText = await loginResponse.text();
        addTestResult(`❌ Login failed: ${errorText}`);
        return null;
      }
    } catch (error) {
      addTestResult(`💥 Login error: ${error}`);
      return null;
    }
  };

  // Test fetching tenants list
  const testFetchTenants = async () => {
    try {
      addTestResult(`📋 Testing tenant list fetch...`);
      
      const response = await apiCall('/api/tenants');
      addTestResult(`📡 Tenants response: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const tenantsList = await response.json();
        setTenants(tenantsList);
        addTestResult(`✅ Fetched ${tenantsList.length} tenants from database`);
        
        tenantsList.forEach((tenant: any, index: number) => {
          addTestResult(`  ${index + 1}. ${tenant.business_name || tenant.name} (${tenant.email}) - ${tenant.status}`);
        });
        
        return tenantsList;
      } else {
        const errorText = await response.text();
        addTestResult(`❌ Failed to fetch tenants: ${errorText}`);
        return [];
      }
    } catch (error) {
      addTestResult(`💥 Tenant fetch error: ${error}`);
      return [];
    }
  };

  // Test duplicate registration
  const testDuplicateRegistration = async () => {
    try {
      addTestResult(`🔄 Testing duplicate registration validation...`);
      
      const duplicateBusiness = {
        business_name: 'Demo Restaurant',
        email: '<EMAIL>',
        admin_name: 'Test Admin',
        admin_pin: '123456'
      };

      const response = await apiCall('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(duplicateBusiness)
      });

      addTestResult(`📡 Duplicate registration response: ${response.status} ${response.statusText}`);

      if (response.status === 409) {
        const errorResult = await response.json();
        addTestResult(`✅ Duplicate validation working: ${errorResult.message}`);
        return true;
      } else {
        addTestResult(`❌ Duplicate validation failed - should return 409 Conflict`);
        return false;
      }
    } catch (error) {
      addTestResult(`💥 Duplicate test error: ${error}`);
      return false;
    }
  };

  // Run comprehensive test
  const runComprehensiveTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    clearMessages();
    
    addTestResult('🚀 Starting comprehensive business registration test...');
    
    // Test 1: Fetch existing tenants
    await testFetchTenants();
    
    // Test 2: Register new business
    const registrationResult = await testBusinessRegistration();
    
    // Test 3: Test duplicate registration
    await testDuplicateRegistration();
    
    // Test 4: Fetch tenants again to verify new business appears
    if (registrationResult) {
      addTestResult('🔄 Re-fetching tenants to verify new business appears...');
      await testFetchTenants();
    }
    
    addTestResult('🏁 Comprehensive test completed!');
    setIsLoading(false);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Business Registration System Test</h2>
        <p className="text-gray-600 mb-6">
          This component tests the complete business registration workflow, database integration, and authentication system.
        </p>

        {/* Error and Success Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-800">{success}</p>
              </div>
              <button
                onClick={() => setSuccess(null)}
                className="ml-auto text-green-400 hover:text-green-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}

        {/* Test Controls */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={testBusinessRegistration}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            <Building className="h-4 w-4 mr-2" />
            Test Registration
          </button>
          
          <button
            onClick={testFetchTenants}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            <Database className="h-4 w-4 mr-2" />
            Fetch Tenants
          </button>

          <button
            onClick={testDuplicateRegistration}
            disabled={isLoading}
            className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
          >
            Test Duplicate
          </button>

          <button
            onClick={runComprehensiveTest}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center"
          >
            <Users className="h-4 w-4 mr-2" />
            Full Test
          </button>

          <button
            onClick={() => setTestResults([])}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Testing...</span>
          </div>
        )}
      </div>

      {/* Current Tenants */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Tenants ({tenants.length})</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tenants.map((tenant) => (
            <div key={tenant.id} className="border rounded-lg p-4">
              <h4 className="font-medium text-gray-900">{tenant.business_name || tenant.name}</h4>
              <p className="text-sm text-gray-600 mb-2">{tenant.email}</p>
              <div className="flex justify-between text-xs text-gray-500">
                <span>Status: {tenant.status}</span>
                <span>Plan: {tenant.plan_type}</span>
              </div>
              <div className="text-xs text-gray-400 mt-1">
                Created: {new Date(tenant.created_at).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
        
        {tenants.length === 0 && (
          <p className="text-gray-500 text-center py-4">No tenants found. Try fetching or creating some!</p>
        )}
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
        <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">No test results yet. Run a test to see results here.</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono text-gray-700">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Current User</h4>
            <div className="space-y-1 text-gray-600">
              <div>Name: {state.currentEmployee?.name || 'Not logged in'}</div>
              <div>Role: {state.currentEmployee?.role || 'None'}</div>
              <div>Tenant: {state.currentTenant?.name || 'None'}</div>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Test Endpoints</h4>
            <div className="space-y-1 text-gray-600">
              <div>POST /api/auth/register - Business registration</div>
              <div>POST /api/auth/login - Authentication</div>
              <div>GET /api/tenants - Tenant listing (super admin)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessRegistrationTest;
