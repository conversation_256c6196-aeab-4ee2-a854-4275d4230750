// 🌐 BARPOS Marketing API Routes
// Phase 7C: Marketing Website & Sales Launch
// Lead generation, analytics, and marketing automation endpoints

const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const nodemailer = require('nodemailer');

const pool = new Pool({
    user: process.env.POSTGRES_USER || 'BARPOS',
    host: process.env.POSTGRES_HOST || 'localhost',
    database: process.env.POSTGRES_DB || 'BARPOS',
    password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
    port: process.env.POSTGRES_PORT || 5432,
});

// Email transporter
const emailTransporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false,
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
    }
});

// ================================
// LEAD CAPTURE & MANAGEMENT
// ================================

// Capture marketing leads
router.post('/leads', async (req, res) => {
    try {
        const {
            name,
            email,
            phone,
            company,
            source,
            campaign,
            message,
            interests = [],
            utm_params = {}
        } = req.body;

        if (!name || !email) {
            return res.status(400).json({
                success: false,
                message: 'Name and email are required'
            });
        }

        // Check if lead already exists
        const existingLead = await pool.query(
            'SELECT id FROM marketing_leads WHERE email = $1',
            [email]
        );

        let leadId;

        if (existingLead.rows.length > 0) {
            // Update existing lead
            const result = await pool.query(`
                UPDATE marketing_leads 
                SET 
                    name = $1, phone = $2, company = $3, source = $4, 
                    campaign = $5, message = $6, interests = $7, 
                    utm_params = $8, updated_at = NOW(), last_contact = NOW()
                WHERE email = $9
                RETURNING *
            `, [name, phone, company, source, campaign, message, JSON.stringify(interests), JSON.stringify(utm_params), email]);
            
            leadId = result.rows[0].id;
        } else {
            // Create new lead
            const result = await pool.query(`
                INSERT INTO marketing_leads (
                    name, email, phone, company, source, campaign, 
                    message, interests, utm_params, status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
                RETURNING *
            `, [name, email, phone, company, source, campaign, message, JSON.stringify(interests), JSON.stringify(utm_params), 'new']);
            
            leadId = result.rows[0].id;
        }

        // Send welcome email
        await sendWelcomeEmail(email, name);

        // Notify sales team for high-value leads
        if (source === 'enterprise_inquiry' || (interests && interests.includes('enterprise'))) {
            await notifySalesTeam(leadId, { name, email, company, message });
        }

        res.json({
            success: true,
            message: 'Lead captured successfully',
            leadId: leadId
        });

    } catch (error) {
        console.error('Error capturing lead:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to capture lead',
            error: error.message
        });
    }
});

// Get leads (admin only)
router.get('/leads', async (req, res) => {
    try {
        const { 
            status, 
            source, 
            campaign, 
            limit = 50, 
            offset = 0,
            search 
        } = req.query;

        let query = `
            SELECT ml.*, COUNT(ma.id) as activity_count
            FROM marketing_leads ml
            LEFT JOIN marketing_activities ma ON ml.id = ma.lead_id
            WHERE 1=1
        `;
        
        const params = [];
        
        if (status) {
            query += ` AND ml.status = $${params.length + 1}`;
            params.push(status);
        }
        
        if (source) {
            query += ` AND ml.source = $${params.length + 1}`;
            params.push(source);
        }
        
        if (campaign) {
            query += ` AND ml.campaign = $${params.length + 1}`;
            params.push(campaign);
        }
        
        if (search) {
            query += ` AND (ml.name ILIKE $${params.length + 1} OR ml.email ILIKE $${params.length + 1} OR ml.company ILIKE $${params.length + 1})`;
            params.push(`%${search}%`);
        }
        
        query += ` GROUP BY ml.id ORDER BY ml.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(parseInt(limit), parseInt(offset));

        const result = await pool.query(query, params);

        res.json({
            success: true,
            leads: result.rows
        });

    } catch (error) {
        console.error('Error getting leads:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get leads',
            error: error.message
        });
    }
});

// Update lead status
router.put('/leads/:leadId', async (req, res) => {
    try {
        const { leadId } = req.params;
        const { status, notes, assigned_to } = req.body;

        const result = await pool.query(`
            UPDATE marketing_leads 
            SET status = $1, notes = $2, assigned_to = $3, updated_at = NOW()
            WHERE id = $4
            RETURNING *
        `, [status, notes, assigned_to, leadId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Lead not found'
            });
        }

        // Log activity
        await pool.query(`
            INSERT INTO marketing_activities (
                lead_id, activity_type, description, created_at
            ) VALUES ($1, $2, $3, NOW())
        `, [leadId, 'status_change', `Status changed to ${status}`]);

        res.json({
            success: true,
            message: 'Lead updated successfully',
            lead: result.rows[0]
        });

    } catch (error) {
        console.error('Error updating lead:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update lead',
            error: error.message
        });
    }
});

// ================================
// ANALYTICS & TRACKING
// ================================

// Track marketing events
router.post('/events', async (req, res) => {
    try {
        const {
            event,
            properties = {},
            timestamp,
            url,
            userAgent,
            sessionId
        } = req.body;

        if (!event) {
            return res.status(400).json({
                success: false,
                message: 'Event name is required'
            });
        }

        // Store event
        await pool.query(`
            INSERT INTO marketing_events (
                event_name, properties, url, user_agent, 
                session_id, ip_address, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
            event,
            JSON.stringify(properties),
            url,
            userAgent,
            sessionId,
            req.ip,
            timestamp ? new Date(timestamp) : new Date()
        ]);

        res.json({
            success: true,
            message: 'Event tracked successfully'
        });

    } catch (error) {
        console.error('Error tracking event:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to track event',
            error: error.message
        });
    }
});

// Get marketing analytics
router.get('/analytics', async (req, res) => {
    try {
        const { period = '30d', metric } = req.query;
        
        let dateFilter = '';
        switch (period) {
            case '7d':
                dateFilter = "created_at >= NOW() - INTERVAL '7 days'";
                break;
            case '30d':
                dateFilter = "created_at >= NOW() - INTERVAL '30 days'";
                break;
            case '90d':
                dateFilter = "created_at >= NOW() - INTERVAL '90 days'";
                break;
            default:
                dateFilter = "created_at >= NOW() - INTERVAL '30 days'";
        }

        // Get lead metrics
        const leadMetrics = await pool.query(`
            SELECT 
                COUNT(*) as total_leads,
                COUNT(*) FILTER (WHERE status = 'new') as new_leads,
                COUNT(*) FILTER (WHERE status = 'qualified') as qualified_leads,
                COUNT(*) FILTER (WHERE status = 'converted') as converted_leads,
                COUNT(DISTINCT source) as sources_count
            FROM marketing_leads 
            WHERE ${dateFilter}
        `);

        // Get event metrics
        const eventMetrics = await pool.query(`
            SELECT 
                event_name,
                COUNT(*) as event_count,
                COUNT(DISTINCT session_id) as unique_sessions
            FROM marketing_events 
            WHERE ${dateFilter}
            GROUP BY event_name
            ORDER BY event_count DESC
            LIMIT 10
        `);

        // Get conversion funnel
        const funnelMetrics = await pool.query(`
            SELECT 
                DATE_TRUNC('day', created_at) as date,
                COUNT(*) FILTER (WHERE event_name = 'page_view') as page_views,
                COUNT(*) FILTER (WHERE event_name = 'trial_modal_opened') as trial_opens,
                COUNT(*) FILTER (WHERE event_name = 'trial_signup_success') as signups
            FROM marketing_events 
            WHERE ${dateFilter}
            GROUP BY DATE_TRUNC('day', created_at)
            ORDER BY date DESC
        `);

        // Get top sources
        const sourceMetrics = await pool.query(`
            SELECT 
                source,
                COUNT(*) as lead_count,
                COUNT(*) FILTER (WHERE status = 'converted') as conversions,
                ROUND(
                    COUNT(*) FILTER (WHERE status = 'converted')::DECIMAL / 
                    NULLIF(COUNT(*), 0) * 100, 2
                ) as conversion_rate
            FROM marketing_leads 
            WHERE ${dateFilter} AND source IS NOT NULL
            GROUP BY source
            ORDER BY lead_count DESC
        `);

        res.json({
            success: true,
            analytics: {
                leads: leadMetrics.rows[0],
                events: eventMetrics.rows,
                funnel: funnelMetrics.rows,
                sources: sourceMetrics.rows
            }
        });

    } catch (error) {
        console.error('Error getting analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get analytics',
            error: error.message
        });
    }
});

// ================================
// HIGH-VALUE LEAD NOTIFICATIONS
// ================================

// Handle high-value lead notifications
router.post('/high-value-lead', async (req, res) => {
    try {
        const { score, timestamp, url, actions } = req.body;

        // Log high-value lead activity
        await pool.query(`
            INSERT INTO marketing_activities (
                activity_type, description, metadata, created_at
            ) VALUES ($1, $2, $3, $4)
        `, [
            'high_value_lead',
            `High-value lead detected with score ${score}`,
            JSON.stringify({ score, url, actions }),
            timestamp ? new Date(timestamp) : new Date()
        ]);

        // Send notification to sales team
        await sendSalesNotification({
            type: 'high_value_lead',
            score: score,
            url: url,
            timestamp: timestamp
        });

        res.json({
            success: true,
            message: 'High-value lead notification processed'
        });

    } catch (error) {
        console.error('Error processing high-value lead:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to process high-value lead',
            error: error.message
        });
    }
});

// ================================
// DEMO SCHEDULING
// ================================

// Schedule demo
router.post('/demo', async (req, res) => {
    try {
        const {
            name,
            email,
            phone,
            company,
            preferredTime,
            message,
            utm_params = {}
        } = req.body;

        if (!name || !email) {
            return res.status(400).json({
                success: false,
                message: 'Name and email are required'
            });
        }

        // Create demo request
        const result = await pool.query(`
            INSERT INTO demo_requests (
                name, email, phone, company, preferred_time, 
                message, utm_params, status, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
            RETURNING *
        `, [name, email, phone, company, preferredTime, message, JSON.stringify(utm_params), 'pending']);

        // Send confirmation email
        await sendDemoConfirmationEmail(email, name, result.rows[0].id);

        // Notify sales team
        await notifySalesTeam(null, {
            type: 'demo_request',
            name,
            email,
            company,
            preferredTime,
            message
        });

        res.json({
            success: true,
            message: 'Demo request submitted successfully',
            demoId: result.rows[0].id
        });

    } catch (error) {
        console.error('Error scheduling demo:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to schedule demo',
            error: error.message
        });
    }
});

// ================================
// EMAIL AUTOMATION
// ================================

// Send welcome email
async function sendWelcomeEmail(email, name) {
    try {
        const emailContent = `
            <h2>Welcome to BARPOS! 👋</h2>
            <p>Hi ${name},</p>
            <p>Thank you for your interest in BARPOS! We're excited to help transform your restaurant operations.</p>
            
            <h3>What's Next?</h3>
            <ul>
                <li>🎯 <a href="https://barpos.com/demo">Schedule a personalized demo</a></li>
                <li>🚀 <a href="https://barpos.com/trial">Start your 30-day free trial</a></li>
                <li>📚 <a href="https://barpos.com/resources">Explore our resource library</a></li>
            </ul>
            
            <p>Questions? Reply to this email or call us at (*************.</p>
            
            <p>Best regards,<br>The BARPOS Team</p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: email,
            subject: 'Welcome to BARPOS - Let\'s Transform Your Restaurant!',
            html: emailContent
        });

    } catch (error) {
        console.error('Error sending welcome email:', error);
    }
}

// Send demo confirmation email
async function sendDemoConfirmationEmail(email, name, demoId) {
    try {
        const emailContent = `
            <h2>Demo Request Confirmed! 🎉</h2>
            <p>Hi ${name},</p>
            <p>We've received your demo request and our team will contact you within 24 hours to schedule your personalized BARPOS demonstration.</p>
            
            <p><strong>Demo Request ID:</strong> ${demoId}</p>
            
            <h3>What to Expect:</h3>
            <ul>
                <li>30-minute personalized demonstration</li>
                <li>Q&A session with our product experts</li>
                <li>Custom pricing based on your needs</li>
                <li>Implementation timeline discussion</li>
            </ul>
            
            <p>In the meantime, feel free to explore our <a href="https://barpos.com/resources">resource library</a>.</p>
            
            <p>Best regards,<br>The BARPOS Sales Team</p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: email,
            subject: 'Your BARPOS Demo is Confirmed!',
            html: emailContent
        });

    } catch (error) {
        console.error('Error sending demo confirmation email:', error);
    }
}

// Notify sales team
async function notifySalesTeam(leadId, data) {
    try {
        const salesEmail = process.env.SALES_EMAIL || '<EMAIL>';
        const slackWebhook = process.env.SLACK_WEBHOOK_URL;

        // Email notification
        const emailContent = `
            <h2>New ${data.type || 'Lead'} Alert! 🚨</h2>
            <p><strong>Name:</strong> ${data.name}</p>
            <p><strong>Email:</strong> ${data.email}</p>
            <p><strong>Company:</strong> ${data.company || 'Not provided'}</p>
            ${data.message ? `<p><strong>Message:</strong> ${data.message}</p>` : ''}
            ${data.preferredTime ? `<p><strong>Preferred Time:</strong> ${data.preferredTime}</p>` : ''}
            
            <p><a href="https://admin.barpos.com/leads/${leadId}">View Lead Details</a></p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: salesEmail,
            subject: `New ${data.type || 'Lead'}: ${data.name} from ${data.company || 'Unknown Company'}`,
            html: emailContent
        });

        // Slack notification
        if (slackWebhook) {
            await fetch(slackWebhook, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    text: `🚨 New ${data.type || 'Lead'}: ${data.name} (${data.email}) from ${data.company || 'Unknown Company'}`
                })
            });
        }

    } catch (error) {
        console.error('Error notifying sales team:', error);
    }
}

// Send sales notification
async function sendSalesNotification(data) {
    try {
        const salesEmail = process.env.SALES_EMAIL || '<EMAIL>';

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'RestroFlow <<EMAIL>>',
            to: salesEmail,
            subject: `High-Value Lead Alert - Score: ${data.score}`,
            html: `
                <h2>High-Value Lead Detected! 🎯</h2>
                <p><strong>Lead Score:</strong> ${data.score}</p>
                <p><strong>Page:</strong> ${data.url}</p>
                <p><strong>Time:</strong> ${data.timestamp}</p>
                <p>This visitor is showing high engagement. Consider reaching out proactively!</p>
            `
        });

    } catch (error) {
        console.error('Error sending sales notification:', error);
    }
}

// ================================
// BETA PROGRAM ENDPOINTS
// ================================

// Submit beta program application
router.post('/beta-applications', async (req, res) => {
    try {
        const {
            restaurantName,
            restaurantType,
            locations,
            monthlyRevenue,
            contactName,
            role,
            email,
            phone,
            currentPOS,
            currentCost,
            goals,
            motivation,
            source = 'beta_program',
            campaign = 'beta_launch'
        } = req.body;

        if (!restaurantName || !email || !contactName || !goals || !motivation) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: restaurantName, email, contactName, goals, motivation'
            });
        }

        // Check if application already exists
        const existingApplication = await pool.query(
            'SELECT id FROM beta_applications WHERE email = $1',
            [email]
        );

        if (existingApplication.rows.length > 0) {
            return res.status(409).json({
                success: false,
                message: 'An application with this email already exists'
            });
        }

        // Create beta application
        const result = await pool.query(`
            INSERT INTO beta_applications (
                restaurant_name, restaurant_type, locations, monthly_revenue,
                contact_name, role, email, phone, current_pos, current_cost,
                goals, motivation, source, campaign, status, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW())
            RETURNING *
        `, [
            restaurantName, restaurantType, locations, monthlyRevenue,
            contactName, role, email, phone, currentPOS, currentCost,
            goals, motivation, source, campaign, 'pending'
        ]);

        const applicationId = result.rows[0].id;

        // Also create marketing lead
        await pool.query(`
            INSERT INTO marketing_leads (
                name, email, phone, company, source, campaign,
                message, interests, status, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
            ON CONFLICT (email) DO UPDATE SET
                name = EXCLUDED.name,
                phone = EXCLUDED.phone,
                company = EXCLUDED.company,
                source = EXCLUDED.source,
                campaign = EXCLUDED.campaign,
                message = EXCLUDED.message,
                interests = EXCLUDED.interests,
                updated_at = NOW()
        `, [
            contactName, email, phone, restaurantName, source, campaign,
            `Beta Application: ${goals}`, JSON.stringify(['beta_program']), 'qualified'
        ]);

        // Send confirmation email
        await sendBetaConfirmationEmail(email, contactName, applicationId);

        // Notify sales team immediately
        await notifyBetaApplication({
            applicationId,
            restaurantName,
            contactName,
            email,
            phone,
            restaurantType,
            locations,
            monthlyRevenue,
            goals,
            motivation
        });

        res.status(201).json({
            success: true,
            message: 'Beta application submitted successfully',
            applicationId: applicationId
        });

    } catch (error) {
        console.error('Error submitting beta application:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to submit beta application',
            error: error.message
        });
    }
});

// Get beta applications (admin only)
router.get('/beta-applications', async (req, res) => {
    try {
        const { status, limit = 50, offset = 0 } = req.query;

        let query = `
            SELECT ba.*,
                   CASE
                       WHEN ba.monthly_revenue = '50k-100k' THEN 1
                       WHEN ba.monthly_revenue = '100k-250k' THEN 2
                       WHEN ba.monthly_revenue = '250k-500k' THEN 3
                       WHEN ba.monthly_revenue = '500k+' THEN 4
                       ELSE 0
                   END as revenue_score,
                   CASE
                       WHEN ba.locations::integer >= 3 THEN 3
                       WHEN ba.locations::integer = 2 THEN 2
                       ELSE 1
                   END as size_score
            FROM beta_applications ba
            WHERE 1=1
        `;

        const params = [];

        if (status) {
            query += ` AND ba.status = $${params.length + 1}`;
            params.push(status);
        }

        query += ` ORDER BY ba.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(parseInt(limit), parseInt(offset));

        const result = await pool.query(query, params);

        res.json({
            success: true,
            applications: result.rows
        });

    } catch (error) {
        console.error('Error getting beta applications:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get beta applications',
            error: error.message
        });
    }
});

// Update beta application status
router.put('/beta-applications/:applicationId', async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { status, notes, reviewer } = req.body;

        const result = await pool.query(`
            UPDATE beta_applications
            SET status = $1, notes = $2, reviewer = $3, reviewed_at = NOW(), updated_at = NOW()
            WHERE id = $4
            RETURNING *
        `, [status, notes, reviewer, applicationId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Beta application not found'
            });
        }

        const application = result.rows[0];

        // Send status update email
        if (status === 'accepted') {
            await sendBetaAcceptanceEmail(application.email, application.contact_name, applicationId);
        } else if (status === 'rejected') {
            await sendBetaRejectionEmail(application.email, application.contact_name, notes);
        }

        res.json({
            success: true,
            message: 'Beta application updated successfully',
            application: application
        });

    } catch (error) {
        console.error('Error updating beta application:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update beta application',
            error: error.message
        });
    }
});

// Send beta confirmation email
async function sendBetaConfirmationEmail(email, name, applicationId) {
    try {
        const emailContent = `
            <h2>Beta Application Received! 🎉</h2>
            <p>Hi ${name},</p>
            <p>Thank you for applying to the RestroFlow beta program! We've received your application and our team is reviewing it now.</p>

            <p><strong>Application ID:</strong> BETA-${applicationId}</p>

            <h3>What's Next?</h3>
            <ul>
                <li>📋 Our team will review your application within 24 hours</li>
                <li>📞 If selected, we'll schedule a qualification call</li>
                <li>🚀 Beta program begins immediately after acceptance</li>
                <li>🎯 90-day free trial with dedicated support</li>
            </ul>

            <p>The beta program is limited to 10 restaurants, and we're looking for partners who are committed to providing feedback and helping us build the best restaurant POS system.</p>

            <p>Questions? Reply to this email or call us at (*************.</p>

            <p>Best regards,<br>The RestroFlow Beta Team</p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: email,
            subject: 'BARPOS Beta Application Received - Review in Progress',
            html: emailContent
        });

    } catch (error) {
        console.error('Error sending beta confirmation email:', error);
    }
}

// Send beta acceptance email
async function sendBetaAcceptanceEmail(email, name, applicationId) {
    try {
        const emailContent = `
            <h2>Congratulations! You're Accepted! 🎉</h2>
            <p>Hi ${name},</p>
            <p>Fantastic news! You've been selected for the exclusive BARPOS beta program.</p>

            <p><strong>Application ID:</strong> BETA-${applicationId}</p>

            <h3>Your Beta Benefits:</h3>
            <ul>
                <li>🎁 90-day FREE trial (3x longer than standard)</li>
                <li>💰 50% discount on first year subscription</li>
                <li>🎯 Dedicated success manager</li>
                <li>⭐ Priority 24/7 support</li>
                <li>🚀 Early access to new features</li>
                <li>🏆 Beta customer recognition</li>
            </ul>

            <h3>Next Steps:</h3>
            <ol>
                <li>📅 Schedule your onboarding call: <a href="https://calendly.com/barpos-beta">Book Here</a></li>
                <li>📋 Complete setup questionnaire (sent separately)</li>
                <li>🚀 Begin your 90-day beta experience</li>
            </ol>

            <p>Welcome to the future of restaurant technology! We're excited to partner with you.</p>

            <p>Best regards,<br>The RestroFlow Beta Team</p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: email,
            subject: '🎉 Welcome to BARPOS Beta Program - You\'re In!',
            html: emailContent
        });

    } catch (error) {
        console.error('Error sending beta acceptance email:', error);
    }
}

// Send beta rejection email
async function sendBetaRejectionEmail(email, name, reason) {
    try {
        const emailContent = `
            <h2>Thank You for Your Interest</h2>
            <p>Hi ${name},</p>
            <p>Thank you for applying to the BARPOS beta program. After careful review, we've decided not to move forward with your application at this time.</p>

            ${reason ? `<p><strong>Feedback:</strong> ${reason}</p>` : ''}

            <p>This doesn't mean BARPOS isn't right for your restaurant! We encourage you to:</p>
            <ul>
                <li>🚀 <a href="https://barpos.com/trial">Start a regular 30-day free trial</a></li>
                <li>📅 <a href="https://barpos.com/demo">Schedule a product demo</a></li>
                <li>📧 Join our newsletter for updates and special offers</li>
            </ul>

            <p>We appreciate your interest and hope to work with you in the future.</p>

            <p>Best regards,<br>The BARPOS Team</p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: email,
            subject: 'BARPOS Beta Program Application Update',
            html: emailContent
        });

    } catch (error) {
        console.error('Error sending beta rejection email:', error);
    }
}

// Notify sales team of beta application
async function notifyBetaApplication(data) {
    try {
        const salesEmail = process.env.SALES_EMAIL || '<EMAIL>';
        const slackWebhook = process.env.SLACK_WEBHOOK_URL;

        // Email notification
        const emailContent = `
            <h2>🚨 NEW BETA APPLICATION! 🚨</h2>
            <p><strong>Application ID:</strong> BETA-${data.applicationId}</p>
            <p><strong>Restaurant:</strong> ${data.restaurantName}</p>
            <p><strong>Contact:</strong> ${data.contactName}</p>
            <p><strong>Email:</strong> ${data.email}</p>
            <p><strong>Phone:</strong> ${data.phone}</p>
            <p><strong>Type:</strong> ${data.restaurantType}</p>
            <p><strong>Locations:</strong> ${data.locations}</p>
            <p><strong>Revenue:</strong> ${data.monthlyRevenue}</p>

            <h3>Goals:</h3>
            <p>${data.goals}</p>

            <h3>Motivation:</h3>
            <p>${data.motivation}</p>

            <p><strong>⚡ ACTION REQUIRED:</strong> Review and respond within 24 hours!</p>
            <p><a href="https://admin.barpos.com/beta-applications/${data.applicationId}">Review Application</a></p>
        `;

        await emailTransporter.sendMail({
            from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
            to: salesEmail,
            subject: `🚨 URGENT: Beta Application from ${data.restaurantName}`,
            html: emailContent
        });

        // Slack notification
        if (slackWebhook) {
            await fetch(slackWebhook, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    text: `🚨 NEW BETA APPLICATION: ${data.restaurantName} (${data.contactName}) - ${data.restaurantType}, ${data.locations} locations, ${data.monthlyRevenue} revenue. REVIEW IMMEDIATELY!`
                })
            });
        }

    } catch (error) {
        console.error('Error notifying beta application:', error);
    }
}

module.exports = router;
