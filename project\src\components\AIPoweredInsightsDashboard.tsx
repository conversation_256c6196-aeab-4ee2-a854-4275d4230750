import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Lightbulb,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  ShoppingCart,
  BarChart3,
  PieChart,
  LineChart,
  Zap,
  Star,
  ArrowUp,
  ArrowDown,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Calendar,
  MapPin,
  Cpu,
  Database,
  Activity
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'prediction' | 'recommendation' | 'anomaly' | 'optimization' | 'trend';
  category: 'revenue' | 'operations' | 'customer' | 'inventory' | 'staff' | 'marketing';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  priority: 'urgent' | 'high' | 'medium' | 'low';
  timeframe: string;
  actionable: boolean;
  metrics: {
    current: number;
    predicted: number;
    change: number;
    unit: string;
  };
  recommendations: string[];
  dataPoints: Array<{
    date: string;
    value: number;
    predicted?: boolean;
  }>;
  tags: string[];
  createdAt: string;
  expiresAt?: string;
}

interface MLModel {
  id: string;
  name: string;
  type: 'forecasting' | 'classification' | 'clustering' | 'optimization';
  status: 'training' | 'active' | 'inactive' | 'error';
  accuracy: number;
  lastTrained: string;
  nextTraining: string;
  dataPoints: number;
  version: string;
  performance: {
    precision: number;
    recall: number;
    f1Score: number;
    mse?: number;
  };
}

interface PredictiveMetrics {
  salesForecast: {
    next7Days: number;
    next30Days: number;
    confidence: number;
    trend: 'up' | 'down' | 'stable';
  };
  customerChurn: {
    riskScore: number;
    atRiskCustomers: number;
    preventionOpportunity: number;
  };
  inventoryOptimization: {
    overstockItems: number;
    understockItems: number;
    optimizationSavings: number;
  };
  staffOptimization: {
    optimalStaffing: number;
    currentStaffing: number;
    efficiencyGain: number;
  };
  marketingROI: {
    predictedROI: number;
    bestChannels: string[];
    budgetOptimization: number;
  };
}

const AIPoweredInsightsDashboard: React.FC = () => {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [mlModels, setMLModels] = useState<MLModel[]>([]);
  const [predictiveMetrics, setPredictiveMetrics] = useState<PredictiveMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadAIData();
    
    if (autoRefresh) {
      const interval = setInterval(loadAIData, 30000); // Update every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedTimeframe]);

  const loadAIData = async () => {
    try {
      setIsLoading(true);
      
      // Load AI insights
      const insightsResponse = await fetch(`http://localhost:4000/api/admin/ai/insights?timeframe=${selectedTimeframe}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (insightsResponse.ok) {
        const insightsData = await insightsResponse.json();
        setInsights(insightsData);
      }

      // Load ML models status
      const modelsResponse = await fetch('http://localhost:4000/api/admin/ai/models', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (modelsResponse.ok) {
        const modelsData = await modelsResponse.json();
        setMLModels(modelsData);
      }

      // Load predictive metrics
      const metricsResponse = await fetch('http://localhost:4000/api/admin/ai/predictive-metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setPredictiveMetrics(metricsData);
      }

    } catch (error) {
      console.error('Error loading AI data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const retrainModel = async (modelId: string) => {
    try {
      await fetch(`http://localhost:4000/api/admin/ai/models/${modelId}/retrain`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      setMLModels(models => models.map(model => 
        model.id === modelId ? { ...model, status: 'training' } : model
      ));
    } catch (error) {
      console.error('Error retraining model:', error);
    }
  };

  const implementRecommendation = async (insightId: string, action: string) => {
    try {
      await fetch(`http://localhost:4000/api/admin/ai/insights/${insightId}/implement`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      });

      console.log(`Implemented recommendation: ${action}`);
    } catch (error) {
      console.error('Error implementing recommendation:', error);
    }
  };

  const exportInsights = async (format: 'pdf' | 'csv' | 'json') => {
    try {
      const response = await fetch(`http://localhost:4000/api/admin/ai/insights/export?format=${format}&timeframe=${selectedTimeframe}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-insights-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting insights:', error);
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'prediction':
        return <TrendingUp className="w-5 h-5" />;
      case 'recommendation':
        return <Lightbulb className="w-5 h-5" />;
      case 'anomaly':
        return <AlertCircle className="w-5 h-5" />;
      case 'optimization':
        return <Target className="w-5 h-5" />;
      case 'trend':
        return <BarChart3 className="w-5 h-5" />;
      default:
        return <Brain className="w-5 h-5" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const filteredInsights = insights.filter(insight => {
    if (selectedCategory === 'all') return true;
    return insight.category === selectedCategory;
  });

  const categories = ['all', 'revenue', 'operations', 'customer', 'inventory', 'staff', 'marketing'];

  if (isLoading) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Brain className="w-8 h-8 animate-pulse mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading AI insights...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      
      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                AI-Powered Insights Dashboard
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Machine learning insights and predictive analytics for business optimization
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <select
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="24h">Last 24 hours</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
              
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  autoRefresh
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
              </button>
              
              <button
                onClick={() => exportInsights('pdf')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Predictive Metrics Overview */}
        {predictiveMetrics && (
          <div className="mb-8">
            <h2 className={`text-2xl font-bold mb-6 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              Predictive Analytics Overview
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              
              {/* Sales Forecast */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Sales Forecast
                  </h3>
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Next 7 Days
                    </span>
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {formatCurrency(predictiveMetrics.salesForecast.next7Days)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Next 30 Days
                    </span>
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {formatCurrency(predictiveMetrics.salesForecast.next30Days)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Confidence
                    </span>
                    <span className={`font-medium ${
                      predictiveMetrics.salesForecast.confidence >= 80 ? 'text-green-600' :
                      predictiveMetrics.salesForecast.confidence >= 60 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {formatPercentage(predictiveMetrics.salesForecast.confidence)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {predictiveMetrics.salesForecast.trend === 'up' ? (
                      <ArrowUp className="w-4 h-4 text-green-600" />
                    ) : predictiveMetrics.salesForecast.trend === 'down' ? (
                      <ArrowDown className="w-4 h-4 text-red-600" />
                    ) : (
                      <div className="w-4 h-4 bg-gray-400 rounded-full" />
                    )}
                    <span className={`text-sm ${
                      predictiveMetrics.salesForecast.trend === 'up' ? 'text-green-600' :
                      predictiveMetrics.salesForecast.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {predictiveMetrics.salesForecast.trend === 'up' ? 'Increasing' :
                       predictiveMetrics.salesForecast.trend === 'down' ? 'Decreasing' : 'Stable'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Customer Churn */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Customer Churn Risk
                  </h3>
                  <Users className="w-6 h-6 text-orange-600" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Risk Score
                    </span>
                    <span className={`font-medium ${
                      predictiveMetrics.customerChurn.riskScore >= 70 ? 'text-red-600' :
                      predictiveMetrics.customerChurn.riskScore >= 40 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {formatPercentage(predictiveMetrics.customerChurn.riskScore)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      At-Risk Customers
                    </span>
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {predictiveMetrics.customerChurn.atRiskCustomers}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Prevention Value
                    </span>
                    <span className={`font-medium text-green-600`}>
                      {formatCurrency(predictiveMetrics.customerChurn.preventionOpportunity)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Inventory Optimization */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    Inventory Optimization
                  </h3>
                  <ShoppingCart className="w-6 h-6 text-purple-600" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Overstock Items
                    </span>
                    <span className={`font-medium ${
                      predictiveMetrics.inventoryOptimization.overstockItems > 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {predictiveMetrics.inventoryOptimization.overstockItems}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Understock Items
                    </span>
                    <span className={`font-medium ${
                      predictiveMetrics.inventoryOptimization.understockItems > 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {predictiveMetrics.inventoryOptimization.understockItems}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Potential Savings
                    </span>
                    <span className={`font-medium text-green-600`}>
                      {formatCurrency(predictiveMetrics.inventoryOptimization.optimizationSavings)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Category Filter */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* AI Insights Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredInsights.map((insight) => (
            <div key={insight.id} className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg hover:shadow-xl transition-shadow`}>
              
              {/* Insight Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    insight.type === 'prediction' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' :
                    insight.type === 'recommendation' ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300' :
                    insight.type === 'anomaly' ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300' :
                    insight.type === 'optimization' ? 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300' :
                    'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300'
                  }`}>
                    {getTypeIcon(insight.type)}
                  </div>
                  <div>
                    <h3 className={`text-lg font-semibold ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {insight.title}
                    </h3>
                    <p className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {insight.category} • {insight.timeframe}
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-col space-y-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${getImpactColor(insight.impact)}`}>
                    {insight.impact} impact
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(insight.priority)}`}>
                    {insight.priority}
                  </span>
                </div>
              </div>

              {/* Insight Description */}
              <p className={`text-sm mb-4 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {insight.description}
              </p>

              {/* Metrics */}
              <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Current: {insight.metrics.current} {insight.metrics.unit}
                    </span>
                  </div>
                  <div>
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Predicted: {insight.metrics.predicted} {insight.metrics.unit}
                    </span>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    insight.metrics.change > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {insight.metrics.change > 0 ? (
                      <ArrowUp className="w-4 h-4" />
                    ) : (
                      <ArrowDown className="w-4 h-4" />
                    )}
                    <span className="text-sm font-medium">
                      {Math.abs(insight.metrics.change).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Confidence and Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Confidence: 
                  </span>
                  <span className={`font-medium ${
                    insight.confidence >= 80 ? 'text-green-600' :
                    insight.confidence >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {formatPercentage(insight.confidence)}
                  </span>
                </div>
                
                {insight.actionable && (
                  <button
                    onClick={() => implementRecommendation(insight.id, 'implement')}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    Implement
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredInsights.length === 0 && (
          <div className="text-center py-12">
            <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className={`text-lg font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-900'
            }`}>
              No AI insights available
            </h3>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-500' : 'text-gray-600'
            }`}>
              Try adjusting your filters or check back later for new insights
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIPoweredInsightsDashboard;
