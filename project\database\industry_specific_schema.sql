-- =====================================================
-- INDUSTRY-SPECIFIC POS SYSTEM DATABASE SCHEMA
-- RESTROFLOW - Multi-Industry Restaurant POS System
-- =====================================================

-- Business Types Configuration Table
CREATE TABLE IF NOT EXISTS business_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color_scheme VARCHAR(20),
    default_theme JSONB NOT NULL DEFAULT '{}',
    feature_set JSONB NOT NULL DEFAULT '{}',
    workflow_config JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert default business types
INSERT INTO business_types (name, code, description, icon, color_scheme, default_theme, feature_set, workflow_config) VALUES
('Fine Dining', 'FINE_DINING', 'Upscale restaurants with table service, wine pairings, and multi-course meals', '🍽️', 'burgundy', 
 '{"primaryColor": "#8B0000", "secondaryColor": "#DAA520", "backgroundColor": "#FFF8DC", "fontFamily": "serif"}',
 '{"wineManagement": true, "courseTiming": true, "guestProfiles": true, "sommelierNotes": true, "tableSideService": true}',
 '{"serviceType": "table", "orderFlow": "multi_course", "paymentTiming": "end_of_meal", "reservationRequired": true}'),

('Quick Service', 'QUICK_SERVICE', 'Fast food restaurants with counter service and quick turnover', '⚡', 'orange',
 '{"primaryColor": "#FF4500", "secondaryColor": "#FF6347", "backgroundColor": "#FFFFFF", "fontFamily": "sans-serif"}',
 '{"orderQueue": true, "kitchenDisplay": true, "mobileOrdering": true, "loyaltyProgram": true, "driveThrough": true}',
 '{"serviceType": "counter", "orderFlow": "single_step", "paymentTiming": "immediate", "reservationRequired": false}'),

('Cafe & Coffee Shop', 'CAFE', 'Coffee shops and cafes with beverage focus and light food options', '☕', 'brown',
 '{"primaryColor": "#8B4513", "secondaryColor": "#228B22", "backgroundColor": "#F5F5DC", "fontFamily": "sans-serif"}',
 '{"beverageCustomization": true, "customerNames": true, "preOrdering": true, "subscriptions": true, "seasonalMenus": true}',
 '{"serviceType": "counter", "orderFlow": "beverage_focused", "paymentTiming": "immediate", "reservationRequired": false}'),

('Bar & Pub', 'BAR', 'Bars and pubs with alcohol service and entertainment focus', '🍺', 'blue',
 '{"primaryColor": "#191970", "secondaryColor": "#DAA520", "backgroundColor": "#F0F8FF", "fontFamily": "sans-serif"}',
 '{"alcoholTracking": true, "ageVerification": true, "tabManagement": true, "happyHour": true, "eventManagement": true}',
 '{"serviceType": "bar", "orderFlow": "tab_based", "paymentTiming": "tab_close", "reservationRequired": false}'),

('Food Truck', 'FOOD_TRUCK', 'Mobile food vendors with outdoor operations and limited space', '🚚', 'vibrant',
 '{"primaryColor": "#FF1493", "secondaryColor": "#00CED1", "backgroundColor": "#FFFFFF", "fontFamily": "sans-serif"}',
 '{"offlineCapability": true, "locationServices": true, "weatherIntegration": true, "socialMedia": true, "mobileOptimized": true}',
 '{"serviceType": "mobile", "orderFlow": "simplified", "paymentTiming": "immediate", "reservationRequired": false}'),

('Catering', 'CATERING', 'Event catering services with advance planning and custom menus', '🎉', 'professional',
 '{"primaryColor": "#4169E1", "secondaryColor": "#C0C0C0", "backgroundColor": "#FFFFFF", "fontFamily": "sans-serif"}',
 '{"eventPlanning": true, "customMenus": true, "deliveryManagement": true, "equipmentTracking": true, "clientPortal": true}',
 '{"serviceType": "catering", "orderFlow": "event_based", "paymentTiming": "advance_deposit", "reservationRequired": true}'),

('Hotel Restaurant', 'HOTEL', 'Hotel restaurants with room service and multiple service types', '🏨', 'luxury',
 '{"primaryColor": "#000080", "secondaryColor": "#DAA520", "backgroundColor": "#FFFFFF", "fontFamily": "serif"}',
 '{"pmsIntegration": true, "roomService": true, "multiLanguage": true, "currencySupport": true, "banquetManagement": true}',
 '{"serviceType": "multi", "orderFlow": "hotel_integrated", "paymentTiming": "room_charge", "reservationRequired": true}');

-- Update tenants table to include business type
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS business_type_id INTEGER REFERENCES business_types(id);
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS custom_config JSONB DEFAULT '{}';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS theme_overrides JSONB DEFAULT '{}';
ALTER TABLE tenants ADD COLUMN IF NOT EXISTS industry_settings JSONB DEFAULT '{}';

-- Industry-specific features configuration
CREATE TABLE IF NOT EXISTS industry_features (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    feature_name VARCHAR(100) NOT NULL,
    feature_code VARCHAR(50) NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}',
    dependencies JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(business_type_id, feature_code)
);

-- Menu customization for different industries
CREATE TABLE IF NOT EXISTS industry_menu_templates (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    template_name VARCHAR(100) NOT NULL,
    category_structure JSONB NOT NULL,
    modifier_templates JSONB DEFAULT '{}',
    pricing_rules JSONB DEFAULT '{}',
    display_options JSONB DEFAULT '{}',
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Workflow templates for different industries
CREATE TABLE IF NOT EXISTS industry_workflows (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    workflow_name VARCHAR(100) NOT NULL,
    workflow_type VARCHAR(50) NOT NULL, -- 'order', 'payment', 'service', 'inventory'
    steps JSONB NOT NULL,
    conditions JSONB DEFAULT '{}',
    integrations JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Industry-specific reporting templates
CREATE TABLE IF NOT EXISTS industry_reports (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    report_name VARCHAR(100) NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    metrics JSONB NOT NULL,
    filters JSONB DEFAULT '{}',
    visualization JSONB DEFAULT '{}',
    schedule_options JSONB DEFAULT '{}',
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Integration configurations per industry
CREATE TABLE IF NOT EXISTS industry_integrations (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    integration_name VARCHAR(100) NOT NULL,
    integration_type VARCHAR(50) NOT NULL, -- 'payment', 'delivery', 'reservation', 'inventory'
    provider VARCHAR(100),
    configuration JSONB NOT NULL,
    is_required BOOLEAN DEFAULT false,
    is_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- User role templates per industry
CREATE TABLE IF NOT EXISTS industry_user_roles (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    role_name VARCHAR(100) NOT NULL,
    permissions JSONB NOT NULL,
    access_level INTEGER DEFAULT 1,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Industry-specific inventory categories
CREATE TABLE IF NOT EXISTS industry_inventory_categories (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    category_name VARCHAR(100) NOT NULL,
    category_type VARCHAR(50) NOT NULL, -- 'food', 'beverage', 'supplies', 'equipment'
    tracking_method VARCHAR(50) DEFAULT 'quantity', -- 'quantity', 'weight', 'volume', 'time'
    reorder_rules JSONB DEFAULT '{}',
    cost_tracking JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Performance metrics templates per industry
CREATE TABLE IF NOT EXISTS industry_kpis (
    id SERIAL PRIMARY KEY,
    business_type_id INTEGER REFERENCES business_types(id),
    kpi_name VARCHAR(100) NOT NULL,
    kpi_type VARCHAR(50) NOT NULL, -- 'financial', 'operational', 'customer', 'staff'
    calculation_method JSONB NOT NULL,
    target_values JSONB DEFAULT '{}',
    alert_thresholds JSONB DEFAULT '{}',
    display_format VARCHAR(50) DEFAULT 'number',
    is_critical BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_business_types_code ON business_types(code);
CREATE INDEX IF NOT EXISTS idx_tenants_business_type ON tenants(business_type_id);
CREATE INDEX IF NOT EXISTS idx_industry_features_business_type ON industry_features(business_type_id);
CREATE INDEX IF NOT EXISTS idx_industry_workflows_business_type ON industry_workflows(business_type_id);
CREATE INDEX IF NOT EXISTS idx_industry_reports_business_type ON industry_reports(business_type_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_business_types_updated_at BEFORE UPDATE ON business_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample data for industry features
INSERT INTO industry_features (business_type_id, feature_name, feature_code, description, configuration) VALUES
-- Fine Dining Features
(1, 'Wine Management', 'WINE_MGMT', 'Comprehensive wine inventory and pairing system', '{"cellarTracking": true, "vintageManagement": true, "pairingEngine": true}'),
(1, 'Course Timing', 'COURSE_TIMING', 'Kitchen coordination for multi-course meals', '{"courseSequencing": true, "kitchenNotifications": true, "serviceAlerts": true}'),
(1, 'Guest Profiles', 'GUEST_PROFILES', 'Detailed customer preference tracking', '{"dietaryRestrictions": true, "visitHistory": true, "preferences": true}'),

-- Quick Service Features
(2, 'Order Queue Management', 'ORDER_QUEUE', 'Visual order queue with timing displays', '{"queueDisplay": true, "completionTimers": true, "priorityOrders": true}'),
(2, 'Kitchen Display System', 'KDS', 'Real-time kitchen order management', '{"orderRouting": true, "preparationTimes": true, "statusUpdates": true}'),
(2, 'Mobile Ordering', 'MOBILE_ORDER', 'Mobile app and online ordering integration', '{"appIntegration": true, "pickupNotifications": true, "orderTracking": true}'),

-- Cafe Features
(3, 'Beverage Customization', 'BEVERAGE_CUSTOM', 'Advanced drink modification system', '{"modifierMatrix": true, "customPricing": true, "recipeManagement": true}'),
(3, 'Customer Recognition', 'CUSTOMER_RECOG', 'Name-based ordering and preference memory', '{"nameCollection": true, "preferenceTracking": true, "loyaltyIntegration": true}'),
(3, 'Pre-ordering System', 'PRE_ORDER', 'Scheduled pickup and subscription management', '{"scheduleOrders": true, "subscriptionBilling": true, "pickupAlerts": true}');

-- Add more sample data as needed...

COMMENT ON TABLE business_types IS 'Configuration for different restaurant/hospitality business types';
COMMENT ON TABLE industry_features IS 'Industry-specific features and their configurations';
COMMENT ON TABLE industry_workflows IS 'Customizable workflows for different business types';
COMMENT ON TABLE industry_reports IS 'Industry-specific reporting templates and metrics';
