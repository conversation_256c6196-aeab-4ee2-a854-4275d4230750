import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Zap,
  Bot,
  Clock,
  TrendingUp,
  Users,
  ShoppingCart,
  Bell,
  Mail,
  MessageSquare,
  Calendar,
  BarChart3,
  Settings,
  Play,
  Pause,
  Edit,
  Trash2,
  Plus,
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: string;
    condition: string;
    value?: any;
  };
  actions: Array<{
    type: string;
    target: string;
    parameters: any;
  }>;
  status: 'active' | 'inactive' | 'error';
  category: string;
  lastTriggered?: Date;
  triggerCount: number;
  successRate: number;
}

export function Phase2DAdvancedAutomation() {
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const mockAutomationRules: AutomationRule[] = [
    {
      id: 'auto-1',
      name: 'Low Stock Alert',
      description: 'Automatically notify managers when inventory falls below threshold',
      trigger: {
        type: 'inventory_level',
        condition: 'below_threshold',
        value: 10
      },
      actions: [
        {
          type: 'send_notification',
          target: 'managers',
          parameters: { message: 'Low stock alert for {item_name}', priority: 'high' }
        },
        {
          type: 'create_purchase_order',
          target: 'supplier',
          parameters: { auto_quantity: true, preferred_supplier: true }
        }
      ],
      status: 'active',
      category: 'inventory',
      lastTriggered: new Date(Date.now() - 3600000), // 1 hour ago
      triggerCount: 45,
      successRate: 98.5
    },
    {
      id: 'auto-2',
      name: 'Happy Hour Pricing',
      description: 'Automatically apply discounts during specified hours',
      trigger: {
        type: 'time_based',
        condition: 'schedule',
        value: { start: '15:00', end: '18:00', days: ['monday', 'tuesday', 'wednesday'] }
      },
      actions: [
        {
          type: 'apply_discount',
          target: 'beverages',
          parameters: { discount_percent: 20, categories: ['beer', 'wine', 'cocktails'] }
        },
        {
          type: 'update_menu_display',
          target: 'digital_menu',
          parameters: { highlight_discounts: true }
        }
      ],
      status: 'active',
      category: 'pricing',
      lastTriggered: new Date(Date.now() - 86400000), // 1 day ago
      triggerCount: 156,
      successRate: 100
    },
    {
      id: 'auto-3',
      name: 'Customer Birthday Rewards',
      description: 'Send birthday offers to customers automatically',
      trigger: {
        type: 'customer_birthday',
        condition: 'date_match',
        value: { days_before: 3 }
      },
      actions: [
        {
          type: 'send_email',
          target: 'customer',
          parameters: { template: 'birthday_offer', discount_code: 'BIRTHDAY20' }
        },
        {
          type: 'send_sms',
          target: 'customer',
          parameters: { message: 'Happy Birthday! Enjoy 20% off your next visit.' }
        }
      ],
      status: 'active',
      category: 'marketing',
      lastTriggered: new Date(Date.now() - 7200000), // 2 hours ago
      triggerCount: 23,
      successRate: 95.7
    },
    {
      id: 'auto-4',
      name: 'Peak Hour Staffing',
      description: 'Automatically adjust staffing recommendations based on predicted demand',
      trigger: {
        type: 'demand_forecast',
        condition: 'high_volume_predicted',
        value: { threshold: 80, hours_ahead: 2 }
      },
      actions: [
        {
          type: 'send_notification',
          target: 'managers',
          parameters: { message: 'High volume predicted - consider additional staff' }
        },
        {
          type: 'suggest_staff_schedule',
          target: 'scheduling_system',
          parameters: { additional_staff: 2, roles: ['server', 'kitchen'] }
        }
      ],
      status: 'active',
      category: 'staffing',
      lastTriggered: new Date(Date.now() - 10800000), // 3 hours ago
      triggerCount: 89,
      successRate: 92.1
    },
    {
      id: 'auto-5',
      name: 'Failed Payment Recovery',
      description: 'Automatically retry failed payments and notify customers',
      trigger: {
        type: 'payment_failed',
        condition: 'transaction_declined',
        value: { retry_attempts: 3 }
      },
      actions: [
        {
          type: 'retry_payment',
          target: 'payment_processor',
          parameters: { delay_minutes: 5, max_retries: 3 }
        },
        {
          type: 'send_notification',
          target: 'customer',
          parameters: { message: 'Payment issue - please update payment method' }
        }
      ],
      status: 'error',
      category: 'payments',
      lastTriggered: new Date(Date.now() - 1800000), // 30 minutes ago
      triggerCount: 12,
      successRate: 75.0
    },
    {
      id: 'auto-6',
      name: 'Table Turnover Optimization',
      description: 'Automatically manage table assignments for optimal turnover',
      trigger: {
        type: 'table_status',
        condition: 'dining_complete',
        value: { idle_minutes: 10 }
      },
      actions: [
        {
          type: 'send_notification',
          target: 'servers',
          parameters: { message: 'Table {table_number} ready for cleanup' }
        },
        {
          type: 'update_table_status',
          target: 'floor_management',
          parameters: { status: 'needs_cleaning', priority: 'high' }
        }
      ],
      status: 'active',
      category: 'operations',
      lastTriggered: new Date(Date.now() - 900000), // 15 minutes ago
      triggerCount: 234,
      successRate: 96.8
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setAutomationRules(mockAutomationRules);
      setLoading(false);
    }, 1000);
  }, []);

  const categories = [
    { id: 'all', name: 'All Rules', count: mockAutomationRules.length },
    { id: 'inventory', name: 'Inventory', count: mockAutomationRules.filter(r => r.category === 'inventory').length },
    { id: 'pricing', name: 'Pricing', count: mockAutomationRules.filter(r => r.category === 'pricing').length },
    { id: 'marketing', name: 'Marketing', count: mockAutomationRules.filter(r => r.category === 'marketing').length },
    { id: 'staffing', name: 'Staffing', count: mockAutomationRules.filter(r => r.category === 'staffing').length },
    { id: 'payments', name: 'Payments', count: mockAutomationRules.filter(r => r.category === 'payments').length },
    { id: 'operations', name: 'Operations', count: mockAutomationRules.filter(r => r.category === 'operations').length },
  ];

  const filteredRules = selectedCategory === 'all' 
    ? automationRules 
    : automationRules.filter(rule => rule.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inactive': return <Pause className="h-4 w-4 text-gray-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'inventory': return <ShoppingCart className="h-4 w-4" />;
      case 'pricing': return <TrendingUp className="h-4 w-4" />;
      case 'marketing': return <Mail className="h-4 w-4" />;
      case 'staffing': return <Users className="h-4 w-4" />;
      case 'payments': return <BarChart3 className="h-4 w-4" />;
      case 'operations': return <Settings className="h-4 w-4" />;
      default: return <Bot className="h-4 w-4" />;
    }
  };

  const toggleRuleStatus = (ruleId: string) => {
    setAutomationRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, status: rule.status === 'active' ? 'inactive' : 'active' }
        : rule
    ));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Advanced Automation</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2D Advanced Automation</h2>
          <p className="text-gray-600">Intelligent automation rules for streamlined operations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Rule
          </Button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center space-x-2"
          >
            <span>{category.name}</span>
            <Badge variant="secondary" className="ml-2">
              {category.count}
            </Badge>
          </Button>
        ))}
      </div>

      {/* Automation Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Rules</p>
                <p className="text-2xl font-bold text-green-600">
                  {automationRules.filter(r => r.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Triggers</p>
                <p className="text-2xl font-bold text-blue-600">
                  {automationRules.reduce((sum, rule) => sum + rule.triggerCount, 0)}
                </p>
              </div>
              <Zap className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(automationRules.reduce((sum, rule) => sum + rule.successRate, 0) / automationRules.length).toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Errors</p>
                <p className="text-2xl font-bold text-red-600">
                  {automationRules.filter(r => r.status === 'error').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Automation Rules */}
      <div className="space-y-4">
        {filteredRules.map((rule) => (
          <Card key={rule.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getCategoryIcon(rule.category)}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{rule.name}</CardTitle>
                    <p className="text-sm text-gray-600">{rule.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(rule.status)}
                  <Badge className={getStatusColor(rule.status)}>
                    {rule.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Trigger</h4>
                  <p className="text-sm text-gray-600">
                    {rule.trigger.type.replace('_', ' ')} - {rule.trigger.condition.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Actions</h4>
                  <p className="text-sm text-gray-600">
                    {rule.actions.length} action{rule.actions.length !== 1 ? 's' : ''} configured
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Performance</h4>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-600">
                      {rule.triggerCount} triggers
                    </span>
                    <span className="text-green-600">
                      {rule.successRate}% success
                    </span>
                  </div>
                </div>
              </div>

              {rule.lastTriggered && (
                <div className="mt-4 text-xs text-gray-500">
                  Last triggered: {rule.lastTriggered.toLocaleString()}
                </div>
              )}

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="capitalize">
                    {rule.category}
                  </Badge>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleRuleStatus(rule.id)}
                  >
                    {rule.status === 'active' ? (
                      <>
                        <Pause className="h-3 w-3 mr-1" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-3 w-3 mr-1" />
                        Activate
                      </>
                    )}
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button size="sm" variant="outline">
                    <BarChart3 className="h-3 w-3 mr-1" />
                    Stats
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
