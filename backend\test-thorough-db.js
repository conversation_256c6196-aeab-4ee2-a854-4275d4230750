const queries = require('./db/queries');
const db = require('./db/pool');

// Test UUID for a known employee
const TEST_EMPLOYEE_ID = '550e8400-e29b-41d4-a716-************'; // Example UUID
const TEST_PIN = '123456';
const TEST_TENANT = 'demo-restaurant';

async function testConnectionPooling() {
  console.log('Testing connection pooling with concurrent queries...');
  const promises = [];
  for (let i = 0; i < 20; i++) {
    promises.push(queries.getEmployeeByPin(TEST_PIN, TEST_TENANT));
  }
  const results = await Promise.all(promises);
  console.log('Concurrent queries completed:', results.filter(r => r).length);
}

async function testCacheInvalidation() {
  console.log('Testing cache invalidation and TTL...');
  const employee = await queries.getEmployeeByPin(TEST_PIN, TEST_TENANT);
  console.log('Initial query:', employee);

  console.log('Waiting for cache to expire (65 seconds)...');
  await new Promise(resolve => setTimeout(resolve, 65000));

  const employeeAfterTTL = await queries.getEmployeeByPin(TEST_PIN, TEST_TENANT);
  console.log('Query after cache TTL:', employeeAfterTTL);
}

async function testErrorHandling() {
  console.log('Testing error handling with invalid query...');
  try {
    await db.query('SELECT * FROM non_existing_table');
  } catch (error) {
    console.log('Successfully caught error for invalid table');
  }

  try {
    await queries.getEmployeeById('invalid-uuid');
    console.log('Should not reach here');
  } catch (error) {
    console.log('Successfully caught error for invalid UUID');
  }
}

async function testPerformanceComparison() {
  console.log('Testing performance comparison with old implementation...');
  console.time('New implementation (with caching)');
  await queries.getEmployeeByPin(TEST_PIN, TEST_TENANT);
  console.timeEnd('New implementation (with caching)');

  // Clear cache to test non-cached performance
  queries.clearCache();

  console.time('New implementation (without cache)');
  await queries.getEmployeeByPin(TEST_PIN, TEST_TENANT);
  console.timeEnd('New implementation (without cache)');

  // Simulate old implementation query
  const { Pool } = require('pg');
  const oldPool = new Pool({
    user: 'BARPOS',
    host: 'localhost',
    database: 'BARPOS',
    password: 'Chaand@0319',
    port: 5432,
  });

  console.time('Old implementation');
  const res = await oldPool.query(`
    SELECT 
      e.id,
      e.name,
      e.role,
      t.id as tenant_id,
      t.name as tenant_name,
      t.slug as tenant_slug
    FROM employees e
    JOIN tenants t ON e.tenant_id = t.id
    WHERE e.pin = $1 AND t.slug = $2
  `, [TEST_PIN, TEST_TENANT]);
  console.timeEnd('Old implementation');

  await oldPool.end();
}

async function testEdgeCases() {
  console.log('Testing edge cases with various query parameters...');
  
  // Test with null values
  const nullResult = await queries.getEmployeeByPin(null, null);
  console.log('Query with null parameters:', nullResult);

  // Test with empty string
  const emptyResult = await queries.getEmployeeByPin('', '');
  console.log('Query with empty strings:', emptyResult);

  // Test with non-existent pin
  const nonExistentResult = await queries.getEmployeeByPin('999999', TEST_TENANT);
  console.log('Query with non-existent pin:', nonExistentResult);
}

async function runAllTests() {
  try {
    console.log('Starting thorough database tests...\n');

    await testConnectionPooling();
    console.log('\n' + '='.repeat(50) + '\n');

    await testErrorHandling();
    console.log('\n' + '='.repeat(50) + '\n');

    await testPerformanceComparison();
    console.log('\n' + '='.repeat(50) + '\n');

    await testEdgeCases();
    console.log('\n' + '='.repeat(50) + '\n');

    // Run cache invalidation test last since it has a long timeout
    await testCacheInvalidation();
    
    console.log('\nAll thorough database tests completed successfully.');
  } catch (error) {
    console.error('Error during tests:', error);
  }
}

// Run the tests
runAllTests().catch(console.error);
