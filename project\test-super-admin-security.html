<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Security Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔒 Super Admin Security Test Suite</h1>
    
    <div class="test-section">
        <h2>1. Backend API Connectivity Test</h2>
        <button onclick="testBackendHealth()">Test Backend Health</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Authentication Test</h2>
        <button onclick="testSuperAdminLogin()">Test Super Admin Login (888888)</button>
        <button onclick="testInvalidLogin()">Test Invalid Login</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Security Features Test</h2>
        <button onclick="testSecurityStatus()">Test Security Status API</button>
        <button onclick="testTokenVerification()">Test Token Verification</button>
        <div id="security-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Access Control Test</h2>
        <button onclick="testHostValidation()">Test Host Validation</button>
        <button onclick="testAccessLogging()">Test Access Logging</button>
        <div id="access-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Test Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="test-logs" class="log-output"></div>
    </div>

    <script>
        let testLogs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLogs.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        function updateLogDisplay() {
            document.getElementById('test-logs').innerHTML = testLogs.join('\n');
        }

        function clearLogs() {
            testLogs = [];
            updateLogDisplay();
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        async function testBackendHealth() {
            log('Testing backend health...');
            try {
                const response = await fetch('/api/admin/health');
                if (response.ok) {
                    const data = await response.json();
                    log('Backend health check successful', 'success');
                    showResult('backend-result', '✅ Backend is healthy and responding', 'success');
                } else {
                    log(`Backend health check failed: ${response.status}`, 'error');
                    showResult('backend-result', `❌ Backend health check failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`Backend connection error: ${error.message}`, 'error');
                showResult('backend-result', `❌ Backend connection error: ${error.message}`, 'error');
            }
        }

        async function testSuperAdminLogin() {
            log('Testing Super Admin login with PIN 888888...');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pin: '888888'
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.employee && data.employee.role === 'super_admin') {
                        log('Super Admin login successful', 'success');
                        showResult('auth-result', '✅ Super Admin authentication successful', 'success');
                        
                        // Store token for further tests
                        localStorage.setItem('testAuthToken', data.token);
                        localStorage.setItem('testEmployee', JSON.stringify(data.employee));
                    } else {
                        log(`Login successful but role is: ${data.employee?.role}`, 'warning');
                        showResult('auth-result', `⚠️ Login successful but role is: ${data.employee?.role}`, 'warning');
                    }
                } else {
                    log(`Login failed: ${response.status}`, 'error');
                    showResult('auth-result', `❌ Login failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`Login error: ${error.message}`, 'error');
                showResult('auth-result', `❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testInvalidLogin() {
            log('Testing invalid login...');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pin: '000000'
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`Invalid login unexpectedly succeeded: ${JSON.stringify(data)}`, 'warning');
                    showResult('auth-result', '⚠️ Invalid login unexpectedly succeeded', 'warning');
                } else {
                    log('Invalid login correctly rejected', 'success');
                    showResult('auth-result', '✅ Invalid login correctly rejected', 'success');
                }
            } catch (error) {
                log(`Invalid login test error: ${error.message}`, 'error');
                showResult('auth-result', `❌ Invalid login test error: ${error.message}`, 'error');
            }
        }

        async function testSecurityStatus() {
            log('Testing security status API...');
            const token = localStorage.getItem('testAuthToken');
            
            if (!token) {
                log('No auth token available for security test', 'warning');
                showResult('security-result', '⚠️ No auth token available. Run login test first.', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/admin/security/status', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('Security status API successful', 'success');
                    showResult('security-result', `✅ Security status: ${JSON.stringify(data)}`, 'success');
                } else {
                    log(`Security status API failed: ${response.status}`, 'error');
                    showResult('security-result', `❌ Security status API failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`Security status error: ${error.message}`, 'error');
                showResult('security-result', `❌ Security status error: ${error.message}`, 'error');
            }
        }

        async function testTokenVerification() {
            log('Testing token verification...');
            const token = localStorage.getItem('testAuthToken');
            
            if (!token) {
                log('No auth token available for verification test', 'warning');
                showResult('security-result', '⚠️ No auth token available. Run login test first.', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('Token verification successful', 'success');
                    showResult('security-result', '✅ Token verification successful', 'success');
                } else {
                    log(`Token verification failed: ${response.status}`, 'error');
                    showResult('security-result', `❌ Token verification failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`Token verification error: ${error.message}`, 'error');
                showResult('security-result', `❌ Token verification error: ${error.message}`, 'error');
            }
        }

        function testHostValidation() {
            log('Testing host validation...');
            const currentHost = window.location.host;
            const allowedHosts = ['localhost:5173', 'localhost:5174', '127.0.0.1:5173', '127.0.0.1:5174'];
            
            if (allowedHosts.includes(currentHost)) {
                log(`Host validation passed: ${currentHost}`, 'success');
                showResult('access-result', `✅ Host validation passed: ${currentHost}`, 'success');
            } else {
                log(`Host validation failed: ${currentHost}`, 'error');
                showResult('access-result', `❌ Host validation failed: ${currentHost}`, 'error');
            }
        }

        function testAccessLogging() {
            log('Testing access logging...');
            try {
                // Simulate access logging
                const accessLog = {
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    ip: 'client-side',
                    referrer: document.referrer || 'direct'
                };
                
                const attempts = JSON.parse(localStorage.getItem('adminAccessLog') || '[]');
                attempts.push(accessLog);
                localStorage.setItem('adminAccessLog', JSON.stringify(attempts));
                
                log('Access logging successful', 'success');
                showResult('access-result', `✅ Access logging working. Total logs: ${attempts.length}`, 'success');
            } catch (error) {
                log(`Access logging error: ${error.message}`, 'error');
                showResult('access-result', `❌ Access logging error: ${error.message}`, 'error');
            }
        }

        // Initialize
        log('Security test suite initialized');
    </script>
</body>
</html>
