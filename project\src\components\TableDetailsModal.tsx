import React, { useState } from 'react';
import { 
  X, 
  Users, 
  Clock, 
  DollarSign, 
  UserCheck, 
  Phone, 
  AlertTriangle,
  CheckCircle,
  Coffee,
  Utensils,
  CreditCard,
  Timer,
  Edit,
  Save,
  Plus,
  Minus
} from 'lucide-react';
import { Table } from '../types';

interface TableDetailsModalProps {
  table: Table;
  isOpen: boolean;
  onClose: () => void;
  onUpdateTable: (tableId: string, updates: Partial<Table>) => void;
  onStartOrder: (tableId: string) => void;
}

const TableDetailsModal: React.FC<TableDetailsModalProps> = ({
  table,
  isOpen,
  onClose,
  onUpdateTable,
  onStartOrder
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    guestCount: table.guestCount || 0,
    specialRequests: table.specialRequests?.join(', ') || '',
    allergies: table.allergies?.join(', ') || '',
    serverAssigned: table.serverAssigned || '',
    status: table.status,
    substatus: table.substatus || ''
  });

  if (!isOpen) return null;

  const getSeatedDuration = () => {
    if (!table.seatedTime) return null;
    const now = new Date();
    const seated = new Date(table.seatedTime);
    const diffMinutes = Math.floor((now.getTime() - seated.getTime()) / (1000 * 60));
    return diffMinutes;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-500';
      case 'occupied': return 'text-red-500';
      case 'reserved': return 'text-yellow-500';
      case 'needs-cleaning': return 'text-orange-500';
      case 'out-of-order': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const handleSave = () => {
    const updates: Partial<Table> = {
      guestCount: editData.guestCount,
      specialRequests: editData.specialRequests ? editData.specialRequests.split(',').map(s => s.trim()) : [],
      allergies: editData.allergies ? editData.allergies.split(',').map(s => s.trim()) : [],
      serverAssigned: editData.serverAssigned,
      status: editData.status as Table['status'],
      substatus: editData.substatus as Table['substatus'] || undefined
    };

    onUpdateTable(table.id, updates);
    setIsEditing(false);
  };

  const handleStatusChange = (newStatus: Table['status'], newSubstatus?: Table['substatus']) => {
    onUpdateTable(table.id, { status: newStatus, substatus: newSubstatus });
  };

  const seatedDuration = getSeatedDuration();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">{table.number}</span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">Table {table.number}</h2>
              <p className={`text-sm capitalize ${getStatusColor(table.status)}`}>
                {table.status} {table.substatus && `(${table.substatus})`}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="h-5 w-5 text-blue-400" />
                <span className="text-white font-medium">Capacity</span>
              </div>
              <p className="text-gray-300">{table.seats} seats</p>
              <p className="text-sm text-gray-400">{table.tableType} table</p>
            </div>

            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <UserCheck className="h-5 w-5 text-green-400" />
                <span className="text-white font-medium">Server</span>
              </div>
              <p className="text-gray-300">{table.serverName || 'Not assigned'}</p>
              {table.section && (
                <p className="text-sm text-gray-400">Section: {table.section}</p>
              )}
            </div>

            {table.status === 'occupied' && (
              <>
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-5 w-5 text-yellow-400" />
                    <span className="text-white font-medium">Seated Time</span>
                  </div>
                  <p className="text-gray-300">
                    {seatedDuration ? `${seatedDuration} minutes ago` : 'Just seated'}
                  </p>
                  {table.estimatedTurnTime && (
                    <p className="text-sm text-gray-400">
                      Est. turn: {new Date(table.estimatedTurnTime).toLocaleTimeString()}
                    </p>
                  )}
                </div>

                {table.orderTotal && table.orderTotal > 0 && (
                  <div className="bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <DollarSign className="h-5 w-5 text-green-400" />
                      <span className="text-white font-medium">Current Order</span>
                    </div>
                    <p className="text-gray-300">${table.orderTotal.toFixed(2)}</p>
                    <p className="text-sm text-gray-400">{table.orderItems} items</p>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Guest Information */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-white font-medium">Guest Information</h3>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                <Edit className="h-4 w-4" />
              </button>
            </div>

            {isEditing ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">
                    Guest Count
                  </label>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setEditData(prev => ({ ...prev, guestCount: Math.max(0, prev.guestCount - 1) }))}
                      className="bg-gray-600 text-white p-2 rounded-md hover:bg-gray-500"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                    <span className="text-white font-medium px-4">{editData.guestCount}</span>
                    <button
                      onClick={() => setEditData(prev => ({ ...prev, guestCount: Math.min(table.seats, prev.guestCount + 1) }))}
                      className="bg-gray-600 text-white p-2 rounded-md hover:bg-gray-500"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">
                    Special Requests
                  </label>
                  <input
                    type="text"
                    value={editData.specialRequests}
                    onChange={(e) => setEditData(prev => ({ ...prev, specialRequests: e.target.value }))}
                    className="w-full bg-gray-600 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Birthday, window seat, etc."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-2">
                    Allergies
                  </label>
                  <input
                    type="text"
                    value={editData.allergies}
                    onChange={(e) => setEditData(prev => ({ ...prev, allergies: e.target.value }))}
                    className="w-full bg-gray-600 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nuts, dairy, gluten, etc."
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={handleSave}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
                  >
                    <Save className="h-4 w-4" />
                    <span>Save</span>
                  </button>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div>
                  <span className="text-gray-400">Guests: </span>
                  <span className="text-white">{table.guestCount || 0}/{table.seats}</span>
                </div>
                {table.specialRequests && table.specialRequests.length > 0 && (
                  <div>
                    <span className="text-gray-400">Special Requests: </span>
                    <span className="text-white">{table.specialRequests.join(', ')}</span>
                  </div>
                )}
                {table.allergies && table.allergies.length > 0 && (
                  <div>
                    <span className="text-gray-400">Allergies: </span>
                    <span className="text-red-400">{table.allergies.join(', ')}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-white font-medium mb-4">Quick Actions</h3>
            
            {table.status === 'available' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <button
                  onClick={() => {
                    handleStatusChange('occupied', 'ordering');
                    onStartOrder(table.id);
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
                >
                  <Coffee className="h-4 w-4" />
                  <span>Seat Guests & Start Order</span>
                </button>
                <button
                  onClick={() => handleStatusChange('reserved')}
                  className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 flex items-center space-x-2"
                >
                  <Clock className="h-4 w-4" />
                  <span>Mark as Reserved</span>
                </button>
              </div>
            )}

            {table.status === 'occupied' && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button
                  onClick={() => handleStatusChange('occupied', 'ordering')}
                  className="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-1 text-sm"
                >
                  <Coffee className="h-4 w-4" />
                  <span>Ordering</span>
                </button>
                <button
                  onClick={() => handleStatusChange('occupied', 'eating')}
                  className="bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 flex items-center space-x-1 text-sm"
                >
                  <Utensils className="h-4 w-4" />
                  <span>Eating</span>
                </button>
                <button
                  onClick={() => handleStatusChange('occupied', 'waiting-for-check')}
                  className="bg-purple-600 text-white px-3 py-2 rounded-md hover:bg-purple-700 flex items-center space-x-1 text-sm"
                >
                  <Timer className="h-4 w-4" />
                  <span>Check</span>
                </button>
                <button
                  onClick={() => handleStatusChange('occupied', 'paying')}
                  className="bg-indigo-600 text-white px-3 py-2 rounded-md hover:bg-indigo-700 flex items-center space-x-1 text-sm"
                >
                  <CreditCard className="h-4 w-4" />
                  <span>Paying</span>
                </button>
              </div>
            )}

            {(table.status === 'occupied' || table.status === 'reserved') && (
              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                <button
                  onClick={() => handleStatusChange('needs-cleaning')}
                  className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 flex items-center space-x-2"
                >
                  <AlertTriangle className="h-4 w-4" />
                  <span>Clear Table</span>
                </button>
                <button
                  onClick={() => handleStatusChange('available')}
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Mark Available</span>
                </button>
              </div>
            )}

            {table.status === 'needs-cleaning' && (
              <button
                onClick={() => handleStatusChange('available')}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
              >
                <CheckCircle className="h-4 w-4" />
                <span>Cleaning Complete</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableDetailsModal;
