import React from 'react';
import UnifiedPOS from './UnifiedPOS';
import TenantLandingPage from './TenantLandingPage';
import { AppProvider } from './context/AppContext';

// This component renders the Unified POS interface for all POS users.
// Tenant users should be directed to TenantLandingPage separately (not here).

const CombinedApp: React.FC = () => {
  // For now, render only the UnifiedPOS wrapped with AppProvider.
  // Tenant users should have a separate entry point or routing.

  return (
    <AppProvider>
      <UnifiedPOS />
    </AppProvider>
  );
};

export default CombinedApp;
