import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  TrendingUp,
  Target,
  Zap,
  BarChart3,
  PieChart,
  LineChart,
  Activity,
  Users,
  DollarSign,
  ShoppingCart,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Lightbulb,
  Cpu,
  Database,
  Eye
} from 'lucide-react';

interface AIInsight {
  id: string;
  type: 'prediction' | 'recommendation' | 'alert' | 'optimization';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  category: string;
  data: any;
  timestamp: Date;
  status: 'new' | 'reviewed' | 'implemented';
}

interface MLModel {
  id: string;
  name: string;
  type: string;
  accuracy: number;
  lastTrained: Date;
  status: 'active' | 'training' | 'inactive';
  predictions: number;
  performance: number;
}

export function Phase3AAIAnalyticsDashboard() {
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [mlModels, setMlModels] = useState<MLModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedView, setSelectedView] = useState<'insights' | 'models' | 'predictions'>('insights');

  const mockAIInsights: AIInsight[] = [
    {
      id: 'insight-1',
      type: 'prediction',
      title: 'Peak Hour Revenue Forecast',
      description: 'AI predicts 23% increase in revenue during 6-8 PM today based on weather and historical patterns',
      confidence: 94.5,
      impact: 'high',
      category: 'revenue',
      data: { predictedIncrease: 23, timeWindow: '6-8 PM', factors: ['weather', 'historical', 'events'] },
      timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      status: 'new'
    },
    {
      id: 'insight-2',
      type: 'recommendation',
      title: 'Inventory Optimization',
      description: 'Recommend increasing beer inventory by 15% for weekend based on consumption patterns',
      confidence: 87.2,
      impact: 'medium',
      category: 'inventory',
      data: { item: 'beer', recommendedIncrease: 15, reason: 'weekend_pattern' },
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      status: 'reviewed'
    },
    {
      id: 'insight-3',
      type: 'alert',
      title: 'Customer Churn Risk',
      description: 'AI detected 12 high-value customers at risk of churning based on visit frequency decline',
      confidence: 91.8,
      impact: 'high',
      category: 'customer',
      data: { customersAtRisk: 12, avgValue: 156.50, riskFactors: ['frequency_decline', 'satisfaction_drop'] },
      timestamp: new Date(Date.now() - 7200000), // 2 hours ago
      status: 'new'
    },
    {
      id: 'insight-4',
      type: 'optimization',
      title: 'Staff Schedule Optimization',
      description: 'AI suggests reducing staff by 1 server during 2-4 PM slot to optimize labor costs',
      confidence: 83.6,
      impact: 'medium',
      category: 'staffing',
      data: { timeSlot: '2-4 PM', staffReduction: 1, role: 'server', savings: 45.00 },
      timestamp: new Date(Date.now() - 10800000), // 3 hours ago
      status: 'implemented'
    },
    {
      id: 'insight-5',
      type: 'prediction',
      title: 'Menu Item Performance',
      description: 'AI predicts "Truffle Pasta" will be top seller this week with 34% increase in orders',
      confidence: 89.3,
      impact: 'medium',
      category: 'menu',
      data: { item: 'Truffle Pasta', predictedIncrease: 34, factors: ['seasonal', 'trending'] },
      timestamp: new Date(Date.now() - 14400000), // 4 hours ago
      status: 'reviewed'
    }
  ];

  const mockMLModels: MLModel[] = [
    {
      id: 'model-1',
      name: 'Revenue Forecasting',
      type: 'Time Series Prediction',
      accuracy: 94.5,
      lastTrained: new Date(Date.now() - 86400000), // 1 day ago
      status: 'active',
      predictions: 1247,
      performance: 96.2
    },
    {
      id: 'model-2',
      name: 'Customer Behavior Analysis',
      type: 'Classification',
      accuracy: 87.8,
      lastTrained: new Date(Date.now() - 172800000), // 2 days ago
      status: 'active',
      predictions: 892,
      performance: 89.1
    },
    {
      id: 'model-3',
      name: 'Inventory Optimization',
      type: 'Regression',
      accuracy: 91.2,
      lastTrained: new Date(Date.now() - 259200000), // 3 days ago
      status: 'training',
      predictions: 634,
      performance: 92.7
    },
    {
      id: 'model-4',
      name: 'Demand Prediction',
      type: 'Neural Network',
      accuracy: 88.9,
      lastTrained: new Date(Date.now() - 345600000), // 4 days ago
      status: 'active',
      predictions: 1156,
      performance: 90.4
    },
    {
      id: 'model-5',
      name: 'Price Optimization',
      type: 'Reinforcement Learning',
      accuracy: 85.3,
      lastTrained: new Date(Date.now() - 432000000), // 5 days ago
      status: 'inactive',
      predictions: 423,
      performance: 87.6
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setAiInsights(mockAIInsights);
      setMlModels(mockMLModels);
      setLoading(false);
    }, 1000);
  }, []);

  const getInsightTypeColor = (type: string) => {
    switch (type) {
      case 'prediction': return 'bg-blue-100 text-blue-800';
      case 'recommendation': return 'bg-green-100 text-green-800';
      case 'alert': return 'bg-red-100 text-red-800';
      case 'optimization': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInsightTypeIcon = (type: string) => {
    switch (type) {
      case 'prediction': return <TrendingUp className="h-4 w-4" />;
      case 'recommendation': return <Lightbulb className="h-4 w-4" />;
      case 'alert': return <AlertTriangle className="h-4 w-4" />;
      case 'optimization': return <Target className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 font-semibold';
      case 'medium': return 'text-yellow-600 font-medium';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'training': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'reviewed': return 'bg-purple-100 text-purple-800';
      case 'implemented': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getModelTypeIcon = (type: string) => {
    switch (type) {
      case 'Time Series Prediction': return <LineChart className="h-4 w-4" />;
      case 'Classification': return <PieChart className="h-4 w-4" />;
      case 'Regression': return <BarChart3 className="h-4 w-4" />;
      case 'Neural Network': return <Brain className="h-4 w-4" />;
      case 'Reinforcement Learning': return <Cpu className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">AI Analytics Dashboard</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3A AI Analytics Dashboard</h2>
          <p className="text-gray-600">Machine learning insights and intelligent recommendations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Train Models
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Insights
          </Button>
        </div>
      </div>

      {/* View Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={selectedView === 'insights' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('insights')}
        >
          <Lightbulb className="h-4 w-4 mr-2" />
          AI Insights
        </Button>
        <Button
          variant={selectedView === 'models' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('models')}
        >
          <Brain className="h-4 w-4 mr-2" />
          ML Models
        </Button>
        <Button
          variant={selectedView === 'predictions' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('predictions')}
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          Predictions
        </Button>
      </div>

      {/* AI Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Models</p>
                <p className="text-2xl font-bold text-blue-600">
                  {mlModels.filter(m => m.status === 'active').length}
                </p>
              </div>
              <Brain className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Accuracy</p>
                <p className="text-2xl font-bold text-green-600">
                  {Math.round(mlModels.reduce((sum, model) => sum + model.accuracy, 0) / mlModels.length)}%
                </p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Predictions</p>
                <p className="text-2xl font-bold text-purple-600">
                  {mlModels.reduce((sum, model) => sum + model.predictions, 0).toLocaleString()}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">New Insights</p>
                <p className="text-2xl font-bold text-orange-600">
                  {aiInsights.filter(i => i.status === 'new').length}
                </p>
              </div>
              <Lightbulb className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {selectedView === 'insights' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">AI-Generated Insights</h3>
          {aiInsights.map((insight) => (
            <Card key={insight.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {getInsightTypeIcon(insight.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{insight.title}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getInsightTypeColor(insight.type)}>
                          {insight.type}
                        </Badge>
                        <Badge className={getStatusColor(insight.status)}>
                          {insight.status}
                        </Badge>
                        <span className={`text-sm ${getImpactColor(insight.impact)}`}>
                          {insight.impact} impact
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">{insight.confidence}%</div>
                    <div className="text-xs text-gray-500">confidence</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{insight.description}</p>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    {insight.timestamp.toLocaleString()}
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="h-3 w-3 mr-1" />
                      Details
                    </Button>
                    {insight.status === 'new' && (
                      <Button size="sm">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Implement
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedView === 'models' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mlModels.map((model) => (
            <Card key={model.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {getModelTypeIcon(model.type)}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{model.name}</CardTitle>
                      <p className="text-sm text-gray-600">{model.type}</p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(model.status)}>
                    {model.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Accuracy</p>
                      <p className="text-xl font-bold text-green-600">{model.accuracy}%</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Performance</p>
                      <p className="text-xl font-bold text-blue-600">{model.performance}%</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Predictions Made</p>
                    <p className="text-lg font-semibold">{model.predictions.toLocaleString()}</p>
                  </div>
                  <div className="text-xs text-gray-500">
                    Last trained: {model.lastTrained.toLocaleDateString()}
                  </div>
                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Retrain
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedView === 'predictions' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Real-time Predictions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <TrendingUp className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Prediction Engine</h3>
              <p className="text-gray-600 mb-4">
                Real-time AI predictions and forecasting dashboard will be displayed here.
              </p>
              <Button>
                <Brain className="h-4 w-4 mr-2" />
                Generate Predictions
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
