const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Cha<PERSON>@0319',
  port: 5432,
});

async function debugSchema() {
  try {
    console.log('🔍 Debugging database schema...');
    
    // Check tenants table structure
    console.log('\n📋 TENANTS TABLE STRUCTURE:');
    const tenantsSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'tenants' 
      ORDER BY ordinal_position
    `);
    tenantsSchema.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Check employees table structure
    console.log('\n👥 EMPLOYEES TABLE STRUCTURE:');
    const employeesSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees' 
      ORDER BY ordinal_position
    `);
    employeesSchema.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Check locations table structure
    console.log('\n🏢 LOCATIONS TABLE STRUCTURE:');
    const locationsSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'locations' 
      ORDER BY ordinal_position
    `);
    locationsSchema.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Check orders table structure
    console.log('\n📋 ORDERS TABLE STRUCTURE:');
    const ordersSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'orders' 
      ORDER BY ordinal_position
    `);
    ordersSchema.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Sample data from each table
    console.log('\n📊 SAMPLE DATA:');
    
    console.log('\nTenants (first 3):');
    const tenantsSample = await pool.query('SELECT id, name, slug FROM tenants LIMIT 3');
    tenantsSample.rows.forEach(row => {
      console.log(`  ID: ${row.id} (${typeof row.id}), Name: ${row.name}`);
    });

    console.log('\nEmployees (first 3):');
    const employeesSample = await pool.query('SELECT id, tenant_id, name FROM employees LIMIT 3');
    employeesSample.rows.forEach(row => {
      console.log(`  ID: ${row.id} (${typeof row.id}), Tenant ID: ${row.tenant_id} (${typeof row.tenant_id}), Name: ${row.name}`);
    });

    console.log('\nLocations (first 3):');
    const locationsSample = await pool.query('SELECT id, tenant_id, name FROM locations LIMIT 3');
    locationsSample.rows.forEach(row => {
      console.log(`  ID: ${row.id} (${typeof row.id}), Tenant ID: ${row.tenant_id} (${typeof row.tenant_id}), Name: ${row.name}`);
    });

    console.log('\nOrders (first 3):');
    const ordersSample = await pool.query('SELECT id, tenant_id, total FROM orders LIMIT 3');
    ordersSample.rows.forEach(row => {
      console.log(`  ID: ${row.id} (${typeof row.id}), Tenant ID: ${row.tenant_id} (${typeof row.tenant_id}), Total: ${row.total}`);
    });

    console.log('\n✅ Schema debugging complete!');
    
  } catch (error) {
    console.error('💥 Error debugging schema:', error);
  } finally {
    await pool.end();
  }
}

debugSchema();
