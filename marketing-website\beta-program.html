<html lang="en">
    <!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow Beta Program - Exclusive Early Access (10 Spots Only)</title>
    <meta name="description" content="Join the exclusive RestroFlow beta program. 90-day free trial, 50% first-year discount, and direct influence on the future of restaurant technology.">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .beta-badge { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .countdown-timer { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        .application-form { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-purple-600">RestroFlow</h1>
                    <span class="ml-4 beta-badge text-white px-3 py-1 rounded-full text-sm font-semibold">BETA PROGRAM</span>
                </div>
                <div class="countdown-timer text-white px-4 py-2 rounded-lg text-sm font-semibold">
                    ⏰ <span id="countdown">7 days remaining</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg py-20">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <div class="bg-yellow-400 text-gray-900 px-6 py-2 rounded-full inline-block mb-6 font-semibold">
                🚨 EXCLUSIVE: Only 10 Spots Available
            </div>
            <h1 class="text-5xl font-bold text-white mb-6">
                Join the RestroFlow Beta Program
            </h1>
            <p class="text-xl text-white mb-8 max-w-3xl mx-auto">
                Be among the first 10 restaurants to experience the future of POS technology.
                Get 90 days FREE, 50% off your first year, and direct influence on our product roadmap.
            </p>
            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl p-6 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-white">
                    <div class="text-center">
                        <div class="text-3xl font-bold">90 Days</div>
                        <div class="text-sm opacity-90">FREE Trial</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold">50% Off</div>
                        <div class="text-sm opacity-90">First Year</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold">Priority</div>
                        <div class="text-sm opacity-90">Support</div>
                    </div>
                </div>
            </div>
            <button onclick="scrollToApplication()" class="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-12 py-4 rounded-lg text-xl font-bold transition-all duration-300 transform hover:scale-105">
                🚀 Apply Now - Limited Spots
            </button>
        </div>
    </section>

    <!-- Beta Benefits -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Exclusive Beta Benefits</h2>
                <p class="text-xl text-gray-600">Why join the BARPOS beta program?</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">🎁</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">90-Day FREE Trial</h3>
                    <p class="text-gray-600">3x longer than our standard trial. Test every feature with zero risk and full support.</p>
                </div>

                <div class="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">💰</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">50% First Year Discount</h3>
                    <p class="text-gray-600">Save thousands on your first year subscription after successful trial completion.</p>
                </div>

                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">🎯</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Priority Support</h3>
                    <p class="text-gray-600">Dedicated success manager and 24/7 priority support throughout your beta experience.</p>
                </div>

                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">🚀</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Feature Influence</h3>
                    <p class="text-gray-600">Direct input on product roadmap. Your feedback shapes the future of BARPOS.</p>
                </div>

                <div class="bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">⭐</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Early Access</h3>
                    <p class="text-gray-600">First access to new features and capabilities before general release.</p>
                </div>

                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-xl">
                    <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center mb-4">
                        <span class="text-2xl text-white">🏆</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Beta Recognition</h3>
                    <p class="text-gray-600">Official beta customer badge and recognition in our marketing materials.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Current Beta Results -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Current Beta Results</h2>
                <p class="text-xl text-gray-600">Real results from our beta restaurants</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                    <div class="text-4xl font-bold text-green-600 mb-2">35%</div>
                    <div class="text-lg font-semibold text-gray-900 mb-2">Revenue Increase</div>
                    <div class="text-gray-600">Average across beta restaurants</div>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">25%</div>
                    <div class="text-lg font-semibold text-gray-900 mb-2">Cost Reduction</div>
                    <div class="text-gray-600">Through automation and efficiency</div>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg text-center">
                    <div class="text-4xl font-bold text-purple-600 mb-2">15 hrs</div>
                    <div class="text-lg font-semibold text-gray-900 mb-2">Time Saved Weekly</div>
                    <div class="text-gray-600">Administrative task automation</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Form -->
    <section id="application" class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Apply for Beta Access</h2>
                <p class="text-xl text-gray-600">Complete the application below. We'll review and contact you within 24 hours.</p>
            </div>

            <div class="application-form bg-white rounded-2xl p-8 border border-gray-200">
                <form id="betaApplication" onsubmit="submitBetaApplication(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Restaurant Information -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Restaurant Information</h3>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Restaurant Name *</label>
                            <input type="text" name="restaurantName" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Restaurant Type *</label>
                            <select name="restaurantType" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select Type</option>
                                <option value="fast_casual">Fast Casual</option>
                                <option value="full_service">Full Service</option>
                                <option value="quick_service">Quick Service</option>
                                <option value="fine_dining">Fine Dining</option>
                                <option value="cafe">Café/Coffee Shop</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Number of Locations *</label>
                            <select name="locations" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select</option>
                                <option value="1">1 Location</option>
                                <option value="2">2 Locations</option>
                                <option value="3">3 Locations</option>
                                <option value="4">4 Locations</option>
                                <option value="5">5 Locations</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Monthly Revenue *</label>
                            <select name="monthlyRevenue" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select Range</option>
                                <option value="50k-100k">$50K - $100K</option>
                                <option value="100k-250k">$100K - $250K</option>
                                <option value="250k-500k">$250K - $500K</option>
                                <option value="500k+">$500K+</option>
                            </select>
                        </div>

                        <!-- Contact Information -->
                        <div class="md:col-span-2 mt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Your Name *</label>
                            <input type="text" name="contactName" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Your Role *</label>
                            <select name="role" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <option value="">Select Role</option>
                                <option value="owner">Owner</option>
                                <option value="manager">Manager</option>
                                <option value="operations">Operations Manager</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                            <input type="tel" name="phone" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <!-- Current System -->
                        <div class="md:col-span-2 mt-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Current POS System</h3>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current POS Provider</label>
                            <input type="text" name="currentPOS" placeholder="e.g., Square, Toast, Clover" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Monthly POS Cost</label>
                            <input type="text" name="currentCost" placeholder="e.g., $200/month" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>

                        <!-- Goals and Motivation -->
                        <div class="md:col-span-2 mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">What are your main goals for switching POS systems? *</label>
                            <textarea name="goals" required rows="3" placeholder="e.g., Improve efficiency, better analytics, reduce costs..." class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"></textarea>
                        </div>

                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Why do you want to join the BARPOS beta program? *</label>
                            <textarea name="motivation" required rows="3" placeholder="Tell us why you're interested in being a beta customer..." class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"></textarea>
                        </div>

                        <!-- Commitment -->
                        <div class="md:col-span-2 mt-6">
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 class="font-semibold text-gray-900 mb-2">Beta Program Commitment</h4>
                                <div class="space-y-2 text-sm text-gray-700">
                                    <label class="flex items-start">
                                        <input type="checkbox" name="commitment1" required class="mt-1 mr-3">
                                        <span>I commit to using BARPOS for the full 90-day beta period</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="checkbox" name="commitment2" required class="mt-1 mr-3">
                                        <span>I agree to provide weekly feedback and participate in check-in calls</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="checkbox" name="commitment3" required class="mt-1 mr-3">
                                        <span>I'm willing to provide testimonials and case study information</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="checkbox" name="commitment4" required class="mt-1 mr-3">
                                        <span>I understand this is a beta program and will provide constructive feedback</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 text-center">
                        <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-12 py-4 rounded-lg text-lg font-semibold transition-colors">
                            Submit Beta Application
                        </button>
                        <p class="text-sm text-gray-600 mt-4">We'll review your application and contact you within 24 hours</p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            </div>

            <div class="space-y-6">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="font-semibold text-gray-900 mb-2">How long is the beta program?</h3>
                    <p class="text-gray-600">The beta program runs for 90 days, giving you 3x longer than our standard trial to fully test and implement BARPOS.</p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="font-semibold text-gray-900 mb-2">What happens after the beta period?</h3>
                    <p class="text-gray-600">You can continue with a 50% discount on your first year, or cancel with no obligations. Most beta customers choose to continue.</p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="font-semibold text-gray-900 mb-2">How much support will I receive?</h3>
                    <p class="text-gray-600">You'll have a dedicated success manager, priority 24/7 support, and weekly check-in calls throughout the beta period.</p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="font-semibold text-gray-900 mb-2">Can I use BARPOS alongside my current system?</h3>
                    <p class="text-gray-600">Yes! Many beta customers run BARPOS in parallel with their existing system during the transition period.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer CTA -->
    <section class="gradient-bg py-16">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-white mb-4">Ready to Transform Your Restaurant?</h2>
            <p class="text-xl text-white mb-8">Join the exclusive BARPOS beta program today. Only 10 spots available.</p>
            <button onclick="scrollToApplication()" class="bg-yellow-400 hover:bg-yellow-500 text-gray-900 px-8 py-3 rounded-lg text-lg font-semibold">
                Apply Now
            </button>
        </div>
    </section>

    <script>
        // Countdown timer
        function updateCountdown() {
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + 7);

            const now = new Date();
            const timeLeft = endDate - now;

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

            document.getElementById('countdown').textContent = `${days} days, ${hours} hours remaining`;
        }

        setInterval(updateCountdown, 1000 * 60); // Update every minute
        updateCountdown();

        // Smooth scroll to application
        function scrollToApplication() {
            document.getElementById('application').scrollIntoView({ behavior: 'smooth' });
        }

        // Submit beta application
        async function submitBetaApplication(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Add beta program specific data
            data.source = 'beta_program';
            data.campaign = 'beta_launch';
            data.program_type = 'beta';

            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Submitting Application...';
            submitButton.disabled = true;

            try {
                const response = await fetch('/api/marketing/beta-applications', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showSuccessMessage();
                    form.reset();
                } else {
                    throw new Error(result.message || 'Failed to submit application');
                }

            } catch (error) {
                console.error('Beta application error:', error);
                showErrorMessage(error.message);
            } finally {
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        }

        function showSuccessMessage() {
            const message = `
                <div class="fixed top-4 right-4 bg-green-500 text-white p-6 rounded-lg shadow-lg z-50 max-w-md">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <span class="text-2xl">🎉</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold">Application Submitted!</h3>
                            <p class="mt-1 text-sm">Thank you for applying to the BARPOS beta program. We'll review your application and contact you within 24 hours.</p>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            ×
                        </button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', message);

            setTimeout(() => {
                const notification = document.querySelector('.fixed.top-4.right-4');
                if (notification) notification.remove();
            }, 10000);
        }

        function showErrorMessage(message) {
            const errorDiv = `
                <div class="fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-md">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <span class="text-xl">❌</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="font-semibold">Error</h3>
                            <p class="text-sm">${message}</p>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            ×
                        </button>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', errorDiv);

            setTimeout(() => {
                const notification = document.querySelector('.fixed.top-4.right-4.bg-red-500');
                if (notification) notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>