To update your project with category management and API integration, please manually update the file project/src/context/AppContext.tsx with the following content:

---
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { AppState, AppAction, Product, OrderItem, Category } from '../types';
import { products } from '../data/products';
import { employees } from '../data/employees';

// Initialize with sample data
const initialState: AppState = {
  products,
  orders: [],
  currentOrder: null,
  employees,
  currentEmployee: null,
  isAuthenticated: false,
  categories: [] as Category[],
};

// Reducer function to handle state updates
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    // Existing cases unchanged...

    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload,
      };

    case 'ADD_CATEGORY':
      return {
        ...state,
        categories: [...state.categories, action.payload],
      };

    default:
      return state;
  }
};

// Helper function to calculate subtotal
const calculateSubtotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => total + (item.price * item.quantity), 0);
};

// Create context
type AppContextType = {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  addProduct: (product: Product) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  fetchCategories: () => Promise<void>;
  addCategory: (name: string) => Promise<void>;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

// Async API calls to backend server
const apiAddProduct = async (product: Product) => {
  const response = await fetch('http://localhost:4000/products', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(product),
  });
  if (!response.ok) {
    throw new Error('Failed to add product');
  }
};

const apiUpdateProduct = async (product: Product) => {
  const response = await fetch(`http://localhost:4000/products/${product.id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(product),
  });
  if (!response.ok) {
    throw new Error('Failed to update product');
  }
};

const apiDeleteProduct = async (productId: string) => {
  const response = await fetch(`http://localhost:4000/products/${productId}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete product');
  }
};

const apiFetchCategories = async (): Promise<Category[]> => {
  const response = await fetch('http://localhost:4000/categories');
  if (!response.ok) {
    throw new Error('Failed to fetch categories');
  }
  const data = await response.json();
  return data;
};

const apiAddCategory = async (name: string): Promise<Category> => {
  const response = await fetch('http://localhost:4000/categories', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to add category');
  }
  const data = await response.json();
  return data;
};

// Provider component
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  React.useEffect(() => {
    fetchCategories();
  }, []);

  const addProduct = async (product: Product) => {
    dispatch({ type: 'ADD_PRODUCT', payload: product });
    try {
      await apiAddProduct(product);
    } catch (error) {
      console.error('Failed to add product online:', error);
    }
  };

  const updateProduct = async (product: Product) => {
    dispatch({ type: 'UPDATE_PRODUCT', payload: product });
    try {
      await apiUpdateProduct(product);
    } catch (error) {
      console.error('Failed to update product online:', error);
    }
  };

  const deleteProduct = async (productId: string) => {
    dispatch({ type: 'DELETE_PRODUCT', payload: productId });
    try {
      await apiDeleteProduct(productId);
    } catch (error) {
      console.error('Failed to delete product online:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const categories = await apiFetchCategories();
      dispatch({ type: 'SET_CATEGORIES', payload: categories });
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const addCategory = async (name: string) => {
    try {
      const newCategory = await apiAddCategory(name);
      dispatch({ type: 'ADD_CATEGORY', payload: newCategory });
    } catch (error) {
      console.error('Failed to add category:', error);
      throw error;
    }
  };

  return (
    <AppContext.Provider value={{ state, dispatch, addProduct, updateProduct, deleteProduct, fetchCategories, addCategory }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the app context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
---

Also, please update your root package.json with the following scripts to start the frontend:

{
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  }
}

Make sure react-scripts is installed by running:

npm install react-scripts --save-dev

After these updates, you can start the frontend with:

npm start

Let me know if you need further assistance.
