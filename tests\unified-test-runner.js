#!/usr/bin/env node

/**
 * Unified Test Runner for BARPOS System
 * Consolidates all testing functionality into a single, comprehensive runner
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

class UnifiedTestRunner {
  constructor() {
    this.results = {
      totalSuites: 0,
      passedSuites: 0,
      failedSuites: 0,
      skippedSuites: 0,
      startTime: Date.now(),
      suiteResults: []
    };
    
    this.config = {
      timeout: 120000, // 2 minutes default timeout
      parallel: false,
      coverage: false,
      verbose: false
    };
  }

  // Parse command line arguments
  parseArgs() {
    const args = process.argv.slice(2);
    
    for (let i = 0; i < args.length; i++) {
      switch (args[i]) {
        case '--coverage':
          this.config.coverage = true;
          break;
        case '--parallel':
          this.config.parallel = true;
          break;
        case '--verbose':
          this.config.verbose = true;
          break;
        case '--timeout':
          this.config.timeout = parseInt(args[++i]) || 120000;
          break;
        case '--help':
          this.showHelp();
          process.exit(0);
          break;
      }
    }
  }

  // Show help information
  showHelp() {
    console.log(`
${colors.cyan}BARPOS Unified Test Runner${colors.reset}

${colors.bright}Usage:${colors.reset}
  node tests/unified-test-runner.js [options] [test-type]

${colors.bright}Test Types:${colors.reset}
  unit          Run unit tests only
  integration   Run integration tests only
  e2e           Run end-to-end tests only
  performance   Run performance tests only
  security      Run security tests only
  all           Run all tests (default)

${colors.bright}Options:${colors.reset}
  --coverage    Generate code coverage report
  --parallel    Run tests in parallel (where possible)
  --verbose     Show detailed output
  --timeout     Set timeout in milliseconds (default: 120000)
  --help        Show this help message

${colors.bright}Examples:${colors.reset}
  node tests/unified-test-runner.js
  node tests/unified-test-runner.js unit --coverage
  node tests/unified-test-runner.js integration --verbose
  node tests/unified-test-runner.js all --parallel --coverage
    `);
  }

  // Log with colors and formatting
  log(message, color = 'reset') {
    const timestamp = new Date().toISOString().substr(11, 8);
    console.log(`${colors.cyan}[${timestamp}]${colors.reset} ${colors[color]}${message}${colors.reset}`);
  }

  // Run a single test suite
  async runTestSuite(suiteName, command, options = {}) {
    return new Promise((resolve) => {
      this.log(`Starting ${suiteName}...`, 'blue');
      
      const startTime = Date.now();
      const [cmd, ...args] = command.split(' ');
      const child = spawn(cmd, args, {
        stdio: this.config.verbose ? 'inherit' : 'pipe',
        cwd: process.cwd(),
        timeout: options.timeout || this.config.timeout,
        shell: true
      });

      let output = '';
      let errorOutput = '';

      if (!this.config.verbose) {
        child.stdout?.on('data', (data) => {
          output += data.toString();
        });

        child.stderr?.on('data', (data) => {
          errorOutput += data.toString();
        });
      }

      child.on('close', (code) => {
        const duration = Date.now() - startTime;
        const result = {
          name: suiteName,
          passed: code === 0,
          duration,
          output: output.trim(),
          error: errorOutput.trim()
        };

        this.results.suiteResults.push(result);
        
        if (code === 0) {
          this.results.passedSuites++;
          this.log(`✅ ${suiteName} passed (${duration}ms)`, 'green');
        } else {
          this.results.failedSuites++;
          this.log(`❌ ${suiteName} failed (${duration}ms)`, 'red');
          if (!this.config.verbose && errorOutput) {
            console.log(`${colors.red}Error output:${colors.reset}\n${errorOutput}`);
          }
        }

        resolve(result);
      });

      child.on('error', (error) => {
        this.results.failedSuites++;
        this.log(`💥 ${suiteName} crashed: ${error.message}`, 'red');
        resolve({
          name: suiteName,
          passed: false,
          duration: Date.now() - startTime,
          error: error.message
        });
      });
    });
  }

  // Test suite definitions
  getTestSuites() {
    return {
      unit: {
        name: 'Unit Tests',
        command: 'npm run test:unit',
        description: 'Component and service unit tests'
      },
      security: {
        name: 'Security Tests',
        command: 'node tests/security/security-audit.js',
        description: 'Security vulnerability testing'
      },
    };
  }

  // Run tests based on type
  async runTests(testType = 'all') {
    const suites = this.getTestSuites();
    let suitesToRun = [];

    if (testType === 'all') {
      suitesToRun = Object.values(suites);
    } else if (suites[testType]) {
      suitesToRun = [suites[testType]];
    } else {
      this.log(`Unknown test type: ${testType}`, 'red');
      this.log('Available types: ' + Object.keys(suites).join(', '), 'yellow');
      process.exit(1);
    }

    this.results.totalSuites = suitesToRun.length;

    this.log(`🚀 Starting BARPOS Test Suite`, 'bright');
    this.log(`Running ${suitesToRun.length} test suite(s)`, 'cyan');
    this.log(`Configuration: ${JSON.stringify(this.config)}`, 'blue');
    console.log('='.repeat(80));

    // Run tests
    if (this.config.parallel && suitesToRun.length > 1) {
      this.log('Running tests in parallel...', 'yellow');
      const promises = suitesToRun.map(suite => 
        this.runTestSuite(suite.name, suite.command)
      );
      await Promise.all(promises);
    } else {
      for (const suite of suitesToRun) {
        await this.runTestSuite(suite.name, suite.command);
      }
    }

    this.generateReport();
  }

  // Generate comprehensive test report
  generateReport() {
    const duration = Date.now() - this.results.startTime;
    const passRate = (this.results.passedSuites / this.results.totalSuites * 100).toFixed(1);

    console.log('\n' + '='.repeat(80));
    this.log('📊 TEST EXECUTION SUMMARY', 'bright');
    console.log('='.repeat(80));

    // Overall statistics
    console.log(`${colors.cyan}Total Suites:${colors.reset} ${this.results.totalSuites}`);
    console.log(`${colors.green}Passed:${colors.reset} ${this.results.passedSuites}`);
    console.log(`${colors.red}Failed:${colors.reset} ${this.results.failedSuites}`);
    console.log(`${colors.yellow}Skipped:${colors.reset} ${this.results.skippedSuites}`);
    console.log(`${colors.blue}Pass Rate:${colors.reset} ${passRate}%`);
    console.log(`${colors.magenta}Duration:${colors.reset} ${(duration / 1000).toFixed(2)}s`);

    // Detailed results
    console.log('\n' + colors.bright + 'DETAILED RESULTS:' + colors.reset);
    this.results.suiteResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      const color = result.passed ? 'green' : 'red';
      console.log(`  ${status} ${colors[color]}${result.name}${colors.reset} (${result.duration}ms)`);
      
      if (!result.passed && result.error && this.config.verbose) {
        console.log(`    ${colors.red}Error: ${result.error}${colors.reset}`);
      }
    });

    // Recommendations
    if (this.results.failedSuites > 0) {
      console.log('\n' + colors.yellow + '⚠️  RECOMMENDATIONS:' + colors.reset);
      console.log('  • Review failed test output above');
      console.log('  • Run individual test suites for detailed debugging');
      console.log('  • Check system dependencies and configuration');
      console.log('  • Ensure database and services are running');
    }

    // Coverage information
    if (this.config.coverage) {
      console.log('\n' + colors.cyan + '📈 COVERAGE REPORT:' + colors.reset);
      console.log('  Coverage reports generated in ./coverage/ directory');
      console.log('  Open ./coverage/lcov-report/index.html for detailed view');
    }

    // Exit with appropriate code
    const exitCode = this.results.failedSuites > 0 ? 1 : 0;
    console.log('\n' + '='.repeat(80));
    
    if (exitCode === 0) {
      this.log('🎉 All tests passed successfully!', 'green');
    } else {
      this.log(`💥 ${this.results.failedSuites} test suite(s) failed`, 'red');
    }
    
    process.exit(exitCode);
  }
}

// Main execution
if (require.main === module) {
  const runner = new UnifiedTestRunner();
  runner.parseArgs();
  
  const testType = process.argv[2] && !process.argv[2].startsWith('--') 
    ? process.argv[2] 
    : 'all';
  
  runner.runTests(testType).catch(error => {
    console.error('💥 Test runner crashed:', error);
    process.exit(1);
  });
}

module.exports = UnifiedTestRunner;
