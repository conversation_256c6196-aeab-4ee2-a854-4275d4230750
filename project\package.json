{"name": "bar-pos-system", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@types/axios": "^0.9.36", "@types/file-saver": "^2.0.7", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "colors": "^1.4.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "node-fetch": "^3.3.2", "pg": "^8.16.0", "puppeteer": "^24.10.0", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.3", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "uuid": "^9.0.1", "xlsx": "^0.18.5", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}