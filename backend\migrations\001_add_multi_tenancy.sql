-- Phase 2: Multi-Tenancy Database Migration
-- This migration adds multi-tenant support to the existing POS system

-- Create tenants table
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'canceled')),
    subscription_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    interval VARCHAR(20) NOT NULL CHECK (interval IN ('month', 'year')),
    features J<PERSON>NB NOT NULL DEFAULT '{}',
    limits JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'unpaid')),
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    stripe_subscription_id VARCHAR(255),
    trial_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create locations table
CREATE TABLE IF NOT EXISTS locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    timezone VARCHAR(100) DEFAULT 'UTC',
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create tenant_settings table
CREATE TABLE IF NOT EXISTS tenant_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE UNIQUE,
    business_name VARCHAR(255) NOT NULL,
    business_type VARCHAR(100),
    timezone VARCHAR(100) DEFAULT 'UTC',
    currency VARCHAR(10) DEFAULT 'USD',
    tax_rate DECIMAL(10,4) DEFAULT 0.0825,
    receipt_header TEXT,
    receipt_footer TEXT,
    business_address TEXT,
    business_phone VARCHAR(50),
    theme_primary_color VARCHAR(50) DEFAULT '#4f46e5',
    theme_secondary_color VARCHAR(50) DEFAULT '#10b981',
    features JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add tenant_id and location_id to existing tables
ALTER TABLE employees ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS location_id UUID REFERENCES locations(id) ON DELETE SET NULL;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '[]';
ALTER TABLE employees ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE employees ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;

ALTER TABLE products ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS location_id UUID REFERENCES locations(id) ON DELETE SET NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS sku VARCHAR(100);
ALTER TABLE products ADD COLUMN IF NOT EXISTS barcode VARCHAR(100);
ALTER TABLE products ADD COLUMN IF NOT EXISTS cost DECIMAL(10,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS margin DECIMAL(5,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS taxable BOOLEAN DEFAULT true;
ALTER TABLE products ADD COLUMN IF NOT EXISTS modifiers JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS variants JSONB DEFAULT '[]';

ALTER TABLE orders ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS location_id UUID REFERENCES locations(id) ON DELETE SET NULL;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS customer_id UUID;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS kitchen_notes TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_number INTEGER;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_info JSONB;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_info JSONB;

ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS location_id UUID REFERENCES locations(id) ON DELETE SET NULL;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS started_at TIMESTAMP;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS ready_at TIMESTAMP;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS served_at TIMESTAMP;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS preparation_time INTEGER;
ALTER TABLE kitchen_orders ADD COLUMN IF NOT EXISTS total_time INTEGER;

ALTER TABLE categories ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;

-- Create customers table for loyalty program
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    points INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    visits INTEGER DEFAULT 0,
    tier VARCHAR(50) DEFAULT 'bronze' CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_visit TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create loyalty_rewards table
CREATE TABLE IF NOT EXISTS loyalty_rewards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    points_cost INTEGER NOT NULL,
    category VARCHAR(50) CHECK (category IN ('discount', 'free-item', 'upgrade')),
    value DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create loyalty_transactions table
CREATE TABLE IF NOT EXISTS loyalty_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    type VARCHAR(20) CHECK (type IN ('earned', 'redeemed')),
    points INTEGER NOT NULL,
    description TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create kitchen_staff table
CREATE TABLE IF NOT EXISTS kitchen_staff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) CHECK (role IN ('chef', 'cook', 'prep')),
    is_active BOOLEAN DEFAULT true,
    max_orders INTEGER DEFAULT 5,
    efficiency DECIMAL(5,2) DEFAULT 100.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create kitchen_metrics table for performance tracking
CREATE TABLE IF NOT EXISTS kitchen_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_orders INTEGER DEFAULT 0,
    completed_orders INTEGER DEFAULT 0,
    average_preparation_time INTEGER DEFAULT 0,
    average_total_time INTEGER DEFAULT 0,
    efficiency DECIMAL(5,2) DEFAULT 0,
    peak_hours JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, location_id, date)
);

-- Create analytics_reports table
CREATE TABLE IF NOT EXISTS analytics_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    report_type VARCHAR(100) NOT NULL,
    report_data JSONB NOT NULL,
    date_range_start DATE NOT NULL,
    date_range_end DATE NOT NULL,
    generated_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, price, interval, features, limits) VALUES
('Starter', 29.99, 'month', 
 '{"kitchen_display": true, "basic_reporting": true, "employee_management": true}',
 '{"locations": 1, "employees": 10, "products": 500, "orders_per_month": 1000, "storage_gb": 5}'
),
('Professional', 79.99, 'month',
 '{"kitchen_display": true, "advanced_reporting": true, "employee_management": true, "loyalty_program": true, "inventory_management": true, "multi_location": true}',
 '{"locations": 5, "employees": 50, "products": 2000, "orders_per_month": 10000, "storage_gb": 25}'
),
('Enterprise', 199.99, 'month',
 '{"kitchen_display": true, "advanced_reporting": true, "employee_management": true, "loyalty_program": true, "inventory_management": true, "multi_location": true, "third_party_integrations": true, "custom_branding": true}',
 '{"locations": -1, "employees": -1, "products": -1, "orders_per_month": -1, "storage_gb": 100}'
)
ON CONFLICT DO NOTHING;

-- Create default tenant for existing data
INSERT INTO tenants (id, name, slug, email, status) 
VALUES ('00000000-0000-0000-0000-000000000001', 'Default Restaurant', 'default', '<EMAIL>', 'active')
ON CONFLICT DO NOTHING;

-- Create default location for existing data
INSERT INTO locations (id, tenant_id, name, address, timezone, is_active)
VALUES ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', 'Main Location', '123 Main St', 'UTC', true)
ON CONFLICT DO NOTHING;

-- Create default subscription for existing tenant
INSERT INTO subscriptions (tenant_id, plan_id, current_period_start, current_period_end)
SELECT 
    '00000000-0000-0000-0000-000000000001',
    id,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP + INTERVAL '1 month'
FROM subscription_plans WHERE name = 'Professional'
ON CONFLICT DO NOTHING;

-- Create default tenant settings
INSERT INTO tenant_settings (tenant_id, business_name, business_type, features)
VALUES ('00000000-0000-0000-0000-000000000001', 'Default Restaurant', 'restaurant', 
        '{"multi_location": true, "kitchen_display": true, "loyalty_program": true, "inventory_management": true, "advanced_reporting": true, "third_party_integrations": false, "custom_branding": false}')
ON CONFLICT DO NOTHING;

-- Update existing data to belong to default tenant
UPDATE employees SET tenant_id = '00000000-0000-0000-0000-000000000001', location_id = '00000000-0000-0000-0000-000000000001' WHERE tenant_id IS NULL;
UPDATE products SET tenant_id = '00000000-0000-0000-0000-000000000001', location_id = '00000000-0000-0000-0000-000000000001' WHERE tenant_id IS NULL;
UPDATE orders SET tenant_id = '00000000-0000-0000-0000-000000000001', location_id = '00000000-0000-0000-0000-000000000001' WHERE tenant_id IS NULL;
UPDATE kitchen_orders SET tenant_id = '00000000-0000-0000-0000-000000000001', location_id = '00000000-0000-0000-0000-000000000001' WHERE tenant_id IS NULL;
UPDATE categories SET tenant_id = '00000000-0000-0000-0000-000000000001' WHERE tenant_id IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_employees_tenant_id ON employees(tenant_id);
CREATE INDEX IF NOT EXISTS idx_employees_location_id ON employees(location_id);
CREATE INDEX IF NOT EXISTS idx_products_tenant_id ON products(tenant_id);
CREATE INDEX IF NOT EXISTS idx_products_location_id ON products(location_id);
CREATE INDEX IF NOT EXISTS idx_orders_tenant_id ON orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_orders_location_id ON orders(location_id);
CREATE INDEX IF NOT EXISTS idx_kitchen_orders_tenant_id ON kitchen_orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_kitchen_orders_location_id ON kitchen_orders(location_id);
CREATE INDEX IF NOT EXISTS idx_kitchen_orders_status ON kitchen_orders(status);
CREATE INDEX IF NOT EXISTS idx_customers_tenant_id ON customers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_transactions_tenant_id ON loyalty_transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_kitchen_metrics_tenant_location_date ON kitchen_metrics(tenant_id, location_id, date);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_settings_updated_at BEFORE UPDATE ON tenant_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
