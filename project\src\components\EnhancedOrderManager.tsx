import React, { useState, useEffect } from 'react';
import { 
  Edit3, 
  Trash2, 
  RefreshCw, 
  DollarSign, 
  Receipt, 
  Printer, 
  Mail, 
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Split,
  Undo,
  Copy,
  Eye,
  Download,
  Send,
  Kitchen,
  User,
  MapPin
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface OrderAction {
  id: string;
  type: 'modify' | 'cancel' | 'refund' | 'split' | 'duplicate' | 'receipt' | 'kitchen';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresConfirmation?: boolean;
  disabled?: boolean;
}

interface EnhancedOrderManagerProps {
  orderId?: string;
  onClose?: () => void;
}

export const EnhancedOrderManager: React.FC<EnhancedOrderManagerProps> = ({
  orderId,
  onClose
}) => {
  const { state, apiCall } = useEnhancedAppContext();
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [showModifyModal, setShowModifyModal] = useState(false);
  const [showSplitModal, setShowSplitModal] = useState(false);
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Order actions configuration
  const orderActions: OrderAction[] = [
    {
      id: 'modify',
      type: 'modify',
      label: 'Modify Order',
      icon: Edit3,
      color: 'blue',
      disabled: selectedOrder?.status === 'completed' || selectedOrder?.status === 'cancelled'
    },
    {
      id: 'split',
      type: 'split',
      label: 'Split Payment',
      icon: Split,
      color: 'purple',
      disabled: selectedOrder?.status !== 'pending_payment'
    },
    {
      id: 'duplicate',
      type: 'duplicate',
      label: 'Duplicate Order',
      icon: Copy,
      color: 'green'
    },
    {
      id: 'kitchen',
      type: 'kitchen',
      label: 'Send to Kitchen',
      icon: Kitchen,
      color: 'orange',
      disabled: selectedOrder?.status !== 'pending'
    },
    {
      id: 'receipt',
      type: 'receipt',
      label: 'Print Receipt',
      icon: Receipt,
      color: 'gray'
    },
    {
      id: 'email_receipt',
      type: 'receipt',
      label: 'Email Receipt',
      icon: Mail,
      color: 'blue'
    },
    {
      id: 'refund',
      type: 'refund',
      label: 'Process Refund',
      icon: Undo,
      color: 'red',
      requiresConfirmation: true,
      disabled: selectedOrder?.status !== 'completed'
    },
    {
      id: 'cancel',
      type: 'cancel',
      label: 'Cancel Order',
      icon: XCircle,
      color: 'red',
      requiresConfirmation: true,
      disabled: selectedOrder?.status === 'completed' || selectedOrder?.status === 'cancelled'
    }
  ];

  // Load order details
  useEffect(() => {
    if (orderId) {
      loadOrderDetails(orderId);
    }
  }, [orderId]);

  const loadOrderDetails = async (id: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiCall(`/api/orders/${id}`);
      if (response.ok) {
        const orderData = await response.json();
        setSelectedOrder(orderData);
      } else {
        throw new Error('Failed to load order details');
      }
    } catch (error) {
      console.error('Error loading order:', error);
      setError('Failed to load order details');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle order actions
  const handleOrderAction = async (action: OrderAction) => {
    if (action.requiresConfirmation) {
      setShowConfirmDialog(action.id);
      return;
    }

    await executeOrderAction(action);
  };

  const executeOrderAction = async (action: OrderAction) => {
    setActionLoading(action.id);
    setError(null);

    try {
      switch (action.type) {
        case 'modify':
          setShowModifyModal(true);
          break;

        case 'split':
          setShowSplitModal(true);
          break;

        case 'duplicate':
          await handleDuplicateOrder();
          break;

        case 'kitchen':
          await handleSendToKitchen();
          break;

        case 'receipt':
          await handleReceiptAction(action.id);
          break;

        case 'refund':
          setShowRefundModal(true);
          break;

        case 'cancel':
          await handleCancelOrder();
          break;

        default:
          console.warn('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      setError(`Failed to ${action.label.toLowerCase()}`);
    } finally {
      setActionLoading(null);
      setShowConfirmDialog(null);
    }
  };

  const handleDuplicateOrder = async () => {
    const response = await apiCall('/api/orders/duplicate', {
      method: 'POST',
      body: JSON.stringify({ order_id: selectedOrder.id })
    });

    if (response.ok) {
      const newOrder = await response.json();
      alert(`Order duplicated successfully! New order ID: ${newOrder.id}`);
    } else {
      throw new Error('Failed to duplicate order');
    }
  };

  const handleSendToKitchen = async () => {
    const response = await apiCall(`/api/orders/${selectedOrder.id}/kitchen`, {
      method: 'POST',
      body: JSON.stringify({
        priority: 'normal',
        special_instructions: selectedOrder.kitchen_notes || ''
      })
    });

    if (response.ok) {
      setSelectedOrder(prev => ({ ...prev, status: 'preparing' }));
      alert('Order sent to kitchen successfully!');
    } else {
      throw new Error('Failed to send order to kitchen');
    }
  };

  const handleReceiptAction = async (actionId: string) => {
    const isEmail = actionId === 'email_receipt';
    const endpoint = isEmail ? 'email-receipt' : 'print-receipt';
    
    const response = await apiCall(`/api/orders/${selectedOrder.id}/${endpoint}`, {
      method: 'POST',
      body: JSON.stringify({
        customer_email: isEmail ? selectedOrder.customer_info?.email : undefined,
        print_options: {
          copies: 1,
          include_logo: true,
          include_qr: true
        }
      })
    });

    if (response.ok) {
      const result = await response.json();
      if (isEmail) {
        alert(`Receipt emailed to ${selectedOrder.customer_info?.email || 'customer'}`);
      } else {
        alert('Receipt sent to printer');
      }
    } else {
      throw new Error(`Failed to ${isEmail ? 'email' : 'print'} receipt`);
    }
  };

  const handleCancelOrder = async () => {
    const response = await apiCall(`/api/orders/${selectedOrder.id}/cancel`, {
      method: 'POST',
      body: JSON.stringify({
        reason: 'Customer request',
        refund_amount: selectedOrder.total_amount
      })
    });

    if (response.ok) {
      setSelectedOrder(prev => ({ ...prev, status: 'cancelled' }));
      alert('Order cancelled successfully');
    } else {
      throw new Error('Failed to cancel order');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'preparing': return 'bg-blue-100 text-blue-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionButtonClass = (action: OrderAction) => {
    const baseClass = "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
    
    switch (action.color) {
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'red': return `${baseClass} bg-red-600 hover:bg-red-700 text-white`;
      case 'gray': return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading order details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
          <span className="text-red-800">{error}</span>
        </div>
        <button
          onClick={() => orderId && loadOrderDetails(orderId)}
          className="mt-3 text-red-600 hover:text-red-800 font-medium"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!selectedOrder) {
    return (
      <div className="p-6 text-center text-gray-500">
        <Eye className="h-12 w-12 mx-auto mb-3 text-gray-400" />
        <p>Select an order to view details and actions</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Order Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Order #{selectedOrder.order_number}</h2>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedOrder.status)}`}>
              {selectedOrder.status.charAt(0).toUpperCase() + selectedOrder.status.slice(1)}
            </span>
            <span className="text-gray-500 text-sm">
              <Clock className="h-4 w-4 inline mr-1" />
              {new Date(selectedOrder.created_at).toLocaleString()}
            </span>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircle className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* Order Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <h3 className="font-semibold text-gray-900 mb-3">Order Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Table:</span>
              <span className="font-medium">{selectedOrder.table_number || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Server:</span>
              <span className="font-medium">{selectedOrder.server_name || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Guests:</span>
              <span className="font-medium">{selectedOrder.guest_count || 1}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total:</span>
              <span className="font-bold text-green-600">${selectedOrder.total_amount?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-semibold text-gray-900 mb-3">Customer Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Name:</span>
              <span className="font-medium">{selectedOrder.customer_info?.name || 'Walk-in'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Phone:</span>
              <span className="font-medium">{selectedOrder.customer_info?.phone || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Email:</span>
              <span className="font-medium">{selectedOrder.customer_info?.email || 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Order Actions */}
      <div className="border-t pt-6">
        <h3 className="font-semibold text-gray-900 mb-4">Order Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {orderActions.map((action) => (
            <button
              key={action.id}
              onClick={() => handleOrderAction(action)}
              disabled={action.disabled || actionLoading === action.id}
              className={getActionButtonClass(action)}
            >
              {actionLoading === action.id ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <action.icon className="h-4 w-4" />
              )}
              <span className="text-sm">{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Action</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to {orderActions.find(a => a.id === showConfirmDialog)?.label.toLowerCase()}?
              This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const action = orderActions.find(a => a.id === showConfirmDialog);
                  if (action) executeOrderAction(action);
                }}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
