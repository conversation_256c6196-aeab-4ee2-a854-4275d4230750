const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./pos_system.db');

console.log('=== CHECKING TENANTS ===');
db.all('SELECT * FROM tenants', (err, rows) => {
  if (err) {
    console.error('Error:', err);
    return;
  }
  console.log('Available tenants:');
  rows.forEach(row => {
    console.log(`- ID: ${row.id}, Name: ${row.name}, Slug: ${row.slug}, Status: ${row.status}`);
  });
  
  console.log('\n=== CHECKING EMPLOYEES ===');
  db.all('SELECT * FROM employees', (err, rows) => {
    if (err) {
      console.error('Error:', err);
      return;
    }
    console.log('Available employees:');
    rows.forEach(row => {
      console.log(`- ID: ${row.id}, Name: ${row.name}, PIN: ${row.pin}, Role: ${row.role}, Tenant: ${row.tenant_id}`);
    });
    
    console.log('\n=== TESTING LOGIN ===');
    // Test the login with the actual data
    const testPins = ['123456', '1234', '567890'];
    const testTenants = ['demo-restaurant', 'acme-corp'];
    
    console.log('Testing PIN combinations...');
    testPins.forEach(pin => {
      testTenants.forEach(tenant => {
        console.log(`Testing PIN: ${pin} with tenant: ${tenant}`);
      });
    });
    
    db.close();
  });
});
