// Centralized Error Handling Middleware
// Provides consistent error responses across the application

class AppError extends Error {
  constructor(message, statusCode, errorCode = null, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Database Error Handler
const handleDatabaseError = (error) => {
  console.error('💥 Database Error:', error);
  
  // PostgreSQL specific error codes
  switch (error.code) {
    case '23505': // Unique violation
      return new AppError('Duplicate entry found', 409, 'DUPLICATE_ENTRY', {
        constraint: error.constraint,
        detail: error.detail
      });
    
    case '23503': // Foreign key violation
      return new AppError('Referenced record not found', 400, 'FOREIGN_KEY_VIOLATION', {
        constraint: error.constraint,
        detail: error.detail
      });
    
    case '23502': // Not null violation
      return new AppError('Required field missing', 400, 'REQUIRED_FIELD_MISSING', {
        column: error.column,
        table: error.table
      });
    
    case '42P01': // Undefined table
      return new AppError('Database table not found', 500, 'TABLE_NOT_FOUND', {
        table: error.table
      });
    
    case '42703': // Undefined column
      return new AppError('Database column not found', 500, 'COLUMN_NOT_FOUND', {
        column: error.column,
        table: error.table
      });
    
    case '22003': // Numeric value out of range
      return new AppError('Numeric value out of range', 400, 'NUMERIC_OVERFLOW', {
        detail: error.detail
      });
    
    case 'ECONNREFUSED':
      return new AppError('Database connection failed', 503, 'DATABASE_UNAVAILABLE');
    
    case 'ETIMEDOUT':
      return new AppError('Database operation timed out', 504, 'DATABASE_TIMEOUT');
    
    default:
      return new AppError('Database operation failed', 500, 'DATABASE_ERROR', {
        code: error.code,
        message: error.message
      });
  }
};

// JWT Error Handler
const handleJWTError = (error) => {
  console.error('🔒 JWT Error:', error);
  
  if (error.name === 'JsonWebTokenError') {
    return new AppError('Invalid authentication token', 401, 'INVALID_TOKEN');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AppError('Authentication token expired', 401, 'TOKEN_EXPIRED');
  }
  
  if (error.name === 'NotBeforeError') {
    return new AppError('Authentication token not active', 401, 'TOKEN_NOT_ACTIVE');
  }
  
  return new AppError('Authentication failed', 401, 'AUTH_ERROR');
};

// Validation Error Handler
const handleValidationError = (error) => {
  console.error('📝 Validation Error:', error);
  
  const errors = error.errors || [];
  const details = errors.map(err => ({
    field: err.param || err.path,
    message: err.msg || err.message,
    value: err.value
  }));
  
  return new AppError('Validation failed', 400, 'VALIDATION_ERROR', details);
};

// Global Error Handler Middleware
const globalErrorHandler = (error, req, res, next) => {
  let err = error;
  
  // Log error details
  console.error('🚨 Global Error Handler:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  
  // Handle specific error types
  if (error.code && (error.code.startsWith('23') || error.code.startsWith('42') || error.code === '22003')) {
    err = handleDatabaseError(error);
  } else if (error.name && error.name.includes('JsonWebToken')) {
    err = handleJWTError(error);
  } else if (error.name === 'ValidationError' || (error.errors && Array.isArray(error.errors))) {
    err = handleValidationError(error);
  } else if (!error.isOperational) {
    // Unknown error - don't leak details in production
    err = new AppError('Something went wrong', 500, 'INTERNAL_ERROR');
  }
  
  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      message: err.message,
      code: err.errorCode || 'UNKNOWN_ERROR',
      statusCode: err.statusCode || 500
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  };
  
  // Add details in development mode
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.details = err.details;
    errorResponse.error.stack = err.stack;
  }
  
  // Add request ID if available
  if (req.requestId) {
    errorResponse.requestId = req.requestId;
  }
  
  res.status(err.statusCode || 500).json(errorResponse);
};

// Async Error Wrapper
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 Handler
const notFoundHandler = (req, res, next) => {
  const error = new AppError(`Route ${req.originalUrl} not found`, 404, 'ROUTE_NOT_FOUND');
  next(error);
};

// Request ID Middleware
const requestIdMiddleware = (req, res, next) => {
  req.requestId = require('crypto').randomUUID();
  res.setHeader('X-Request-ID', req.requestId);
  next();
};

module.exports = {
  AppError,
  globalErrorHandler,
  asyncHandler,
  notFoundHandler,
  requestIdMiddleware,
  handleDatabaseError,
  handleJWTError,
  handleValidationError
};
