import React, { useState, useEffect } from 'react';
import {
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit3,
  User<PERSON>heck,
  DollarSign,
  Timer,
  Phone,
  Calendar,
  MapPin,
  Settings,
  Eye,
  Coffee,
  Utensils,
  CreditCard,
  AlertTriangle,
  Wifi,
  WifiOff,
  ShoppingCart
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import EnhancedPaymentModal from './EnhancedPaymentModal';
import EnhancedReceiptSystem from './EnhancedReceiptSystem';
import TableDetailsModal from './TableDetailsModal';
import ReservationManager from './ReservationManager';

interface Table {
  id: string;
  number: number;
  seats: number;
  x: number;
  y: number;
  width: number;
  height: number;
  shape: 'rectangle' | 'circle' | 'oval';
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order' | 'being-seated';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  section: string;
  tableType: 'regular' | 'bar' | 'private' | 'outdoor' | 'booth';
  guestCount?: number;
  serverAssigned?: string;
  serverName?: string;
  seatedTime?: Date;
  estimatedTurnTime?: Date;
  currentOrderId?: string;
  orderTotal?: number;
  orderItems?: number;
  isVip?: boolean;
  specialRequests?: string[];
  allergies?: string[];
  notes?: string;
  combinedWith?: string[];
  maxCapacity?: number;
}

interface TableSection {
  id: string;
  name: string;
  color: string;
  serverAssigned?: string;
}

interface Reservation {
  id: string;
  tableId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  reservationTime: string;
  duration: number;
  status: 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no-show';
  specialRequests?: string[];
  notes?: string;
}

interface WaitlistEntry {
  id: string;
  customerName: string;
  customerPhone: string;
  partySize: number;
  estimatedWait: number;
  status: 'waiting' | 'notified' | 'seated' | 'cancelled';
  addedTime: string;
  preferredSection?: string;
  specialRequests?: string[];
}

interface FloorLayoutData {
  tables: Table[];
  sections: TableSection[];
  layout_settings: {
    width: number;
    height: number;
    background_color: string;
    gridSize: number;
  };
}

const EnhancedFloorLayout: React.FC = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [floorData, setFloorData] = useState<FloorLayoutData | null>(null);
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [waitlist, setWaitlist] = useState<WaitlistEntry[]>([]);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [showTableDetails, setShowTableDetails] = useState(false);
  const [showReservations, setShowReservations] = useState(false);
  const [showWaitlist, setShowWaitlist] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [showReceipt, setShowReceipt] = useState(false);
  const [showPOSIntegration, setShowPOSIntegration] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedSection, setSelectedSection] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [currentOrder, setCurrentOrder] = useState<any>(null);
  const [receiptData, setReceiptData] = useState<any>(null);

  useEffect(() => {
    fetchFloorLayout();
    fetchReservations();
    fetchWaitlist();
    
    // Set up real-time updates
    const interval = setInterval(() => {
      fetchFloorLayout();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchFloorLayout = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/floor/layout', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setFloorData(data);
      } else {
        console.error('Failed to fetch floor layout');
        // Use mock data if API fails
        setFloorData({
          tables: [
            {
              id: '1', number: 1, seats: 4, x: 50, y: 50, width: 80, height: 80,
              shape: 'rectangle', status: 'available', section: 'main',
              tableType: 'regular'
            },
            {
              id: '2', number: 2, seats: 2, x: 200, y: 50, width: 60, height: 60,
              shape: 'circle', status: 'occupied', section: 'main',
              tableType: 'regular', guestCount: 2, orderTotal: 45.50, orderItems: 3
            },
            {
              id: '3', number: 3, seats: 6, x: 350, y: 50, width: 100, height: 80,
              shape: 'rectangle', status: 'reserved', section: 'main',
              tableType: 'regular'
            }
          ],
          sections: [
            { id: 'main', name: 'Main Dining', color: '#3B82F6' },
            { id: 'bar', name: 'Bar Area', color: '#EF4444' },
            { id: 'patio', name: 'Patio', color: '#10B981' }
          ],
          layout_settings: {
            width: 800,
            height: 600,
            background_color: '#F3F4F6',
            gridSize: 20
          }
        });
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching floor layout:', error);
      setLoading(false);
    }
  };

  const fetchReservations = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/reservations', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setReservations(data);
      } else {
        setReservations([]); // Set empty array if API fails
      }
    } catch (error) {
      console.error('Error fetching reservations:', error);
      setReservations([]);
    }
  };

  const fetchWaitlist = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/waitlist', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const data = await response.json();
        setWaitlist(data);
      } else {
        setWaitlist([]); // Set empty array if API fails
      }
    } catch (error) {
      console.error('Error fetching waitlist:', error);
      setWaitlist([]);
    }
  };

  const updateTableStatus = async (tableId: string, status: Table['status'], substatus?: Table['substatus']) => {
    try {
      const token = localStorage.getItem('authToken');
      await fetch(`http://localhost:4000/api/floor/tables/${tableId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status, substatus })
      });
      fetchFloorLayout(); // Refresh data
    } catch (error) {
      console.error('Error updating table status:', error);
    }
  };

  const getTableStatusColor = (table: Table) => {
    const { status, substatus } = table;
    
    switch (status) {
      case 'available':
        return 'bg-green-500 hover:bg-green-600 border-green-600';
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return 'bg-blue-500 hover:bg-blue-600 border-blue-600';
          case 'eating':
            return 'bg-red-500 hover:bg-red-600 border-red-600';
          case 'waiting-for-check':
            return 'bg-purple-500 hover:bg-purple-600 border-purple-600';
          case 'paying':
            return 'bg-indigo-500 hover:bg-indigo-600 border-indigo-600';
          default:
            return 'bg-red-500 hover:bg-red-600 border-red-600';
        }
      case 'reserved':
        return 'bg-yellow-500 hover:bg-yellow-600 border-yellow-600';
      case 'needs-cleaning':
        return 'bg-orange-500 hover:bg-orange-600 border-orange-600';
      case 'out-of-order':
        return 'bg-gray-500 hover:bg-gray-600 border-gray-600';
      case 'being-seated':
        return 'bg-cyan-500 hover:bg-cyan-600 border-cyan-600';
      default:
        return 'bg-gray-500 hover:bg-gray-600 border-gray-600';
    }
  };

  const getTableStatusIcon = (table: Table) => {
    const { status, substatus } = table;
    
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return <Coffee className="h-4 w-4" />;
          case 'eating':
            return <Utensils className="h-4 w-4" />;
          case 'waiting-for-check':
            return <Timer className="h-4 w-4" />;
          case 'paying':
            return <CreditCard className="h-4 w-4" />;
          default:
            return <Users className="h-4 w-4" />;
        }
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      case 'out-of-order':
        return <WifiOff className="h-4 w-4" />;
      case 'being-seated':
        return <UserCheck className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getSeatedDuration = (seatedTime?: Date) => {
    if (!seatedTime) return null;
    const now = new Date();
    const seated = new Date(seatedTime);
    const diffMinutes = Math.floor((now.getTime() - seated.getTime()) / (1000 * 60));
    return diffMinutes;
  };

  const handleTableClick = (table: Table) => {
    setSelectedTable(table);

    if (isEditMode) {
      setShowTableDetails(true);
      return;
    }

    // Handle different table statuses
    switch (table.status) {
      case 'available':
        setShowTableDetails(true);
        break;
      case 'occupied':
        // Show options for occupied table
        setShowTableDetails(true);
        break;
      case 'reserved':
        setShowTableDetails(true);
        break;
      default:
        setShowTableDetails(true);
    }
  };

  const handleStartOrder = (table: Table) => {
    // Set table as current order context
    dispatch({
      type: 'SET_CURRENT_TABLE',
      payload: table
    });

    // Update table status to ordering
    updateTableStatus(table.id, 'occupied', 'ordering');

    // Show POS integration
    setShowPOSIntegration(true);
    setShowTableDetails(false);
  };

  const handlePaymentRequest = (orderData: any) => {
    setCurrentOrder(orderData);
    setShowPayment(true);
    setShowPOSIntegration(false);
  };

  const handlePaymentComplete = (paymentResult: any) => {
    // Update table status
    if (selectedTable) {
      updateTableStatus(selectedTable.id, 'available');
    }

    // Generate receipt
    setReceiptData({
      orderId: paymentResult.id,
      transactionId: paymentResult.transactionId,
      timestamp: paymentResult.timestamp,
      tableNumber: selectedTable?.number,
      serverName: selectedTable?.serverName,
      guestCount: selectedTable?.guestCount,
      items: paymentResult.items,
      subtotal: paymentResult.subtotal,
      tax: paymentResult.tax,
      tip: paymentResult.tip,
      total: paymentResult.total,
      paymentMethod: paymentResult.paymentMethod,
      authCode: paymentResult.authCode,
      customerInfo: paymentResult.customerInfo
    });

    setShowPayment(false);
    setShowReceipt(true);

    // Refresh floor layout
    fetchFloorLayout();
  };

  const handleReceiptComplete = () => {
    setShowReceipt(false);
    setReceiptData(null);
    setCurrentOrder(null);
    setSelectedTable(null);
  };

  const TableComponent: React.FC<{ table: Table }> = ({ table }) => {
    const isSelected = selectedTable?.id === table.id;
    const seatedDuration = getSeatedDuration(table.seatedTime);
    const section = floorData?.sections.find(s => s.id === table.section);
    
    // Filter by section if selected
    if (selectedSection !== 'all' && table.section !== selectedSection) {
      return null;
    }
    
    return (
      <div
        key={table.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          table.shape === 'circle' ? 'rounded-full' : table.shape === 'oval' ? 'rounded-full' : 'rounded-lg'
        } ${getTableStatusColor(table)} ${
          isSelected ? 'ring-4 ring-purple-400 ring-opacity-75 scale-105' : ''
        } flex flex-col items-center justify-center text-white font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border-2`}
        style={{
          left: `${table.x}px`,
          top: `${table.y}px`,
          width: `${table.width}px`,
          height: `${table.height}px`,
        }}
        onClick={() => handleTableClick(table)}
      >
        {/* Table Number and Icon */}
        <div className="flex items-center space-x-1 mb-1">
          {getTableStatusIcon(table)}
          <span className="text-sm font-bold">{table.number}</span>
        </div>
        
        {/* Seats and Guest Count */}
        <div className="text-xs opacity-90 text-center">
          <div>{table.guestCount || 0}/{table.seats} seats</div>
          {table.serverName && (
            <div className="truncate max-w-16">{table.serverName.split(' ')[0]}</div>
          )}
        </div>

        {/* Order Information */}
        {table.orderTotal && table.orderTotal > 0 && (
          <div className="text-xs opacity-90 text-center">
            <div>${table.orderTotal.toFixed(2)}</div>
            <div>{table.orderItems} items</div>
          </div>
        )}

        {/* Seated Duration */}
        {seatedDuration && seatedDuration > 0 && (
          <div className="text-xs opacity-75 text-center">
            {seatedDuration}min
          </div>
        )}

        {/* Special Indicators */}
        <div className="absolute -top-1 -right-1 flex flex-col space-y-1">
          {table.isVip && (
            <div className="w-3 h-3 bg-gold-400 rounded-full border border-white"></div>
          )}
          {table.specialRequests && table.specialRequests.length > 0 && (
            <div className="w-3 h-3 bg-blue-400 rounded-full border border-white"></div>
          )}
          {table.allergies && table.allergies.length > 0 && (
            <div className="w-3 h-3 bg-red-400 rounded-full border border-white"></div>
          )}
        </div>

        {/* Section Color Indicator */}
        {section && (
          <div 
            className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-4 h-1 rounded-full opacity-75"
            style={{ backgroundColor: section.color }}
          ></div>
        )}
      </div>
    );
  };

  const StatusLegend: React.FC = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 p-4 bg-gray-800 rounded-lg">
      {[
        { status: 'available', label: 'Available', color: 'bg-green-500', icon: <CheckCircle className="h-3 w-3" /> },
        { status: 'ordering', label: 'Ordering', color: 'bg-blue-500', icon: <Coffee className="h-3 w-3" /> },
        { status: 'eating', label: 'Eating', color: 'bg-red-500', icon: <Utensils className="h-3 w-3" /> },
        { status: 'check', label: 'Check', color: 'bg-purple-500', icon: <Timer className="h-3 w-3" /> },
        { status: 'reserved', label: 'Reserved', color: 'bg-yellow-500', icon: <Clock className="h-3 w-3" /> },
        { status: 'cleaning', label: 'Cleaning', color: 'bg-orange-500', icon: <AlertCircle className="h-3 w-3" /> }
      ].map((item) => (
        <div key={item.status} className="flex items-center space-x-2">
          <div className={`w-4 h-4 ${item.color} rounded flex items-center justify-center text-white`}>
            {item.icon}
          </div>
          <span className="text-gray-300 text-xs">{item.label}</span>
        </div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-300">Loading floor layout...</p>
        </div>
      </div>
    );
  }

  if (!floorData) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <p className="text-gray-300">Failed to load floor layout</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header with controls */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center p-4 bg-gray-800 rounded-lg mb-4 gap-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            <span>Floor Layout</span>
            <span className="text-sm text-gray-400 ml-2">({Math.round(zoomLevel * 100)}%)</span>
          </h2>
          
          {/* Section Filter */}
          <select
            value={selectedSection}
            onChange={(e) => setSelectedSection(e.target.value)}
            className="bg-gray-700 text-white px-3 py-1 rounded-md text-sm"
          >
            <option value="all">All Sections</option>
            {floorData.sections.map((section) => (
              <option key={section.id} value={section.id}>{section.name}</option>
            ))}
          </select>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          {/* Quick Stats */}
          <div className="flex items-center space-x-4 text-sm text-gray-300">
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>{floorData.tables.filter(t => t.status === 'occupied').length}/{floorData.tables.length}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{waitlist.length} waiting</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{reservations.length} reserved</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowReservations(true)}
              className="bg-yellow-600 text-white px-3 py-1 rounded-md text-sm hover:bg-yellow-700 flex items-center space-x-1"
            >
              <Calendar className="h-4 w-4" />
              <span>Reservations</span>
            </button>
            <button
              onClick={() => setShowWaitlist(true)}
              className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 flex items-center space-x-1"
            >
              <Clock className="h-4 w-4" />
              <span>Waitlist</span>
            </button>
            <button
              onClick={() => setIsEditMode(!isEditMode)}
              className={`px-3 py-1 rounded-md text-sm flex items-center space-x-1 ${
                isEditMode ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <Edit3 className="h-4 w-4" />
              <span>{isEditMode ? 'Exit Edit' : 'Edit'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Status Legend */}
      <StatusLegend />

      {/* Floor Plan */}
      <div className="flex-grow bg-gray-800 rounded-lg p-4 relative overflow-auto mt-4">
        <div 
          className="relative w-full h-full min-h-[500px] transition-transform duration-200"
          style={{ 
            minWidth: `${floorData.layout_settings.width}px`,
            minHeight: `${floorData.layout_settings.height}px`,
            transform: `scale(${zoomLevel})`,
            transformOrigin: '0 0'
          }}
        >
          {/* Background grid */}
          <div 
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: 'radial-gradient(circle, #ffffff 1px, transparent 1px)',
              backgroundSize: `${floorData.layout_settings.gridSize}px ${floorData.layout_settings.gridSize}px`
            }}
          />
          
          {/* Section Backgrounds */}
          {floorData.sections.map((section) => {
            const sectionTables = floorData.tables.filter(t => t.section === section.id);
            if (sectionTables.length === 0) return null;
            
            const minX = Math.min(...sectionTables.map(t => t.x)) - 20;
            const minY = Math.min(...sectionTables.map(t => t.y)) - 20;
            const maxX = Math.max(...sectionTables.map(t => t.x + t.width)) + 20;
            const maxY = Math.max(...sectionTables.map(t => t.y + t.height)) + 20;
            
            return (
              <div
                key={section.id}
                className="absolute rounded-lg opacity-10 pointer-events-none"
                style={{
                  left: `${minX}px`,
                  top: `${minY}px`,
                  width: `${maxX - minX}px`,
                  height: `${maxY - minY}px`,
                  backgroundColor: section.color
                }}
              />
            );
          })}
          
          {/* Tables */}
          <div className="relative">
            {floorData.tables.map((table) => (
              <TableComponent key={table.id} table={table} />
            ))}
          </div>

          {/* Selected table info */}
          {selectedTable && (
            <div className="absolute top-4 right-4 bg-purple-600 text-white p-4 rounded-lg shadow-lg max-w-xs">
              <h4 className="font-semibold mb-2">Table {selectedTable.number}</h4>
              <div className="space-y-1 text-sm">
                <p>{selectedTable.guestCount || 0}/{selectedTable.seats} guests</p>
                <p className="capitalize">{selectedTable.status}</p>
                {selectedTable.substatus && (
                  <p className="capitalize text-gray-200">{selectedTable.substatus}</p>
                )}
                {selectedTable.serverName && (
                  <p>Server: {selectedTable.serverName}</p>
                )}
                {selectedTable.orderTotal && selectedTable.orderTotal > 0 && (
                  <p>Order: ${selectedTable.orderTotal.toFixed(2)}</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Zoom Controls */}
      <div className="flex justify-center mt-4">
        <div className="flex items-center bg-gray-800 rounded-lg">
          <button
            onClick={() => setZoomLevel(prev => Math.max(prev - 0.2, 0.5))}
            className="px-3 py-2 text-gray-300 hover:text-white transition-colors"
          >
            -
          </button>
          <button
            onClick={() => setZoomLevel(1)}
            className="px-3 py-2 text-gray-300 hover:text-white transition-colors border-l border-r border-gray-600"
          >
            100%
          </button>
          <button
            onClick={() => setZoomLevel(prev => Math.min(prev + 0.2, 2))}
            className="px-3 py-2 text-gray-300 hover:text-white transition-colors"
          >
            +
          </button>
        </div>
      </div>

      {/* Enhanced Modals */}
      {showTableDetails && selectedTable && (
        <TableDetailsModal
          table={selectedTable}
          isOpen={showTableDetails}
          onClose={() => setShowTableDetails(false)}
          onUpdateTable={(tableId, updates) => {
            // Update table in local state
            setFloorData(prev => prev ? {
              ...prev,
              tables: prev.tables.map(t => t.id === tableId ? { ...t, ...updates } : t)
            } : null);
            // Update in backend
            updateTableStatus(tableId, updates.status as Table['status'], updates.substatus as Table['substatus']);
          }}
          onStartOrder={handleStartOrder}
        />
      )}

      {showReservations && (
        <ReservationManager
          isOpen={showReservations}
          onClose={() => setShowReservations(false)}
          tables={floorData?.tables || []}
        />
      )}

      {showPayment && currentOrder && (
        <EnhancedPaymentModal
          isOpen={showPayment}
          onClose={() => setShowPayment(false)}
          orderData={currentOrder}
          onPaymentComplete={handlePaymentComplete}
        />
      )}

      {showReceipt && receiptData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Receipt & Order Complete</h2>
                <button
                  onClick={handleReceiptComplete}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <EnhancedReceiptSystem
                receiptData={receiptData}
                businessInfo={{
                  name: state.currentTenant?.name || 'Restaurant POS',
                  address: ['123 Main Street', 'City, State 12345'],
                  phone: '(*************',
                  email: '<EMAIL>',
                  website: 'www.restaurant.com',
                  taxId: 'TAX123456789'
                }}
                onPrint={() => console.log('Receipt printed')}
                onEmail={(email) => console.log('Receipt emailed to:', email)}
                onSMS={(phone) => console.log('Receipt sent to:', phone)}
                onDownload={() => console.log('Receipt downloaded')}
              />

              <div className="mt-6 flex justify-end">
                <button
                  onClick={handleReceiptComplete}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
                >
                  Continue
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedFloorLayout;
