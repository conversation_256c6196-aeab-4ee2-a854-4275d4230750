import React, { useState } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface SimpleRestructuredPOSProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const SimpleRestructuredPOS: React.FC<SimpleRestructuredPOSProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const { state } = useEnhancedAppContext();
  const [activeTab, setActiveTab] = useState('pos');

  return (
    <div className={`h-screen flex transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`}>
      {/* Simple Sidebar */}
      <div className={`w-64 border-r flex flex-col transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h1 className="text-xl font-bold">🚀 Restructured POS</h1>
          <p className="text-sm text-gray-500">New Interface Active</p>
        </div>
        
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {['pos', 'orders', 'inventory', 'analytics', 'settings'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                  activeTab === tab
                    ? 'bg-blue-500 text-white'
                    : isDarkMode 
                      ? 'text-gray-300 hover:bg-gray-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </nav>
        
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm">User: {state.currentEmployee?.name || 'Employee'}</p>
          <p className="text-xs text-gray-500">Role: {state.currentEmployee?.role || 'Staff'}</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className={`px-6 py-4 border-b transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              🎉 Restructured Interface - {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </h2>
            <div className="flex items-center space-x-4">
              <div className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                ✅ Restructured Mode Active
              </div>
              <button
                onClick={onThemeToggle}
                className={`px-3 py-1 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                }`}
              >
                {isDarkMode ? '☀️ Light' : '🌙 Dark'}
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6">
          <div className={`h-full rounded-lg border-2 border-dashed p-8 text-center ${
            isDarkMode ? 'border-gray-600' : 'border-gray-300'
          }`}>
            <div className="max-w-md mx-auto">
              <div className="text-6xl mb-4">🚀</div>
              <h3 className="text-2xl font-bold mb-4">
                Restructured POS Interface
              </h3>
              <p className="text-lg mb-6">
                The new interface is now active! This is the restructured version with:
              </p>
              <div className="text-left space-y-2 mb-6">
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span>Optimized Layout</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span>Enhanced Components</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span>Better User Experience</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span>Modern Design System</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">✅</span>
                  <span>Improved Performance</span>
                </div>
              </div>
              
              <div className={`p-4 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
              }`}>
                <h4 className="font-semibold mb-2">Current Tab: {activeTab}</h4>
                <p className="text-sm">
                  Click the sidebar items to see the restructured navigation in action.
                  The full components will be loaded progressively.
                </p>
              </div>
              
              <div className="mt-6 space-y-2">
                <p className="text-sm font-medium">Debug Information:</p>
                <div className={`text-xs p-2 rounded ${
                  isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
                }`}>
                  <p>Theme: {isDarkMode ? 'Dark' : 'Light'}</p>
                  <p>User: {state.currentEmployee?.name || 'Not logged in'}</p>
                  <p>Tenant: {state.currentTenant?.name || 'No tenant'}</p>
                  <p>URL: {window.location.href}</p>
                  <p>localStorage Flag: {localStorage.getItem('useRestructuredPOS') || 'not set'}</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default SimpleRestructuredPOS;
