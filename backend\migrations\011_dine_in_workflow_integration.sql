-- Migration: Dine-In Workflow Integration
-- Description: Enhanced schema for unified POS + Floor Layout workflow
-- Version: 011
-- Date: 2024-12-16

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enhanced table_assignments with session management
ALTER TABLE table_assignments 
ADD COLUMN IF NOT EXISTS session_id UUID,
ADD COLUMN IF NOT EXISTS assignment_type VARCHAR(20) DEFAULT 'dine_in' CHECK (assignment_type IN ('dine_in', 'reservation', 'maintenance')),
ADD COLUMN IF NOT EXISTS employee_confirmation_pin VARCHAR(10),
ADD COLUMN IF NOT EXISTS concurrent_lock BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS lock_expiry TIMESTAMP,
ADD COLUMN IF NOT EXISTS terminal_id VARCHAR(50),
ADD COLUMN IF NOT EXISTS ip_address INET;

-- Create employee_sessions table for tracking employee sessions
CREATE TABLE IF NOT EXISTS employee_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    session_id UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    terminal_id VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    location_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced orders table with workflow tracking
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS workflow_step VARCHAR(30) DEFAULT 'created' CHECK (workflow_step IN ('created', 'table_assigned', 'ordering', 'payment', 'completed', 'cancelled')),
ADD COLUMN IF NOT EXISTS table_assignment_id UUID REFERENCES table_assignments(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS employee_session_id UUID REFERENCES employee_sessions(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS order_source VARCHAR(20) DEFAULT 'pos' CHECK (order_source IN ('pos', 'online', 'mobile', 'kiosk')),
ADD COLUMN IF NOT EXISTS table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS guest_count INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS server_notes TEXT,
ADD COLUMN IF NOT EXISTS special_requests TEXT[];

-- Create table_locks table for concurrent access prevention
CREATE TABLE IF NOT EXISTS table_locks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_id UUID REFERENCES tables(id) ON DELETE CASCADE,
    employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
    session_id UUID NOT NULL,
    lock_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_time TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    lock_type VARCHAR(20) DEFAULT 'assignment' CHECK (lock_type IN ('assignment', 'ordering', 'payment')),
    terminal_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create workflow_audit_log table for tracking workflow steps
CREATE TABLE IF NOT EXISTS workflow_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
    employee_id UUID REFERENCES employees(id) ON DELETE SET NULL,
    session_id UUID,
    workflow_step VARCHAR(30) NOT NULL,
    previous_step VARCHAR(30),
    step_data JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    terminal_id VARCHAR(50),
    ip_address INET,
    duration_seconds INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT
);

-- Create real_time_events table for WebSocket event tracking
CREATE TABLE IF NOT EXISTS real_time_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB NOT NULL,
    table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
    employee_id UUID REFERENCES employees(id) ON DELETE SET NULL,
    session_id UUID,
    broadcast_to TEXT[], -- Array of session IDs to broadcast to
    processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Enhanced tables table with additional workflow fields
ALTER TABLE tables 
ADD COLUMN IF NOT EXISTS last_assigned_employee UUID REFERENCES employees(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS last_assignment_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS average_turn_time INTEGER, -- in minutes
ADD COLUMN IF NOT EXISTS total_orders_served INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_cleaned TIMESTAMP,
ADD COLUMN IF NOT EXISTS maintenance_notes TEXT,
ADD COLUMN IF NOT EXISTS is_priority BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS workflow_status VARCHAR(30) DEFAULT 'available' CHECK (workflow_status IN ('available', 'locked', 'assigned', 'ordering', 'occupied', 'payment', 'cleaning', 'maintenance'));

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_employee_sessions_employee_id ON employee_sessions(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_sessions_session_id ON employee_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_employee_sessions_status ON employee_sessions(status);
CREATE INDEX IF NOT EXISTS idx_employee_sessions_last_activity ON employee_sessions(last_activity);

CREATE INDEX IF NOT EXISTS idx_table_assignments_session_id ON table_assignments(session_id);
CREATE INDEX IF NOT EXISTS idx_table_assignments_employee_id ON table_assignments(employee_id);
CREATE INDEX IF NOT EXISTS idx_table_assignments_table_id ON table_assignments(table_id);
CREATE INDEX IF NOT EXISTS idx_table_assignments_status ON table_assignments(status);
CREATE INDEX IF NOT EXISTS idx_table_assignments_assignment_type ON table_assignments(assignment_type);

CREATE INDEX IF NOT EXISTS idx_table_locks_table_id ON table_locks(table_id);
CREATE INDEX IF NOT EXISTS idx_table_locks_employee_id ON table_locks(employee_id);
CREATE INDEX IF NOT EXISTS idx_table_locks_session_id ON table_locks(session_id);
CREATE INDEX IF NOT EXISTS idx_table_locks_expiry_time ON table_locks(expiry_time);
CREATE INDEX IF NOT EXISTS idx_table_locks_is_active ON table_locks(is_active);

CREATE INDEX IF NOT EXISTS idx_workflow_audit_log_order_id ON workflow_audit_log(order_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_log_table_id ON workflow_audit_log(table_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_log_employee_id ON workflow_audit_log(employee_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_log_workflow_step ON workflow_audit_log(workflow_step);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_log_timestamp ON workflow_audit_log(timestamp);

CREATE INDEX IF NOT EXISTS idx_real_time_events_event_type ON real_time_events(event_type);
CREATE INDEX IF NOT EXISTS idx_real_time_events_table_id ON real_time_events(table_id);
CREATE INDEX IF NOT EXISTS idx_real_time_events_processed ON real_time_events(processed);
CREATE INDEX IF NOT EXISTS idx_real_time_events_created_at ON real_time_events(created_at);

CREATE INDEX IF NOT EXISTS idx_orders_workflow_step ON orders(workflow_step);
CREATE INDEX IF NOT EXISTS idx_orders_table_assignment_id ON orders(table_assignment_id);
CREATE INDEX IF NOT EXISTS idx_orders_employee_session_id ON orders(employee_session_id);
CREATE INDEX IF NOT EXISTS idx_orders_table_id ON orders(table_id);

CREATE INDEX IF NOT EXISTS idx_tables_workflow_status ON tables(workflow_status);
CREATE INDEX IF NOT EXISTS idx_tables_last_assigned_employee ON tables(last_assigned_employee);
CREATE INDEX IF NOT EXISTS idx_tables_last_assignment_time ON tables(last_assignment_time);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
CREATE TRIGGER update_employee_sessions_updated_at 
    BEFORE UPDATE ON employee_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_table_assignments_updated_at 
    BEFORE UPDATE ON table_assignments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for automatic session activity tracking
CREATE OR REPLACE FUNCTION update_session_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_activity when any session-related action occurs
    UPDATE employee_sessions 
    SET last_activity = CURRENT_TIMESTAMP 
    WHERE session_id = NEW.session_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_session_activity_on_assignment 
    AFTER INSERT OR UPDATE ON table_assignments 
    FOR EACH ROW 
    WHEN (NEW.session_id IS NOT NULL)
    EXECUTE FUNCTION update_session_activity();

-- Create trigger for automatic lock cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_locks()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark expired locks as inactive
    UPDATE table_locks 
    SET is_active = false 
    WHERE expiry_time < CURRENT_TIMESTAMP AND is_active = true;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create a scheduled job to clean up expired locks (requires pg_cron extension)
-- This is optional and depends on pg_cron being available
-- SELECT cron.schedule('cleanup-expired-locks', '*/1 * * * *', 'SELECT cleanup_expired_locks();');

-- Create views for common queries
CREATE OR REPLACE VIEW active_table_assignments AS
SELECT 
    ta.*,
    t.number as table_number,
    t.seats as table_seats,
    t.section as table_section,
    e.name as employee_name,
    e.role as employee_role,
    es.terminal_id,
    es.last_activity
FROM table_assignments ta
JOIN tables t ON ta.table_id = t.id
JOIN employees e ON ta.employee_id = e.id
LEFT JOIN employee_sessions es ON ta.session_id = es.session_id
WHERE ta.status = 'active';

CREATE OR REPLACE VIEW table_workflow_status AS
SELECT 
    t.*,
    ta.employee_id as assigned_employee_id,
    ta.session_id as assignment_session_id,
    ta.seated_time,
    ta.guest_count,
    e.name as assigned_employee_name,
    o.id as current_order_id,
    o.workflow_step as order_workflow_step,
    o.total_amount as order_total,
    tl.is_active as is_locked,
    tl.expiry_time as lock_expiry
FROM tables t
LEFT JOIN table_assignments ta ON t.id = ta.table_id AND ta.status = 'active'
LEFT JOIN employees e ON ta.employee_id = e.id
LEFT JOIN orders o ON t.id = o.table_id AND o.order_status IN ('pending', 'confirmed', 'preparing')
LEFT JOIN table_locks tl ON t.id = tl.table_id AND tl.is_active = true AND tl.expiry_time > CURRENT_TIMESTAMP;

-- Insert initial data for testing (optional)
-- This can be removed in production
INSERT INTO employee_sessions (employee_id, session_id, status, terminal_id) 
SELECT id, uuid_generate_v4(), 'active', 'terminal_1' 
FROM employees 
WHERE role IN ('employee', 'manager', 'super_admin') 
ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE employee_sessions IS 'Tracks active employee sessions across multiple terminals';
COMMENT ON TABLE table_locks IS 'Prevents concurrent access to tables during assignment process';
COMMENT ON TABLE workflow_audit_log IS 'Audit trail for all workflow step transitions';
COMMENT ON TABLE real_time_events IS 'Queue for WebSocket events to be broadcast to connected clients';
COMMENT ON VIEW active_table_assignments IS 'Current active table assignments with employee and session details';
COMMENT ON VIEW table_workflow_status IS 'Comprehensive view of table status including assignments, orders, and locks';

-- Migration completion log
INSERT INTO migration_log (version, description, applied_at) 
VALUES ('011', 'Dine-In Workflow Integration - Enhanced schema for unified POS + Floor Layout workflow', CURRENT_TIMESTAMP)
ON CONFLICT (version) DO UPDATE SET 
    description = EXCLUDED.description,
    applied_at = EXCLUDED.applied_at;
