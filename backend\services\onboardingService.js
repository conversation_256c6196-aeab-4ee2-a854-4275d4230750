// 👥 BARPOS Customer Onboarding Service
// Phase 7B: Customer Systems & Billing
// Automated customer onboarding and setup workflow

const { Pool } = require('pg');
const nodemailer = require('nodemailer');
const bcrypt = require('bcrypt');

class OnboardingService {
    constructor() {
        this.pool = new Pool({
            user: process.env.POSTGRES_USER || 'BARPOS',
            host: process.env.POSTGRES_HOST || 'localhost',
            database: process.env.POSTGRES_DB || 'BARPOS',
            password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
            port: process.env.POSTGRES_PORT || 5432,
        });

        // Email transporter
        this.emailTransporter = nodemailer.createTransporter({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false,
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });

        this.onboardingSteps = [
            { id: 1, name: 'account_setup', title: 'Account Setup', description: 'Create tenant account and admin user' },
            { id: 2, name: 'restaurant_info', title: 'Restaurant Information', description: 'Configure restaurant details and settings' },
            { id: 3, name: 'menu_setup', title: 'Menu Setup', description: 'Add menu categories and items' },
            { id: 4, name: 'staff_setup', title: 'Staff Setup', description: 'Add employees and configure roles' },
            { id: 5, name: 'payment_setup', title: 'Payment Setup', description: 'Configure payment methods and billing' },
            { id: 6, name: 'testing', title: 'Testing & Training', description: 'Test system functionality and train staff' },
            { id: 7, name: 'go_live', title: 'Go Live', description: 'Launch system for production use' }
        ];
    }

    // Start onboarding process for new customer
    async startOnboarding(customerData) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');
            console.log('🚀 Starting onboarding for:', customerData.restaurantName);

            // 1. Create tenant
            const tenant = await this.createTenant(client, customerData);
            
            // 2. Create admin user
            const adminUser = await this.createAdminUser(client, tenant.id, customerData);
            
            // 3. Create onboarding record
            const onboarding = await this.createOnboardingRecord(client, tenant.id);
            
            // 4. Setup demo data
            await this.setupDemoData(client, tenant.id);
            
            // 5. Send welcome email
            await this.sendWelcomeEmail(customerData, adminUser);
            
            await client.query('COMMIT');
            
            console.log('✅ Onboarding started successfully for tenant:', tenant.id);
            return {
                success: true,
                tenant: tenant,
                adminUser: adminUser,
                onboarding: onboarding,
                loginUrl: `${process.env.FRONTEND_URL}/login`,
                credentials: {
                    pin: adminUser.pin,
                    restaurantCode: tenant.restaurant_code
                }
            };

        } catch (error) {
            await client.query('ROLLBACK');
            console.error('❌ Error starting onboarding:', error);
            throw new Error(`Failed to start onboarding: ${error.message}`);
        } finally {
            client.release();
        }
    }

    // Create tenant account
    async createTenant(client, customerData) {
        const restaurantCode = this.generateRestaurantCode(customerData.restaurantName);
        
        const tenantResult = await client.query(`
            INSERT INTO tenants (
                name, email, phone, address, city, state, postal_code, country,
                restaurant_code, timezone, currency, is_active, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
            RETURNING *
        `, [
            customerData.restaurantName,
            customerData.email,
            customerData.phone || '',
            customerData.address || '',
            customerData.city || '',
            customerData.state || '',
            customerData.postalCode || '',
            customerData.country || 'US',
            restaurantCode,
            customerData.timezone || 'America/New_York',
            customerData.currency || 'USD',
            true
        ]);

        return tenantResult.rows[0];
    }

    // Create admin user for tenant
    async createAdminUser(client, tenantId, customerData) {
        const pin = this.generateSecurePin();
        const hashedPin = await bcrypt.hash(pin, 10);

        const userResult = await client.query(`
            INSERT INTO employees (
                tenant_id, name, email, role, pin, is_active, 
                permissions, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING *
        `, [
            tenantId,
            customerData.adminName || customerData.contactName,
            customerData.email,
            'tenant_admin',
            hashedPin,
            true,
            JSON.stringify({
                pos: true,
                inventory: true,
                reports: true,
                settings: true,
                staff: true,
                billing: true
            })
        ]);

        const user = userResult.rows[0];
        user.pin = pin; // Return plain PIN for initial setup
        return user;
    }

    // Create onboarding tracking record
    async createOnboardingRecord(client, tenantId) {
        const onboardingResult = await client.query(`
            INSERT INTO customer_onboarding (
                tenant_id, current_step, total_steps, status, 
                started_at, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, NOW(), NOW(), NOW())
            RETURNING *
        `, [
            tenantId,
            1,
            this.onboardingSteps.length,
            'in_progress'
        ]);

        // Create step records
        for (const step of this.onboardingSteps) {
            await client.query(`
                INSERT INTO onboarding_steps (
                    tenant_id, step_id, step_name, title, description, 
                    status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            `, [
                tenantId,
                step.id,
                step.name,
                step.title,
                step.description,
                step.id === 1 ? 'completed' : 'pending'
            ]);
        }

        return onboardingResult.rows[0];
    }

    // Setup demo data for new tenant
    async setupDemoData(client, tenantId) {
        console.log('📊 Setting up demo data for tenant:', tenantId);

        // Create demo location
        const locationResult = await client.query(`
            INSERT INTO locations (
                tenant_id, name, address, phone, is_active, 
                created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING *
        `, [
            tenantId,
            'Main Location',
            '123 Demo Street, Demo City, DC 12345',
            '(*************',
            true
        ]);

        const locationId = locationResult.rows[0].id;

        // Create demo menu categories
        const categories = [
            { name: 'Appetizers', description: 'Start your meal right' },
            { name: 'Main Courses', description: 'Hearty main dishes' },
            { name: 'Desserts', description: 'Sweet endings' },
            { name: 'Beverages', description: 'Drinks and refreshments' }
        ];

        for (const category of categories) {
            const categoryResult = await client.query(`
                INSERT INTO menu_categories (
                    tenant_id, name, description, is_active, 
                    created_at, updated_at
                ) VALUES ($1, $2, $3, $4, NOW(), NOW())
                RETURNING *
            `, [tenantId, category.name, category.description, true]);

            // Add demo items for each category
            const items = this.getDemoItemsForCategory(category.name);
            for (const item of items) {
                await client.query(`
                    INSERT INTO menu_items (
                        tenant_id, category_id, name, description, price, 
                        is_active, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
                `, [
                    tenantId,
                    categoryResult.rows[0].id,
                    item.name,
                    item.description,
                    item.price,
                    true
                ]);
            }
        }

        // Create demo tables for floor layout
        const tables = [
            { number: 1, seats: 2, section: 'Main Dining' },
            { number: 2, seats: 4, section: 'Main Dining' },
            { number: 3, seats: 4, section: 'Main Dining' },
            { number: 4, seats: 6, section: 'Main Dining' },
            { number: 5, seats: 2, section: 'Patio' },
            { number: 6, seats: 4, section: 'Patio' }
        ];

        for (const table of tables) {
            await client.query(`
                INSERT INTO tables (
                    tenant_id, location_id, table_number, seats, section,
                    status, is_active, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            `, [
                tenantId,
                locationId,
                table.number,
                table.seats,
                table.section,
                'available',
                true
            ]);
        }

        console.log('✅ Demo data setup completed');
    }

    // Get demo items for category
    getDemoItemsForCategory(categoryName) {
        const items = {
            'Appetizers': [
                { name: 'Caesar Salad', description: 'Fresh romaine lettuce with caesar dressing', price: 8.99 },
                { name: 'Chicken Wings', description: 'Spicy buffalo wings with ranch dip', price: 12.99 },
                { name: 'Mozzarella Sticks', description: 'Crispy breaded mozzarella with marinara', price: 9.99 }
            ],
            'Main Courses': [
                { name: 'Grilled Chicken', description: 'Herb-seasoned grilled chicken breast', price: 18.99 },
                { name: 'Beef Burger', description: 'Juicy beef patty with lettuce, tomato, onion', price: 14.99 },
                { name: 'Fish & Chips', description: 'Beer-battered cod with crispy fries', price: 16.99 },
                { name: 'Pasta Alfredo', description: 'Creamy alfredo sauce over fettuccine', price: 15.99 }
            ],
            'Desserts': [
                { name: 'Chocolate Cake', description: 'Rich chocolate layer cake', price: 6.99 },
                { name: 'Ice Cream Sundae', description: 'Vanilla ice cream with chocolate sauce', price: 5.99 },
                { name: 'Apple Pie', description: 'Classic apple pie with cinnamon', price: 5.99 }
            ],
            'Beverages': [
                { name: 'Coffee', description: 'Fresh brewed coffee', price: 2.99 },
                { name: 'Soft Drinks', description: 'Coca-Cola, Pepsi, Sprite, etc.', price: 2.49 },
                { name: 'Fresh Juice', description: 'Orange, apple, or cranberry juice', price: 3.49 },
                { name: 'Beer', description: 'Domestic and imported beers', price: 4.99 }
            ]
        };

        return items[categoryName] || [];
    }

    // Update onboarding step
    async updateOnboardingStep(tenantId, stepName, status, data = {}) {
        try {
            console.log('📝 Updating onboarding step:', stepName, 'for tenant:', tenantId);

            // Update step status
            await this.pool.query(`
                UPDATE onboarding_steps 
                SET status = $1, completed_at = $2, data = $3, updated_at = NOW()
                WHERE tenant_id = $4 AND step_name = $5
            `, [
                status,
                status === 'completed' ? new Date() : null,
                JSON.stringify(data),
                tenantId,
                stepName
            ]);

            // Update overall progress
            const progressResult = await this.pool.query(`
                SELECT 
                    COUNT(*) as total_steps,
                    COUNT(*) FILTER (WHERE status = 'completed') as completed_steps
                FROM onboarding_steps 
                WHERE tenant_id = $1
            `, [tenantId]);

            const { total_steps, completed_steps } = progressResult.rows[0];
            const progress = Math.round((completed_steps / total_steps) * 100);

            await this.pool.query(`
                UPDATE customer_onboarding 
                SET 
                    current_step = $1,
                    progress_percentage = $2,
                    status = $3,
                    completed_at = $4,
                    updated_at = NOW()
                WHERE tenant_id = $5
            `, [
                parseInt(completed_steps) + 1,
                progress,
                progress === 100 ? 'completed' : 'in_progress',
                progress === 100 ? new Date() : null,
                tenantId
            ]);

            // Send progress email if significant milestone
            if (progress === 50 || progress === 100) {
                await this.sendProgressEmail(tenantId, progress);
            }

            return { success: true, progress: progress };

        } catch (error) {
            console.error('❌ Error updating onboarding step:', error);
            throw new Error(`Failed to update onboarding step: ${error.message}`);
        }
    }

    // Get onboarding status
    async getOnboardingStatus(tenantId) {
        try {
            const onboardingResult = await this.pool.query(`
                SELECT co.*, t.name as tenant_name, t.email as tenant_email
                FROM customer_onboarding co
                JOIN tenants t ON co.tenant_id = t.id
                WHERE co.tenant_id = $1
            `, [tenantId]);

            if (onboardingResult.rows.length === 0) {
                return { success: false, message: 'Onboarding record not found' };
            }

            const stepsResult = await this.pool.query(`
                SELECT * FROM onboarding_steps 
                WHERE tenant_id = $1 
                ORDER BY step_id
            `, [tenantId]);

            return {
                success: true,
                onboarding: onboardingResult.rows[0],
                steps: stepsResult.rows
            };

        } catch (error) {
            console.error('❌ Error getting onboarding status:', error);
            throw new Error(`Failed to get onboarding status: ${error.message}`);
        }
    }

    // Send welcome email
    async sendWelcomeEmail(customerData, adminUser) {
        try {
            const emailContent = `
                <h2>Welcome to BARPOS! 🎉</h2>
                <p>Dear ${customerData.adminName || customerData.contactName},</p>
                
                <p>Congratulations! Your BARPOS account has been successfully created for <strong>${customerData.restaurantName}</strong>.</p>
                
                <h3>Your Login Credentials:</h3>
                <ul>
                    <li><strong>PIN:</strong> ${adminUser.pin}</li>
                    <li><strong>Restaurant Code:</strong> ${customerData.restaurantCode}</li>
                    <li><strong>Login URL:</strong> <a href="${process.env.FRONTEND_URL}/login">${process.env.FRONTEND_URL}/login</a></li>
                </ul>
                
                <h3>Next Steps:</h3>
                <ol>
                    <li>Log in to your account using the credentials above</li>
                    <li>Complete the onboarding wizard</li>
                    <li>Customize your menu and settings</li>
                    <li>Add your staff members</li>
                    <li>Start taking orders!</li>
                </ol>
                
                <p>Your 30-day free trial has started. No payment is required during the trial period.</p>
                
                <p>If you need any assistance, please don't hesitate to contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                
                <p>Welcome aboard!</p>
                <p>The BARPOS Team</p>
            `;

            await this.emailTransporter.sendMail({
                from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
                to: customerData.email,
                subject: 'Welcome to BARPOS - Your Account is Ready!',
                html: emailContent
            });

            console.log('✅ Welcome email sent to:', customerData.email);

        } catch (error) {
            console.error('❌ Error sending welcome email:', error);
            // Don't throw error - email failure shouldn't stop onboarding
        }
    }

    // Send progress email
    async sendProgressEmail(tenantId, progress) {
        try {
            const tenantResult = await this.pool.query(
                'SELECT name, email FROM tenants WHERE id = $1',
                [tenantId]
            );

            if (tenantResult.rows.length === 0) return;

            const tenant = tenantResult.rows[0];
            const subject = progress === 100 ? 
                'Congratulations! Your BARPOS Setup is Complete 🎉' :
                'You\'re Halfway There! BARPOS Setup Progress Update';

            const emailContent = progress === 100 ? `
                <h2>Setup Complete! 🎉</h2>
                <p>Congratulations! You've successfully completed your BARPOS setup.</p>
                <p>Your restaurant <strong>${tenant.name}</strong> is now ready to start taking orders!</p>
                <p><a href="${process.env.FRONTEND_URL}/login">Login to your POS system</a></p>
            ` : `
                <h2>Great Progress! 📈</h2>
                <p>You're ${progress}% through your BARPOS setup for <strong>${tenant.name}</strong>.</p>
                <p>Keep going - you're doing great!</p>
                <p><a href="${process.env.FRONTEND_URL}/login">Continue your setup</a></p>
            `;

            await this.emailTransporter.sendMail({
                from: process.env.EMAIL_FROM || 'BARPOS <<EMAIL>>',
                to: tenant.email,
                subject: subject,
                html: emailContent
            });

        } catch (error) {
            console.error('❌ Error sending progress email:', error);
        }
    }

    // Helper methods
    generateRestaurantCode(restaurantName) {
        const cleaned = restaurantName.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        const random = Math.random().toString(36).substring(2, 6);
        return `${cleaned.substring(0, 8)}-${random}`;
    }

    generateSecurePin() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
}

module.exports = new OnboardingService();
