import React, { useState, useEffect, useRef } from 'react';
import { 
  Smartphone, 
  QrCode, 
  Fingerprint, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Camera,
  Wifi,
  WifiOff,
  RefreshCw,
  Download,
  Bluetooth
} from 'lucide-react';
import { sessionManager } from '../utils/SessionManager';

interface MobileAuthIntegrationProps {
  onMobileLogin: (success: boolean, method: string) => void;
  tenantSlug?: string;
}

interface QRCodeData {
  sessionId: string;
  tenantSlug: string;
  timestamp: number;
  expiresAt: number;
  serverUrl: string;
}

interface BiometricCapabilities {
  fingerprint: boolean;
  faceId: boolean;
  voiceRecognition: boolean;
  available: boolean;
}

const MobileAuthIntegration: React.FC<MobileAuthIntegrationProps> = ({
  onMobileLogin,
  tenantSlug
}) => {
  const [qrCode, setQrCode] = useState<string>('');
  const [qrCodeData, setQrCodeData] = useState<QRCodeData | null>(null);
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);
  const [qrStatus, setQrStatus] = useState<'idle' | 'waiting' | 'scanned' | 'expired'>('idle');
  const [biometricCapabilities, setBiometricCapabilities] = useState<BiometricCapabilities>({
    fingerprint: false,
    faceId: false,
    voiceRecognition: false,
    available: false
  });
  const [isCheckingBiometrics, setIsCheckingBiometrics] = useState(false);
  const [mobileAppStatus, setMobileAppStatus] = useState<'not_installed' | 'installed' | 'checking'>('checking');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const qrCheckInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    checkMobileAppStatus();
    checkBiometricCapabilities();
  }, []);

  useEffect(() => {
    return () => {
      if (qrCheckInterval.current) {
        clearInterval(qrCheckInterval.current);
      }
    };
  }, []);

  const checkMobileAppStatus = async () => {
    try {
      // Check if mobile app is installed by attempting to open custom URL scheme
      const testUrl = 'restroflow://check-app';
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = testUrl;
      document.body.appendChild(iframe);
      
      setTimeout(() => {
        document.body.removeChild(iframe);
        setMobileAppStatus('not_installed');
      }, 1000);

      // Also check for PWA installation
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          setMobileAppStatus('installed');
        }
      }
    } catch (error) {
      console.error('Error checking mobile app status:', error);
      setMobileAppStatus('not_installed');
    }
  };

  const checkBiometricCapabilities = async () => {
    setIsCheckingBiometrics(true);
    
    try {
      // Check for WebAuthn support
      const webAuthnSupported = 'credentials' in navigator && 'create' in navigator.credentials;
      
      // Check for specific biometric capabilities
      let capabilities: BiometricCapabilities = {
        fingerprint: false,
        faceId: false,
        voiceRecognition: false,
        available: webAuthnSupported
      };

      if (webAuthnSupported) {
        try {
          // Check for platform authenticator (biometrics)
          const available = await (navigator.credentials as any).get({
            publicKey: {
              challenge: new Uint8Array(32),
              allowCredentials: [],
              userVerification: 'preferred'
            }
          });
          
          capabilities.fingerprint = true; // Assume fingerprint if WebAuthn works
        } catch (error) {
          // Biometrics might not be set up
        }
      }

      // Check for Face ID (iOS Safari specific)
      if (navigator.userAgent.includes('iPhone') || navigator.userAgent.includes('iPad')) {
        capabilities.faceId = webAuthnSupported;
      }

      setBiometricCapabilities(capabilities);
    } catch (error) {
      console.error('Error checking biometric capabilities:', error);
    } finally {
      setIsCheckingBiometrics(false);
    }
  };

  const generateQRCode = async () => {
    setIsGeneratingQR(true);
    setQrStatus('idle');

    try {
      const sessionId = `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = Date.now() + (5 * 60 * 1000); // 5 minutes
      
      const qrData: QRCodeData = {
        sessionId,
        tenantSlug: tenantSlug || '',
        timestamp: Date.now(),
        expiresAt,
        serverUrl: 'http://localhost:4000'
      };

      // Generate QR code data
      const qrString = JSON.stringify(qrData);
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrString)}`;
      
      setQrCode(qrCodeUrl);
      setQrCodeData(qrData);
      setQrStatus('waiting');

      // Register QR session with backend
      await registerQRSession(qrData);

      // Start checking for QR scan
      startQRStatusCheck(sessionId);

      console.log('✅ QR Code generated successfully');
    } catch (error) {
      console.error('❌ Error generating QR code:', error);
    } finally {
      setIsGeneratingQR(false);
    }
  };

  const registerQRSession = async (qrData: QRCodeData) => {
    try {
      await fetch('http://localhost:4000/api/auth/qr/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(qrData)
      });
    } catch (error) {
      console.error('Failed to register QR session:', error);
    }
  };

  const startQRStatusCheck = (sessionId: string) => {
    if (qrCheckInterval.current) {
      clearInterval(qrCheckInterval.current);
    }

    qrCheckInterval.current = setInterval(async () => {
      try {
        const response = await fetch(`http://localhost:4000/api/auth/qr/status/${sessionId}`);
        const data = await response.json();

        if (data.status === 'scanned') {
          setQrStatus('scanned');
          
          // Verify the authentication
          if (data.token && data.employee) {
            sessionManager.createSession({
              token: data.token,
              employee: data.employee,
              tenant: data.tenant,
              adminAccess: data.adminAccess || false
            });

            onMobileLogin(true, 'qr_code');
            
            if (qrCheckInterval.current) {
              clearInterval(qrCheckInterval.current);
            }
          }
        } else if (data.status === 'expired') {
          setQrStatus('expired');
          if (qrCheckInterval.current) {
            clearInterval(qrCheckInterval.current);
          }
        }
      } catch (error) {
        console.error('Error checking QR status:', error);
      }
    }, 2000); // Check every 2 seconds

    // Auto-expire after 5 minutes
    setTimeout(() => {
      if (qrStatus === 'waiting') {
        setQrStatus('expired');
        if (qrCheckInterval.current) {
          clearInterval(qrCheckInterval.current);
        }
      }
    }, 5 * 60 * 1000);
  };

  const authenticateWithBiometrics = async (type: 'fingerprint' | 'faceId') => {
    try {
      setIsCheckingBiometrics(true);

      // Create WebAuthn credential request
      const challenge = new Uint8Array(32);
      crypto.getRandomValues(challenge);

      const credential = await (navigator.credentials as any).get({
        publicKey: {
          challenge,
          allowCredentials: [],
          userVerification: 'required',
          timeout: 60000
        }
      });

      if (credential) {
        // Send biometric authentication to backend
        const response = await fetch('http://localhost:4000/api/auth/biometric', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            credentialId: credential.id,
            authenticatorData: Array.from(new Uint8Array(credential.response.authenticatorData)),
            signature: Array.from(new Uint8Array(credential.response.signature)),
            tenantSlug,
            biometricType: type
          })
        });

        const data = await response.json();

        if (response.ok && data.token) {
          sessionManager.createSession({
            token: data.token,
            employee: data.employee,
            tenant: data.tenant,
            adminAccess: data.adminAccess || false
          });

          onMobileLogin(true, `biometric_${type}`);
          console.log(`✅ ${type} authentication successful`);
        } else {
          console.error(`❌ ${type} authentication failed:`, data.error);
          onMobileLogin(false, `biometric_${type}`);
        }
      }
    } catch (error) {
      console.error(`❌ ${type} authentication error:`, error);
      onMobileLogin(false, `biometric_${type}`);
    } finally {
      setIsCheckingBiometrics(false);
    }
  };

  const downloadMobileApp = () => {
    // In production, this would redirect to app stores
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      window.open('https://apps.apple.com/app/restroflow-pos', '_blank');
    } else if (userAgent.includes('Android')) {
      window.open('https://play.google.com/store/apps/details?id=com.restroflow.pos', '_blank');
    } else {
      // PWA installation
      if ('serviceWorker' in navigator) {
        window.location.href = '/install-pwa';
      }
    }
  };

  const getQRStatusIcon = () => {
    switch (qrStatus) {
      case 'waiting':
        return <Loader2 className="w-6 h-6 animate-spin text-blue-600" />;
      case 'scanned':
        return <CheckCircle className="w-6 h-6 text-green-600" />;
      case 'expired':
        return <AlertCircle className="w-6 h-6 text-red-600" />;
      default:
        return <QrCode className="w-6 h-6 text-gray-600" />;
    }
  };

  const getQRStatusMessage = () => {
    switch (qrStatus) {
      case 'waiting':
        return 'Waiting for mobile app to scan QR code...';
      case 'scanned':
        return 'QR code scanned! Authenticating...';
      case 'expired':
        return 'QR code expired. Please generate a new one.';
      default:
        return 'Generate QR code for mobile authentication';
    }
  };

  return (
    <div className={`w-full max-w-md mx-auto p-6 rounded-lg ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    } shadow-lg`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
          <Smartphone className="w-8 h-8 text-white" />
        </div>
        <h2 className={`text-2xl font-bold ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Mobile Authentication
        </h2>
        <p className={`text-sm ${
          isDarkMode ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Login using your mobile device
        </p>
      </div>

      {/* Mobile App Status */}
      <div className={`mb-6 p-4 rounded-lg ${
        mobileAppStatus === 'installed'
          ? 'bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-700'
          : 'bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700'
      }`}>
        <div className="flex items-center space-x-3">
          {mobileAppStatus === 'checking' ? (
            <Loader2 className="w-5 h-5 animate-spin text-gray-600" />
          ) : mobileAppStatus === 'installed' ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <Download className="w-5 h-5 text-yellow-600" />
          )}
          <div>
            <p className={`font-medium ${
              mobileAppStatus === 'installed'
                ? 'text-green-800 dark:text-green-200'
                : 'text-yellow-800 dark:text-yellow-200'
            }`}>
              {mobileAppStatus === 'checking' ? 'Checking app status...' :
               mobileAppStatus === 'installed' ? 'Mobile app detected' : 'Mobile app not found'}
            </p>
            {mobileAppStatus === 'not_installed' && (
              <button
                onClick={downloadMobileApp}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
              >
                Download RestroFlow Mobile App
              </button>
            )}
          </div>
        </div>
      </div>

      {/* QR Code Authentication */}
      <div className="mb-6">
        <h3 className={`text-lg font-semibold mb-4 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          QR Code Login
        </h3>
        
        <div className={`p-4 rounded-lg border-2 border-dashed ${
          qrStatus === 'waiting' ? 'border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20' :
          qrStatus === 'scanned' ? 'border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20' :
          qrStatus === 'expired' ? 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20' :
          'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700'
        }`}>
          
          {qrCode ? (
            <div className="text-center">
              <img 
                src={qrCode} 
                alt="QR Code for mobile login"
                className="mx-auto mb-4 rounded-lg"
              />
              <div className="flex items-center justify-center space-x-2 mb-2">
                {getQRStatusIcon()}
                <span className={`text-sm font-medium ${
                  qrStatus === 'waiting' ? 'text-blue-700 dark:text-blue-300' :
                  qrStatus === 'scanned' ? 'text-green-700 dark:text-green-300' :
                  qrStatus === 'expired' ? 'text-red-700 dark:text-red-300' :
                  'text-gray-700 dark:text-gray-300'
                }`}>
                  {getQRStatusMessage()}
                </span>
              </div>
              
              {qrStatus === 'expired' && (
                <button
                  onClick={generateQRCode}
                  className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 inline mr-2" />
                  Generate New QR Code
                </button>
              )}
            </div>
          ) : (
            <div className="text-center">
              <QrCode className={`w-16 h-16 mx-auto mb-4 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`} />
              <button
                onClick={generateQRCode}
                disabled={isGeneratingQR}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 mx-auto"
              >
                {isGeneratingQR ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <QrCode className="w-5 h-5" />
                )}
                <span>{isGeneratingQR ? 'Generating...' : 'Generate QR Code'}</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Biometric Authentication */}
      <div className="mb-6">
        <h3 className={`text-lg font-semibold mb-4 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Biometric Authentication
        </h3>
        
        {isCheckingBiometrics ? (
          <div className="text-center py-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2 text-blue-600" />
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Checking biometric capabilities...
            </p>
          </div>
        ) : biometricCapabilities.available ? (
          <div className="grid grid-cols-2 gap-3">
            {biometricCapabilities.fingerprint && (
              <button
                onClick={() => authenticateWithBiometrics('fingerprint')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  isDarkMode 
                    ? 'border-gray-600 hover:border-blue-500 bg-gray-700 hover:bg-gray-600' 
                    : 'border-gray-300 hover:border-blue-500 bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <Fingerprint className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                <p className={`text-sm font-medium ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-700'
                }`}>
                  Fingerprint
                </p>
              </button>
            )}
            
            {biometricCapabilities.faceId && (
              <button
                onClick={() => authenticateWithBiometrics('faceId')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  isDarkMode 
                    ? 'border-gray-600 hover:border-blue-500 bg-gray-700 hover:bg-gray-600' 
                    : 'border-gray-300 hover:border-blue-500 bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <Camera className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                <p className={`text-sm font-medium ${
                  isDarkMode ? 'text-gray-200' : 'text-gray-700'
                }`}>
                  Face ID
                </p>
              </button>
            )}
          </div>
        ) : (
          <div className={`p-4 rounded-lg ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
          }`}>
            <div className="text-center">
              <Shield className={`w-8 h-8 mx-auto mb-2 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`} />
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Biometric authentication not available on this device
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Connection Status */}
      <div className={`p-3 rounded-lg ${
        isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Wifi className="w-4 h-4 text-green-600" />
            <span className={`text-sm ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Connected to server
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-green-600">Online</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileAuthIntegration;
