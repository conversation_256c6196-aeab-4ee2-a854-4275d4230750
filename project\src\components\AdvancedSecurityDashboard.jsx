// Phase 3L: Global Compliance & Advanced Security System
// Advanced Security Dashboard Component

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  ShieldCheckIcon,
  LockClosedIcon,
  KeyIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon,
  UserIcon,
  GlobeAltIcon,
  BoltIcon,
  ChartBarIcon,
  FireIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';

const AdvancedSecurityDashboard = ({ className = '' }) => {
  const { t, formatNumber } = useTranslation();
  const [securityData, setSecurityData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');

  useEffect(() => {
    loadSecurityData();
    
    // Auto-refresh every 15 seconds for real-time security monitoring
    const interval = setInterval(loadSecurityData, 15000);
    return () => clearInterval(interval);
  }, [selectedTimeframe]);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      
      // Simulate comprehensive security data
      const mockSecurityData = {
        overview: {
          securityScore: 98.7,
          threatLevel: 'low',
          activeThreats: 0,
          blockedAttacks: 247,
          lastSecurityIncident: 'None in 180 days',
          quantumReadiness: 95.2,
          zeroTrustScore: 94.8,
          biometricAccuracy: 99.95
        },
        authentication: {
          totalAttempts: 15847,
          successfulLogins: 15623,
          failedAttempts: 224,
          biometricAuth: {
            voice: { attempts: 4521, success: 4498, accuracy: 99.49 },
            facial: { attempts: 3892, success: 3885, accuracy: 99.82 },
            fingerprint: { attempts: 2847, success: 2844, accuracy: 99.89 },
            iris: { attempts: 1234, success: 1233, accuracy: 99.92 },
            behavioral: { attempts: 2156, success: 2089, accuracy: 96.89 },
            multiModal: { attempts: 1197, success: 1196, accuracy: 99.92 }
          },
          anomalousAttempts: 12,
          blockedIPs: 34,
          suspiciousActivity: 8
        },
        encryption: {
          status: 'Quantum-Resistant',
          algorithm: 'AES-256-GCM + Post-Quantum',
          keyRotation: 'Every 24 hours',
          dataAtRest: 100, // % encrypted
          dataInTransit: 100, // % encrypted
          quantumResistance: 98.5,
          encryptionOverhead: 2.3, // ms
          keyManagement: 'HSM-Protected'
        },
        threats: {
          realTimeThreats: [
            {
              id: 'threat_001',
              type: 'Brute Force',
              severity: 'medium',
              source: '************',
              status: 'blocked',
              timestamp: new Date(Date.now() - 300000).toISOString(),
              attempts: 15,
              blocked: true
            },
            {
              id: 'threat_002',
              type: 'SQL Injection',
              severity: 'high',
              source: '*************',
              status: 'blocked',
              timestamp: new Date(Date.now() - 600000).toISOString(),
              attempts: 3,
              blocked: true
            },
            {
              id: 'threat_003',
              type: 'Anomalous Behavior',
              severity: 'low',
              source: 'Internal User',
              status: 'monitoring',
              timestamp: new Date(Date.now() - 900000).toISOString(),
              attempts: 1,
              blocked: false
            }
          ],
          threatStats: {
            last24h: { total: 247, blocked: 247, success: 0 },
            last7d: { total: 1834, blocked: 1834, success: 0 },
            last30d: { total: 7892, blocked: 7892, success: 0 }
          },
          topThreatTypes: [
            { type: 'Brute Force', count: 89, percentage: 36 },
            { type: 'SQL Injection', count: 67, percentage: 27 },
            { type: 'XSS Attempts', count: 45, percentage: 18 },
            { type: 'CSRF', count: 28, percentage: 11 },
            { type: 'Other', count: 18, percentage: 8 }
          ]
        },
        vulnerabilities: {
          total: 11,
          critical: 0,
          high: 1,
          medium: 3,
          low: 7,
          lastScan: '2025-06-10T12:00:00Z',
          nextScan: '2025-06-11T12:00:00Z',
          patchingStatus: {
            upToDate: 94.7,
            pending: 5.3,
            overdue: 0
          }
        },
        compliance: {
          frameworks: ['GDPR', 'CCPA', 'PCI DSS', 'ISO 27001', 'SOC 2'],
          overallScore: 96.8,
          lastAudit: '2025-06-01',
          nextAudit: '2025-12-01',
          issues: 2,
          certifications: 5
        },
        monitoring: {
          uptime: 99.98,
          responseTime: 45, // ms
          errorRate: 0.02,
          alertsLast24h: 3,
          falsePositives: 1,
          meanTimeToDetection: 2.3, // minutes
          meanTimeToResponse: 8.7 // minutes
        }
      };
      
      setSecurityData(mockSecurityData);
      setError(null);
    } catch (error) {
      console.error('Failed to load security data:', error);
      setError('Failed to load security data');
    } finally {
      setLoading(false);
    }
  };

  const SecurityMetricCard = ({ title, value, subtitle, icon: Icon, color = 'blue', status = 'normal' }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400`}>
            {typeof value === 'number' ? formatNumber(value) : value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/20 rounded-lg`}>
          <Icon className={`w-6 h-6 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
      {status !== 'normal' && (
        <div className="mt-2">
          <span className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
            status === 'warning' 
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
              : status === 'critical'
              ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
              : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
          }`}>
            {status}
          </span>
        </div>
      )}
    </div>
  );

  const BiometricAuthCard = ({ authData }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <UserIcon className="w-5 h-5 text-purple-500" />
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('security.biometric_authentication', 'Biometric Authentication')}
        </h4>
      </div>
      
      <div className="space-y-3">
        {Object.entries(authData.biometricAuth).map(([method, data]) => (
          <div key={method} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <div>
                <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {method.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {data.attempts} attempts
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-bold text-green-600 dark:text-green-400">
                {data.accuracy}%
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {data.success}/{data.attempts}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Overall Success Rate</span>
          <span className="text-lg font-bold text-green-600 dark:text-green-400">
            {((authData.successfulLogins / authData.totalAttempts) * 100).toFixed(2)}%
          </span>
        </div>
      </div>
    </div>
  );

  const ThreatMonitoringCard = ({ threats }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <FireIcon className="w-5 h-5 text-red-500" />
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('security.threat_monitoring', 'Real-Time Threat Monitoring')}
        </h4>
      </div>
      
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {threats.realTimeThreats.map((threat, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className={`w-3 h-3 rounded-full mt-1 flex-shrink-0 ${
              threat.severity === 'high' ? 'bg-red-500' :
              threat.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
            }`}></div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {threat.type}
                </span>
                <span className={`text-xs px-2 py-1 rounded ${
                  threat.status === 'blocked' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                }`}>
                  {threat.status}
                </span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {threat.source} • {threat.attempts} attempts • {new Date(threat.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-red-600 dark:text-red-400">
              {threats.threatStats.last24h.total}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">24h Threats</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              {threats.threatStats.last24h.blocked}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Blocked</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              100%
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Success Rate</div>
          </div>
        </div>
      </div>
    </div>
  );

  const QuantumSecurityCard = ({ encryption }) => (
    <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-800 p-4">
      <div className="flex items-center space-x-2 mb-4">
        <BeakerIcon className="w-5 h-5 text-purple-600" />
        <h4 className="text-lg font-semibold text-purple-900 dark:text-purple-100">
          {t('security.quantum_security', 'Quantum-Resistant Security')}
        </h4>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {encryption.quantumResistance}%
          </div>
          <div className="text-xs text-purple-700 dark:text-purple-300">Quantum Resistance</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {encryption.encryptionOverhead}ms
          </div>
          <div className="text-xs text-blue-700 dark:text-blue-300">Encryption Overhead</div>
        </div>
      </div>
      
      <div className="mt-4 space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-purple-700 dark:text-purple-300">Algorithm</span>
          <span className="text-purple-900 dark:text-purple-100 font-medium">
            {encryption.algorithm}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-purple-700 dark:text-purple-300">Key Management</span>
          <span className="text-purple-900 dark:text-purple-100 font-medium">
            {encryption.keyManagement}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-purple-700 dark:text-purple-300">Key Rotation</span>
          <span className="text-purple-900 dark:text-purple-100 font-medium">
            {encryption.keyRotation}
          </span>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <ShieldCheckIcon className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">
            {t('security.loading', 'Loading security data...')}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2 text-red-500">
          <ExclamationTriangleIcon className="w-5 h-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (!securityData) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('security.dashboard', 'Advanced Security Dashboard')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('security.subtitle', 'Real-time security monitoring and threat detection')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <SecurityMetricCard
          title={t('security.security_score', 'Security Score')}
          value={`${securityData.overview.securityScore}%`}
          subtitle={t('security.overall_security', 'Overall security posture')}
          icon={ShieldCheckIcon}
          color="green"
          status="excellent"
        />
        <SecurityMetricCard
          title={t('security.threat_level', 'Threat Level')}
          value={securityData.overview.threatLevel.toUpperCase()}
          subtitle={t('security.current_threat', 'Current threat assessment')}
          icon={ExclamationTriangleIcon}
          color="green"
          status="normal"
        />
        <SecurityMetricCard
          title={t('security.blocked_attacks', 'Blocked Attacks')}
          value={securityData.overview.blockedAttacks}
          subtitle={t('security.last_24h', 'Last 24 hours')}
          icon={FireIcon}
          color="red"
          status="normal"
        />
        <SecurityMetricCard
          title={t('security.quantum_readiness', 'Quantum Readiness')}
          value={`${securityData.overview.quantumReadiness}%`}
          subtitle={t('security.post_quantum', 'Post-quantum cryptography')}
          icon={BeakerIcon}
          color="purple"
          status="excellent"
        />
      </div>

      {/* Security Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BiometricAuthCard authData={securityData.authentication} />
        <ThreatMonitoringCard threats={securityData.threats} />
      </div>

      {/* Quantum Security */}
      <QuantumSecurityCard encryption={securityData.encryption} />

      {/* Vulnerability and Monitoring Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('security.vulnerability_status', 'Vulnerability Status')}
          </h4>
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {securityData.vulnerabilities.critical}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Critical</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {securityData.vulnerabilities.high}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">High</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {securityData.vulnerabilities.medium}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Medium</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {securityData.vulnerabilities.low}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Low</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('security.monitoring_metrics', 'Monitoring Metrics')}
          </h4>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">System Uptime</span>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">
                {securityData.monitoring.uptime}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Response Time</span>
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                {securityData.monitoring.responseTime}ms
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">MTTD</span>
              <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                {securityData.monitoring.meanTimeToDetection}min
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">MTTR</span>
              <span className="text-sm font-medium text-orange-600 dark:text-orange-400">
                {securityData.monitoring.meanTimeToResponse}min
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSecurityDashboard;
