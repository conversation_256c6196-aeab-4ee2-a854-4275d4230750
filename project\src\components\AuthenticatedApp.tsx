import React, { useState, useEffect } from 'react';
import { LogOut, Shield, User, Building } from 'lucide-react';
import SuperAdminLandingPage from './SuperAdminLandingPage';
import TenantAdminLandingPage from './TenantAdminLandingPage';
import UnifiedPOSSystem from '../UnifiedPOSSystem';

interface AuthData {
  token: string;
  employee: {
    id: string;
    name: string;
    role: string;
    permissions: string[];
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
    business_name: string;
    features: Record<string, boolean>;
  };
  location?: {
    id: string;
    name: string;
  };
}

interface AuthenticatedAppProps {
  authData: AuthData;
  onLogout: () => void;
}

const AuthenticatedApp: React.FC<AuthenticatedAppProps> = ({ authData, onLogout }) => {
  const [currentView, setCurrentView] = useState<'landing' | 'pos'>('landing');
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Determine the appropriate landing page based on user role
  const getLandingPage = () => {
    const { role } = authData.employee;
    
    switch (role) {
      case 'super_admin':
        return <SuperAdminLandingPage />;
      case 'tenant_admin':
      case 'manager':
        return <TenantAdminLandingPage />;
      default:
        // Regular employees go directly to POS
        return <UnifiedPOSSystem />;
    }
  };

  const canAccessPOS = () => {
    const { role, permissions } = authData.employee;
    return role === 'super_admin' || 
           role === 'manager' || 
           role === 'employee' || 
           permissions.includes('pos') || 
           permissions.includes('all');
  };

  const canAccessAdmin = () => {
    const { role } = authData.employee;
    // Grant admin access to all admin roles including 'admin' for comprehensive restaurant management
    return role === 'super_admin' || role === 'tenant_admin' || role === 'admin' || role === 'manager';
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('authData');
    onLogout();
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Shield className="h-4 w-4" />;
      case 'tenant_admin':
      case 'admin': // Include admin role with same styling as tenant_admin
      case 'manager':
        return <Building className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-600';
      case 'tenant_admin':
      case 'admin': // Include admin role with same styling as tenant_admin
      case 'manager':
        return 'bg-blue-600';
      default:
        return 'bg-green-600';
    }
  };

  const formatRole = (role: string) => {
    return role.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Auto-redirect employees to POS if they can't access admin
  useEffect(() => {
    if (!canAccessAdmin() && canAccessPOS()) {
      setCurrentView('pos');
    }
  }, [authData]);

  if (currentView === 'pos') {
    return (
      <div className="min-h-screen bg-gray-100">
        {/* POS Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-3">
              <div className="flex items-center space-x-4">
                {canAccessAdmin() && (
                  <button
                    onClick={() => setCurrentView('landing')}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    ← Back to Dashboard
                  </button>
                )}
                <div className="text-lg font-semibold text-gray-900">
                  {authData.tenant.business_name} - POS System
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600">
                  {authData.location?.name || 'Main Location'}
                </div>
                <div className="relative">
                  <button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
                  >
                    <div className={`w-6 h-6 ${getRoleColor(authData.employee.role)} rounded-full flex items-center justify-center`}>
                      {getRoleIcon(authData.employee.role)}
                    </div>
                    <span>{authData.employee.name}</span>
                  </button>
                  
                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                        <div className="font-medium">{authData.employee.name}</div>
                        <div className="text-gray-500">{formatRole(authData.employee.role)}</div>
                      </div>
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* POS Content */}
        <UnifiedPOSSystem />
      </div>
    );
  }

  // Landing page view
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Quick Access Bar (only for admin roles) */}
      {canAccessAdmin() && (
        <div className="bg-gray-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-2">
              <div className="flex items-center space-x-6">
                <div className="text-sm">
                  <span className="text-gray-300">Logged in as:</span>
                  <span className="ml-2 font-medium">{authData.employee.name}</span>
                  <span className="ml-2 text-gray-400">({formatRole(authData.employee.role)})</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-300">Business:</span>
                  <span className="ml-2 font-medium">{authData.tenant.business_name}</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {canAccessPOS() && (
                  <button
                    onClick={() => setCurrentView('pos')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
                  >
                    Open POS
                  </button>
                )}
                <div className="relative">
                  <button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center space-x-2 text-sm hover:text-gray-300"
                  >
                    <div className={`w-6 h-6 ${getRoleColor(authData.employee.role)} rounded-full flex items-center justify-center`}>
                      {getRoleIcon(authData.employee.role)}
                    </div>
                    <span>{authData.employee.name}</span>
                  </button>
                  
                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                        <div className="font-medium">{authData.employee.name}</div>
                        <div className="text-gray-500">{formatRole(authData.employee.role)}</div>
                        <div className="text-xs text-gray-400 mt-1">
                          {authData.tenant.business_name}
                        </div>
                      </div>
                      {canAccessPOS() && (
                        <button
                          onClick={() => setCurrentView('pos')}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <User className="h-4 w-4 mr-2" />
                          Open POS System
                        </button>
                      )}
                      <button
                        onClick={handleLogout}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Landing Page Content */}
      {getLandingPage()}
    </div>
  );
};

export default AuthenticatedApp;
