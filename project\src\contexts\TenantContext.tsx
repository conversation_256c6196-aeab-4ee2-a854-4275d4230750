import React, { createContext, useContext, useState, useEffect } from 'react';

interface Tenant {
  id: number;
  name: string;
  slug: string;
}

interface TenantContextType {
  currentTenant: Tenant | null;
  setCurrentTenant: (tenant: Tenant) => void;
  clearTenant: () => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({ children }: { children: React.ReactNode }) {
  const [currentTenant, setCurrentTenantState] = useState<Tenant | null>(null);

  useEffect(() => {
    // Check for stored tenant data on app load
    const storedTenant = localStorage.getItem('pos_tenant');
    if (storedTenant) {
      try {
        const tenantData = JSON.parse(storedTenant);
        setCurrentTenantState(tenantData);
      } catch (error) {
        console.error('Error parsing stored tenant data:', error);
        localStorage.removeItem('pos_tenant');
      }
    }
  }, []);

  const setCurrentTenant = (tenant: Tenant) => {
    setCurrentTenantState(tenant);
    localStorage.setItem('pos_tenant', JSON.stringify(tenant));
  };

  const clearTenant = () => {
    setCurrentTenantState(null);
    localStorage.removeItem('pos_tenant');
  };

  return (
    <TenantContext.Provider value={{ currentTenant, setCurrentTenant, clearTenant }}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}
