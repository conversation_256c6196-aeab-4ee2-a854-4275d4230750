import React from 'react';
import { Construction, Clock, Star, BarChart3, Settings as SettingsIcon, ChefHat, History, Globe, QrCode } from 'lucide-react';

interface ComingSoonPlaceholderProps {
  title: string;
  description: string;
  icon: 'loyalty' | 'analytics' | 'settings' | 'kitchen' | 'orders' | 'menu' | 'qr';
  features?: string[];
}

const ComingSoonPlaceholder: React.FC<ComingSoonPlaceholderProps> = ({ 
  title, 
  description, 
  icon, 
  features = [] 
}) => {
  const getIcon = () => {
    switch (icon) {
      case 'loyalty':
        return <Star className="h-16 w-16 text-blue-500" />;
      case 'analytics':
        return <BarChart3 className="h-16 w-16 text-green-500" />;
      case 'settings':
        return <SettingsIcon className="h-16 w-16 text-gray-500" />;
      case 'kitchen':
        return <ChefHat className="h-16 w-16 text-orange-500" />;
      case 'orders':
        return <History className="h-16 w-16 text-purple-500" />;
      case 'menu':
        return <Globe className="h-16 w-16 text-indigo-500" />;
      case 'qr':
        return <QrCode className="h-16 w-16 text-pink-500" />;
      default:
        return <Construction className="h-16 w-16 text-yellow-500" />;
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gray-100 rounded-lg">
            {getIcon()}
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <p className="text-sm text-gray-500">{description}</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <Construction className="h-24 w-24 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Coming Soon</h3>
            <p className="text-gray-600 mb-6">
              This feature is currently under development and will be available in a future update.
            </p>
          </div>

          {features.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Planned Features:</h4>
              <ul className="space-y-2">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-center text-gray-700">
                    <Clock className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 text-sm">
              <strong>Note:</strong> The core POS functionality (Point of Sale, Floor Layout, Inventory, and Staff Management) 
              is fully functional. Additional features are being developed to enhance your restaurant management experience.
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-4">
        <div className="text-center">
          <p className="text-gray-500 text-sm">
            For immediate assistance, please use the available features or contact support.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ComingSoonPlaceholder;
