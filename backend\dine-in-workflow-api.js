const express = require('express');
const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Database connection
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

// Middleware for authentication
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// WebSocket instance (to be injected)
let io = null;
const setSocketIO = (socketIO) => {
  io = socketIO;
};

// Utility function to broadcast events
const broadcastEvent = (event, data, roomId = 'tables') => {
  if (io) {
    io.to(roomId).emit(event, data);
    console.log(`📢 Broadcasting ${event} to room ${roomId}:`, data);
  }
};

// Get available tables for dine-in
router.get('/tables', authenticateToken, async (req, res) => {
  try {
    const { status = 'available' } = req.query;
    
    const query = `
      SELECT 
        t.*,
        ta.employee_id as assigned_employee_id,
        ta.session_id as assignment_session_id,
        e.name as assigned_employee_name,
        tl.is_active as is_locked,
        tl.expiry_time as lock_expiry
      FROM tables t
      LEFT JOIN table_assignments ta ON t.id = ta.table_id AND ta.status = 'active'
      LEFT JOIN employees e ON ta.employee_id = e.id
      LEFT JOIN table_locks tl ON t.id = tl.table_id AND tl.is_active = true AND tl.expiry_time > CURRENT_TIMESTAMP
      WHERE t.status = $1 AND t.tenant_id = $2
      ORDER BY t.section, t.number
    `;

    const result = await pool.query(query, [status, req.user.tenantId]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({ error: 'Failed to fetch tables' });
  }
});

// Check table availability for concurrent access prevention
router.post('/tables/:tableId/check-availability', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    const { employeeId, sessionId } = req.body;

    // Check if table is already locked by another session
    const lockQuery = `
      SELECT * FROM table_locks 
      WHERE table_id = $1 AND is_active = true AND expiry_time > CURRENT_TIMESTAMP
    `;
    const lockResult = await pool.query(lockQuery, [tableId]);

    if (lockResult.rows.length > 0) {
      const existingLock = lockResult.rows[0];
      if (existingLock.session_id !== sessionId) {
        const employeeQuery = 'SELECT name FROM employees WHERE id = $1';
        const employeeResult = await pool.query(employeeQuery, [existingLock.employee_id]);
        const blockingEmployee = employeeResult.rows[0]?.name || 'Another employee';

        // Broadcast concurrent access blocked event
        broadcastEvent('table:concurrent:blocked', {
          tableId,
          blockingEmployee,
          sessionId: existingLock.session_id
        });

        return res.status(409).json({ 
          error: 'Table is currently being assigned by another employee',
          message: `Table is locked by ${blockingEmployee}`,
          blockingEmployee
        });
      }
    }

    // Check if table is already assigned
    const assignmentQuery = `
      SELECT * FROM table_assignments 
      WHERE table_id = $1 AND status = 'active'
    `;
    const assignmentResult = await pool.query(assignmentQuery, [tableId]);

    if (assignmentResult.rows.length > 0) {
      return res.status(409).json({ 
        error: 'Table is already assigned',
        message: 'This table is currently assigned to another order'
      });
    }

    res.json({ available: true, message: 'Table is available for assignment' });
  } catch (error) {
    console.error('Error checking table availability:', error);
    res.status(500).json({ error: 'Failed to check table availability' });
  }
});

// Acquire table lock
router.post('/tables/:tableId/lock', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    const { employeeId, sessionId, duration = 30000 } = req.body;

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // Clean up expired locks first
      await client.query(`
        UPDATE table_locks 
        SET is_active = false 
        WHERE expiry_time < CURRENT_TIMESTAMP AND is_active = true
      `);

      // Check for existing active locks
      const existingLockQuery = `
        SELECT * FROM table_locks 
        WHERE table_id = $1 AND is_active = true AND expiry_time > CURRENT_TIMESTAMP
      `;
      const existingLock = await client.query(existingLockQuery, [tableId]);

      if (existingLock.rows.length > 0 && existingLock.rows[0].session_id !== sessionId) {
        await client.query('ROLLBACK');
        return res.status(409).json({ 
          error: 'Table is already locked by another session',
          success: false 
        });
      }

      // Create or update lock
      const expiryTime = new Date(Date.now() + duration);
      const lockQuery = `
        INSERT INTO table_locks (table_id, employee_id, session_id, expiry_time, terminal_id)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (table_id) 
        DO UPDATE SET 
          employee_id = EXCLUDED.employee_id,
          session_id = EXCLUDED.session_id,
          expiry_time = EXCLUDED.expiry_time,
          is_active = true,
          created_at = CURRENT_TIMESTAMP
        RETURNING *
      `;

      const lockResult = await client.query(lockQuery, [
        tableId, 
        employeeId, 
        sessionId, 
        expiryTime,
        req.headers['x-terminal-id'] || 'unknown'
      ]);

      await client.query('COMMIT');

      const lockData = lockResult.rows[0];

      // Broadcast lock acquired event
      broadcastEvent('table:lock:acquired', {
        tableId,
        employeeId,
        sessionId,
        expiry: expiryTime,
        lockId: lockData.id
      });

      // Schedule lock expiry cleanup
      setTimeout(() => {
        broadcastEvent('table:lock:expired', { tableId, sessionId });
      }, duration);

      res.json({ 
        success: true, 
        lockId: lockData.id,
        expiry: expiryTime,
        message: 'Table locked successfully' 
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error acquiring table lock:', error);
    res.status(500).json({ error: 'Failed to acquire table lock', success: false });
  }
});

// Release table lock
router.post('/tables/:tableId/unlock', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    const { sessionId } = req.body;

    const unlockQuery = `
      UPDATE table_locks 
      SET is_active = false 
      WHERE table_id = $1 AND session_id = $2 AND is_active = true
      RETURNING *
    `;

    const result = await pool.query(unlockQuery, [tableId, sessionId]);

    if (result.rows.length > 0) {
      // Broadcast lock released event
      broadcastEvent('table:lock:released', { tableId, sessionId });
      
      res.json({ success: true, message: 'Table lock released successfully' });
    } else {
      res.status(404).json({ error: 'No active lock found for this table and session' });
    }
  } catch (error) {
    console.error('Error releasing table lock:', error);
    res.status(500).json({ error: 'Failed to release table lock' });
  }
});

// Verify employee PIN
router.post('/employees/verify-pin', authenticateToken, async (req, res) => {
  try {
    const { employeeId, pin } = req.body;

    if (!employeeId || !pin) {
      return res.status(400).json({ error: 'Employee ID and PIN are required' });
    }

    const query = 'SELECT * FROM employees WHERE id = $1 AND tenant_id = $2';
    const result = await pool.query(query, [employeeId, req.user.tenantId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Employee not found' });
    }

    const employee = result.rows[0];
    
    // For development, allow simple PIN comparison
    // In production, use bcrypt.compare(pin, employee.pin_hash)
    const isValidPin = employee.pin === pin || await bcrypt.compare(pin, employee.pin_hash || '');

    if (!isValidPin) {
      return res.status(401).json({ error: 'Invalid PIN' });
    }

    // Update last activity
    await pool.query(
      'UPDATE employees SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
      [employeeId]
    );

    res.json({ 
      success: true, 
      message: 'PIN verified successfully',
      employee: {
        id: employee.id,
        name: employee.name,
        role: employee.role
      }
    });
  } catch (error) {
    console.error('Error verifying employee PIN:', error);
    res.status(500).json({ error: 'Failed to verify PIN' });
  }
});

// Create table assignment
router.post('/table-assignments', authenticateToken, async (req, res) => {
  try {
    const {
      tableId,
      employeeId,
      sessionId,
      assignmentType = 'dine_in',
      employeeConfirmationPin,
      guestCount = 1,
      specialRequests
    } = req.body;

    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Verify the table is still available
      const tableCheck = await client.query(
        'SELECT * FROM tables WHERE id = $1 AND status = $2',
        [tableId, 'available']
      );

      if (tableCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(409).json({ error: 'Table is no longer available' });
      }

      // Create table assignment
      const assignmentQuery = `
        INSERT INTO table_assignments (
          table_id, employee_id, session_id, assignment_type, 
          employee_confirmation_pin, guest_count, special_requests,
          terminal_id, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'active')
        RETURNING *
      `;

      const assignmentResult = await client.query(assignmentQuery, [
        tableId,
        employeeId,
        sessionId,
        assignmentType,
        employeeConfirmationPin,
        guestCount,
        specialRequests,
        req.headers['x-terminal-id'] || 'unknown'
      ]);

      // Update table status
      await client.query(
        `UPDATE tables SET 
         status = 'being-seated', 
         workflow_status = 'assigned',
         last_assigned_employee = $2,
         last_assignment_time = CURRENT_TIMESTAMP
         WHERE id = $1`,
        [tableId, employeeId]
      );

      // Release any existing locks
      await client.query(
        'UPDATE table_locks SET is_active = false WHERE table_id = $1',
        [tableId]
      );

      // Log workflow step
      await client.query(`
        INSERT INTO workflow_audit_log (
          table_id, employee_id, session_id, workflow_step, 
          step_data, terminal_id, ip_address
        ) VALUES ($1, $2, $3, 'table_assigned', $4, $5, $6)
      `, [
        tableId,
        employeeId,
        sessionId,
        JSON.stringify({ assignmentType, guestCount }),
        req.headers['x-terminal-id'] || 'unknown',
        req.ip
      ]);

      await client.query('COMMIT');

      const assignment = assignmentResult.rows[0];

      // Get employee details for broadcasting
      const employeeQuery = 'SELECT name, role FROM employees WHERE id = $1';
      const employeeResult = await client.query(employeeQuery, [employeeId]);
      const employee = employeeResult.rows[0];

      // Broadcast assignment created event
      broadcastEvent('table:assignment:created', {
        tableId,
        employeeId,
        employeeName: employee.name,
        assignmentId: assignment.id,
        sessionId,
        assignmentType,
        guestCount
      });

      // Broadcast table status change
      broadcastEvent('table:status:changed', {
        tableId,
        status: 'being-seated',
        workflowStatus: 'assigned',
        employeeId,
        employeeName: employee.name
      });

      res.status(201).json({
        success: true,
        assignment,
        message: 'Table assignment created successfully'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating table assignment:', error);
    res.status(500).json({ error: 'Failed to create table assignment' });
  }
});

// Update table status
router.put('/tables/:tableId/status', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    const { 
      status, 
      substatus, 
      currentOrderId, 
      orderTotal, 
      orderItems,
      guestCount 
    } = req.body;

    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Update table status
      const updateQuery = `
        UPDATE tables SET 
          status = $1,
          substatus = $2,
          current_order_id = $3,
          order_total = $4,
          order_items = $5,
          guest_count = $6,
          workflow_status = CASE 
            WHEN $1 = 'occupied' THEN 'occupied'
            WHEN $1 = 'available' THEN 'available'
            ELSE workflow_status
          END,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $7
        RETURNING *
      `;

      const result = await client.query(updateQuery, [
        status,
        substatus,
        currentOrderId,
        orderTotal,
        orderItems,
        guestCount,
        tableId
      ]);

      if (result.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ error: 'Table not found' });
      }

      // Log workflow step
      await client.query(`
        INSERT INTO workflow_audit_log (
          table_id, employee_id, workflow_step, step_data, terminal_id
        ) VALUES ($1, $2, 'status_updated', $3, $4)
      `, [
        tableId,
        req.user.employeeId,
        JSON.stringify({ status, substatus, orderTotal, orderItems }),
        req.headers['x-terminal-id'] || 'unknown'
      ]);

      await client.query('COMMIT');

      const updatedTable = result.rows[0];

      // Broadcast table status change
      broadcastEvent('table:status:changed', {
        tableId,
        status,
        substatus,
        currentOrderId,
        orderTotal,
        orderItems,
        guestCount,
        employeeId: req.user.employeeId
      });

      res.json({
        success: true,
        table: updatedTable,
        message: 'Table status updated successfully'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error updating table status:', error);
    res.status(500).json({ error: 'Failed to update table status' });
  }
});

// Get workflow audit log
router.get('/workflow-audit/:tableId', authenticateToken, async (req, res) => {
  try {
    const { tableId } = req.params;
    const { limit = 50 } = req.query;

    const query = `
      SELECT 
        wal.*,
        e.name as employee_name,
        t.number as table_number
      FROM workflow_audit_log wal
      LEFT JOIN employees e ON wal.employee_id = e.id
      LEFT JOIN tables t ON wal.table_id = t.id
      WHERE wal.table_id = $1
      ORDER BY wal.timestamp DESC
      LIMIT $2
    `;

    const result = await pool.query(query, [tableId, limit]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching workflow audit log:', error);
    res.status(500).json({ error: 'Failed to fetch workflow audit log' });
  }
});

// Export router and socket setter
module.exports = { router, setSocketIO };
