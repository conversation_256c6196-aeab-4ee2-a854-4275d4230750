// 👥 BARPOS Customer Onboarding API Routes
// Phase 7B: Customer Systems & Billing
// Customer onboarding and support system endpoints

const express = require('express');
const router = express.Router();
const onboardingService = require('../services/onboardingService');
const billingService = require('../services/billingService');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { Pool } = require('pg');

const pool = new Pool({
    user: process.env.POSTGRES_USER || 'BARPOS',
    host: process.env.POSTGRES_HOST || 'localhost',
    database: process.env.POSTGRES_DB || 'BARPOS',
    password: process.env.POSTGRES_PASSWORD || 'Chaand@0319',
    port: process.env.POSTGRES_PORT || 5432,
});

// ================================
// CUSTOMER SIGNUP & ONBOARDING
// ================================

// Start customer onboarding (public endpoint)
router.post('/signup', async (req, res) => {
    try {
        const {
            restaurantName,
            contactName,
            adminName,
            email,
            phone,
            address,
            city,
            state,
            postalCode,
            country,
            timezone,
            currency,
            plan = 'starter'
        } = req.body;

        // Validate required fields
        if (!restaurantName || !email || !contactName) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields: restaurantName, email, contactName'
            });
        }

        // Check if email already exists
        const existingTenant = await pool.query(
            'SELECT id FROM tenants WHERE email = $1',
            [email]
        );

        if (existingTenant.rows.length > 0) {
            return res.status(409).json({
                success: false,
                message: 'An account with this email already exists'
            });
        }

        const customerData = {
            restaurantName,
            contactName,
            adminName: adminName || contactName,
            email,
            phone,
            address,
            city,
            state,
            postalCode,
            country: country || 'US',
            timezone: timezone || 'America/New_York',
            currency: currency || 'USD',
            plan
        };

        // Start onboarding process
        const result = await onboardingService.startOnboarding(customerData);

        // Create billing customer
        await billingService.createCustomer({
            id: result.tenant.id,
            name: result.tenant.name,
            email: result.tenant.email,
            phone: result.tenant.phone,
            address: result.tenant.address,
            plan: plan
        });

        res.status(201).json({
            success: true,
            message: 'Account created successfully! Check your email for login credentials.',
            tenant: {
                id: result.tenant.id,
                name: result.tenant.name,
                restaurantCode: result.tenant.restaurant_code
            },
            credentials: result.credentials,
            loginUrl: result.loginUrl,
            trialEnd: result.trial_end
        });

    } catch (error) {
        console.error('Error in customer signup:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create account',
            error: error.message
        });
    }
});

// Get onboarding status
router.get('/status/:tenantId', authenticateToken, async (req, res) => {
    try {
        const { tenantId } = req.params;

        // Check access
        if (req.user.role !== 'super_admin' && req.user.tenantId !== parseInt(tenantId)) {
            return res.status(403).json({ success: false, message: 'Access denied' });
        }

        const result = await onboardingService.getOnboardingStatus(parseInt(tenantId));
        
        if (!result.success) {
            return res.status(404).json(result);
        }

        res.json({
            success: true,
            onboarding: result.onboarding,
            steps: result.steps
        });

    } catch (error) {
        console.error('Error getting onboarding status:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get onboarding status',
            error: error.message
        });
    }
});

// Update onboarding step
router.put('/steps/:tenantId/:stepName', authenticateToken, async (req, res) => {
    try {
        const { tenantId, stepName } = req.params;
        const { status, data = {} } = req.body;

        // Check access
        if (req.user.role !== 'super_admin' && req.user.tenantId !== parseInt(tenantId)) {
            return res.status(403).json({ success: false, message: 'Access denied' });
        }

        if (!['pending', 'in_progress', 'completed', 'skipped'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be: pending, in_progress, completed, or skipped'
            });
        }

        const result = await onboardingService.updateOnboardingStep(
            parseInt(tenantId),
            stepName,
            status,
            data
        );

        res.json({
            success: true,
            message: 'Onboarding step updated successfully',
            progress: result.progress
        });

    } catch (error) {
        console.error('Error updating onboarding step:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update onboarding step',
            error: error.message
        });
    }
});

// Get onboarding resources
router.get('/resources/:stepName', async (req, res) => {
    try {
        const { stepName } = req.params;

        const result = await pool.query(`
            SELECT * FROM onboarding_resources 
            WHERE step_name = $1 AND is_active = true 
            ORDER BY sort_order, title
        `, [stepName]);

        res.json({
            success: true,
            resources: result.rows
        });

    } catch (error) {
        console.error('Error getting onboarding resources:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get onboarding resources',
            error: error.message
        });
    }
});

// ================================
// SUPPORT SYSTEM
// ================================

// Create support ticket
router.post('/support/tickets', authenticateToken, async (req, res) => {
    try {
        const {
            subject,
            description,
            priority = 'medium',
            category
        } = req.body;

        if (!subject || !description) {
            return res.status(400).json({
                success: false,
                message: 'Subject and description are required'
            });
        }

        // Generate ticket number
        const ticketNumber = `BARPOS-${Date.now()}-${Math.random().toString(36).substring(2, 6).toUpperCase()}`;

        const result = await pool.query(`
            INSERT INTO support_tickets (
                tenant_id, ticket_number, subject, description, 
                priority, category, created_by, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING *
        `, [
            req.user.tenantId,
            ticketNumber,
            subject,
            description,
            priority,
            category,
            req.user.id
        ]);

        // Add initial message
        await pool.query(`
            INSERT INTO support_messages (
                ticket_id, sender_type, sender_id, message, created_at
            ) VALUES ($1, $2, $3, $4, NOW())
        `, [
            result.rows[0].id,
            'customer',
            req.user.id,
            description
        ]);

        res.status(201).json({
            success: true,
            message: 'Support ticket created successfully',
            ticket: result.rows[0]
        });

    } catch (error) {
        console.error('Error creating support ticket:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create support ticket',
            error: error.message
        });
    }
});

// Get support tickets for tenant
router.get('/support/tickets', authenticateToken, async (req, res) => {
    try {
        const { status, limit = 20, offset = 0 } = req.query;
        
        let query = `
            SELECT st.*, e.name as created_by_name
            FROM support_tickets st
            LEFT JOIN employees e ON st.created_by = e.id
            WHERE st.tenant_id = $1
        `;
        
        const params = [req.user.tenantId];
        
        if (status) {
            query += ` AND st.status = $${params.length + 1}`;
            params.push(status);
        }
        
        query += ` ORDER BY st.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(parseInt(limit), parseInt(offset));

        const result = await pool.query(query, params);

        res.json({
            success: true,
            tickets: result.rows
        });

    } catch (error) {
        console.error('Error getting support tickets:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get support tickets',
            error: error.message
        });
    }
});

// Get support ticket details
router.get('/support/tickets/:ticketId', authenticateToken, async (req, res) => {
    try {
        const { ticketId } = req.params;

        // Get ticket
        const ticketResult = await pool.query(`
            SELECT st.*, e.name as created_by_name
            FROM support_tickets st
            LEFT JOIN employees e ON st.created_by = e.id
            WHERE st.id = $1 AND st.tenant_id = $2
        `, [ticketId, req.user.tenantId]);

        if (ticketResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Support ticket not found'
            });
        }

        // Get messages
        const messagesResult = await pool.query(`
            SELECT sm.*, e.name as sender_name
            FROM support_messages sm
            LEFT JOIN employees e ON sm.sender_id = e.id
            WHERE sm.ticket_id = $1
            ORDER BY sm.created_at ASC
        `, [ticketId]);

        res.json({
            success: true,
            ticket: ticketResult.rows[0],
            messages: messagesResult.rows
        });

    } catch (error) {
        console.error('Error getting support ticket:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get support ticket',
            error: error.message
        });
    }
});

// Add message to support ticket
router.post('/support/tickets/:ticketId/messages', authenticateToken, async (req, res) => {
    try {
        const { ticketId } = req.params;
        const { message } = req.body;

        if (!message) {
            return res.status(400).json({
                success: false,
                message: 'Message is required'
            });
        }

        // Verify ticket belongs to user's tenant
        const ticketResult = await pool.query(
            'SELECT id FROM support_tickets WHERE id = $1 AND tenant_id = $2',
            [ticketId, req.user.tenantId]
        );

        if (ticketResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Support ticket not found'
            });
        }

        // Add message
        const result = await pool.query(`
            INSERT INTO support_messages (
                ticket_id, sender_type, sender_id, message, created_at
            ) VALUES ($1, $2, $3, $4, NOW())
            RETURNING *
        `, [ticketId, 'customer', req.user.id, message]);

        // Update ticket status if closed
        await pool.query(`
            UPDATE support_tickets 
            SET status = 'open', updated_at = NOW()
            WHERE id = $1 AND status = 'closed'
        `, [ticketId]);

        res.json({
            success: true,
            message: 'Message added successfully',
            messageData: result.rows[0]
        });

    } catch (error) {
        console.error('Error adding support message:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add message',
            error: error.message
        });
    }
});

// ================================
// KNOWLEDGE BASE
// ================================

// Get knowledge base articles
router.get('/knowledge-base', async (req, res) => {
    try {
        const { category, search, limit = 20, offset = 0 } = req.query;
        
        let query = `
            SELECT id, title, slug, excerpt, category, tags, view_count, 
                   helpful_count, not_helpful_count, created_at, updated_at
            FROM knowledge_base 
            WHERE is_published = true
        `;
        
        const params = [];
        
        if (category) {
            query += ` AND category = $${params.length + 1}`;
            params.push(category);
        }
        
        if (search) {
            query += ` AND (title ILIKE $${params.length + 1} OR content ILIKE $${params.length + 1})`;
            params.push(`%${search}%`);
        }
        
        query += ` ORDER BY helpful_count DESC, view_count DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
        params.push(parseInt(limit), parseInt(offset));

        const result = await pool.query(query, params);

        res.json({
            success: true,
            articles: result.rows
        });

    } catch (error) {
        console.error('Error getting knowledge base articles:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get knowledge base articles',
            error: error.message
        });
    }
});

// Get knowledge base article by slug
router.get('/knowledge-base/:slug', async (req, res) => {
    try {
        const { slug } = req.params;

        const result = await pool.query(`
            SELECT * FROM knowledge_base 
            WHERE slug = $1 AND is_published = true
        `, [slug]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Article not found'
            });
        }

        // Increment view count
        await pool.query(`
            UPDATE knowledge_base 
            SET view_count = view_count + 1 
            WHERE slug = $1
        `, [slug]);

        res.json({
            success: true,
            article: result.rows[0]
        });

    } catch (error) {
        console.error('Error getting knowledge base article:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get article',
            error: error.message
        });
    }
});

// Rate knowledge base article
router.post('/knowledge-base/:slug/rate', async (req, res) => {
    try {
        const { slug } = req.params;
        const { helpful } = req.body; // true for helpful, false for not helpful

        if (typeof helpful !== 'boolean') {
            return res.status(400).json({
                success: false,
                message: 'helpful must be true or false'
            });
        }

        const field = helpful ? 'helpful_count' : 'not_helpful_count';
        
        await pool.query(`
            UPDATE knowledge_base 
            SET ${field} = ${field} + 1 
            WHERE slug = $1 AND is_published = true
        `, [slug]);

        res.json({
            success: true,
            message: 'Rating recorded successfully'
        });

    } catch (error) {
        console.error('Error rating article:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to rate article',
            error: error.message
        });
    }
});

module.exports = router;
