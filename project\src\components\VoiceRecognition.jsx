// Phase 3I: Multi-Language Localization System
// Voice Recognition Component with Multi-Language Support

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { 
  MicrophoneIcon, 
  StopIcon, 
  SpeakerWaveIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const VoiceRecognition = ({ 
  onCommand,
  onTranscript,
  className = '',
  size = 'md',
  showTranscript = true,
  autoStart = false,
  continuous = false
}) => {
  const { currentLanguage, t, getCultural } = useTranslation();
  
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState(null);
  const [voiceCommands, setVoiceCommands] = useState([]);
  const [isSupported, setIsSupported] = useState(false);
  const [volume, setVolume] = useState(0);
  
  const recognitionRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneRef = useRef(null);
  const animationRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (SpeechRecognition) {
        setIsSupported(true);
        recognitionRef.current = new SpeechRecognition();
        
        const recognition = recognitionRef.current;
        recognition.continuous = continuous;
        recognition.interimResults = true;
        recognition.maxAlternatives = 3;
        
        recognition.onstart = () => {
          setIsListening(true);
          setError(null);
          console.log('🎤 Voice recognition started');
        };
        
        recognition.onend = () => {
          setIsListening(false);
          console.log('🎤 Voice recognition ended');
        };
        
        recognition.onerror = (event) => {
          setError(event.error);
          setIsListening(false);
          console.error('🎤 Voice recognition error:', event.error);
        };
        
        recognition.onresult = (event) => {
          let finalTranscript = '';
          let interimTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const result = event.results[i];
            const transcript = result[0].transcript;
            const confidence = result[0].confidence;
            
            if (result.isFinal) {
              finalTranscript += transcript;
              setConfidence(confidence);
              
              // Check for voice commands
              checkVoiceCommands(transcript, confidence);
            } else {
              interimTranscript += transcript;
            }
          }
          
          const fullTranscript = finalTranscript || interimTranscript;
          setTranscript(fullTranscript);
          
          if (onTranscript) {
            onTranscript(fullTranscript, confidence);
          }
        };
      } else {
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [continuous, onTranscript]);

  // Load voice commands for current language
  useEffect(() => {
    loadVoiceCommands();
  }, [currentLanguage]);

  // Set language for speech recognition
  useEffect(() => {
    if (recognitionRef.current) {
      recognitionRef.current.lang = currentLanguage;
      console.log(`🌍 Voice recognition language set to: ${currentLanguage}`);
    }
  }, [currentLanguage]);

  // Auto-start if enabled
  useEffect(() => {
    if (autoStart && isSupported) {
      startListening();
    }
  }, [autoStart, isSupported]);

  const loadVoiceCommands = async () => {
    try {
      const response = await fetch(`/api/i18n/voice/${currentLanguage}`);
      const data = await response.json();
      setVoiceCommands(data.voice.commands || []);
      console.log(`🎤 Loaded ${data.voice.commands.length} voice commands for ${currentLanguage}`);
    } catch (error) {
      console.error('Failed to load voice commands:', error);
    }
  };

  const checkVoiceCommands = (transcript, confidence) => {
    const normalizedTranscript = transcript.toLowerCase().trim();
    
    for (const command of voiceCommands) {
      const normalizedPhrase = command.phrase.toLowerCase();
      
      if (normalizedTranscript.includes(normalizedPhrase) && confidence >= command.confidence) {
        console.log(`🎤 Voice command detected: ${command.phrase} (${command.action})`);
        
        if (onCommand) {
          onCommand({
            action: command.action,
            phrase: command.phrase,
            transcript: normalizedTranscript,
            confidence: confidence,
            language: currentLanguage
          });
        }
        break;
      }
    }
  };

  const startListening = async () => {
    if (!isSupported || !recognitionRef.current) {
      setError(t('voice.not_supported', 'Voice recognition not supported'));
      return;
    }

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Setup audio analysis for volume visualization
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);
      
      microphoneRef.current.connect(analyserRef.current);
      analyserRef.current.fftSize = 256;
      
      // Start volume monitoring
      monitorVolume();
      
      // Start speech recognition
      recognitionRef.current.start();
      setTranscript('');
      setError(null);
    } catch (error) {
      setError(t('voice.permission_denied', 'Microphone permission denied'));
      console.error('Microphone access denied:', error);
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    setVolume(0);
  };

  const monitorVolume = () => {
    if (!analyserRef.current) return;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateVolume = () => {
      analyserRef.current.getByteFrequencyData(dataArray);
      
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }
      
      const average = sum / bufferLength;
      setVolume(average / 255); // Normalize to 0-1
      
      if (isListening) {
        animationRef.current = requestAnimationFrame(updateVolume);
      }
    };
    
    updateVolume();
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-8 h-8';
      case 'lg': return 'w-16 h-16';
      case 'xl': return 'w-20 h-20';
      default: return 'w-12 h-12';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'w-4 h-4';
      case 'lg': return 'w-8 h-8';
      case 'xl': return 'w-10 h-10';
      default: return 'w-6 h-6';
    }
  };

  if (!isSupported) {
    return (
      <div className={`flex items-center space-x-2 text-gray-500 dark:text-gray-400 ${className}`}>
        <ExclamationTriangleIcon className="w-5 h-5" />
        <span className="text-sm">{t('voice.not_supported', 'Voice recognition not supported')}</span>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center space-y-3 ${className}`}>
      {/* Voice Recognition Button */}
      <div className="relative">
        <button
          onClick={isListening ? stopListening : startListening}
          disabled={!!error}
          className={`
            ${getSizeClasses()}
            rounded-full flex items-center justify-center transition-all duration-300
            ${isListening 
              ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg' 
              : 'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg'
            }
            ${error ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            ${isListening ? 'animate-pulse' : ''}
          `}
          style={{
            transform: isListening ? `scale(${1 + volume * 0.3})` : 'scale(1)'
          }}
        >
          {isListening ? (
            <StopIcon className={getIconSize()} />
          ) : (
            <MicrophoneIcon className={getIconSize()} />
          )}
        </button>
        
        {/* Volume Indicator */}
        {isListening && (
          <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping opacity-75"></div>
        )}
      </div>

      {/* Status Indicator */}
      <div className="flex items-center space-x-2">
        {isListening && (
          <div className="flex items-center space-x-1 text-red-500">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">{t('voice.listening', 'Listening...')}</span>
          </div>
        )}
        
        {error && (
          <div className="flex items-center space-x-1 text-red-500">
            <XMarkIcon className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        )}
        
        {confidence > 0 && !isListening && (
          <div className="flex items-center space-x-1 text-green-500">
            <CheckCircleIcon className="w-4 h-4" />
            <span className="text-sm">{Math.round(confidence * 100)}% {t('voice.confidence', 'confidence')}</span>
          </div>
        )}
      </div>

      {/* Transcript Display */}
      {showTranscript && transcript && (
        <div className="max-w-md p-3 bg-gray-100 dark:bg-gray-800 rounded-lg border">
          <div className="flex items-start space-x-2">
            <SpeakerWaveIcon className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-900 dark:text-white">{transcript}</p>
              {confidence > 0 && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {t('voice.confidence', 'Confidence')}: {Math.round(confidence * 100)}%
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Available Commands */}
      {voiceCommands.length > 0 && (
        <details className="w-full max-w-md">
          <summary className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer hover:text-gray-900 dark:hover:text-white">
            {t('voice.available_commands', 'Available Commands')} ({voiceCommands.length})
          </summary>
          <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
            <div className="space-y-1">
              {voiceCommands.slice(0, 5).map((command, index) => (
                <div key={index} className="flex justify-between items-center text-xs">
                  <span className="text-gray-700 dark:text-gray-300">"{command.phrase}"</span>
                  <span className="text-gray-500 dark:text-gray-400">{command.action}</span>
                </div>
              ))}
              {voiceCommands.length > 5 && (
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                  +{voiceCommands.length - 5} {t('voice.more_commands', 'more commands')}
                </p>
              )}
            </div>
          </div>
        </details>
      )}
    </div>
  );
};

export default VoiceRecognition;
