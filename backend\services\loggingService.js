// Professional Logging Service
// Replaces console.log with structured logging

const fs = require('fs');
const path = require('path');

class LoggingService {
  constructor() {
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.logFile = process.env.LOG_FILE || './logs/app.log';
    this.enableConsole = process.env.NODE_ENV === 'development';
    
    // Create logs directory if it doesn't exist
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    // Log levels
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };
    
    this.currentLevel = this.levels[this.logLevel] || this.levels.info;
  }

  // Format log message
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...meta
    };

    return JSON.stringify(logEntry);
  }

  // Write to file
  writeToFile(formattedMessage) {
    try {
      fs.appendFileSync(this.logFile, formattedMessage + '\n');
    } catch (error) {
      // Fallback to console if file write fails
      console.error('Failed to write to log file:', error.message);
      console.log(formattedMessage);
    }
  }

  // Write to console with colors
  writeToConsole(level, message, meta = {}) {
    if (!this.enableConsole) return;

    const colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[35m', // Magenta
      trace: '\x1b[37m'  // White
    };

    const reset = '\x1b[0m';
    const timestamp = new Date().toISOString();
    const color = colors[level] || colors.info;
    
    const prefix = `${color}[${timestamp}] ${level.toUpperCase()}:${reset}`;
    
    if (Object.keys(meta).length > 0) {
      console.log(prefix, message, meta);
    } else {
      console.log(prefix, message);
    }
  }

  // Core logging method
  log(level, message, meta = {}) {
    if (this.levels[level] > this.currentLevel) {
      return; // Skip if level is below current threshold
    }

    const formattedMessage = this.formatMessage(level, message, meta);
    
    // Write to file
    this.writeToFile(formattedMessage);
    
    // Write to console
    this.writeToConsole(level, message, meta);
  }

  // Convenience methods
  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  trace(message, meta = {}) {
    this.log('trace', message, meta);
  }

  // HTTP request logging
  logRequest(req, res, responseTime) {
    const meta = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.employeeId,
      tenantId: req.user?.tenantId
    };

    const level = res.statusCode >= 400 ? 'warn' : 'info';
    this.log(level, `HTTP ${req.method} ${req.url}`, meta);
  }

  // Database operation logging
  logDatabase(operation, table, duration, error = null) {
    const meta = {
      operation,
      table,
      duration: `${duration}ms`,
      error: error?.message
    };

    const level = error ? 'error' : 'debug';
    const message = error 
      ? `Database ${operation} failed on ${table}` 
      : `Database ${operation} on ${table}`;
    
    this.log(level, message, meta);
  }

  // Authentication logging
  logAuth(action, userId, tenantId, success, details = {}) {
    const meta = {
      action,
      userId,
      tenantId,
      success,
      ...details
    };

    const level = success ? 'info' : 'warn';
    const message = `Authentication ${action} ${success ? 'successful' : 'failed'}`;
    
    this.log(level, message, meta);
  }

  // Payment logging
  logPayment(action, amount, method, transactionId, success, error = null) {
    const meta = {
      action,
      amount,
      method,
      transactionId,
      success,
      error: error?.message
    };

    const level = success ? 'info' : 'error';
    const message = `Payment ${action} ${success ? 'successful' : 'failed'}`;
    
    this.log(level, message, meta);
  }

  // Security event logging
  logSecurity(event, severity, details = {}) {
    const meta = {
      event,
      severity,
      ...details
    };

    const level = severity === 'high' || severity === 'critical' ? 'error' : 'warn';
    this.log(level, `Security event: ${event}`, meta);
  }

  // Performance logging
  logPerformance(operation, duration, threshold = 1000) {
    const meta = {
      operation,
      duration: `${duration}ms`,
      threshold: `${threshold}ms`,
      slow: duration > threshold
    };

    const level = duration > threshold ? 'warn' : 'debug';
    const message = duration > threshold 
      ? `Slow operation detected: ${operation}` 
      : `Performance: ${operation}`;
    
    this.log(level, message, meta);
  }

  // System health logging
  logHealth(component, status, metrics = {}) {
    const meta = {
      component,
      status,
      ...metrics
    };

    const level = status === 'healthy' ? 'info' : 'warn';
    this.log(level, `Health check: ${component} is ${status}`, meta);
  }

  // Cleanup old log files
  cleanup(daysToKeep = 30) {
    try {
      const logDir = path.dirname(this.logFile);
      const files = fs.readdirSync(logDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      files.forEach(file => {
        const filePath = path.join(logDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate && file.endsWith('.log')) {
          fs.unlinkSync(filePath);
          this.info(`Cleaned up old log file: ${file}`);
        }
      });
    } catch (error) {
      this.error('Failed to cleanup log files', { error: error.message });
    }
  }
}

// Create singleton instance
const logger = new LoggingService();

// Express middleware for request logging
const requestLoggingMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    logger.logRequest(req, res, responseTime);
  });
  
  next();
};

module.exports = {
  logger,
  requestLoggingMiddleware,
  LoggingService
};
