const fetch = require('node-fetch');

async function testBusinessRegistration() {
  try {
    console.log('🚀 Testing business registration...');
    
    const testBusiness = {
      business_name: `Test Restaurant ${Date.now()}`,
      business_type: 'restaurant',
      address: '123 Test Street',
      city: 'Test City',
      state: 'ON',
      zip_code: 'K1A 0A6',
      phone: '************',
      email: `test${Date.now()}@example.com`,
      admin_name: 'Test Admin',
      admin_email: `admin${Date.now()}@example.com`,
      admin_phone: '************',
      admin_pin: '123456',
      plan_type: 'starter',
      payment_method: 'credit_card'
    };

    console.log('📋 Registration data:', JSON.stringify(testBusiness, null, 2));

    const response = await fetch('http://localhost:4000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testBusiness)
    });

    console.log('📡 Response status:', response.status, response.statusText);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Registration successful!');
      console.log('🏢 Tenant ID:', result.tenant.id);
      console.log('👤 Admin ID:', result.admin.id);
      console.log('📍 Location ID:', result.location.id);
      console.log('🔑 Token received:', result.token ? 'Yes' : 'No');
      
      // Test login with the new PIN
      await testLogin(result.admin.pin || testBusiness.admin_pin, result.tenant.slug);
      
      return result;
    } else {
      const errorText = await response.text();
      console.log('❌ Registration failed:', errorText);
      return null;
    }
  } catch (error) {
    console.error('💥 Registration error:', error.message);
    return null;
  }
}

async function testLogin(pin, tenantSlug) {
  try {
    console.log('🔐 Testing login with PIN:', pin, 'Tenant:', tenantSlug);
    
    const response = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: pin,
        tenant_slug: tenantSlug
      })
    });

    console.log('📡 Login response status:', response.status, response.statusText);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Login successful!');
      console.log('👤 Employee:', result.employee.name, '(' + result.employee.role + ')');
      console.log('🏢 Tenant:', result.tenant.name);
      console.log('📍 Location:', result.location.name);
      return result;
    } else {
      const errorText = await response.text();
      console.log('❌ Login failed:', errorText);
      return null;
    }
  } catch (error) {
    console.error('💥 Login error:', error.message);
    return null;
  }
}

async function testTenantListing() {
  try {
    console.log('📋 Testing tenant listing...');
    
    // First login as super admin
    const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '123456'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Super admin login failed');
      return;
    }

    const loginResult = await loginResponse.json();
    console.log('✅ Super admin logged in');

    // Now fetch tenants
    const tenantsResponse = await fetch('http://localhost:4000/api/tenants', {
      headers: {
        'Authorization': `Bearer ${loginResult.token}`
      }
    });

    console.log('📡 Tenants response status:', tenantsResponse.status, tenantsResponse.statusText);

    if (tenantsResponse.ok) {
      const tenants = await tenantsResponse.json();
      console.log('✅ Fetched', tenants.length, 'tenants');
      tenants.forEach((tenant, index) => {
        console.log(`  ${index + 1}. ${tenant.business_name || tenant.name} (${tenant.email}) - ${tenant.status}`);
      });
      return tenants;
    } else {
      const errorText = await tenantsResponse.text();
      console.log('❌ Failed to fetch tenants:', errorText);
      return [];
    }
  } catch (error) {
    console.error('💥 Tenant listing error:', error.message);
    return [];
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting comprehensive business registration test...');
  console.log('='.repeat(60));
  
  // Test 1: Fetch existing tenants
  console.log('\n📋 Step 1: Fetching existing tenants...');
  await testTenantListing();
  
  // Test 2: Register new business
  console.log('\n🏢 Step 2: Registering new business...');
  const registrationResult = await testBusinessRegistration();
  
  // Test 3: Fetch tenants again to verify new business appears
  if (registrationResult) {
    console.log('\n🔄 Step 3: Re-fetching tenants to verify new business appears...');
    await testTenantListing();
  }
  
  console.log('\n🏁 Comprehensive test completed!');
  console.log('='.repeat(60));
}

// Run the test
runComprehensiveTest();
