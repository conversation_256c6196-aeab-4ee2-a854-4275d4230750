// Phase 3K: AI Cultural Intelligence System
// Cultural Analytics Dashboard Component

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  GlobeAltIcon,
  BrainIcon,
  HeartIcon,
  UserGroupIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ChartBarIcon,
  MapIcon,
  CalendarIcon,
  SparklesIcon,
  EyeIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const CulturalAnalyticsDashboard = ({ className = '' }) => {
  const { t, formatNumber } = useTranslation();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('north-america');
  const [selectedMetric, setSelectedMetric] = useState('overview');

  useEffect(() => {
    loadCulturalAnalytics();
    
    // Auto-refresh every 60 seconds
    const interval = setInterval(loadCulturalAnalytics, 60000);
    return () => clearInterval(interval);
  }, [selectedRegion]);

  const loadCulturalAnalytics = async () => {
    try {
      setLoading(true);
      
      // Simulate comprehensive cultural analytics data
      const mockAnalytics = {
        overview: {
          totalCulturalAnalyses: 2847,
          averageCulturalAccuracy: 91.3,
          emotionRecognitionAccuracy: 87.6,
          culturalAdaptationSuccess: 94.1,
          regionsSupported: 5,
          culturalGroupsIdentified: 23,
          averageResponseTime: 180, // milliseconds
          culturalSensitivityScore: 96.8
        },
        performance: {
          culturalAccuracy: {
            overall: 91.3,
            byRegion: {
              'north-america': 94.2,
              'east-asia': 89.7,
              'europe': 92.1,
              'middle-east': 88.4,
              'latin-america': 90.6
            },
            byContext: {
              'dining-preferences': 93.8,
              'communication-style': 89.2,
              'service-expectations': 91.7,
              'payment-preferences': 94.5,
              'cultural-events': 87.9
            }
          },
          emotionRecognition: {
            overall: 87.6,
            byEmotion: {
              'happy': 92.1,
              'frustrated': 85.3,
              'angry': 83.7,
              'excited': 89.4,
              'calm': 91.2,
              'confused': 82.8
            },
            byCulture: {
              'direct-cultures': 89.8,
              'indirect-cultures': 85.1,
              'expressive-cultures': 90.3,
              'reserved-cultures': 84.9
            }
          },
          culturalAdaptation: {
            uiAdaptation: 94.1,
            contentLocalization: 92.7,
            servicePersonalization: 89.3,
            marketingAdaptation: 91.8,
            communicationStyle: 88.6
          }
        },
        usage: {
          dailyAnalyses: [
            { date: '2025-06-04', analyses: 387, accuracy: 90.2, adaptations: 156 },
            { date: '2025-06-05', analyses: 423, accuracy: 91.8, adaptations: 178 },
            { date: '2025-06-06', analyses: 456, accuracy: 92.1, adaptations: 189 },
            { date: '2025-06-07', analyses: 398, accuracy: 90.7, adaptations: 167 },
            { date: '2025-06-08', analyses: 512, accuracy: 93.4, adaptations: 203 },
            { date: '2025-06-09', analyses: 478, accuracy: 91.9, adaptations: 192 },
            { date: '2025-06-10', analyses: 193, accuracy: 92.6, adaptations: 89 }
          ],
          regionalDistribution: [
            { region: 'North America', percentage: 42, analyses: 1196, satisfaction: 94.2 },
            { region: 'East Asia', percentage: 28, analyses: 797, satisfaction: 91.7 },
            { region: 'Europe', percentage: 18, analyses: 512, satisfaction: 93.1 },
            { region: 'Middle East', percentage: 8, analyses: 228, satisfaction: 89.4 },
            { region: 'Latin America', percentage: 4, analyses: 114, satisfaction: 90.8 }
          ],
          culturalEvents: [
            { event: 'Chinese New Year', impact: 'high', analyses: 234, adaptations: 89 },
            { event: 'Ramadan', impact: 'medium', analyses: 156, adaptations: 67 },
            { event: 'Christmas', impact: 'high', analyses: 298, adaptations: 112 },
            { event: 'Diwali', impact: 'medium', analyses: 134, adaptations: 56 },
            { event: 'Thanksgiving', impact: 'high', analyses: 267, adaptations: 98 }
          ]
        },
        insights: {
          culturalTrends: [
            {
              trend: 'Increased demand for cultural personalization',
              impact: 'high',
              growth: '+23%',
              recommendation: 'Expand cultural adaptation features'
            },
            {
              trend: 'Rising importance of emotional intelligence',
              impact: 'medium',
              growth: '+18%',
              recommendation: 'Enhance emotion recognition accuracy'
            },
            {
              trend: 'Growing multi-cultural customer base',
              impact: 'high',
              growth: '+31%',
              recommendation: 'Develop cross-cultural intelligence'
            }
          ],
          improvements: [
            {
              area: 'Emotion Recognition',
              issue: 'Lower accuracy for reserved cultures',
              recommendation: 'Enhance subtle emotion detection algorithms',
              priority: 'high',
              estimatedImprovement: '5-8%'
            },
            {
              area: 'Cultural Adaptation',
              issue: 'Limited Middle Eastern cultural patterns',
              recommendation: 'Expand Middle Eastern cultural database',
              priority: 'medium',
              estimatedImprovement: '3-5%'
            }
          ]
        }
      };
      
      setAnalytics(mockAnalytics);
      setError(null);
    } catch (error) {
      console.error('Failed to load cultural analytics:', error);
      setError('Failed to load cultural analytics');
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, icon: Icon, color = 'blue', trend = null }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400`}>
            {typeof value === 'number' ? formatNumber(value) : value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/20 rounded-lg`}>
          <Icon className={`w-6 h-6 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
      {trend && (
        <div className="flex items-center mt-2">
          {trend.direction === 'up' ? (
            <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
          ) : (
            <TrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
          )}
          <span className={`text-xs ${trend.direction === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {trend.value}
          </span>
        </div>
      )}
    </div>
  );

  const AccuracyChart = ({ data, title }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <div className="space-y-3">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
              {key.replace(/-/g, ' ')}
            </span>
            <div className="flex items-center space-x-2">
              <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${value}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white w-12 text-right">
                {value}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const RegionalDistributionChart = ({ data }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {t('cultural.regional_distribution', 'Regional Distribution')}
      </h3>
      <div className="space-y-3">
        {data.map((region, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="flex items-center space-x-3">
              <GlobeAltIcon className="w-5 h-5 text-blue-500" />
              <div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {region.region}
                </span>
                <div className="flex items-center space-x-4 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {region.analyses} analyses
                  </span>
                  <span className="text-xs text-green-600 dark:text-green-400">
                    {region.satisfaction}% satisfaction
                  </span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {region.percentage}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const CulturalTrendsCard = ({ trends }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {t('cultural.trends', 'Cultural Trends')}
      </h3>
      <div className="space-y-3">
        {trends.map((trend, index) => (
          <div key={index} className="border-l-4 border-blue-500 pl-4">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {trend.trend}
              </h4>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  trend.impact === 'high' 
                    ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                }`}>
                  {trend.impact}
                </span>
                <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                  {trend.growth}
                </span>
              </div>
            </div>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              {trend.recommendation}
            </p>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <BrainIcon className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">
            {t('cultural.loading_analytics', 'Loading cultural analytics...')}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2 text-red-500">
          <ExclamationTriangleIcon className="w-5 h-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('cultural.analytics_dashboard', 'Cultural Analytics Dashboard')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('cultural.analytics_subtitle', 'AI-powered cultural intelligence insights and performance')}
          </p>
        </div>
        <button
          onClick={loadCulturalAnalytics}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          {t('common.refresh', 'Refresh')}
        </button>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title={t('cultural.total_analyses', 'Total Analyses')}
          value={analytics.overview.totalCulturalAnalyses}
          subtitle={t('cultural.this_week', 'This week')}
          icon={BrainIcon}
          color="blue"
          trend={{ direction: 'up', value: '+23%' }}
        />
        <MetricCard
          title={t('cultural.cultural_accuracy', 'Cultural Accuracy')}
          value={`${analytics.overview.averageCulturalAccuracy}%`}
          subtitle={t('cultural.behavior_prediction', 'Behavior prediction')}
          icon={CheckCircleIcon}
          color="green"
          trend={{ direction: 'up', value: '+3.2%' }}
        />
        <MetricCard
          title={t('cultural.emotion_accuracy', 'Emotion Accuracy')}
          value={`${analytics.overview.emotionRecognitionAccuracy}%`}
          subtitle={t('cultural.emotion_recognition', 'Emotion recognition')}
          icon={HeartIcon}
          color="purple"
          trend={{ direction: 'up', value: '+1.8%' }}
        />
        <MetricCard
          title={t('cultural.adaptation_success', 'Adaptation Success')}
          value={`${analytics.overview.culturalAdaptationSuccess}%`}
          subtitle={t('cultural.ui_adaptation', 'UI adaptation')}
          icon={SparklesIcon}
          color="emerald"
          trend={{ direction: 'up', value: '+2.1%' }}
        />
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AccuracyChart
          data={analytics.performance.culturalAccuracy.byRegion}
          title={t('cultural.accuracy_by_region', 'Cultural Accuracy by Region')}
        />
        <AccuracyChart
          data={analytics.performance.emotionRecognition.byEmotion}
          title={t('cultural.emotion_recognition_accuracy', 'Emotion Recognition Accuracy')}
        />
      </div>

      {/* Regional Distribution and Cultural Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RegionalDistributionChart data={analytics.usage.regionalDistribution} />
        <CulturalTrendsCard trends={analytics.insights.culturalTrends} />
      </div>

      {/* Cultural Adaptation Performance */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('cultural.adaptation_performance', 'Cultural Adaptation Performance')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {Object.entries(analytics.performance.culturalAdaptation).map(([key, value]) => (
            <div key={key} className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {value}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cultural Events Impact */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('cultural.events_impact', 'Cultural Events Impact')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {analytics.usage.culturalEvents.map((event, index) => (
            <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <CalendarIcon className="w-4 h-4 text-purple-500" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {event.event}
                </span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600 dark:text-gray-400">Impact</span>
                  <span className={`font-medium ${
                    event.impact === 'high' ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {event.impact}
                  </span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600 dark:text-gray-400">Analyses</span>
                  <span className="font-medium text-gray-900 dark:text-white">{event.analyses}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600 dark:text-gray-400">Adaptations</span>
                  <span className="font-medium text-blue-600">{event.adaptations}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CulturalAnalyticsDashboard;
