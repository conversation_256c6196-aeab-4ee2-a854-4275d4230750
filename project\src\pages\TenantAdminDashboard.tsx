import React, { useState, useEffect, useCallback } from 'react';
import {
  BarChart3, Users, DollarSign, ShoppingCart, TrendingUp, TrendingDown,
  Calendar, Clock, MapPin, Settings, Bell, Search, Filter, Download,
  Eye, Edit, Trash2, Plus, RefreshCw, AlertTriangle, CheckCircle,
  Package, Utensils, CreditCard, Smartphone, Wifi, WifiOff,
  Star, Heart, MessageSquare, Phone, Mail, Globe, Shield,
  PieChart, LineChart, Activity, Zap, Target, Award,
  Building, Store, UserCheck, Layers, Database, Server,
  Lock, Key, LogOut, Menu, X, Sun, Moon, ChevronDown,
  Home, FileText, Calendar as CalendarIcon, Briefcase
} from 'lucide-react';

interface TenantData {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  theme: 'light' | 'dark';
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl?: string;
  };
}

interface DashboardMetrics {
  todaySales: number;
  todayOrders: number;
  activeStaff: number;
  occupiedTables: number;
  totalTables: number;
  averageOrderValue: number;
  customerSatisfaction: number;
  kitchenEfficiency: number;
  recentOrders: Array<{
    id: string;
    table: string;
    items: string;
    amount: number;
    status: 'pending' | 'preparing' | 'ready' | 'served';
    timestamp: string;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    revenue: number;
  }>;
  staffPerformance: Array<{
    id: string;
    name: string;
    ordersServed: number;
    efficiency: number;
    rating: number;
  }>;
  salesTrend: Array<{
    time: string;
    sales: number;
    orders: number;
  }>;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: 'tenant_admin' | 'manager' | 'staff';
  tenantId: string;
  permissions: string[];
  lastLogin?: string;
  isActive: boolean;
}

const TenantAdminDashboard: React.FC = () => {
  // State Management
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<TenantData | null>(null);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeSection, setActiveSection] = useState('overview');
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    timestamp: string;
    read: boolean;
  }>>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // WebSocket connection for real-time updates
  const [ws, setWs] = useState<WebSocket | null>(null);

  // Authentication and session management
  const [sessionTimeout, setSessionTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Load initial data
  useEffect(() => {
    initializeDashboard();
    setupWebSocket();
    setupSessionManagement();
    
    return () => {
      if (ws) ws.close();
      if (sessionTimeout) clearTimeout(sessionTimeout);
    };
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (isAuthenticated) {
        refreshMetrics();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  const initializeDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check authentication
      const token = localStorage.getItem('tenantAuthToken');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Load user data
      const userResponse = await fetch('/api/tenant/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!userResponse.ok) {
        throw new Error('Authentication failed');
      }

      const userData = await userResponse.json();
      setUser(userData);
      setIsAuthenticated(true);

      // Load tenant data
      const tenantResponse = await fetch(`/api/tenant/${userData.tenantId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (tenantResponse.ok) {
        const tenantData = await tenantResponse.json();
        setTenant(tenantData);
        setTheme(tenantData.theme || 'light');
      }

      // Load dashboard metrics
      await refreshMetrics();

    } catch (error) {
      console.error('Dashboard initialization error:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard');
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  };

  const refreshMetrics = async () => {
    try {
      const token = localStorage.getItem('tenantAuthToken');
      if (!token || !user) return;

      const response = await fetch(`/api/tenant/${user.tenantId}/metrics`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const metricsData = await response.json();
        setMetrics(metricsData);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Error refreshing metrics:', error);
    }
  };

  const setupWebSocket = () => {
    try {
      const token = localStorage.getItem('tenantAuthToken');
      if (!token) return;

      const wsUrl = `ws://localhost:4000/ws/tenant?token=${token}`;
      const websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log('WebSocket connected for real-time updates');
        setWs(websocket);
      };

      websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      websocket.onclose = () => {
        console.log('WebSocket disconnected');
        setWs(null);
        // Attempt to reconnect after 5 seconds
        setTimeout(setupWebSocket, 5000);
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Error setting up WebSocket:', error);
    }
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'metrics_update':
        setMetrics(prev => prev ? { ...prev, ...data.payload } : null);
        break;
      case 'new_order':
        addNotification('info', `New order received: ${data.payload.orderId}`, new Date().toISOString());
        break;
      case 'order_status_change':
        addNotification('success', `Order ${data.payload.orderId} status: ${data.payload.status}`, new Date().toISOString());
        break;
      case 'low_inventory':
        addNotification('warning', `Low inventory alert: ${data.payload.item}`, new Date().toISOString());
        break;
      case 'system_alert':
        addNotification('error', data.payload.message, new Date().toISOString());
        break;
      default:
        console.log('Unknown WebSocket message type:', data.type);
    }
  };

  const setupSessionManagement = () => {
    const resetSessionTimeout = () => {
      if (sessionTimeout) clearTimeout(sessionTimeout);
      
      // Set session timeout to 30 minutes of inactivity
      const timeout = setTimeout(() => {
        handleLogout();
      }, 30 * 60 * 1000);
      
      setSessionTimeout(timeout);
    };

    // Reset timeout on user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, resetSessionTimeout, true);
    });

    resetSessionTimeout();
  };

  const addNotification = (type: 'info' | 'warning' | 'error' | 'success', message: string, timestamp: string) => {
    const notification = {
      id: Date.now().toString(),
      type,
      message,
      timestamp,
      read: false
    };
    
    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep only 10 notifications
  };

  const handleLogout = async () => {
    try {
      const token = localStorage.getItem('tenantAuthToken');
      if (token) {
        await fetch('/api/tenant/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('tenantAuthToken');
      setIsAuthenticated(false);
      setUser(null);
      setTenant(null);
      setMetrics(null);
      if (ws) ws.close();
      if (sessionTimeout) clearTimeout(sessionTimeout);
      // Redirect to login
      window.location.href = '/tenant/login';
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    
    // Save theme preference
    if (tenant) {
      fetch(`/api/tenant/${tenant.id}/settings`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('tenantAuthToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ theme: newTheme })
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'served': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'info': return <Bell className="h-4 w-4 text-blue-500" />;
      default: return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className={`text-lg ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            Loading Tenant Dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !isAuthenticated) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className={`text-xl font-semibold mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-900'}`}>
            Authentication Required
          </h2>
          <p className={`text-gray-600 mb-4 ${theme === 'dark' ? 'text-gray-400' : ''}`}>
            {error || 'Please log in to access the tenant dashboard'}
          </p>
          <button
            onClick={() => window.location.href = '/tenant/login'}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Main Dashboard Interface
  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>

        {/* Sidebar Header */}
        <div className={`flex items-center justify-between p-4 border-b ${
          theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center">
            {tenant?.branding?.logoUrl ? (
              <img src={tenant.branding.logoUrl} alt={tenant.name} className="h-8 w-8 rounded" />
            ) : (
              <Store className="h-8 w-8 text-blue-600" />
            )}
            <div className="ml-3">
              <h2 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {tenant?.name || 'RestroFlow'}
              </h2>
              <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                Tenant Admin
              </p>
            </div>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className={`p-1 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Navigation Menu */}
        <nav className="mt-4 px-4">
          <div className="space-y-2">
            {[
              { id: 'overview', name: 'Overview', icon: Home },
              { id: 'orders', name: 'Live Orders', icon: ShoppingCart },
              { id: 'analytics', name: 'Analytics', icon: BarChart3 },
              { id: 'inventory', name: 'Inventory', icon: Package },
              { id: 'staff', name: 'Staff Management', icon: Users },
              { id: 'customers', name: 'Customers', icon: UserCheck },
              { id: 'reports', name: 'Reports', icon: FileText },
              { id: 'marketing', name: 'Marketing', icon: Target },
              { id: 'reservations', name: 'Reservations', icon: CalendarIcon },
              { id: 'settings', name: 'Settings', icon: Settings }
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveSection(item.id)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeSection === item.id
                    ? `${tenant?.branding?.primaryColor ? 'bg-blue-100 text-blue-700' : 'bg-blue-100 text-blue-700'} ${
                        theme === 'dark' ? 'bg-blue-900 text-blue-200' : ''
                      }`
                    : `${theme === 'dark' ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`
                }`}
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </button>
            ))}
          </div>
        </nav>

        {/* User Profile Section */}
        <div className={`absolute bottom-0 left-0 right-0 p-4 border-t ${
          theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
            }`}>
              <Users className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`} />
            </div>
            <div className="ml-3 flex-1">
              <p className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {user?.name}
              </p>
              <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                {user?.role?.replace('_', ' ').toUpperCase()}
              </p>
            </div>
            <button
              onClick={handleLogout}
              className={`p-1 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
              title="Logout"
            >
              <LogOut className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`${sidebarOpen ? 'ml-64' : 'ml-0'} transition-all duration-300 ease-in-out`}>
        {/* Top Header */}
        <header className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} shadow-sm border-b`}>
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Left side */}
              <div className="flex items-center">
                <button
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className={`p-2 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  <Menu className="h-5 w-5" />
                </button>

                <div className="ml-4">
                  <h1 className={`text-xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                    {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
                  </h1>
                  <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    Last updated: {lastUpdate.toLocaleTimeString()}
                  </p>
                </div>
              </div>

              {/* Right side */}
              <div className="flex items-center space-x-4">
                {/* Search */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className={`h-4 w-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`} />
                  </div>
                  <input
                    type="text"
                    className={`block w-64 pl-10 pr-3 py-2 border rounded-md text-sm ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500'
                    }`}
                    placeholder="Search orders, customers, products..."
                  />
                </div>

                {/* Notifications */}
                <div className="relative">
                  <button className={`p-2 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'} relative`}>
                    <Bell className="h-5 w-5" />
                    {notifications.filter(n => !n.read).length > 0 && (
                      <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                    )}
                  </button>
                </div>

                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className={`p-2 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
                </button>

                {/* Refresh */}
                <button
                  onClick={refreshMetrics}
                  className={`p-2 rounded-md ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  <RefreshCw className="h-5 w-5" />
                </button>

                {/* Connection Status */}
                <div className="flex items-center">
                  {ws ? (
                    <div className="flex items-center text-green-600">
                      <Wifi className="h-4 w-4 mr-1" />
                      <span className="text-xs">Live</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-red-600">
                      <WifiOff className="h-4 w-4 mr-1" />
                      <span className="text-xs">Offline</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Dashboard Content */}
        <main className="p-4 sm:p-6 lg:p-8">
          {activeSection === 'overview' && (
            <div className="space-y-6">
              {/* Key Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Today's Sales */}
                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <DollarSign className="h-5 w-5 text-green-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Today's Sales
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        ${metrics?.todaySales?.toLocaleString() || '0'}
                      </p>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-600 ml-1">+12.5%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Today's Orders */}
                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <ShoppingCart className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Today's Orders
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {metrics?.todayOrders || 0}
                      </p>
                      <div className="flex items-center mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-600 ml-1">+8.2%</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Active Staff */}
                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-purple-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Active Staff
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {metrics?.activeStaff || 0}
                      </p>
                      <div className="flex items-center mt-1">
                        <Clock className="h-4 w-4 text-blue-500" />
                        <span className="text-sm text-blue-600 ml-1">On duty</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Table Occupancy */}
                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <Utensils className="h-5 w-5 text-orange-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Table Occupancy
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {metrics?.occupiedTables || 0}/{metrics?.totalTables || 0}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{
                              width: `${metrics?.totalTables ? (metrics.occupiedTables / metrics.totalTables) * 100 : 0}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-orange-600">
                          {metrics?.totalTables ? Math.round((metrics.occupiedTables / metrics.totalTables) * 100) : 0}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Charts and Live Data */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Sales Trend Chart */}
                <div className={`lg:col-span-2 ${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                      Sales Trend (Today)
                    </h3>
                    <div className="flex space-x-2">
                      <button className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded">Hourly</button>
                      <button className={`px-3 py-1 text-xs rounded ${
                        theme === 'dark' ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-500 hover:bg-gray-100'
                      }`}>Daily</button>
                    </div>
                  </div>
                  <div className="h-64 flex items-end space-x-2">
                    {metrics?.salesTrend?.map((hour, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-blue-500 rounded-t transition-all duration-300"
                          style={{ height: `${Math.max((hour.sales / 1000) * 100, 5)}%` }}
                        ></div>
                        <span className={`text-xs mt-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          {hour.time}
                        </span>
                      </div>
                    )) || Array.from({ length: 12 }, (_, i) => (
                      <div key={i} className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-gray-300 rounded-t" style={{ height: '20%' }}></div>
                        <span className={`text-xs mt-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          {i + 8}:00
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Live Orders & Notifications */}
                <div className="space-y-6">
                  {/* Live Orders */}
                  <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                    <h3 className={`text-lg font-medium mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                      Live Orders
                    </h3>
                    <div className="space-y-3">
                      {metrics?.recentOrders?.slice(0, 5).map((order) => (
                        <div key={order.id} className={`p-3 rounded-lg border-l-4 ${
                          order.status === 'pending' ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20' :
                          order.status === 'preparing' ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' :
                          order.status === 'ready' ? 'border-green-400 bg-green-50 dark:bg-green-900/20' :
                          'border-gray-400 bg-gray-50 dark:bg-gray-700'
                        }`}>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                                {order.table}
                              </p>
                              <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                                {order.items}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                                ${order.amount.toFixed(2)}
                              </p>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                                {order.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      )) || (
                        <div className={`text-center py-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          <ShoppingCart className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No recent orders</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recent Notifications */}
                  <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                    <h3 className={`text-lg font-medium mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                      Recent Notifications
                    </h3>
                    <div className="space-y-3">
                      {notifications.slice(0, 5).map((notification) => (
                        <div key={notification.id} className={`flex items-start space-x-3 p-3 rounded-lg ${
                          theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'
                        }`}>
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1">
                            <p className={`text-sm ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                              {notification.message}
                            </p>
                            <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                              {new Date(notification.timestamp).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      ))}
                      {notifications.length === 0 && (
                        <div className={`text-center py-4 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No recent notifications</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Live Orders Section */}
          {activeSection === 'orders' && (
            <div className="space-y-6">
              <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow`}>
                <div className={`px-6 py-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
                  <div className="flex items-center justify-between">
                    <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                      Live Orders Management
                    </h3>
                    <div className="flex space-x-2">
                      <button className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded">All</button>
                      <button className={`px-3 py-1 text-xs rounded ${
                        theme === 'dark' ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-500 hover:bg-gray-100'
                      }`}>Pending</button>
                      <button className={`px-3 py-1 text-xs rounded ${
                        theme === 'dark' ? 'text-gray-400 hover:bg-gray-700' : 'text-gray-500 hover:bg-gray-100'
                      }`}>Preparing</button>
                    </div>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className={theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}>
                      <tr>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Order ID</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Table</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Items</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Amount</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Status</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Time</th>
                        <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                          theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                        }`}>Actions</th>
                      </tr>
                    </thead>
                    <tbody className={`divide-y ${theme === 'dark' ? 'bg-gray-800 divide-gray-700' : 'bg-white divide-gray-200'}`}>
                      {metrics?.recentOrders?.map((order) => (
                        <tr key={order.id}>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                            theme === 'dark' ? 'text-white' : 'text-gray-900'
                          }`}>
                            #{order.id}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                          }`}>
                            {order.table}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                          }`}>
                            {order.items}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                            theme === 'dark' ? 'text-white' : 'text-gray-900'
                          }`}>
                            ${order.amount.toFixed(2)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                              {order.status}
                            </span>
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
                          }`}>
                            {order.timestamp}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button className="text-blue-600 hover:text-blue-900">
                                <Eye className="h-4 w-4" />
                              </button>
                              <button className="text-green-600 hover:text-green-900">
                                <Edit className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      )) || (
                        <tr>
                          <td colSpan={7} className={`px-6 py-8 text-center ${
                            theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>No orders found</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Analytics Section */}
          {activeSection === 'analytics' && (
            <div className="space-y-6">
              {/* Performance Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Avg Order Value
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        ${metrics?.averageOrderValue?.toFixed(2) || '0.00'}
                      </p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-blue-500" />
                  </div>
                </div>

                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Customer Satisfaction
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {metrics?.customerSatisfaction?.toFixed(1) || '0.0'}/5
                      </p>
                    </div>
                    <Star className="h-8 w-8 text-yellow-500" />
                  </div>
                </div>

                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Kitchen Efficiency
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        {metrics?.kitchenEfficiency || 0}%
                      </p>
                    </div>
                    <Zap className="h-8 w-8 text-green-500" />
                  </div>
                </div>

                <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                        Table Turnover
                      </p>
                      <p className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        2.3x
                      </p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-500" />
                  </div>
                </div>
              </div>

              {/* Top Products */}
              <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-6`}>
                <h3 className={`text-lg font-medium mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  Top Performing Products
                </h3>
                <div className="space-y-4">
                  {metrics?.topProducts?.map((product, index) => (
                    <div key={product.id} className={`flex items-center justify-between p-4 rounded-lg ${
                      theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'
                    }`}>
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                          index === 0 ? 'bg-yellow-100 text-yellow-600' :
                          index === 1 ? 'bg-gray-100 text-gray-600' :
                          index === 2 ? 'bg-orange-100 text-orange-600' :
                          'bg-blue-100 text-blue-600'
                        }`}>
                          <span className="text-sm font-medium">#{index + 1}</span>
                        </div>
                        <div>
                          <p className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                            {product.name}
                          </p>
                          <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                            {product.sales} sales
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                          ${product.revenue.toFixed(2)}
                        </p>
                        <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                          Revenue
                        </p>
                      </div>
                    </div>
                  )) || (
                    <div className={`text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No product data available</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Other sections placeholder */}
          {!['overview', 'orders', 'analytics'].includes(activeSection) && (
            <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow p-8 text-center`}>
              <div className={`text-6xl mb-4 ${theme === 'dark' ? 'text-gray-600' : 'text-gray-300'}`}>
                🚧
              </div>
              <h3 className={`text-xl font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)} Section
              </h3>
              <p className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                This section is under development and will be available soon.
              </p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default TenantAdminDashboard;
