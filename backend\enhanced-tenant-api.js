// Enhanced Tenant Management API - Phase 1 & 2 Implementation
// Comprehensive tenant management with advanced features

const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

// Middleware for super admin authentication
const requireSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Access denied: Super admin privileges required' });
  }
  next();
};

// Audit logging function
const logAuditAction = async (tenantId, adminUserId, adminEmail, action, entityType, entityId, oldValues, newValues, reason, ipAddress, userAgent) => {
  try {
    await pool.query(`
      INSERT INTO tenant_audit_logs (
        tenant_id, admin_user_id, admin_email, action, entity_type, entity_id,
        old_values, new_values, reason, ip_address, user_agent, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
    `, [
      tenantId, adminUserId, adminEmail, action, entityType, entityId,
      JSON.stringify(oldValues), JSON.stringify(newValues), reason,
      ipAddress, userAgent
    ]);
  } catch (error) {
    console.error('💥 Error logging audit action:', error);
  }
};

// Enhanced tenant listing with filtering, pagination, and search
const getEnhancedTenants = async (req, res) => {
  try {
    console.log('🏢 Fetching enhanced tenants with advanced filtering...');
    
    // Extract query parameters
    const {
      page = 1,
      limit = 20,
      search = '',
      status = '',
      plan_type = '',
      business_type = '',
      sort_by = 'created_at',
      sort_order = 'DESC',
      date_from = '',
      date_to = ''
    } = req.query;
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // Build WHERE clause for filtering
    let whereConditions = ['t.deleted_at IS NULL'];
    let queryParams = [];
    let paramIndex = 1;
    
    // Search functionality
    if (search) {
      whereConditions.push(`(
        t.name ILIKE $${paramIndex} OR 
        t.email ILIKE $${paramIndex} OR 
        t.slug ILIKE $${paramIndex} OR
        t.phone ILIKE $${paramIndex} OR
        t.city ILIKE $${paramIndex} OR
        t.description ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }
    
    // Status filter
    if (status) {
      whereConditions.push(`t.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }
    
    // Plan type filter
    if (plan_type) {
      whereConditions.push(`ts.plan_type = $${paramIndex}`);
      queryParams.push(plan_type);
      paramIndex++;
    }
    
    // Business type filter
    if (business_type) {
      whereConditions.push(`t.business_type = $${paramIndex}`);
      queryParams.push(business_type);
      paramIndex++;
    }
    
    // Date range filter
    if (date_from) {
      whereConditions.push(`t.created_at >= $${paramIndex}`);
      queryParams.push(date_from);
      paramIndex++;
    }
    
    if (date_to) {
      whereConditions.push(`t.created_at <= $${paramIndex}`);
      queryParams.push(date_to);
      paramIndex++;
    }
    
    const whereClause = whereConditions.join(' AND ');
    
    // Validate sort parameters
    const validSortFields = ['name', 'created_at', 'last_login_at', 'status', 'business_type', 'plan_type'];
    const sortField = validSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    
    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total
      FROM tenants t
      LEFT JOIN tenant_subscriptions ts ON t.id = ts.tenant_id
      WHERE ${whereClause}
    `;
    
    const countResult = await pool.query(countQuery, queryParams);
    const totalCount = parseInt(countResult.rows[0].total);
    
    // Get tenants with comprehensive data
    const tenantsQuery = `
      SELECT 
        t.*,
        ts.plan_type,
        ts.plan_status,
        ts.monthly_price,
        ts.yearly_price,
        ts.trial_ends_at,
        ts.next_billing_date,
        ts.features,
        ts.max_employees,
        ts.max_locations,
        COUNT(DISTINCT tl.id) as location_count,
        COUNT(DISTINCT e.id) as employee_count,
        COUNT(DISTINCT CASE WHEN e.is_active = true THEN e.id END) as active_employee_count
      FROM tenants t
      LEFT JOIN tenant_subscriptions ts ON t.id = ts.tenant_id
      LEFT JOIN tenant_locations tl ON t.id = tl.tenant_id
      LEFT JOIN employees e ON t.id = e.tenant_id
      WHERE ${whereClause}
      GROUP BY t.id, ts.plan_type, ts.plan_status, ts.monthly_price, ts.yearly_price, 
               ts.trial_ends_at, ts.next_billing_date, ts.features, ts.max_employees, ts.max_locations
      ORDER BY ${sortField === 'plan_type' ? 'ts.plan_type' : `t.${sortField}`} ${sortDirection}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const tenantsResult = await pool.query(tenantsQuery, queryParams);
    
    // Get revenue data for the last 30 days (mock data for now)
    const tenants = tenantsResult.rows.map(tenant => {
      // Calculate trial status
      const isTrialActive = tenant.trial_ends_at && new Date(tenant.trial_ends_at) > new Date();
      const trialDaysLeft = isTrialActive ? 
        Math.ceil((new Date(tenant.trial_ends_at) - new Date()) / (1000 * 60 * 60 * 24)) : 0;
      
      return {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        email: tenant.email,
        phone: tenant.phone,
        address_line1: tenant.address_line1,
        address_line2: tenant.address_line2,
        city: tenant.city,
        state: tenant.state,
        postal_code: tenant.postal_code,
        country: tenant.country,
        status: tenant.status,
        business_type: tenant.business_type,
        timezone: tenant.timezone,
        website: tenant.website,
        description: tenant.description,
        logo_url: tenant.logo_url,
        
        // Subscription details
        plan_type: tenant.plan_type || 'starter',
        plan_status: tenant.plan_status || 'active',
        monthly_price: parseFloat(tenant.monthly_price) || 29.99,
        yearly_price: parseFloat(tenant.yearly_price) || 299.99,
        trial_ends_at: tenant.trial_ends_at,
        trial_days_left: trialDaysLeft,
        is_trial_active: isTrialActive,
        next_billing_date: tenant.next_billing_date,
        features: tenant.features || {},
        max_employees: tenant.max_employees || 5,
        max_locations: tenant.max_locations || 1,
        
        // Metrics
        location_count: parseInt(tenant.location_count) || 0,
        employee_count: parseInt(tenant.employee_count) || 0,
        active_employee_count: parseInt(tenant.active_employee_count) || 0,
        orders_30d: Math.floor(Math.random() * 200), // Mock data
        revenue_30d: Math.floor(Math.random() * 10000), // Mock data
        avg_order_value: Math.floor(Math.random() * 50) + 20, // Mock data
        
        // Timestamps
        last_login_at: tenant.last_login_at,
        suspended_at: tenant.suspended_at,
        suspended_reason: tenant.suspended_reason,
        created_at: tenant.created_at,
        updated_at: tenant.updated_at
      };
    });
    
    console.log(`✅ Successfully fetched ${tenants.length} tenants with enhanced data`);
    
    // Return paginated response with metadata
    res.json({
      success: true,
      data: {
        tenants,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total_count: totalCount,
          total_pages: Math.ceil(totalCount / parseInt(limit)),
          has_next: parseInt(page) < Math.ceil(totalCount / parseInt(limit)),
          has_prev: parseInt(page) > 1
        },
        filters: {
          search,
          status,
          plan_type,
          business_type,
          sort_by: sortField,
          sort_order: sortDirection,
          date_from,
          date_to
        },
        summary: {
          total_tenants: totalCount,
          active_tenants: tenants.filter(t => t.status === 'active').length,
          trial_tenants: tenants.filter(t => t.is_trial_active).length,
          suspended_tenants: tenants.filter(t => t.status === 'suspended').length
        }
      }
    });
    
  } catch (error) {
    console.error('💥 Error fetching enhanced tenants:', error);
    res.status(500).json({ 
      success: false,
      error: 'Internal server error', 
      message: error.message 
    });
  }
};

// Enhanced tenant creation with comprehensive setup
const createEnhancedTenant = async (req, res) => {
  try {
    console.log('🏢 Creating enhanced tenant with comprehensive setup...');

    const {
      // Basic Information
      name,
      slug,
      email,
      phone,
      business_type = 'restaurant',
      timezone = 'America/New_York',
      website,
      description,

      // Address Information
      address_line1,
      address_line2,
      city,
      state,
      postal_code,
      country = 'US',

      // Subscription Information
      plan_type = 'starter',
      billing_cycle = 'monthly',
      trial_days = 14,

      // Admin User Information
      admin_name,
      admin_email,
      admin_pin = '123456'
    } = req.body;

    // Validate required fields
    if (!name || !slug || !email || !admin_name || !admin_email) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        required: ['name', 'slug', 'email', 'admin_name', 'admin_email']
      });
    }

    // Check if slug already exists
    const existingTenant = await pool.query('SELECT id FROM tenants WHERE slug = $1', [slug]);
    if (existingTenant.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Tenant slug already exists'
      });
    }

    // Check if email already exists
    const existingEmail = await pool.query('SELECT id FROM tenants WHERE email = $1', [email]);
    if (existingEmail.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }

    // Begin transaction
    await pool.query('BEGIN');

    try {
      // Create tenant
      const tenantResult = await pool.query(`
        INSERT INTO tenants (
          name, slug, email, phone, business_type, timezone, website, description,
          address_line1, address_line2, city, state, postal_code, country,
          status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, 'active', NOW(), NOW())
        RETURNING *
      `, [
        name, slug, email, phone, business_type, timezone, website, description,
        address_line1, address_line2, city, state, postal_code, country
      ]);

      const newTenant = tenantResult.rows[0];

      // Create subscription
      const subscriptionPrices = {
        starter: { monthly: 29.99, yearly: 299.99, max_employees: 5, max_locations: 1 },
        pro: { monthly: 79.99, yearly: 799.99, max_employees: 25, max_locations: 3 },
        enterprise: { monthly: 199.99, yearly: 1999.99, max_employees: 100, max_locations: 10 }
      };

      const planDetails = subscriptionPrices[plan_type] || subscriptionPrices.starter;
      const trialEndsAt = new Date(Date.now() + trial_days * 24 * 60 * 60 * 1000);

      await pool.query(`
        INSERT INTO tenant_subscriptions (
          tenant_id, plan_type, plan_status, billing_cycle, monthly_price, yearly_price,
          trial_days, max_employees, max_locations, trial_ends_at, next_billing_date,
          features, created_at, updated_at
        ) VALUES ($1, $2, 'trial', $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
      `, [
        newTenant.id, plan_type, billing_cycle, planDetails.monthly, planDetails.yearly,
        trial_days, planDetails.max_employees, planDetails.max_locations, trialEndsAt, trialEndsAt,
        JSON.stringify({
          pos: true,
          inventory: plan_type !== 'starter',
          analytics: plan_type === 'enterprise',
          multi_location: plan_type !== 'starter',
          api_access: plan_type === 'enterprise',
          priority_support: plan_type === 'enterprise'
        })
      ]);

      // Create primary location
      await pool.query(`
        INSERT INTO tenant_locations (
          tenant_id, name, address_line1, address_line2, city, state, postal_code, country,
          phone, email, is_primary, status, timezone, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, true, 'active', $11, NOW(), NOW())
      `, [
        newTenant.id, `${name} - Main Location`, address_line1, address_line2,
        city, state, postal_code, country, phone, email, timezone
      ]);

      // Create admin user
      const bcrypt = require('bcryptjs');
      const hashedPin = await bcrypt.hash(admin_pin, 10);

      await pool.query(`
        INSERT INTO employees (
          tenant_id, name, email, pin, role, is_active, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, 'tenant_admin', true, NOW(), NOW())
      `, [newTenant.id, admin_name, admin_email, hashedPin]);

      // Log audit action
      await logAuditAction(
        newTenant.id, req.user.id, req.user.email, 'create', 'tenant', newTenant.id,
        null, newTenant, 'Tenant created by super admin', req.ip, req.get('User-Agent')
      );

      await pool.query('COMMIT');

      console.log(`✅ Created enhanced tenant: ${name} (ID: ${newTenant.id})`);

      res.status(201).json({
        success: true,
        data: {
          tenant: newTenant,
          plan_type,
          trial_ends_at: trialEndsAt,
          admin_pin,
          message: 'Tenant created successfully with full setup'
        }
      });

    } catch (error) {
      await pool.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('💥 Error creating enhanced tenant:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create tenant',
      message: error.message
    });
  }
};

// Bulk operations for tenant management
const bulkTenantOperations = async (req, res) => {
  try {
    console.log('🔄 Processing bulk tenant operations...');

    const { action, tenant_ids, data = {} } = req.body;

    if (!action || !tenant_ids || !Array.isArray(tenant_ids) || tenant_ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid bulk operation request',
        required: ['action', 'tenant_ids (array)']
      });
    }

    const validActions = ['activate', 'suspend', 'delete', 'update_plan', 'export'];
    if (!validActions.includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action',
        valid_actions: validActions
      });
    }

    let results = [];
    let errors = [];

    // Begin transaction for bulk operations
    await pool.query('BEGIN');

    try {
      for (const tenantId of tenant_ids) {
        try {
          let result = null;

          switch (action) {
            case 'activate':
              result = await pool.query(`
                UPDATE tenants SET status = 'active', updated_at = NOW()
                WHERE id = $1 AND deleted_at IS NULL
                RETURNING id, name, status
              `, [tenantId]);
              break;

            case 'suspend':
              const reason = data.reason || 'Bulk suspension by admin';
              result = await pool.query(`
                UPDATE tenants
                SET status = 'suspended', suspended_at = NOW(), suspended_reason = $2, updated_at = NOW()
                WHERE id = $1 AND deleted_at IS NULL
                RETURNING id, name, status
              `, [tenantId, reason]);
              break;

            case 'delete':
              if (!data.confirm_delete) {
                throw new Error('Confirmation required for bulk delete');
              }
              // Soft delete
              result = await pool.query(`
                UPDATE tenants
                SET deleted_at = NOW(), status = 'deleted', updated_at = NOW()
                WHERE id = $1 AND deleted_at IS NULL
                RETURNING id, name, status
              `, [tenantId]);
              break;

            case 'update_plan':
              if (!data.plan_type) {
                throw new Error('Plan type required for plan update');
              }
              result = await pool.query(`
                UPDATE tenant_subscriptions
                SET plan_type = $2, updated_at = NOW()
                WHERE tenant_id = $1
                RETURNING tenant_id, plan_type
              `, [tenantId, data.plan_type]);
              break;
          }

          if (result && result.rows.length > 0) {
            results.push({
              tenant_id: tenantId,
              success: true,
              data: result.rows[0]
            });

            // Log audit action
            await logAuditAction(
              tenantId, req.user.id, req.user.email, `bulk_${action}`, 'tenant', tenantId,
              null, result.rows[0], data.reason || `Bulk ${action} operation`,
              req.ip, req.get('User-Agent')
            );
          } else {
            errors.push({
              tenant_id: tenantId,
              error: 'Tenant not found or already processed'
            });
          }

        } catch (error) {
          errors.push({
            tenant_id: tenantId,
            error: error.message
          });
        }
      }

      // Handle export action separately (doesn't modify data)
      if (action === 'export') {
        const exportData = await pool.query(`
          SELECT
            t.*, ts.plan_type, ts.plan_status, ts.monthly_price,
            COUNT(DISTINCT tl.id) as location_count,
            COUNT(DISTINCT e.id) as employee_count
          FROM tenants t
          LEFT JOIN tenant_subscriptions ts ON t.id = ts.tenant_id
          LEFT JOIN tenant_locations tl ON t.id = tl.tenant_id
          LEFT JOIN employees e ON t.id = e.tenant_id
          WHERE t.id = ANY($1) AND t.deleted_at IS NULL
          GROUP BY t.id, ts.plan_type, ts.plan_status, ts.monthly_price
        `, [tenant_ids]);

        await pool.query('COMMIT');

        return res.json({
          success: true,
          action: 'export',
          data: exportData.rows,
          count: exportData.rows.length
        });
      }

      await pool.query('COMMIT');

      console.log(`✅ Bulk ${action} completed: ${results.length} successful, ${errors.length} errors`);

      res.json({
        success: true,
        action,
        results: {
          successful: results,
          errors: errors,
          summary: {
            total_processed: tenant_ids.length,
            successful_count: results.length,
            error_count: errors.length
          }
        }
      });

    } catch (error) {
      await pool.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('💥 Error in bulk tenant operations:', error);
    res.status(500).json({
      success: false,
      error: 'Bulk operation failed',
      message: error.message
    });
  }
};

// Advanced tenant search with multiple criteria
const advancedTenantSearch = async (req, res) => {
  try {
    console.log('🔍 Performing advanced tenant search...');

    const {
      query = '',
      filters = {},
      sort = { field: 'created_at', order: 'DESC' },
      page = 1,
      limit = 20
    } = req.body;

    let searchConditions = ['t.deleted_at IS NULL'];
    let queryParams = [];
    let paramIndex = 1;

    // Text search across multiple fields
    if (query) {
      searchConditions.push(`(
        t.name ILIKE $${paramIndex} OR
        t.email ILIKE $${paramIndex} OR
        t.slug ILIKE $${paramIndex} OR
        t.phone ILIKE $${paramIndex} OR
        t.city ILIKE $${paramIndex} OR
        t.description ILIKE $${paramIndex} OR
        t.website ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${query}%`);
      paramIndex++;
    }

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        switch (key) {
          case 'status':
          case 'business_type':
          case 'timezone':
          case 'country':
            searchConditions.push(`t.${key} = $${paramIndex}`);
            queryParams.push(value);
            paramIndex++;
            break;
          case 'plan_type':
          case 'plan_status':
            searchConditions.push(`ts.${key} = $${paramIndex}`);
            queryParams.push(value);
            paramIndex++;
            break;
          case 'revenue_min':
            // This would require actual revenue calculation
            break;
          case 'employee_count_min':
            searchConditions.push(`(SELECT COUNT(*) FROM employees WHERE tenant_id = t.id AND is_active = true) >= $${paramIndex}`);
            queryParams.push(parseInt(value));
            paramIndex++;
            break;
          case 'created_after':
            searchConditions.push(`t.created_at >= $${paramIndex}`);
            queryParams.push(value);
            paramIndex++;
            break;
          case 'created_before':
            searchConditions.push(`t.created_at <= $${paramIndex}`);
            queryParams.push(value);
            paramIndex++;
            break;
        }
      }
    });

    const whereClause = searchConditions.join(' AND ');
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get search results
    const searchQuery = `
      SELECT
        t.*,
        ts.plan_type,
        ts.plan_status,
        ts.monthly_price,
        ts.features,
        COUNT(DISTINCT tl.id) as location_count,
        COUNT(DISTINCT e.id) as employee_count,
        COUNT(DISTINCT CASE WHEN e.is_active = true THEN e.id END) as active_employee_count
      FROM tenants t
      LEFT JOIN tenant_subscriptions ts ON t.id = ts.tenant_id
      LEFT JOIN tenant_locations tl ON t.id = tl.tenant_id
      LEFT JOIN employees e ON t.id = e.tenant_id
      WHERE ${whereClause}
      GROUP BY t.id, ts.plan_type, ts.plan_status, ts.monthly_price, ts.features
      ORDER BY t.${sort.field} ${sort.order}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(parseInt(limit), offset);

    const searchResult = await pool.query(searchQuery, queryParams);

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT t.id) as total
      FROM tenants t
      LEFT JOIN tenant_subscriptions ts ON t.id = ts.tenant_id
      WHERE ${whereClause}
    `;

    const countResult = await pool.query(countQuery, queryParams.slice(0, -2));
    const totalCount = parseInt(countResult.rows[0].total);

    console.log(`✅ Advanced search completed: ${searchResult.rows.length} results found`);

    res.json({
      success: true,
      data: {
        tenants: searchResult.rows,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total_count: totalCount,
          total_pages: Math.ceil(totalCount / parseInt(limit))
        },
        search_criteria: {
          query,
          filters,
          sort
        }
      }
    });

  } catch (error) {
    console.error('💥 Error in advanced tenant search:', error);
    res.status(500).json({
      success: false,
      error: 'Search failed',
      message: error.message
    });
  }
};

module.exports = {
  getEnhancedTenants,
  createEnhancedTenant,
  bulkTenantOperations,
  advancedTenantSearch,
  requireSuperAdmin,
  logAuditAction,
  pool
};
