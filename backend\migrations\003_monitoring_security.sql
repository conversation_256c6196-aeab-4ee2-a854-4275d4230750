-- Phase 3: System Monitoring & Security Migration
-- Execute this migration to add monitoring and security features

-- =====================================================
-- SYSTEM MONITORING TABLES
-- =====================================================

-- System Health Metrics (Enhanced)
CREATE TABLE IF NOT EXISTS system_health_metrics (
  id SERIAL PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT NOW(),
  metric_category VARCHAR(50) NOT NULL, -- 'performance', 'security', 'availability', 'capacity'
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(15,4) NOT NULL,
  metric_unit VARCHAR(20),
  threshold_warning DECIMAL(15,4),
  threshold_critical DECIMAL(15,4),
  status VARCHAR(20) DEFAULT 'normal', -- 'normal', 'warning', 'critical'
  tenant_id INTEGER REFERENCES tenants(id),
  location_id INTEGER REFERENCES tenant_locations(id),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

-- System Performance Monitoring (Enhanced)
CREATE TABLE IF NOT EXISTS system_performance_detailed (
  id SERIAL PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT NOW(),
  server_id VARCHAR(50) DEFAULT 'main',
  cpu_usage DECIMAL(5,2),
  cpu_cores INTEGER,
  memory_usage DECIMAL(5,2),
  memory_total_gb DECIMAL(8,2),
  memory_available_gb DECIMAL(8,2),
  disk_usage DECIMAL(5,2),
  disk_total_gb DECIMAL(10,2),
  disk_available_gb DECIMAL(10,2),
  network_in_mbps DECIMAL(10,2),
  network_out_mbps DECIMAL(10,2),
  active_connections INTEGER,
  database_connections INTEGER,
  response_time_avg DECIMAL(8,3),
  response_time_p95 DECIMAL(8,3),
  response_time_p99 DECIMAL(8,3),
  error_rate DECIMAL(5,4),
  throughput_rps INTEGER,
  uptime_seconds BIGINT,
  load_average DECIMAL(5,2)
);

-- System Alerts (Enhanced)
CREATE TABLE IF NOT EXISTS system_alerts_detailed (
  id SERIAL PRIMARY KEY,
  alert_type VARCHAR(50) NOT NULL, -- 'performance', 'security', 'availability', 'capacity', 'business'
  severity VARCHAR(20) NOT NULL, -- 'info', 'low', 'medium', 'high', 'critical'
  title VARCHAR(200) NOT NULL,
  description TEXT,
  source_system VARCHAR(50), -- 'monitoring', 'security', 'application', 'infrastructure'
  affected_component VARCHAR(100),
  tenant_id INTEGER REFERENCES tenants(id),
  location_id INTEGER REFERENCES tenant_locations(id),
  metric_value DECIMAL(15,4),
  threshold_value DECIMAL(15,4),
  is_resolved BOOLEAN DEFAULT false,
  resolved_by INTEGER REFERENCES admin_users(id),
  resolved_at TIMESTAMP,
  resolution_notes TEXT,
  escalation_level INTEGER DEFAULT 1,
  notification_sent BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- SECURITY MONITORING TABLES
-- =====================================================

-- Security Events
CREATE TABLE IF NOT EXISTS security_events (
  id SERIAL PRIMARY KEY,
  event_type VARCHAR(50) NOT NULL, -- 'login_attempt', 'permission_denied', 'data_access', 'configuration_change'
  severity VARCHAR(20) NOT NULL, -- 'info', 'warning', 'high', 'critical'
  user_id INTEGER REFERENCES employees(id),
  admin_user_id INTEGER REFERENCES admin_users(id),
  tenant_id INTEGER REFERENCES tenants(id),
  source_ip INET,
  user_agent TEXT,
  session_id VARCHAR(100),
  event_details JSONB NOT NULL,
  risk_score INTEGER DEFAULT 0, -- 0-100
  is_suspicious BOOLEAN DEFAULT false,
  is_blocked BOOLEAN DEFAULT false,
  geolocation JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Security Audit Trail (Enhanced)
CREATE TABLE IF NOT EXISTS security_audit_trail (
  id SERIAL PRIMARY KEY,
  audit_type VARCHAR(50) NOT NULL, -- 'data_access', 'configuration', 'user_management', 'system_change'
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id VARCHAR(100),
  user_id INTEGER REFERENCES employees(id),
  admin_user_id INTEGER REFERENCES admin_users(id),
  tenant_id INTEGER REFERENCES tenants(id),
  before_state JSONB,
  after_state JSONB,
  change_summary TEXT,
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(100),
  compliance_tags TEXT[], -- ['gdpr', 'pci_dss', 'sox', 'hipaa']
  retention_period INTEGER DEFAULT 2555, -- days (7 years default)
  is_sensitive BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Compliance Reports
CREATE TABLE IF NOT EXISTS compliance_reports (
  id SERIAL PRIMARY KEY,
  report_type VARCHAR(50) NOT NULL, -- 'gdpr', 'pci_dss', 'sox', 'security_audit'
  report_period_start DATE NOT NULL,
  report_period_end DATE NOT NULL,
  generated_by INTEGER REFERENCES admin_users(id),
  report_data JSONB NOT NULL,
  findings_summary TEXT,
  compliance_score DECIMAL(5,2), -- 0-100
  issues_count INTEGER DEFAULT 0,
  critical_issues_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'final', 'submitted'
  file_path TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- BACKUP & DISASTER RECOVERY TABLES
-- =====================================================

-- Backup Jobs
CREATE TABLE IF NOT EXISTS backup_jobs (
  id SERIAL PRIMARY KEY,
  job_name VARCHAR(100) NOT NULL,
  backup_type VARCHAR(50) NOT NULL, -- 'full', 'incremental', 'differential'
  target_type VARCHAR(50) NOT NULL, -- 'database', 'files', 'configuration', 'full_system'
  schedule_cron VARCHAR(100), -- Cron expression for scheduling
  retention_days INTEGER DEFAULT 30,
  compression_enabled BOOLEAN DEFAULT true,
  encryption_enabled BOOLEAN DEFAULT true,
  storage_location TEXT,
  is_active BOOLEAN DEFAULT true,
  last_run_at TIMESTAMP,
  next_run_at TIMESTAMP,
  created_by INTEGER REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Backup Executions
CREATE TABLE IF NOT EXISTS backup_executions (
  id SERIAL PRIMARY KEY,
  job_id INTEGER REFERENCES backup_jobs(id) ON DELETE CASCADE,
  execution_status VARCHAR(20) NOT NULL, -- 'running', 'completed', 'failed', 'cancelled'
  started_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  duration_seconds INTEGER,
  backup_size_bytes BIGINT,
  compressed_size_bytes BIGINT,
  file_count INTEGER,
  backup_file_path TEXT,
  checksum VARCHAR(64),
  error_message TEXT,
  logs TEXT,
  metadata JSONB DEFAULT '{}'
);

-- Disaster Recovery Plans
CREATE TABLE IF NOT EXISTS disaster_recovery_plans (
  id SERIAL PRIMARY KEY,
  plan_name VARCHAR(100) NOT NULL,
  disaster_type VARCHAR(50) NOT NULL, -- 'hardware_failure', 'data_corruption', 'security_breach', 'natural_disaster'
  priority_level INTEGER NOT NULL, -- 1-5 (1 = highest)
  rto_minutes INTEGER, -- Recovery Time Objective
  rpo_minutes INTEGER, -- Recovery Point Objective
  recovery_steps JSONB NOT NULL,
  contact_list JSONB,
  last_tested_at TIMESTAMP,
  test_results TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by INTEGER REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE ANALYTICS TABLES
-- =====================================================

-- API Performance Metrics
CREATE TABLE IF NOT EXISTS api_performance_metrics (
  id SERIAL PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT NOW(),
  endpoint VARCHAR(200) NOT NULL,
  method VARCHAR(10) NOT NULL,
  response_time_ms INTEGER NOT NULL,
  status_code INTEGER NOT NULL,
  request_size_bytes INTEGER,
  response_size_bytes INTEGER,
  user_id INTEGER REFERENCES employees(id),
  tenant_id INTEGER REFERENCES tenants(id),
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  trace_id VARCHAR(100)
);

-- Database Performance Metrics
CREATE TABLE IF NOT EXISTS database_performance_metrics (
  id SERIAL PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT NOW(),
  query_type VARCHAR(50), -- 'SELECT', 'INSERT', 'UPDATE', 'DELETE'
  table_name VARCHAR(100),
  execution_time_ms DECIMAL(10,3),
  rows_affected INTEGER,
  query_hash VARCHAR(64),
  query_plan_hash VARCHAR(64),
  is_slow_query BOOLEAN DEFAULT false,
  tenant_id INTEGER REFERENCES tenants(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- System Monitoring Indexes
CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_health_category ON system_health_metrics(metric_category, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health_metrics(status, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_performance_timestamp ON system_performance_detailed(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON system_alerts_detailed(severity, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_resolved ON system_alerts_detailed(is_resolved, created_at DESC);

-- Security Indexes
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_user ON security_events(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_suspicious ON security_events(is_suspicious, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_audit_type ON security_audit_trail(audit_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_audit_user ON security_audit_trail(admin_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_audit_tenant ON security_audit_trail(tenant_id, created_at DESC);

-- Performance Indexes
CREATE INDEX IF NOT EXISTS idx_api_performance_endpoint ON api_performance_metrics(endpoint, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_api_performance_tenant ON api_performance_metrics(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_api_performance_slow ON api_performance_metrics(response_time_ms DESC, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_db_performance_table ON database_performance_metrics(table_name, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_db_performance_slow ON database_performance_metrics(is_slow_query, timestamp DESC);

-- Backup Indexes
CREATE INDEX IF NOT EXISTS idx_backup_executions_job ON backup_executions(job_id, started_at DESC);
CREATE INDEX IF NOT EXISTS idx_backup_executions_status ON backup_executions(execution_status, started_at DESC);

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Sample System Health Metrics
INSERT INTO system_health_metrics (metric_category, metric_name, metric_value, metric_unit, threshold_warning, threshold_critical, status) VALUES
('performance', 'cpu_usage', 45.2, 'percent', 70.0, 90.0, 'normal'),
('performance', 'memory_usage', 62.8, 'percent', 80.0, 95.0, 'normal'),
('performance', 'disk_usage', 78.5, 'percent', 85.0, 95.0, 'warning'),
('performance', 'response_time_avg', 245.3, 'milliseconds', 500.0, 1000.0, 'normal'),
('availability', 'uptime', 99.95, 'percent', 99.0, 95.0, 'normal'),
('security', 'failed_login_attempts', 12, 'count', 50, 100, 'normal'),
('capacity', 'active_connections', 156, 'count', 500, 800, 'normal');

-- Sample Security Events
INSERT INTO security_events (event_type, severity, event_details, risk_score, source_ip) VALUES
('login_attempt', 'info', '{"success": true, "method": "pin"}', 10, '*************'),
('login_attempt', 'warning', '{"success": false, "method": "pin", "attempts": 3}', 45, '*********'),
('permission_denied', 'warning', '{"resource": "admin_users", "action": "delete"}', 35, '*************'),
('data_access', 'info', '{"table": "tenants", "action": "select", "rows": 156}', 15, '*************');

-- Sample Backup Jobs
INSERT INTO backup_jobs (job_name, backup_type, target_type, schedule_cron, retention_days, storage_location, created_by) VALUES
('Daily Database Backup', 'full', 'database', '0 2 * * *', 30, '/backups/database/', 1),
('Weekly Full System Backup', 'full', 'full_system', '0 1 * * 0', 90, '/backups/system/', 1),
('Hourly Incremental Backup', 'incremental', 'database', '0 * * * *', 7, '/backups/incremental/', 1);

-- Sample Disaster Recovery Plans
INSERT INTO disaster_recovery_plans (plan_name, disaster_type, priority_level, rto_minutes, rpo_minutes, recovery_steps, contact_list, created_by) VALUES
('Database Failure Recovery', 'hardware_failure', 1, 60, 15, 
 '[{"step": 1, "action": "Assess damage", "duration": 10}, {"step": 2, "action": "Restore from backup", "duration": 30}, {"step": 3, "action": "Verify data integrity", "duration": 15}, {"step": 4, "action": "Resume operations", "duration": 5}]',
 '[{"role": "DBA", "name": "John Doe", "phone": "******-0123"}, {"role": "SysAdmin", "name": "Jane Smith", "phone": "******-0124"}]',
 1);

COMMIT;
