import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface EnhancedLoginProps {
  onLogin: (success: boolean) => void;
  onShowRegistration?: () => void;
}

const EnhancedLogin: React.FC<EnhancedLoginProps> = ({ onLogin, onShowRegistration }) => {
  console.log('🎨 EnhancedLogin component is rendering!');
  const { login, state } = useEnhancedAppContext();
  const [pin, setPin] = useState('');
  const [tenantSlug, setTenantSlug] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showTenantField, setShowTenantField] = useState(false);

  // Check if we have a stored tenant slug
  useEffect(() => {
    const storedTenantSlug = localStorage.getItem('tenantSlug');
    if (storedTenantSlug) {
      setTenantSlug(storedTenantSlug);
    } else {
      // Set default tenant slug for demo
      setTenantSlug('demo-restaurant');
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin.trim()) {
      setError('Please enter your PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const success = await login(pin, tenantSlug || undefined);
      if (success) {
        // Check if this is for super admin access
        if (window.location.pathname.includes('super-admin')) {
          // Wait a moment for localStorage to be updated
          setTimeout(() => {
            const currentEmployeeData = localStorage.getItem('currentEmployee');

            if (currentEmployeeData) {
              try {
                const currentEmployee = JSON.parse(currentEmployeeData);

                if (currentEmployee.role === 'super_admin') {
                  onLogin(true);
                } else {
                  setError(`Access Denied: Super Admin privileges required (current role: ${currentEmployee.role})`);
                }
              } catch (parseError) {
                setError('Access Denied: Invalid employee data');
              }
            } else {
              setError('Access Denied: No employee data available');
            }
          }, 100);
        } else {
          onLogin(true);
        }
      } else {
        setError('Invalid PIN or tenant. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Backend server is not running. Please start the backend server on port 4000.');
      } else {
        setError('Login failed. Please check your PIN and try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  const handleBackspace = () => {
    setPin(prev => prev.slice(0, -1));
  };

  const handleClear = () => {
    setPin('');
    setError('');
  };

  console.log('🎨 EnhancedLogin: Rendering with sophisticated design');

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Enhanced Login Status Indicator */}
      <div className="fixed top-4 left-4 bg-emerald-500 text-white px-4 py-2 rounded-lg font-bold text-sm shadow-lg z-50 animate-pulse">
        ✅ ENHANCED LOGIN ACTIVE
      </div>

      {/* Version Info */}
      <div className="fixed top-4 right-4 text-white/70 text-xs font-medium z-50">
        v2.0.0 Enterprise
      </div>

      {/* Main Login Card */}
      <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl p-8 w-full max-w-md relative z-10 border border-white/20">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Restaurant POS</h1>
          <p className="text-gray-600 text-lg">Enter your PIN to continue</p>
        </div>

        {/* Development Credentials Panel */}
        <div className="mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div className="flex items-center mb-3">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <p className="text-sm font-semibold text-blue-800">🔧 Development Credentials</p>
          </div>
          <div className="text-sm text-blue-700 space-y-1">
            <div className="flex justify-between">
              <span className="font-medium">Super Admin:</span>
              <span className="font-mono">888888 / 999999</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Employee:</span>
              <span className="font-mono">123456 / 234567</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Manager:</span>
              <span className="font-mono">567890</span>
            </div>
            <div className="mt-3 pt-2 border-t border-blue-200">
              <div className="flex justify-between">
                <span className="font-medium text-blue-600">Tenant:</span>
                <span className="font-mono text-blue-800">demo-restaurant</span>
              </div>
            </div>
          </div>
        </div>
        {/* PIN Display Section */}
        <div className="mb-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3 text-center">
            Enter Your PIN
          </label>
          <div className="flex justify-center space-x-3 mb-6">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className={`w-12 h-12 border-2 rounded-xl flex items-center justify-center text-2xl font-bold transition-all duration-200 ${
                  i < pin.length
                    ? 'border-blue-500 bg-blue-50 text-blue-600 shadow-md scale-105'
                    : 'border-gray-300 bg-gray-50 text-gray-400'
                }`}
              >
                {i < pin.length ? '●' : '○'}
              </div>
            ))}
          </div>
        </div>

        {/* Number Pad */}
        <div className="mb-6">
          <div className="grid grid-cols-3 gap-3 mb-4">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
              <button
                key={digit}
                type="button"
                onClick={() => handlePinInput(digit.toString())}
                disabled={isLoading}
                className="h-14 bg-gray-100 hover:bg-gray-200 active:bg-gray-300 rounded-xl text-xl font-semibold text-gray-700 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                data-testid={`pin-button-${digit}`}
              >
                {digit}
              </button>
            ))}

            {/* Bottom row: Clear, 0, Backspace */}
            <button
              type="button"
              onClick={handleClear}
              disabled={isLoading}
              className="h-14 bg-red-100 hover:bg-red-200 active:bg-red-300 rounded-xl text-sm font-semibold text-red-600 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              data-testid="pin-clear-button"
            >
              Clear
            </button>

            <button
              type="button"
              onClick={() => handlePinInput('0')}
              disabled={isLoading}
              className="h-14 bg-gray-100 hover:bg-gray-200 active:bg-gray-300 rounded-xl text-xl font-semibold text-gray-700 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              data-testid="pin-button-0"
            >
              0
            </button>

            <button
              type="button"
              onClick={handleBackspace}
              disabled={isLoading}
              className="h-14 bg-yellow-100 hover:bg-yellow-200 active:bg-yellow-300 rounded-xl text-lg font-semibold text-yellow-600 transition-all duration-150 transform hover:scale-105 active:scale-95 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
              data-testid="pin-backspace-button"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tenant Selection (Collapsible) */}
        {showTenantField && (
          <div className="mb-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Restaurant Code (Optional)
            </label>
            <input
              type="text"
              value={tenantSlug}
              onChange={(e) => setTenantSlug(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder="Enter restaurant code"
              data-testid="tenant-input"
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl text-red-700 text-sm flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>{error}</span>
          </div>
        )}

        {/* Sign In Button */}
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔐 EnhancedLogin: Sign In button clicked');
            handleSubmit(e);
          }}
          disabled={pin.length === 0 || isLoading}
          className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 transform ${
            pin.length === 0 || isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95'
          }`}
          data-testid="sign-in-button"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing In...
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              Sign In
            </div>
          )}
        </button>

        {/* Additional Options */}
        <div className="mt-6 text-center space-y-3">
          {onShowRegistration && (
            <div>
              <button
                type="button"
                onClick={onShowRegistration}
                className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Create New Business Account
              </button>
              <p className="text-xs text-gray-500 mt-2">
                New to our platform? Start your 14-day free trial
              </p>
            </div>
          )}

          <button
            type="button"
            onClick={() => setShowTenantField(!showTenantField)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200"
            data-testid="tenant-toggle-button"
          >
            {showTenantField ? 'Hide' : 'Show'} Restaurant Code
          </button>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Secure Multi-Tenant POS System</p>
          <p className="mt-1">© 2024 Restaurant POS. All rights reserved.</p>
        </div>
      </div>

      {/* Background Animation */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: -1,
        overflow: 'hidden'
      }}>
        <div style={{
          position: 'absolute',
          top: '-160px',
          right: '-128px',
          width: '320px',
          height: '320px',
          background: '#c4b5fd',
          borderRadius: '50%',
          mixBlendMode: 'multiply',
          filter: 'blur(40px)',
          opacity: 0.7,
          animation: 'blob 7s infinite'
        }}></div>
        <div style={{
          position: 'absolute',
          bottom: '-160px',
          left: '-128px',
          width: '320px',
          height: '320px',
          background: '#93c5fd',
          borderRadius: '50%',
          mixBlendMode: 'multiply',
          filter: 'blur(40px)',
          opacity: 0.7,
          animation: 'blob 7s infinite',
          animationDelay: '2s'
        }}></div>
        <div style={{
          position: 'absolute',
          top: '160px',
          left: '160px',
          width: '320px',
          height: '320px',
          background: '#a5b4fc',
          borderRadius: '50%',
          mixBlendMode: 'multiply',
          filter: 'blur(40px)',
          opacity: 0.7,
          animation: 'blob 7s infinite',
          animationDelay: '4s'
        }}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% {
            transform: translate(0px, 0px) scale(1);
          }
          33% {
            transform: translate(30px, -50px) scale(1.1);
          }
          66% {
            transform: translate(-20px, 20px) scale(0.9);
          }
          100% {
            transform: translate(0px, 0px) scale(1);
          }
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default EnhancedLogin;
