const express = require('express');

const app = express();
const PORT = 4000;

// Middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});
app.use(express.json());

// Simple health check without database
app.get('/api/admin/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Mock system metrics
app.get('/api/admin/metrics/system', (req, res) => {
  res.json({
    totalTenants: 3,
    activeTenants: 3,
    totalUsers: 6,
    activeUsers: 6,
    monthlyRevenue: 288.20,
    totalTransactions: 10,
    systemUptime: 99.8,
    databaseConnections: 3,
    apiRequests: 27822,
    errorRate: 0.03,
    responseTime: 95,
    memoryUsage: 42,
    cpuUsage: 23,
    diskUsage: 67,
    lastUpdated: new Date().toISOString()
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 Simple API Server running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/admin/health`);
  console.log(`📈 System metrics: http://localhost:${PORT}/api/admin/metrics/system`);
  console.log('✅ Server is ready to accept connections');
  console.log('🔄 Server will keep running... Press Ctrl+C to stop');
});

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Server shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Prevent the process from exiting
process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = app;
