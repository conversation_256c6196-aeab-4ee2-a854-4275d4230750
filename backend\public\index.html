<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BARPOS Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #374151;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 3px;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-white">BARPOS Management System</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="showTab('settings')" id="settingsTab" class="px-4 py-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                        Settings
                    </button>
                    <button onclick="showTab('api-test')" id="apiTestTab" class="px-4 py-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700 transition-colors">
                        API Test
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Settings Tab Content -->
    <div id="settingsContent" class="tab-content">
        <div class="p-4 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700">
            <h2 class="text-xl font-semibold text-white mb-6">System Settings</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <!-- Employee Management -->
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-900 p-2 rounded-md mr-3">
                            <i data-lucide="users" class="h-5 w-5 text-blue-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-white">Employee Management</h3>
                    </div>

                    <p class="text-gray-400 text-sm mb-4">
                        Manage employee accounts, roles, and permissions
                    </p>

                    <div class="overflow-x-auto">
                        <table class="w-full mb-4" id="employeesTable">
                            <thead>
                                <tr class="text-left text-gray-400 border-b border-gray-700">
                                    <th class="pb-2">Name</th>
                                    <th class="pb-2">Role</th>
                                    <th class="pb-2">Created</th>
                                    <th class="pb-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody">
                                <!-- Employees will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <button 
                        class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors w-full"
                        onclick="showEmployeeModal()"
                    >
                        Manage Employees
                    </button>
                </div>

                <!-- Menu Management -->
                <div class="bg-gray-800 rounded-lg p-4">
                    <div class="flex items-center mb-4">
                        <div class="bg-amber-900 p-2 rounded-md mr-3">
                            <i data-lucide="file-text" class="h-5 w-5 text-amber-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-white">Menu Management</h3>
                    </div>

                    <p class="text-gray-400 text-sm mb-4">
                        Update menu items, prices, and categories
                    </p>

                    <div class="mb-4">
                        <div class="flex justify-between text-gray-400 mb-2">
                            <span>Total Menu Items</span>
                            <span class="font-medium text-white" id="totalProducts">0</span>
                        </div>
                        <div class="flex justify-between items-center text-gray-400 mb-2">
                            <span>Categories</span>
                            <span class="font-medium text-white" id="totalCategories">0</span>
                            <button
                                class="ml-2 text-green-500 hover:text-green-400 transition-colors"
                                onclick="showAddCategoryModal()"
                                title="Add New Category"
                            >
                                <i data-lucide="plus" class="h-5 w-5"></i>
                            </button>
                        </div>
                        <div class="flex justify-between text-gray-400">
                            <span>Last Updated</span>
                            <span class="font-medium text-white">Today</span>
                        </div>
                    </div>

                    <div class="space-y-2 mb-4">
                        <button 
                            class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors w-full"
                            onclick="showMenuModal()"
                        >
                            Manage Menu
                        </button>
                        <div class="flex space-x-2">
                            <button
                                class="bg-green-600 hover:bg-green-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                                onclick="downloadTemplate()"
                            >
                                Download Template
                            </button>
                            <button
                                class="bg-blue-600 hover:bg-blue-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                                onclick="triggerImport()"
                            >
                                Import Excel
                            </button>
                            <button
                                class="bg-amber-600 hover:bg-amber-500 text-white py-2 px-4 rounded-md transition-colors flex-1 text-sm"
                                onclick="exportProducts()"
                            >
                                Export to Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="bg-gray-800 rounded-lg p-4 mb-6">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-900 p-2 rounded-md mr-3">
                        <i data-lucide="settings" class="h-5 w-5 text-purple-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-white">System Configuration</h3>
                </div>

                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-white font-medium">Tax Rate</h4>
                            <p class="text-gray-400 text-sm">Default tax rate for orders</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input 
                                type="text" 
                                id="currentTaxRate"
                                value="8.25%"
                                class="bg-gray-700 text-white px-3 py-2 rounded-md w-20 text-right"
                                readonly
                            />
                            <button 
                                onclick="showConfigModal()"
                                class="bg-gray-700 hover:bg-gray-600 px-2 py-2 rounded-md text-gray-300 transition-colors"
                            >
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-white font-medium">Receipt Settings</h4>
                            <p class="text-gray-400 text-sm">Customize receipt format and footer</p>
                        </div>
                        <button 
                            onclick="showConfigModal()"
                            class="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-md text-gray-300 transition-colors"
                        >
                            Configure
                        </button>
                    </div>

                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-white font-medium">Backup Database</h4>
                            <p class="text-gray-400 text-sm">Create a backup of all system data</p>
                        </div>
                        <button class="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-md text-gray-300 transition-colors">
                            Backup Now
                        </button>
                    </div>
                </div>
            </div>

            <!-- About -->
            <div class="bg-gray-800 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white mb-2">About</h3>
                <p class="text-gray-400 mb-4">Bar POS System v1.0.0</p>

                <div class="flex justify-between text-gray-400 text-sm">
                    <span>© 2025 Bar POS System</span>
                    <button class="text-purple-400 hover:text-purple-300 transition-colors">
                        Check for Updates
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Test Tab Content -->
    <div id="apiTestContent" class="tab-content hidden">
        <div class="max-w-4xl mx-auto p-8">
            <h1 class="text-3xl font-bold mb-8">BARPOS API Test</h1>

            <!-- Login Section -->
            <div class="mb-8 p-6 bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Login Test</h2>
                <div class="flex gap-4">
                    <input type="password" id="pin" placeholder="Enter PIN" class="px-4 py-2 bg-gray-700 rounded">
                    <button onclick="testLogin()" class="px-6 py-2 bg-purple-600 rounded hover:bg-purple-500">Login</button>
                </div>
                <pre id="loginResult" class="mt-4 p-4 bg-gray-700 rounded hidden"></pre>
            </div>

            <!-- Products Section -->
            <div class="mb-8 p-6 bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Products Test</h2>
                
                <!-- Product Actions -->
                <div class="flex gap-4 mb-4">
                    <button onclick="testGetProducts()" class="px-6 py-2 bg-blue-600 rounded hover:bg-blue-500">Get Products</button>
                    <button onclick="testDownloadTemplate()" class="px-6 py-2 bg-green-600 rounded hover:bg-green-500">Download Template</button>
                    <input type="file" id="excelFile" accept=".xlsx" class="hidden" onchange="testImportProducts(event)">
                    <button onclick="document.getElementById('excelFile').click()" class="px-6 py-2 bg-amber-600 rounded hover:bg-amber-500">Import Excel</button>
                    <button onclick="testExportProducts()" class="px-6 py-2 bg-purple-600 rounded hover:bg-purple-500">Export Excel</button>
                </div>

                <!-- Add/Update Product Form -->
                <div class="mb-4 p-4 bg-gray-700 rounded">
                    <h3 class="text-lg font-semibold mb-4">Add/Update Product</h3>
                    <input type="hidden" id="productId">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <input type="text" id="productName" placeholder="Product Name" class="px-4 py-2 bg-gray-600 rounded">
                        <input type="number" id="productPrice" placeholder="Price" step="0.01" class="px-4 py-2 bg-gray-600 rounded">
                        <input type="text" id="productCategory" placeholder="Category" class="px-4 py-2 bg-gray-600 rounded">
                        <input type="text" id="productDescription" placeholder="Description" class="px-4 py-2 bg-gray-600 rounded">
                        <div class="flex items-center">
                            <input type="checkbox" id="productInStock" class="mr-2">
                            <label for="productInStock">In Stock</label>
                        </div>
                    </div>
                    <div class="flex gap-4">
                        <button onclick="testAddProduct()" class="px-6 py-2 bg-green-600 rounded hover:bg-green-500">Add Product</button>
                        <button onclick="testUpdateProduct()" class="px-6 py-2 bg-yellow-600 rounded hover:bg-yellow-500">Update Product</button>
                        <button onclick="clearProductForm()" class="px-6 py-2 bg-gray-600 rounded hover:bg-gray-500">Clear Form</button>
                    </div>
                </div>

                <pre id="productsResult" class="mt-4 p-4 bg-gray-700 rounded hidden"></pre>
            </div>

            <!-- Categories Section -->
            <div class="mb-8 p-6 bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Categories Test</h2>
                <div class="flex gap-4 mb-4">
                    <input type="text" id="categoryName" placeholder="Category Name" class="px-4 py-2 bg-gray-700 rounded">
                    <button onclick="testAddCategory()" class="px-6 py-2 bg-green-600 rounded hover:bg-green-500">Add Category</button>
                    <button onclick="testGetCategories()" class="px-6 py-2 bg-blue-600 rounded hover:bg-blue-500">Get Categories</button>
                </div>
                <pre id="categoriesResult" class="mt-4 p-4 bg-gray-700 rounded hidden"></pre>
            </div>

            <!-- Employees Section -->
            <div class="mb-8 p-6 bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Employees Test</h2>
                <div class="mb-4 p-4 bg-gray-700 rounded">
                    <h3 class="text-lg font-semibold mb-4">Add/Update Employee</h3>
                    <input type="hidden" id="employeeId">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <input type="text" id="employeeName" placeholder="Employee Name" class="px-4 py-2 bg-gray-600 rounded">
                        <input type="password" id="employeePin" placeholder="PIN" class="px-4 py-2 bg-gray-600 rounded">
                        <select id="employeeRole" class="px-4 py-2 bg-gray-600 rounded">
                            <option value="admin">Admin</option>
                            <option value="manager">Manager</option>
                            <option value="server">Server</option>
                            <option value="bartender">Bartender</option>
                        </select>
                    </div>
                    <div class="flex gap-4">
                        <button onclick="testAddEmployee()" class="px-6 py-2 bg-green-600 rounded hover:bg-green-500">Add Employee</button>
                        <button onclick="testUpdateEmployee()" class="px-6 py-2 bg-yellow-600 rounded hover:bg-yellow-500">Update Employee</button>
                        <button onclick="clearEmployeeForm()" class="px-6 py-2 bg-gray-600 rounded hover:bg-gray-500">Clear Form</button>
                    </div>
                </div>
                <div class="flex gap-4 mb-4">
                    <button onclick="testGetEmployees()" class="px-6 py-2 bg-blue-600 rounded hover:bg-blue-500">Get Employees</button>
                </div>
                <pre id="employeesResult" class="mt-4 p-4 bg-gray-700 rounded hidden"></pre>
            </div>

            <!-- System Config Section -->
            <div class="mb-8 p-6 bg-gray-800 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">System Configuration Test</h2>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <input type="number" id="taxRate" placeholder="Tax Rate" step="0.0001" class="px-4 py-2 bg-gray-700 rounded">
                    <input type="text" id="businessName" placeholder="Business Name" class="px-4 py-2 bg-gray-700 rounded">
                    <input type="text" id="businessAddress" placeholder="Business Address" class="px-4 py-2 bg-gray-700 rounded">
                    <input type="text" id="businessPhone" placeholder="Business Phone" class="px-4 py-2 bg-gray-700 rounded">
                    <input type="text" id="receiptHeader" placeholder="Receipt Header" class="px-4 py-2 bg-gray-700 rounded">
                    <input type="text" id="receiptFooter" placeholder="Receipt Footer" class="px-4 py-2 bg-gray-700 rounded">
                </div>
                <div class="flex gap-4 mb-4">
                    <button onclick="testGetConfig()" class="px-6 py-2 bg-blue-600 rounded hover:bg-blue-500">Get Config</button>
                    <button onclick="testUpdateConfig()" class="px-6 py-2 bg-green-600 rounded hover:bg-green-500">Update Config</button>
                </div>
                <pre id="configResult" class="mt-4 p-4 bg-gray-700 rounded hidden"></pre>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Employee Modal -->
    <div id="employeeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-white" id="employeeModalTitle">Add Employee</h3>
                <button onclick="closeEmployeeModal()" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>

            <form id="employeeForm" class="space-y-4">
                <input type="hidden" id="modalEmployeeId">
                <div>
                    <label class="block text-gray-300 mb-1">Name *</label>
                    <input id="modalEmployeeName" type="text" required class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">PIN *</label>
                    <input id="modalEmployeePin" type="password" required class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Role *</label>
                    <select id="modalEmployeeRole" required class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="server">Server</option>
                        <option value="bartender">Bartender</option>
                        <option value="manager">Manager</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="flex justify-end space-x-2 mt-6">
                    <button type="button" onclick="closeEmployeeModal()" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors">
                        Save Employee
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Menu Modal -->
    <div id="menuModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-white">Menu Management</h3>
                <button onclick="closeMenuModal()" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>

            <div id="menuContent">
                <!-- Menu items will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Config Modal -->
    <div id="configModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-white">System Configuration</h3>
                <button onclick="closeConfigModal()" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>

            <form id="configForm" class="space-y-4">
                <div>
                    <label class="block text-gray-300 mb-1">Tax Rate (%)</label>
                    <input id="modalTaxRate" type="number" step="0.01" min="0" max="100" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Business Name</label>
                    <input id="modalBusinessName" type="text" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Business Address</label>
                    <input id="modalBusinessAddress" type="text" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Business Phone</label>
                    <input id="modalBusinessPhone" type="text" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Receipt Header</label>
                    <textarea id="modalReceiptHeader" rows="2" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
                </div>
                <div>
                    <label class="block text-gray-300 mb-1">Receipt Footer</label>
                    <textarea id="modalReceiptFooter" rows="2" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
                </div>
                <div class="flex justify-end space-x-2 mt-6">
                    <button type="button" onclick="closeConfigModal()" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-md transition-colors">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div id="addCategoryModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-60 hidden">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-white">Add New Category</h3>
                <button onclick="closeAddCategoryModal()" class="text-gray-400 hover:text-white transition-colors">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>

            <form id="addCategoryForm">
                <input id="newCategoryName" type="text" placeholder="Enter category name" class="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                <div class="mt-6 flex justify-end space-x-2">
                    <button type="button" onclick="closeAddCategoryModal()" class="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-green-600 hover:bg-green-500 text-white py-2 px-4 rounded-md transition-colors">
                        Add Category
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hidden file input for Excel import -->
    <input type="file" id="hiddenFileInput" accept=".xlsx" class="hidden">

    <script>
        const API_URL = 'http://localhost:4000';

        // State management for settings
        let settingsState = {
            employees: [],
            products: [],
            categories: [],
            systemConfig: {
                tax_rate: 0.0825,
                receipt_header: 'Thank you for visiting!',
                receipt_footer: 'Please come again',
                business_name: 'Bar POS',
                business_address: '',
                business_phone: ''
            }
        };

        // Tab Management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('nav button').forEach(tab => {
                tab.classList.remove('bg-gray-700', 'text-white');
                tab.classList.add('text-gray-300');
            });
            
            // Show selected tab content
            if (tabName === 'settings') {
                document.getElementById('settingsContent').classList.remove('hidden');
                document.getElementById('settingsTab').classList.add('bg-gray-700', 'text-white');
                document.getElementById('settingsTab').classList.remove('text-gray-300');
                // Load settings data
                loadSettingsData();
            } else if (tabName === 'api-test') {
                document.getElementById('apiTestContent').classList.remove('hidden');
                document.getElementById('apiTestTab').classList.add('bg-gray-700', 'text-white');
                document.getElementById('apiTestTab').classList.remove('text-gray-300');
            }
        }

        // Settings Data Loading Functions
        async function loadSettingsData() {
            try {
                await Promise.all([
                    fetchEmployeesForSettings(),
                    fetchProductsForSettings(),
                    fetchCategoriesForSettings(),
                    fetchSettingsConfig()
                ]);
            } catch (error) {
                console.error('Error loading settings data:', error);
            }
        }

        async function fetchEmployeesForSettings() {
            try {
                const response = await fetch(`${API_URL}/employees`);
                if (!response.ok) throw new Error('Failed to fetch employees');
                const employees = await response.json();
                settingsState.employees = employees;
                renderEmployeesTable();
            } catch (error) {
                console.error('Error fetching employees:', error);
            }
        }

        async function fetchProductsForSettings() {
            try {
                const response = await fetch(`${API_URL}/products`);
                if (!response.ok) throw new Error('Failed to fetch products');
                const products = await response.json();
                settingsState.products = products;
                const totalProductsEl = document.getElementById('totalProducts');
                if (totalProductsEl) {
                    totalProductsEl.textContent = products.length;
                }
            } catch (error) {
                console.error('Error fetching products:', error);
            }
        }

        async function fetchCategoriesForSettings() {
            try {
                const response = await fetch(`${API_URL}/categories`);
                if (!response.ok) throw new Error('Failed to fetch categories');
                const categories = await response.json();
                settingsState.categories = categories;
                const totalCategoriesEl = document.getElementById('totalCategories');
                if (totalCategoriesEl) {
                    totalCategoriesEl.textContent = categories.length;
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
            }
        }

        async function fetchSettingsConfig() {
            try {
                const response = await fetch(`${API_URL}/settings`);
                if (!response.ok) throw new Error('Failed to fetch settings');
                const settings = await response.json();
                settingsState.systemConfig = settings;
                updateSettingsDisplay();
            } catch (error) {
                console.error('Error fetching settings:', error);
            }
        }

        function renderEmployeesTable() {
            const tbody = document.getElementById('employeesTableBody');
            if (!tbody) return;
            
            tbody.innerHTML = settingsState.employees.map(employee => `
                <tr class="border-b border-gray-700 last:border-0">
                    <td class="py-2 text-white">${employee.name}</td>
                    <td class="py-2 text-gray-300">
                        <span class="capitalize">${employee.role}</span>
                    </td>
                    <td class="py-2 text-gray-400 text-sm">
                        ${employee.created_at ? new Date(employee.created_at).toLocaleDateString() : ''}
                    </td>
                    <td class="py-2">
                        <div class="flex space-x-2">
                            <button
                                onclick="editEmployee('${employee.id}')"
                                class="text-blue-400 hover:text-blue-300 transition-colors p-1"
                                title="Edit ${employee.name}"
                            >
                                <i data-lucide="edit-2" class="h-4 w-4"></i>
                            </button>
                            <button
                                onclick="deleteEmployee('${employee.id}')"
                                class="text-red-400 hover:text-red-300 transition-colors p-1"
                                title="Delete ${employee.name}"
                            >
                                <i data-lucide="trash" class="h-4 w-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            lucide.createIcons();
        }

        function updateSettingsDisplay() {
            const currentTaxRateEl = document.getElementById('currentTaxRate');
            if (currentTaxRateEl && settingsState.systemConfig.tax_rate) {
                currentTaxRateEl.value = `${(settingsState.systemConfig.tax_rate * 100).toFixed(2)}%`;
            }
        }

        // Modal Functions
        function showEmployeeModal() {
            document.getElementById('employeeModal').classList.remove('hidden');
            document.getElementById('employeeModalTitle').textContent = 'Add Employee';
            document.getElementById('modalEmployeeId').value = '';
            document.getElementById('modalEmployeeName').value = '';
            document.getElementById('modalEmployeePin').value = '';
            document.getElementById('modalEmployeeRole').value = 'server';
        }

        function closeEmployeeModal() {
            document.getElementById('employeeModal').classList.add('hidden');
        }

        function showMenuModal() {
            document.getElementById('menuModal').classList.remove('hidden');
            loadMenuContent();
        }

        function closeMenuModal() {
            document.getElementById('menuModal').classList.add('hidden');
        }

        function showConfigModal() {
            document.getElementById('configModal').classList.remove('hidden');
            // Populate form with current values
            document.getElementById('modalTaxRate').value = (settingsState.systemConfig.tax_rate * 100).toFixed(2);
            document.getElementById('modalBusinessName').value = settingsState.systemConfig.business_name || '';
            document.getElementById('modalBusinessAddress').value = settingsState.systemConfig.business_address || '';
            document.getElementById('modalBusinessPhone').value = settingsState.systemConfig.business_phone || '';
            document.getElementById('modalReceiptHeader').value = settingsState.systemConfig.receipt_header || '';
            document.getElementById('modalReceiptFooter').value = settingsState.systemConfig.receipt_footer || '';
        }

        function closeConfigModal() {
            document.getElementById('configModal').classList.add('hidden');
        }

        function showAddCategoryModal() {
            document.getElementById('addCategoryModal').classList.remove('hidden');
            document.getElementById('newCategoryName').value = '';
        }

        function closeAddCategoryModal() {
            document.getElementById('addCategoryModal').classList.add('hidden');
        }

        function loadMenuContent() {
            const menuContent = document.getElementById('menuContent');
            menuContent.innerHTML = `
                <div class="mb-4">
                    <button onclick="showAddProductForm()" class="bg-green-600 hover:bg-green-500 text-white py-2 px-4 rounded-md transition-colors">
                        Add New Product
                    </button>
                </div>
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    ${settingsState.products.map(product => `
                        <div class="bg-gray-700 p-3 rounded-md flex justify-between items-center">
                            <div>
                                <h4 class="text-white font-medium">${product.name}</h4>
                                <p class="text-gray-400 text-sm">$${parseFloat(product.price).toFixed(2)} - ${product.category_name || 'No Category'}</p>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="editProduct('${product.id}')" class="text-blue-400 hover:text-blue-300 p-1">
                                    <i data-lucide="edit-2" class="h-4 w-4"></i>
                                </button>
                                <button onclick="deleteProduct('${product.id}')" class="text-red-400 hover:text-red-300 p-1">
                                    <i data-lucide="trash" class="h-4 w-4"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            lucide.createIcons();
        }

        // Employee Management Functions
        async function editEmployee(employeeId) {
            const employee = settingsState.employees.find(e => e.id === employeeId);
            if (!employee) return;

            document.getElementById('employeeModalTitle').textContent = 'Edit Employee';
            document.getElementById('modalEmployeeId').value = employee.id;
            document.getElementById('modalEmployeeName').value = employee.name;
            document.getElementById('modalEmployeePin').value = '';
            document.getElementById('modalEmployeeRole').value = employee.role;
            document.getElementById('employeeModal').classList.remove('hidden');
        }

        async function deleteEmployee(employeeId) {
            if (!confirm('Are you sure you want to delete this employee?')) return;

            try {
                const response = await fetch(`${API_URL}/employees/${employeeId}`, {
                    method: 'DELETE'
                });
                if (!response.ok) throw new Error('Failed to delete employee');
                await fetchEmployeesForSettings();
                alert('Employee deleted successfully');
            } catch (error) {
                console.error('Error:', error);
                alert('Failed to delete employee');
            }
        }

        // Excel Functions
        function downloadTemplate() {
            window.location.href = `${API_URL}/products/template`;
        }

        function triggerImport() {
            document.getElementById('hiddenFileInput').click();
        }

        function exportProducts() {
            window.location.href = `${API_URL}/products/export`;
        }

        // Form Handlers
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
            showTab('settings'); // Show settings by default

            // Employee form handler
            const employeeForm = document.getElementById('employeeForm');
            if (employeeForm) {
                employeeForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    const employeeId = document.getElementById('modalEmployeeId').value;
                    const formData = {
                        name: document.getElementById('modalEmployeeName').value,
                        pin: document.getElementById('modalEmployeePin').value,
                        role: document.getElementById('modalEmployeeRole').value
                    };

                    try {
                        let response;
                        if (employeeId) {
                            // Update existing employee
                            if (!formData.pin) delete formData.pin; // Don't update PIN if empty
                            response = await fetch(`${API_URL}/employees/${employeeId}`, {
                                method: 'PUT',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(formData)
                            });
                        } else {
                            // Create new employee
                            response = await fetch(`${API_URL}/employees`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify(formData)
                            });
                        }

                        if (!response.ok) throw new Error('Failed to save employee');
                        await fetchEmployeesForSettings();
                        closeEmployeeModal();
                        alert('Employee saved successfully');
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Failed to save employee');
                    }
                });
            }

            // Config form handler
            const configForm = document.getElementById('configForm');
            if (configForm) {
                configForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    const formData = {
                        tax_rate: parseFloat(document.getElementById('modalTaxRate').value) / 100,
                        business_name: document.getElementById('modalBusinessName').value,
                        business_address: document.getElementById('modalBusinessAddress').value,
                        business_phone: document.getElementById('modalBusinessPhone').value,
                        receipt_header: document.getElementById('modalReceiptHeader').value,
                        receipt_footer: document.getElementById('modalReceiptFooter').value
                    };

                    try {
                        const response = await fetch(`${API_URL}/settings`, {
                            method: 'PUT',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(formData)
                        });
                        if (!response.ok) throw new Error('Failed to update settings');
                        await fetchSettingsConfig();
                        closeConfigModal();
                        alert('Settings updated successfully');
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Failed to update settings');
                    }
                });
            }

            // Add category form handler
            const addCategoryForm = document.getElementById('addCategoryForm');
            if (addCategoryForm) {
                addCategoryForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    const name = document.getElementById('newCategoryName').value;

                    try {
                        const response = await fetch(`${API_URL}/categories`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ name })
                        });
                        if (!response.ok) throw new Error('Failed to add category');
                        await fetchCategoriesForSettings();
                        closeAddCategoryModal();
                        alert('Category added successfully');
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Failed to add category');
                    }
                });
            }

            // File import handler
            const hiddenFileInput = document.getElementById('hiddenFileInput');
            if (hiddenFileInput) {
                hiddenFileInput.addEventListener('change', async function(e) {
                    const file = e.target.files[0];
                    if (!file) return;

                    const formData = new FormData();
                    formData.append('file', file);

                    try {
                        const response = await fetch(`${API_URL}/products/bulk`, {
                            method: 'POST',
                            body: formData
                        });
                        if (!response.ok) throw new Error('Failed to import products');
                        const result = await response.json();
                        await fetchProductsForSettings();
                        alert(`Successfully imported products`);
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Failed to import products');
                    }
                });
            }
        });

        // API Test Functions (prefixed with 'test' to avoid conflicts)
        async function testLogin() {
            const pin = document.getElementById('pin').value;
            try {
                const response = await fetch(`${API_URL}/employees/validate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ pin })
                });
                const result = await response.json();
                showResult('loginResult', result);
            } catch (error) {
                showResult('loginResult', { error: error.message });
            }
        }

        async function testGetProducts() {
            try {
                const response = await fetch(`${API_URL}/products`);
                const result = await response.json();
                showResult('productsResult', result);
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        async function testAddProduct() {
            const product = {
                name: document.getElementById('productName').value,
                price: parseFloat(document.getElementById('productPrice').value),
                category: document.getElementById('productCategory').value,
                description: document.getElementById('productDescription').value,
                inStock: document.getElementById('productInStock').checked
            };

            try {
                const response = await fetch(`${API_URL}/products`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(product)
                });
                const result = await response.json();
                showResult('productsResult', result);
                if (response.ok) {
                    clearProductForm();
                    testGetProducts();
                }
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        async function testUpdateProduct() {
            const productId = document.getElementById('productId').value;
            if (!productId) {
                showResult('productsResult', { error: 'No product selected for update' });
                return;
            }

            const product = {
                name: document.getElementById('productName').value,
                price: parseFloat(document.getElementById('productPrice').value),
                category: document.getElementById('productCategory').value,
                description: document.getElementById('productDescription').value,
                inStock: document.getElementById('productInStock').checked
            };

            try {
                const response = await fetch(`${API_URL}/products/${productId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(product)
                });
                const result = await response.json();
                showResult('productsResult', result);
                if (response.ok) {
                    clearProductForm();
                    testGetProducts();
                }
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        function clearProductForm() {
            document.getElementById('productId').value = '';
            document.getElementById('productName').value = '';
            document.getElementById('productPrice').value = '';
            document.getElementById('productCategory').value = '';
            document.getElementById('productDescription').value = '';
            document.getElementById('productInStock').checked = true;
        }

        async function testDownloadTemplate() {
            try {
                window.location.href = `${API_URL}/products/template`;
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        async function testImportProducts(event) {
            const file = event.target.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch(`${API_URL}/products/bulk`, {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                showResult('productsResult', result);
                if (response.ok) {
                    testGetProducts();
                }
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        async function testExportProducts() {
            try {
                window.location.href = `${API_URL}/products/export`;
            } catch (error) {
                showResult('productsResult', { error: error.message });
            }
        }

        // Category Functions
        async function testAddCategory() {
            const name = document.getElementById('categoryName').value;
            try {
                const response = await fetch(`${API_URL}/categories`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name })
                });
                const result = await response.json();
                showResult('categoriesResult', result);
                if (response.ok) {
                    document.getElementById('categoryName').value = '';
                    testGetCategories();
                }
            } catch (error) {
                showResult('categoriesResult', { error: error.message });
            }
        }

        async function testGetCategories() {
            try {
                const response = await fetch(`${API_URL}/categories`);
                const result = await response.json();
                showResult('categoriesResult', result);
            } catch (error) {
                showResult('categoriesResult', { error: error.message });
            }
        }

        // Employee Functions
        async function testGetEmployees() {
            try {
                const response = await fetch(`${API_URL}/employees`);
                const result = await response.json();
                showResult('employeesResult', result);
            } catch (error) {
                showResult('employeesResult', { error: error.message });
            }
        }

        async function testAddEmployee() {
            const employee = {
                name: document.getElementById('employeeName').value,
                pin: document.getElementById('employeePin').value,
                role: document.getElementById('employeeRole').value
            };

            try {
                const response = await fetch(`${API_URL}/employees`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(employee)
                });
                const result = await response.json();
                showResult('employeesResult', result);
                if (response.ok) {
                    clearEmployeeForm();
                    testGetEmployees();
                }
            } catch (error) {
                showResult('employeesResult', { error: error.message });
            }
        }

        async function testUpdateEmployee() {
            const employeeId = document.getElementById('employeeId').value;
            if (!employeeId) {
                showResult('employeesResult', { error: 'No employee selected for update' });
                return;
            }

            const employee = {
                name: document.getElementById('employeeName').value,
                role: document.getElementById('employeeRole').value
            };

            const pin = document.getElementById('employeePin').value;
            if (pin) {
                employee.pin = pin;
            }

            try {
                const response = await fetch(`${API_URL}/employees/${employeeId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(employee)
                });
                const result = await response.json();
                showResult('employeesResult', result);
                if (response.ok) {
                    clearEmployeeForm();
                    testGetEmployees();
                }
            } catch (error) {
                showResult('employeesResult', { error: error.message });
            }
        }

        function clearEmployeeForm() {
            document.getElementById('employeeId').value = '';
            document.getElementById('employeeName').value = '';
            document.getElementById('employeePin').value = '';
            document.getElementById('employeeRole').value = 'server';
        }

        // System Config Functions
        async function testGetConfig() {
            try {
                const response = await fetch(`${API_URL}/settings`);
                const result = await response.json();
                showResult('configResult', result);
                
                // Fill form with current values
                document.getElementById('taxRate').value = result.tax_rate;
                document.getElementById('businessName').value = result.business_name;
                document.getElementById('businessAddress').value = result.business_address;
                document.getElementById('businessPhone').value = result.business_phone;
                document.getElementById('receiptHeader').value = result.receipt_header;
                document.getElementById('receiptFooter').value = result.receipt_footer;
            } catch (error) {
                showResult('configResult', { error: error.message });
            }
        }

        async function testUpdateConfig() {
            const config = {
                tax_rate: parseFloat(document.getElementById('taxRate').value),
                business_name: document.getElementById('businessName').value,
                business_address: document.getElementById('businessAddress').value,
                business_phone: document.getElementById('businessPhone').value,
                receipt_header: document.getElementById('receiptHeader').value,
                receipt_footer: document.getElementById('receiptFooter').value
            };

            try {
                const response = await fetch(`${API_URL}/settings`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                const result = await response.json();
                showResult('configResult', result);
                if (response.ok) {
                    testGetConfig();
                }
            } catch (error) {
                showResult('configResult', { error: error.message });
            }
        }

        // Utility Functions
        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.classList.remove('hidden');
        }
    </script>
</body>
</html>
