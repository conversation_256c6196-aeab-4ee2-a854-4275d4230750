import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Smartphone, Nfc, QrCode, CreditCard, CheckCircle, Clock, AlertTriangle, RefreshCw } from 'lucide-react';

interface DigitalWallet {
  id: string;
  name: string;
  type: 'apple_pay' | 'google_pay' | 'samsung_pay' | 'paypal' | 'venmo' | 'cashapp';
  icon: string;
  enabled: boolean;
  processing_fee: number;
  daily_transactions: number;
  daily_volume: number;
  success_rate: number;
  avg_processing_time: number;
  supported_devices: string[];
  security_features: string[];
}

interface NFCPayment {
  id: string;
  transaction_id: string;
  amount: number;
  payment_method: string;
  device_type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  timestamp: string;
  processing_time: number;
  customer_id?: string;
}

interface QRPayment {
  id: string;
  qr_code: string;
  amount: number;
  description: string;
  expires_at: string;
  status: 'active' | 'used' | 'expired';
  payment_method?: string;
  completed_at?: string;
}

const DigitalWalletPayments: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [digitalWallets, setDigitalWallets] = useState<DigitalWallet[]>([]);
  const [nfcTransactions, setNfcTransactions] = useState<NFCPayment[]>([]);
  const [qrPayments, setQrPayments] = useState<QRPayment[]>([]);
  const [activeView, setActiveView] = useState<'wallets' | 'nfc' | 'qr'>('wallets');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showQRGenerator, setShowQRGenerator] = useState(false);
  const [qrAmount, setQrAmount] = useState('');
  const [qrDescription, setQrDescription] = useState('');

  // Load digital wallet data
  useEffect(() => {
    const loadDigitalWalletData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📱 Loading digital wallet data...');
        
        const [walletsResponse, nfcResponse, qrResponse] = await Promise.all([
          apiCall('/api/payments/digital-wallets'),
          apiCall('/api/payments/nfc-transactions'),
          apiCall('/api/payments/qr-payments')
        ]);
        
        if (walletsResponse.ok && nfcResponse.ok && qrResponse.ok) {
          const walletsData = await walletsResponse.json();
          const nfcData = await nfcResponse.json();
          const qrData = await qrResponse.json();
          
          setDigitalWallets(walletsData);
          setNfcTransactions(nfcData);
          setQrPayments(qrData);
          console.log('✅ Digital wallet data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading digital wallet data:', error);
        setError('Failed to load digital wallet data. Using mock data.');
        
        // Fallback to mock data
        const mockWallets: DigitalWallet[] = [
          {
            id: 'apple_pay',
            name: 'Apple Pay',
            type: 'apple_pay',
            icon: '🍎',
            enabled: true,
            processing_fee: 2.6,
            daily_transactions: 89,
            daily_volume: 2450.75,
            success_rate: 98.5,
            avg_processing_time: 1.2,
            supported_devices: ['iPhone', 'iPad', 'Apple Watch', 'Mac'],
            security_features: ['Touch ID', 'Face ID', 'Secure Element', 'Tokenization']
          },
          {
            id: 'google_pay',
            name: 'Google Pay',
            type: 'google_pay',
            icon: '🟢',
            enabled: true,
            processing_fee: 2.6,
            daily_transactions: 67,
            daily_volume: 1890.25,
            success_rate: 97.8,
            avg_processing_time: 1.4,
            supported_devices: ['Android Phone', 'Wear OS', 'Chrome Browser'],
            security_features: ['Fingerprint', 'PIN', 'Pattern', 'Tokenization']
          },
          {
            id: 'samsung_pay',
            name: 'Samsung Pay',
            type: 'samsung_pay',
            icon: '📱',
            enabled: true,
            processing_fee: 2.7,
            daily_transactions: 34,
            daily_volume: 890.50,
            success_rate: 96.2,
            avg_processing_time: 1.6,
            supported_devices: ['Samsung Galaxy', 'Galaxy Watch'],
            security_features: ['Fingerprint', 'Iris Scan', 'Knox Security']
          },
          {
            id: 'paypal',
            name: 'PayPal',
            type: 'paypal',
            icon: '💙',
            enabled: true,
            processing_fee: 3.1,
            daily_transactions: 45,
            daily_volume: 1250.00,
            success_rate: 99.1,
            avg_processing_time: 2.1,
            supported_devices: ['Any Device', 'Web Browser', 'Mobile App'],
            security_features: ['Two-Factor Auth', 'Encryption', 'Fraud Protection']
          },
          {
            id: 'venmo',
            name: 'Venmo',
            type: 'venmo',
            icon: '💸',
            enabled: false,
            processing_fee: 2.9,
            daily_transactions: 0,
            daily_volume: 0,
            success_rate: 0,
            avg_processing_time: 0,
            supported_devices: ['Mobile App'],
            security_features: ['PIN', 'Biometric', 'Bank Encryption']
          }
        ];

        const mockNFC: NFCPayment[] = [
          {
            id: 'nfc_1',
            transaction_id: 'TXN_001',
            amount: 24.50,
            payment_method: 'Apple Pay',
            device_type: 'iPhone',
            status: 'completed',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            processing_time: 1.1
          },
          {
            id: 'nfc_2',
            transaction_id: 'TXN_002',
            amount: 18.75,
            payment_method: 'Google Pay',
            device_type: 'Android',
            status: 'completed',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            processing_time: 1.3
          },
          {
            id: 'nfc_3',
            transaction_id: 'TXN_003',
            amount: 45.00,
            payment_method: 'Samsung Pay',
            device_type: 'Galaxy S24',
            status: 'processing',
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            processing_time: 0
          }
        ];

        const mockQR: QRPayment[] = [
          {
            id: 'qr_1',
            qr_code: 'QR_PAY_001',
            amount: 32.50,
            description: 'Table 5 - Lunch Order',
            expires_at: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
            status: 'active'
          },
          {
            id: 'qr_2',
            qr_code: 'QR_PAY_002',
            amount: 28.75,
            description: 'Takeout Order #102',
            expires_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            status: 'expired'
          }
        ];

        setDigitalWallets(mockWallets);
        setNfcTransactions(mockNFC);
        setQrPayments(mockQR);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadDigitalWalletData();
    
    // Refresh every 10 seconds for real-time updates
    const interval = setInterval(loadDigitalWalletData, 10000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const generateQRPayment = async () => {
    if (!qrAmount || !qrDescription) {
      alert('Please enter amount and description');
      return;
    }

    try {
      console.log('📱 Generating QR payment code...');
      
      const response = await apiCall('/api/payments/qr-generate', {
        method: 'POST',
        body: JSON.stringify({
          amount: parseFloat(qrAmount),
          description: qrDescription,
          expires_in: 900 // 15 minutes
        })
      });
      
      if (response.ok) {
        const newQR = await response.json();
        setQrPayments(prev => [newQR, ...prev]);
        setQrAmount('');
        setQrDescription('');
        setShowQRGenerator(false);
        console.log('✅ QR payment generated successfully');
      }
    } catch (error) {
      console.error('❌ Error generating QR payment:', error);
      alert('Failed to generate QR payment. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': case 'used': return 'bg-green-100 text-green-800';
      case 'processing': case 'active': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': case 'used': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing': case 'active': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'failed': case 'expired': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getWalletStats = () => {
    const enabledWallets = digitalWallets.filter(w => w.enabled).length;
    const totalTransactions = digitalWallets.reduce((sum, w) => sum + w.daily_transactions, 0);
    const totalVolume = digitalWallets.reduce((sum, w) => sum + w.daily_volume, 0);
    const avgSuccessRate = digitalWallets.length > 0 
      ? digitalWallets.reduce((sum, w) => sum + w.success_rate, 0) / digitalWallets.length 
      : 0;
    
    return { enabledWallets, totalTransactions, totalVolume, avgSuccessRate };
  };

  const stats = getWalletStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading digital wallet data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Digital Wallet & NFC Payments</h2>
            <p className="text-sm text-gray-500">Manage contactless payment methods</p>
          </div>
          <div className="flex items-center space-x-3">
            {activeView === 'qr' && (
              <button
                onClick={() => setShowQRGenerator(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
              >
                <QrCode className="h-4 w-4" />
                <span>Generate QR</span>
              </button>
            )}
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Enabled Wallets</p>
                <p className="text-2xl font-bold text-gray-900">{stats.enabledWallets}/{digitalWallets.length}</p>
              </div>
              <Smartphone className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Daily Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalTransactions}</p>
              </div>
              <Nfc className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Daily Volume</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalVolume.toFixed(2)}</p>
              </div>
              <CreditCard className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avgSuccessRate.toFixed(1)}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'wallets', label: 'Digital Wallets', icon: Smartphone },
            { id: 'nfc', label: 'NFC Transactions', icon: Nfc },
            { id: 'qr', label: 'QR Payments', icon: QrCode }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'wallets' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {digitalWallets.map((wallet) => (
              <div
                key={wallet.id}
                className="bg-white border border-gray-200 rounded-lg p-4"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{wallet.icon}</span>
                    <div>
                      <h3 className="font-semibold text-gray-900">{wallet.name}</h3>
                      <p className="text-sm text-gray-600">{wallet.processing_fee}% processing fee</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={wallet.enabled}
                        onChange={() => {
                          setDigitalWallets(prev => prev.map(w => 
                            w.id === wallet.id ? { ...w, enabled: !w.enabled } : w
                          ));
                        }}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Daily Transactions</p>
                    <p className="font-semibold text-gray-900">{wallet.daily_transactions}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Daily Volume</p>
                    <p className="font-semibold text-gray-900">${wallet.daily_volume.toFixed(0)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Success Rate</p>
                    <p className="font-semibold text-green-600">{wallet.success_rate}%</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Avg Time</p>
                    <p className="font-semibold text-gray-900">{wallet.avg_processing_time}s</p>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">Supported Devices</p>
                  <div className="flex flex-wrap gap-1">
                    {wallet.supported_devices.slice(0, 2).map((device) => (
                      <span key={device} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {device}
                      </span>
                    ))}
                    {wallet.supported_devices.length > 2 && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        +{wallet.supported_devices.length - 2}
                      </span>
                    )}
                  </div>
                </div>

                <div>
                  <p className="text-xs text-gray-500 mb-1">Security Features</p>
                  <div className="flex flex-wrap gap-1">
                    {wallet.security_features.slice(0, 2).map((feature) => (
                      <span key={feature} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                        {feature}
                      </span>
                    ))}
                    {wallet.security_features.length > 2 && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        +{wallet.security_features.length - 2}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'nfc' && (
          <div className="space-y-3">
            {nfcTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className="bg-white border border-gray-200 rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(transaction.status)}
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(transaction.status)}`}>
                        {transaction.status.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{transaction.transaction_id}</h4>
                      <p className="text-sm text-gray-600">
                        {transaction.payment_method} • {transaction.device_type}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">${transaction.amount.toFixed(2)}</p>
                    <p className="text-sm text-gray-500">
                      {new Date(transaction.timestamp).toLocaleTimeString()}
                    </p>
                    {transaction.processing_time > 0 && (
                      <p className="text-xs text-gray-400">{transaction.processing_time}s</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'qr' && (
          <div className="space-y-3">
            {qrPayments.map((qr) => (
              <div
                key={qr.id}
                className="bg-white border border-gray-200 rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(qr.status)}
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(qr.status)}`}>
                        {qr.status.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{qr.description}</h4>
                      <p className="text-sm text-gray-600">Code: {qr.qr_code}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">${qr.amount.toFixed(2)}</p>
                    <p className="text-sm text-gray-500">
                      Expires: {new Date(qr.expires_at).toLocaleTimeString()}
                    </p>
                    {qr.completed_at && (
                      <p className="text-xs text-green-600">
                        Paid: {new Date(qr.completed_at).toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* QR Generator Modal */}
      {showQRGenerator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Generate QR Payment</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                <input
                  type="number"
                  step="0.01"
                  value={qrAmount}
                  onChange={(e) => setQrAmount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={qrDescription}
                  onChange={(e) => setQrDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Order description"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowQRGenerator(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={generateQRPayment}
                disabled={!qrAmount || !qrDescription}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                Generate QR
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DigitalWalletPayments;
