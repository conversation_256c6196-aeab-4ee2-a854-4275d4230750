const http = require('http');

const data = JSON.stringify({
  name: "Test Employee",
  pin: "1234",
  role: "server"
});

const options = {
  hostname: 'localhost',
  port: 4000,
  path: '/employees',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

const req = http.request(options, res => {
  console.log(`Status: ${res.statusCode}`);
  res.on('data', d => {
    process.stdout.write(d);
  });
});

req.on('error', error => {
  console.error(error);
});

req.write(data);
req.end();
