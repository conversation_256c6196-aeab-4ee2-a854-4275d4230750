// Run Phase 3 Database Schema Fixes
require('dotenv').config();
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'BARPOS',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'BARPOS',
  password: process.env.DB_PASSWORD || 'Chaand@0319',
  port: parseInt(process.env.DB_PORT) || 5432,
});

async function runSchemaFixes() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Starting Phase 3 Database Schema Fixes...\n');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', '010_schema_fixes_phase3.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📋 Found ${statements.length} SQL statements to execute\n`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
        
        // Skip empty statements and comments
        if (!statement || statement.startsWith('--')) {
          continue;
        }
        
        await client.query(statement);
        console.log(`✅ Statement ${i + 1} completed successfully`);
        
      } catch (error) {
        console.error(`❌ Error in statement ${i + 1}:`, error.message);
        
        // Continue with non-critical errors
        if (error.code === '42P07' || // relation already exists
            error.code === '42701' || // column already exists
            error.code === '42P16') {  // extension already exists
          console.log(`⚠️  Non-critical error, continuing...`);
          continue;
        }
        
        // Log but continue for other errors
        console.log(`⚠️  Error logged, continuing with next statement...`);
      }
    }
    
    console.log('\n🎉 Phase 3 Database Schema Fixes completed!');
    
    // Verify the fixes
    console.log('\n🔍 Verifying schema fixes...');
    
    // Check if pg_stat_statements extension is available
    try {
      const extResult = await client.query(`
        SELECT extname FROM pg_extension WHERE extname = 'pg_stat_statements'
      `);
      
      if (extResult.rows.length > 0) {
        console.log('✅ pg_stat_statements extension is available');
      } else {
        console.log('⚠️  pg_stat_statements extension not found (may require superuser privileges)');
      }
    } catch (error) {
      console.log('⚠️  Could not check pg_stat_statements extension');
    }
    
    // Check column names in orders table
    try {
      const columnsResult = await client.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'orders' 
        AND column_name IN ('amount', 'total_amount', 'total')
      `);
      
      const columns = columnsResult.rows.map(row => row.column_name);
      console.log('✅ Orders table columns:', columns.join(', '));
      
      if (columns.includes('total_amount')) {
        console.log('✅ Orders table uses standardized total_amount column');
      }
    } catch (error) {
      console.log('⚠️  Could not verify orders table columns');
    }
    
    // Check exchange rate precision
    try {
      const precisionResult = await client.query(`
        SELECT column_name, numeric_precision, numeric_scale
        FROM information_schema.columns 
        WHERE table_name = 'global_exchange_rates' 
        AND column_name = 'exchange_rate'
      `);
      
      if (precisionResult.rows.length > 0) {
        const { numeric_precision, numeric_scale } = precisionResult.rows[0];
        console.log(`✅ Exchange rate precision: ${numeric_precision},${numeric_scale}`);
      }
    } catch (error) {
      console.log('⚠️  Could not verify exchange rate precision');
    }
    
    // Test a sample query
    try {
      const testResult = await client.query(`
        SELECT COUNT(*) as tenant_count FROM tenants
      `);
      console.log(`✅ Database query test passed: ${testResult.rows[0].tenant_count} tenants found`);
    } catch (error) {
      console.log('⚠️  Database query test failed:', error.message);
    }
    
    console.log('\n🎯 Schema fixes verification completed!');
    
  } catch (error) {
    console.error('💥 Fatal error during schema fixes:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the schema fixes
if (require.main === module) {
  runSchemaFixes()
    .then(() => {
      console.log('\n✨ All schema fixes applied successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Schema fixes failed:', error);
      process.exit(1);
    });
}

module.exports = { runSchemaFixes };
