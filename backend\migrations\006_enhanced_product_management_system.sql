-- Enhanced Product Management System Database Schema
-- This migration enhances the product management system with comprehensive CRUD functionality

-- Enhanced products table (building on existing structure)
ALTER TABLE products ADD COLUMN IF NOT EXISTS barcode VARCHAR(100);
ALTER TABLE products ADD COLUMN IF NOT EXISTS sku VARCHAR(100) UNIQUE;
ALTER TABLE products ADD COLUMN IF NOT EXISTS cost_price DECIMAL(10,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS profit_margin DECIMAL(5,2);
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false;
ALTER TABLE products ADD COLUMN IF NOT EXISTS preparation_time INTEGER DEFAULT 0; -- in minutes
ALTER TABLE products ADD COLUMN IF NOT EXISTS allergens JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS nutritional_info JSONB DEFAULT '{}';
ALTER TABLE products ADD COLUMN IF NOT EXISTS modifiers JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS variants JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS tags JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0;
ALTER TABLE products ADD COLUMN IF NOT EXISTS image_url VARCHAR(500);
ALTER TABLE products ADD COLUMN IF NOT EXISTS gallery_images JSONB DEFAULT '[]';
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE products ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES employees(id) ON DELETE SET NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES employees(id) ON DELETE SET NULL;
ALTER TABLE products ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE products ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Enhanced categories table (building on existing structure)
ALTER TABLE categories ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS image_url VARCHAR(500);
ALTER TABLE categories ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#3B82F6'; -- Hex color code
ALTER TABLE categories ADD COLUMN IF NOT EXISTS icon VARCHAR(100) DEFAULT 'package';
ALTER TABLE categories ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE categories ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Product pricing with location-specific options (enhanced from tenant admin system)
CREATE TABLE IF NOT EXISTS product_pricing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    price DECIMAL(10,2) NOT NULL,
    cost_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'CAD',
    is_active BOOLEAN DEFAULT true,
    effective_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    effective_until TIMESTAMP,
    created_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, location_id, effective_from)
);

-- Product inventory tracking (enhanced from tenant admin system)
CREATE TABLE IF NOT EXISTS product_inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    current_stock INTEGER DEFAULT 0,
    minimum_stock INTEGER DEFAULT 0,
    maximum_stock INTEGER DEFAULT 1000,
    reorder_point INTEGER DEFAULT 10,
    unit_of_measure VARCHAR(20) DEFAULT 'each',
    cost_per_unit DECIMAL(10,2),
    last_restocked TIMESTAMP,
    last_counted TIMESTAMP,
    expiry_date DATE,
    supplier_id UUID,
    supplier_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, location_id)
);

-- Stock movements tracking (enhanced from tenant admin system)
CREATE TABLE IF NOT EXISTS stock_movements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    movement_type VARCHAR(20) CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer', 'waste', 'return')),
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,
    unit_cost DECIMAL(10,2),
    total_value DECIMAL(10,2),
    reference_type VARCHAR(50), -- 'order', 'restock', 'adjustment', 'transfer', 'waste'
    reference_id UUID,
    notes TEXT,
    performed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product variants (for items with different sizes, colors, etc.)
CREATE TABLE IF NOT EXISTS product_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    variant_name VARCHAR(100) NOT NULL,
    variant_type VARCHAR(50), -- 'size', 'color', 'style', etc.
    price_adjustment DECIMAL(10,2) DEFAULT 0,
    cost_adjustment DECIMAL(10,2) DEFAULT 0,
    sku_suffix VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product modifiers (add-ons, customizations)
CREATE TABLE IF NOT EXISTS product_modifiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    modifier_name VARCHAR(100) NOT NULL,
    modifier_type VARCHAR(50), -- 'addon', 'substitution', 'customization'
    price DECIMAL(10,2) DEFAULT 0,
    cost DECIMAL(10,2) DEFAULT 0,
    is_required BOOLEAN DEFAULT false,
    max_selections INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Low stock alerts (enhanced from tenant admin system)
CREATE TABLE IF NOT EXISTS stock_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    alert_type VARCHAR(20) CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock', 'expiring', 'expired')),
    current_stock INTEGER,
    threshold_value INTEGER,
    severity VARCHAR(10) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    message TEXT,
    is_acknowledged BOOLEAN DEFAULT false,
    acknowledged_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    acknowledged_at TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product import/export logs (enhanced from tenant admin system)
CREATE TABLE IF NOT EXISTS import_export_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    operation_type VARCHAR(10) CHECK (operation_type IN ('import', 'export')),
    entity_type VARCHAR(20) DEFAULT 'products' CHECK (entity_type IN ('products', 'categories', 'inventory')),
    file_name VARCHAR(255),
    file_size INTEGER,
    file_format VARCHAR(10), -- 'csv', 'xlsx', 'json'
    records_processed INTEGER DEFAULT 0,
    records_successful INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed', 'cancelled')),
    error_details JSONB DEFAULT '[]',
    success_details JSONB DEFAULT '[]',
    performed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Product analytics and performance tracking
CREATE TABLE IF NOT EXISTS product_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    units_sold INTEGER DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0,
    cost_of_goods DECIMAL(10,2) DEFAULT 0,
    profit DECIMAL(10,2) DEFAULT 0,
    profit_margin DECIMAL(5,2) DEFAULT 0,
    times_ordered INTEGER DEFAULT 0,
    average_order_quantity DECIMAL(5,2) DEFAULT 0,
    customer_rating DECIMAL(3,2) DEFAULT 0,
    return_count INTEGER DEFAULT 0,
    waste_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, location_id, date)
);

-- Supplier management
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    payment_terms VARCHAR(50),
    delivery_schedule VARCHAR(100),
    minimum_order DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    rating DECIMAL(3,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Product supplier relationships
CREATE TABLE IF NOT EXISTS product_suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE CASCADE,
    supplier_sku VARCHAR(100),
    cost_price DECIMAL(10,2),
    minimum_order_quantity INTEGER DEFAULT 1,
    lead_time_days INTEGER DEFAULT 7,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, supplier_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_products_tenant_category ON products(tenant_id, category_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode);
CREATE INDEX IF NOT EXISTS idx_products_active_featured ON products(is_active, is_featured);
CREATE INDEX IF NOT EXISTS idx_categories_tenant_parent ON categories(tenant_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_product_pricing_product_location ON product_pricing(product_id, location_id);
CREATE INDEX IF NOT EXISTS idx_product_inventory_location_stock ON product_inventory(location_id, current_stock);
CREATE INDEX IF NOT EXISTS idx_stock_movements_product_date ON stock_movements(product_id, created_at);
CREATE INDEX IF NOT EXISTS idx_stock_alerts_tenant_unresolved ON stock_alerts(tenant_id, is_acknowledged, resolved_at);
CREATE INDEX IF NOT EXISTS idx_product_analytics_product_date ON product_analytics(product_id, date);
CREATE INDEX IF NOT EXISTS idx_import_export_logs_tenant_status ON import_export_logs(tenant_id, status);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_pricing_updated_at BEFORE UPDATE ON product_pricing FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_product_inventory_updated_at BEFORE UPDATE ON product_inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for automatic stock alerts
CREATE OR REPLACE FUNCTION check_stock_levels()
RETURNS TRIGGER AS $$
BEGIN
    -- Check for low stock
    IF NEW.current_stock <= NEW.minimum_stock AND NEW.current_stock > 0 THEN
        INSERT INTO stock_alerts (tenant_id, location_id, product_id, alert_type, current_stock, threshold_value, severity, message)
        SELECT 
            p.tenant_id,
            NEW.location_id,
            NEW.product_id,
            'low_stock',
            NEW.current_stock,
            NEW.minimum_stock,
            CASE 
                WHEN NEW.current_stock <= NEW.minimum_stock * 0.5 THEN 'high'
                WHEN NEW.current_stock <= NEW.minimum_stock * 0.8 THEN 'medium'
                ELSE 'low'
            END,
            'Product ' || p.name || ' is running low on stock'
        FROM products p
        WHERE p.id = NEW.product_id
        AND NOT EXISTS (
            SELECT 1 FROM stock_alerts sa 
            WHERE sa.product_id = NEW.product_id 
            AND sa.location_id = NEW.location_id 
            AND sa.alert_type = 'low_stock' 
            AND sa.is_acknowledged = false
        );
    END IF;

    -- Check for out of stock
    IF NEW.current_stock = 0 THEN
        INSERT INTO stock_alerts (tenant_id, location_id, product_id, alert_type, current_stock, threshold_value, severity, message)
        SELECT 
            p.tenant_id,
            NEW.location_id,
            NEW.product_id,
            'out_of_stock',
            NEW.current_stock,
            0,
            'critical',
            'Product ' || p.name || ' is out of stock'
        FROM products p
        WHERE p.id = NEW.product_id
        AND NOT EXISTS (
            SELECT 1 FROM stock_alerts sa 
            WHERE sa.product_id = NEW.product_id 
            AND sa.location_id = NEW.location_id 
            AND sa.alert_type = 'out_of_stock' 
            AND sa.is_acknowledged = false
        );
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER check_stock_levels_trigger 
    AFTER UPDATE ON product_inventory 
    FOR EACH ROW 
    EXECUTE FUNCTION check_stock_levels();

-- Insert default categories for existing tenants
INSERT INTO categories (tenant_id, name, description, color, icon, sort_order)
SELECT 
    t.id as tenant_id,
    category_data.name,
    category_data.description,
    category_data.color,
    category_data.icon,
    category_data.sort_order
FROM tenants t
CROSS JOIN (
    VALUES 
        ('Appetizers', 'Starters and small plates', '#F59E0B', 'star', 1),
        ('Main Course', 'Primary dishes and entrees', '#3B82F6', 'utensils', 2),
        ('Salads', 'Fresh salads and healthy options', '#10B981', 'leaf', 3),
        ('Desserts', 'Sweet treats and desserts', '#EC4899', 'cake', 4),
        ('Beverages', 'Drinks and refreshments', '#8B5CF6', 'coffee', 5),
        ('Sides', 'Side dishes and accompaniments', '#F97316', 'plus', 6)
) AS category_data(name, description, color, icon, sort_order)
WHERE NOT EXISTS (
    SELECT 1 FROM categories c 
    WHERE c.tenant_id = t.id AND c.name = category_data.name
);

-- Insert sample products for demonstration (only if no products exist)
INSERT INTO products (tenant_id, category_id, name, description, price, cost_price, sku, current_stock, minimum_stock, preparation_time, is_active, is_featured)
SELECT 
    t.id as tenant_id,
    c.id as category_id,
    product_data.name,
    product_data.description,
    product_data.price,
    product_data.cost_price,
    product_data.sku,
    product_data.current_stock,
    product_data.minimum_stock,
    product_data.preparation_time,
    product_data.is_active,
    product_data.is_featured
FROM tenants t
JOIN categories c ON c.tenant_id = t.id AND c.name = 'Main Course'
CROSS JOIN (
    VALUES 
        ('Grilled Chicken Breast', 'Tender grilled chicken breast with herbs and spices', 18.99, 8.50, 'GCB001', 25, 10, 15, true, true),
        ('Beef Burger', 'Juicy beef patty with lettuce, tomato, and special sauce', 16.99, 7.80, 'BB001', 20, 10, 12, true, true),
        ('Salmon Fillet', 'Fresh Atlantic salmon with lemon butter sauce', 24.99, 12.00, 'SF001', 15, 5, 18, true, false)
) AS product_data(name, description, price, cost_price, sku, current_stock, minimum_stock, preparation_time, is_active, is_featured)
WHERE NOT EXISTS (
    SELECT 1 FROM products p 
    WHERE p.tenant_id = t.id AND p.sku = product_data.sku
);

-- Initialize product inventory for existing products
INSERT INTO product_inventory (product_id, location_id, current_stock, minimum_stock, maximum_stock, reorder_point)
SELECT 
    p.id as product_id,
    l.id as location_id,
    p.current_stock,
    p.minimum_stock,
    100 as maximum_stock,
    p.minimum_stock + 5 as reorder_point
FROM products p
JOIN locations l ON l.tenant_id = p.tenant_id
WHERE NOT EXISTS (
    SELECT 1 FROM product_inventory pi 
    WHERE pi.product_id = p.id AND pi.location_id = l.id
);
