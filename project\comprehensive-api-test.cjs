#!/usr/bin/env node

/**
 * Comprehensive API Endpoint Testing Suite
 * Tests all backend endpoints systematically and identifies missing endpoints
 */

// Simple HTTP request function
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          data: body,
          success: res.statusCode < 400
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test results storage
let testResults = {
  passed: 0,
  failed: 0,
  missing: 0,
  errors: []
};

// Critical endpoints to test
const criticalEndpoints = [
  // Core functionality
  { method: 'GET', path: '/api/health', description: 'Health check' },
  { method: 'GET', path: '/api/products', description: 'Get products' },
  { method: 'GET', path: '/api/categories', description: 'Get categories' },
  { method: 'GET', path: '/api/orders', description: 'Get orders' },

  // Phase 3I-3L Missing Endpoints
  { method: 'GET', path: '/api/i18n/translations/en', description: 'Multi-language translations' },
  { method: 'POST', path: '/api/voice/recognize', description: 'Voice recognition' },
  { method: 'POST', path: '/api/cultural/analyze', description: 'Cultural intelligence' },
  { method: 'GET', path: '/api/compliance/gdpr', description: 'GDPR compliance' },

  // Phase 4 Enterprise (should exist)
  { method: 'GET', path: '/api/admin/analytics/advanced', description: 'Advanced analytics' },
  { method: 'GET', path: '/api/admin/ai/insights', description: 'AI insights' },
  { method: 'GET', path: '/api/admin/system/health', description: 'System health' }
];

// Helper functions
function logResult(status, endpoint, message) {
  const timestamp = new Date().toISOString();
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${statusIcon} [${timestamp}] ${endpoint}: ${message}`);
}

// Test individual endpoint
async function testEndpoint(endpoint) {
  try {
    const response = await makeRequest(endpoint.method, endpoint.path);

    if (response.status === 404) {
      testResults.missing++;
      logResult('MISSING', `${endpoint.method} ${endpoint.path}`, `404 Not Found - ${endpoint.description}`);
      return false;
    } else if (response.status === 401 || response.status === 403) {
      testResults.passed++;
      logResult('PASS', `${endpoint.method} ${endpoint.path}`, `Endpoint exists (${response.status}) - ${endpoint.description}`);
      return true;
    } else if (response.status >= 200 && response.status < 400) {
      testResults.passed++;
      logResult('PASS', `${endpoint.method} ${endpoint.path}`, `Success (${response.status}) - ${endpoint.description}`);
      return true;
    } else {
      testResults.failed++;
      logResult('FAIL', `${endpoint.method} ${endpoint.path}`, `Error ${response.status} - ${endpoint.description}`);
      return false;
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      logResult('FAIL', `${endpoint.method} ${endpoint.path}`, 'Backend server not running');
      testResults.failed++;
      return false;
    } else {
      testResults.failed++;
      testResults.errors.push({ endpoint: `${endpoint.method} ${endpoint.path}`, error: error.message });
      logResult('FAIL', `${endpoint.method} ${endpoint.path}`, `Error: ${error.message}`);
      return false;
    }
  }
}

// Main testing function
async function runComprehensiveAudit() {
  console.log('🔍 COMPREHENSIVE POS SYSTEM AUDIT');
  console.log('='.repeat(80));
  console.log(`📡 Testing ${criticalEndpoints.length} critical endpoints...\n`);

  // Test backend connectivity first
  try {
    const healthCheck = await makeRequest('GET', '/api/health');
    if (healthCheck.success) {
      logResult('PASS', 'Backend Connectivity', `Server is running (${healthCheck.status})`);
    } else {
      logResult('FAIL', 'Backend Connectivity', `Server error (${healthCheck.status})`);
    }
  } catch (error) {
    logResult('FAIL', 'Backend Connectivity', 'Server is not responding');
    console.log('\n❌ Backend server is not running. Please start the server first.');
    console.log('   Command: cd backend && node working-server.js\n');
    return;
  }

  console.log('\n🧪 Testing Individual Endpoints...\n');

  // Test each endpoint
  for (const endpoint of criticalEndpoints) {
    await testEndpoint(endpoint);
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Generate comprehensive report
  console.log('\n' + '='.repeat(80));
  console.log('📊 AUDIT RESULTS');
  console.log('='.repeat(80));

  console.log(`\n📈 SUMMARY:`);
  console.log(`   ✅ Working: ${testResults.passed}`);
  console.log(`   ❌ Failed: ${testResults.failed}`);
  console.log(`   ⚠️  Missing: ${testResults.missing}`);
  console.log(`   📊 Total: ${criticalEndpoints.length}`);

  const successRate = ((testResults.passed / criticalEndpoints.length) * 100).toFixed(1);
  console.log(`   🎯 Success Rate: ${successRate}%`);

  // Identify missing Phase 3I-3L endpoints
  const missingPhase3Endpoints = criticalEndpoints.filter((endpoint, index) => {
    return endpoint.path.includes('/i18n/') ||
           endpoint.path.includes('/voice/') ||
           endpoint.path.includes('/cultural/') ||
           endpoint.path.includes('/compliance/');
  });

  console.log(`\n🚨 CRITICAL ISSUES IDENTIFIED:`);

  if (testResults.missing > 0) {
    console.log(`\n❌ MISSING ENDPOINTS (${testResults.missing}):`);
    console.log('   These endpoints are referenced in frontend components but not implemented:');

    const missingI18n = testResults.missing > 0 && criticalEndpoints.some(e => e.path.includes('/i18n/'));
    const missingVoice = testResults.missing > 0 && criticalEndpoints.some(e => e.path.includes('/voice/'));
    const missingCultural = testResults.missing > 0 && criticalEndpoints.some(e => e.path.includes('/cultural/'));
    const missingCompliance = testResults.missing > 0 && criticalEndpoints.some(e => e.path.includes('/compliance/'));

    if (missingI18n) console.log('   • Phase 3I: Multi-language localization endpoints missing');
    if (missingVoice) console.log('   • Phase 3J: Voice recognition endpoints missing');
    if (missingCultural) console.log('   • Phase 3K: Cultural intelligence endpoints missing');
    if (missingCompliance) console.log('   • Phase 3L: Global compliance endpoints missing');
  }

  if (testResults.failed > 0) {
    console.log(`\n🔧 BROKEN ENDPOINTS (${testResults.failed}):`);
    testResults.errors.forEach(error => {
      console.log(`   • ${error.endpoint}: ${error.error}`);
    });
  }

  // Recommendations
  console.log('\n🎯 IMPLEMENTATION PRIORITIES:');

  if (testResults.missing > 0) {
    console.log('   1. 🚨 HIGH PRIORITY: Implement missing Phase 3I-3L endpoints');
    console.log('   2. 📝 Create backend API files for multi-language, voice, cultural, and compliance features');
    console.log('   3. 🔗 Update frontend components to handle missing endpoint gracefully');
  }

  if (testResults.failed > 0) {
    console.log('   4. 🔧 Fix broken endpoints and error handling');
    console.log('   5. 🔐 Review authentication and authorization logic');
  }

  if (successRate < 70) {
    console.log('   6. ⚠️ CRITICAL: System has significant functionality gaps');
    console.log('   7. 🎯 Focus on core POS functionality first, then Phase 3 features');
  } else {
    console.log('   ✅ Core system is stable. Focus on implementing Phase 3I-3L features.');
  }

  console.log('\n📋 NEXT STEPS:');
  console.log('   1. Implement missing backend endpoints for Phase 3I-3L');
  console.log('   2. Add proper error handling for missing endpoints in frontend');
  console.log('   3. Create mock responses for development/testing');
  console.log('   4. Update integration tests to cover new endpoints');

  console.log('\n' + '='.repeat(80));
  console.log('🏁 Audit Complete!');
  console.log('='.repeat(80));
}

// Run the audit
runComprehensiveAudit().catch(error => {
  console.error('💥 Audit failed:', error);
  process.exit(1);
});


