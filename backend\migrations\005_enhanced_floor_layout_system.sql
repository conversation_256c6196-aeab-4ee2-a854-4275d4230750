-- Enhanced Floor Layout System Database Schema
-- This migration adds comprehensive floor layout and table management capabilities

-- Floor layouts table
CREATE TABLE IF NOT EXISTS floor_layouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    width INTEGER DEFAULT 800,
    height INTEGER DEFAULT 600,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, location_id, name)
);

-- Floor sections table
CREATE TABLE IF NOT EXISTS floor_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    floor_layout_id UUID REFERENCES floor_layouts(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color code
    x INTEGER NOT NULL DEFAULT 0,
    y INTEGER NOT NULL DEFAULT 0,
    width INTEGER NOT NULL DEFAULT 200,
    height INTEGER NOT NULL DEFAULT 200,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced tables table
CREATE TABLE IF NOT EXISTS tables (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    floor_layout_id UUID REFERENCES floor_layouts(id) ON DELETE CASCADE,
    section_id UUID REFERENCES floor_sections(id) ON DELETE SET NULL,
    number INTEGER NOT NULL,
    name VARCHAR(100),
    seats INTEGER NOT NULL DEFAULT 4,
    max_capacity INTEGER,
    x INTEGER NOT NULL DEFAULT 0,
    y INTEGER NOT NULL DEFAULT 0,
    width INTEGER NOT NULL DEFAULT 80,
    height INTEGER NOT NULL DEFAULT 80,
    shape VARCHAR(20) DEFAULT 'rectangle' CHECK (shape IN ('rectangle', 'circle', 'oval')),
    table_type VARCHAR(20) DEFAULT 'regular' CHECK (table_type IN ('regular', 'bar', 'booth', 'private', 'outdoor')),
    status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'reserved', 'needs-cleaning', 'out-of-order')),
    substatus VARCHAR(30) CHECK (substatus IN ('ordering', 'eating', 'waiting-for-check', 'paying')),
    rotation INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    notes TEXT,
    created_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, location_id, number)
);

-- Table assignments (current occupancy)
CREATE TABLE IF NOT EXISTS table_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_id UUID REFERENCES tables(id) ON DELETE CASCADE,
    order_id UUID, -- References orders table
    guest_count INTEGER DEFAULT 1,
    server_id UUID REFERENCES employees(id) ON DELETE SET NULL,
    server_name VARCHAR(100),
    seated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estimated_duration INTEGER, -- in minutes
    special_requests TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table reservations
CREATE TABLE IF NOT EXISTS table_reservations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    table_id UUID REFERENCES tables(id) ON DELETE CASCADE,
    customer_name VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20),
    customer_email VARCHAR(255),
    party_size INTEGER NOT NULL,
    reservation_date DATE NOT NULL,
    reservation_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 120,
    status VARCHAR(20) DEFAULT 'confirmed' CHECK (status IN ('pending', 'confirmed', 'seated', 'completed', 'cancelled', 'no-show')),
    special_requests TEXT,
    notes TEXT,
    created_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    confirmed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    seated_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table status history
CREATE TABLE IF NOT EXISTS table_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_id UUID REFERENCES tables(id) ON DELETE CASCADE,
    previous_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    previous_substatus VARCHAR(30),
    new_substatus VARCHAR(30),
    guest_count INTEGER,
    order_id UUID,
    changed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    reason VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table performance metrics
CREATE TABLE IF NOT EXISTS table_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_id UUID REFERENCES tables(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_seatings INTEGER DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    total_guests INTEGER DEFAULT 0,
    average_duration INTEGER DEFAULT 0, -- in minutes
    turnover_rate DECIMAL(5,2) DEFAULT 0,
    utilization_rate DECIMAL(5,2) DEFAULT 0,
    customer_satisfaction DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(table_id, date)
);

-- Table transfer history
CREATE TABLE IF NOT EXISTS table_transfers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL,
    from_table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
    to_table_id UUID REFERENCES tables(id) ON DELETE SET NULL,
    from_table_number INTEGER,
    to_table_number INTEGER,
    guest_count INTEGER,
    reason VARCHAR(255),
    transferred_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    transferred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Enhanced orders table updates for floor integration
ALTER TABLE orders ADD COLUMN IF NOT EXISTS table_id UUID REFERENCES tables(id) ON DELETE SET NULL;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS table_number INTEGER;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS guest_count INTEGER DEFAULT 1;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS server_id UUID REFERENCES employees(id) ON DELETE SET NULL;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS server_name VARCHAR(100);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_type VARCHAR(20) DEFAULT 'dine_in' CHECK (order_type IN ('dine_in', 'takeout', 'delivery', 'bar'));
ALTER TABLE orders ADD COLUMN IF NOT EXISTS seated_time TIMESTAMP;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS service_time INTEGER; -- in minutes

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_floor_layouts_tenant_location ON floor_layouts(tenant_id, location_id);
CREATE INDEX IF NOT EXISTS idx_floor_sections_layout ON floor_sections(floor_layout_id);
CREATE INDEX IF NOT EXISTS idx_tables_tenant_location ON tables(tenant_id, location_id);
CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status, substatus);
CREATE INDEX IF NOT EXISTS idx_tables_section ON tables(section_id);
CREATE INDEX IF NOT EXISTS idx_table_assignments_table ON table_assignments(table_id, status);
CREATE INDEX IF NOT EXISTS idx_table_reservations_date_time ON table_reservations(reservation_date, reservation_time);
CREATE INDEX IF NOT EXISTS idx_table_reservations_status ON table_reservations(status);
CREATE INDEX IF NOT EXISTS idx_table_status_history_table_time ON table_status_history(table_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_table_performance_table_date ON table_performance(table_id, date);
CREATE INDEX IF NOT EXISTS idx_table_transfers_order ON table_transfers(order_id);
CREATE INDEX IF NOT EXISTS idx_orders_table ON orders(table_id);
CREATE INDEX IF NOT EXISTS idx_orders_type_date ON orders(order_type, DATE(timestamp));

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_floor_layouts_updated_at BEFORE UPDATE ON floor_layouts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_floor_sections_updated_at BEFORE UPDATE ON floor_sections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_table_assignments_updated_at BEFORE UPDATE ON table_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_table_reservations_updated_at BEFORE UPDATE ON table_reservations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_table_performance_updated_at BEFORE UPDATE ON table_performance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for table status history
CREATE OR REPLACE FUNCTION log_table_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status OR OLD.substatus IS DISTINCT FROM NEW.substatus THEN
        INSERT INTO table_status_history (
            table_id, 
            previous_status, 
            new_status, 
            previous_substatus, 
            new_substatus,
            changed_by,
            reason
        ) VALUES (
            NEW.id,
            OLD.status,
            NEW.status,
            OLD.substatus,
            NEW.substatus,
            NEW.updated_by,
            'Status change'
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER log_table_status_changes 
    AFTER UPDATE ON tables 
    FOR EACH ROW 
    EXECUTE FUNCTION log_table_status_change();

-- Insert default floor layout for existing tenants
INSERT INTO floor_layouts (tenant_id, location_id, name, description, width, height)
SELECT 
    t.id as tenant_id,
    l.id as location_id,
    'Main Dining Room' as name,
    'Default floor layout for main dining area' as description,
    800 as width,
    600 as height
FROM tenants t
CROSS JOIN locations l
WHERE l.tenant_id = t.id
AND NOT EXISTS (
    SELECT 1 FROM floor_layouts fl 
    WHERE fl.tenant_id = t.id AND fl.location_id = l.id
);

-- Insert default sections for new floor layouts
INSERT INTO floor_sections (floor_layout_id, name, color, x, y, width, height, sort_order)
SELECT 
    fl.id as floor_layout_id,
    section_data.name,
    section_data.color,
    section_data.x,
    section_data.y,
    section_data.width,
    section_data.height,
    section_data.sort_order
FROM floor_layouts fl
CROSS JOIN (
    VALUES 
        ('Main Dining', '#3B82F6', 50, 50, 400, 300, 1),
        ('Bar Area', '#10B981', 500, 50, 250, 150, 2),
        ('Private Dining', '#F59E0B', 50, 400, 300, 150, 3)
) AS section_data(name, color, x, y, width, height, sort_order)
WHERE NOT EXISTS (
    SELECT 1 FROM floor_sections fs 
    WHERE fs.floor_layout_id = fl.id
);

-- Insert sample tables for demonstration
INSERT INTO tables (tenant_id, location_id, floor_layout_id, section_id, number, name, seats, x, y, width, height, shape, table_type)
SELECT 
    fl.tenant_id,
    fl.location_id,
    fl.id as floor_layout_id,
    fs.id as section_id,
    table_data.number,
    table_data.name,
    table_data.seats,
    table_data.x,
    table_data.y,
    table_data.width,
    table_data.height,
    table_data.shape,
    table_data.table_type
FROM floor_layouts fl
JOIN floor_sections fs ON fs.floor_layout_id = fl.id AND fs.name = 'Main Dining'
CROSS JOIN (
    VALUES 
        (1, 'Window Table', 4, 100, 100, 80, 80, 'rectangle', 'regular'),
        (2, NULL, 2, 200, 100, 60, 60, 'circle', 'regular'),
        (3, 'Corner Booth', 6, 300, 150, 100, 80, 'rectangle', 'booth'),
        (4, NULL, 8, 450, 100, 120, 80, 'oval', 'regular')
) AS table_data(number, name, seats, x, y, width, height, shape, table_type)
WHERE NOT EXISTS (
    SELECT 1 FROM tables t 
    WHERE t.tenant_id = fl.tenant_id 
    AND t.location_id = fl.location_id 
    AND t.number = table_data.number
);

-- Insert bar tables
INSERT INTO tables (tenant_id, location_id, floor_layout_id, section_id, number, name, seats, x, y, width, height, shape, table_type)
SELECT 
    fl.tenant_id,
    fl.location_id,
    fl.id as floor_layout_id,
    fs.id as section_id,
    5 as number,
    'Bar Counter' as name,
    3 as seats,
    550 as x,
    80 as y,
    150 as width,
    40 as height,
    'rectangle' as shape,
    'bar' as table_type
FROM floor_layouts fl
JOIN floor_sections fs ON fs.floor_layout_id = fl.id AND fs.name = 'Bar Area'
WHERE NOT EXISTS (
    SELECT 1 FROM tables t 
    WHERE t.tenant_id = fl.tenant_id 
    AND t.location_id = fl.location_id 
    AND t.number = 5
);
