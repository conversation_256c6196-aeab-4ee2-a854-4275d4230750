// Database-First Middleware
// Reduces dependency on mock data by prioritizing database queries

const { Pool } = require('pg');

// Database connection pool (reuse from main server)
let pool;

const initializePool = (dbPool) => {
  pool = dbPool;
};

// Database-first product fetcher
const getProductsFromDatabase = async (tenantId) => {
  try {
    const result = await pool.query(`
      SELECT 
        p.id,
        p.name,
        p.price,
        p.description,
        p.in_stock,
        c.name as category,
        p.category_id,
        p.created_at,
        p.updated_at
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.tenant_id = $1 OR p.tenant_id IS NULL
      ORDER BY p.name
    `, [tenantId]);

    return {
      success: true,
      data: result.rows,
      source: 'database'
    };
  } catch (error) {
    console.error('❌ Database query failed for products:', error.message);
    return {
      success: false,
      error: error.message,
      source: 'database'
    };
  }
};

// Database-first categories fetcher
const getCategoriesFromDatabase = async (tenantId) => {
  try {
    const result = await pool.query(`
      SELECT 
        id,
        name,
        description,
        created_at
      FROM categories
      WHERE tenant_id = $1 OR tenant_id IS NULL
      ORDER BY name
    `, [tenantId]);

    return {
      success: true,
      data: result.rows,
      source: 'database'
    };
  } catch (error) {
    console.error('❌ Database query failed for categories:', error.message);
    return {
      success: false,
      error: error.message,
      source: 'database'
    };
  }
};

// Database-first orders fetcher
const getOrdersFromDatabase = async (tenantId, filters = {}) => {
  try {
    let query = `
      SELECT 
        id,
        order_number,
        status,
        order_type,
        items,
        subtotal,
        tax_amount,
        discount_amount,
        total_amount,
        payment_status,
        payment_method,
        notes,
        created_at,
        updated_at,
        completed_at
      FROM orders
      WHERE tenant_id = $1
    `;
    
    const params = [tenantId];
    let paramIndex = 2;

    if (filters.status) {
      query += ` AND status = $${paramIndex}`;
      params.push(filters.status);
      paramIndex++;
    }

    if (filters.date) {
      query += ` AND DATE(created_at) = $${paramIndex}`;
      params.push(filters.date);
      paramIndex++;
    }

    if (filters.limit) {
      query += ` ORDER BY created_at DESC LIMIT $${paramIndex}`;
      params.push(parseInt(filters.limit));
    } else {
      query += ` ORDER BY created_at DESC LIMIT 50`;
    }

    const result = await pool.query(query, params);

    return {
      success: true,
      data: result.rows,
      source: 'database'
    };
  } catch (error) {
    console.error('❌ Database query failed for orders:', error.message);
    return {
      success: false,
      error: error.message,
      source: 'database'
    };
  }
};

// Database-first tenant fetcher
const getTenantsFromDatabase = async (filters = {}) => {
  try {
    let query = `
      SELECT 
        t.id,
        t.name,
        t.slug,
        t.email,
        t.phone,
        t.address,
        t.status,
        t.plan,
        t.locations,
        t.created_at,
        t.updated_at,
        t.last_login,
        COUNT(DISTINCT u.id) as user_count,
        COALESCE(SUM(tr.total_amount), 0) as total_revenue,
        COUNT(DISTINCT tr.id) as transaction_count
      FROM tenants t
      LEFT JOIN users u ON t.id = u.tenant_id
      LEFT JOIN transactions tr ON t.id = tr.tenant_id AND tr.status = 'completed'
    `;

    const params = [];
    let paramIndex = 1;

    if (filters.status) {
      query += ` WHERE t.status = $${paramIndex}`;
      params.push(filters.status);
      paramIndex++;
    }

    query += ` GROUP BY t.id, t.name, t.slug, t.email, t.phone, t.address, t.status, t.plan, t.locations, t.created_at, t.updated_at, t.last_login`;
    query += ` ORDER BY t.created_at DESC`;

    if (filters.limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(parseInt(filters.limit));
    }

    const result = await pool.query(query, params);

    return {
      success: true,
      data: result.rows,
      source: 'database'
    };
  } catch (error) {
    console.error('❌ Database query failed for tenants:', error.message);
    return {
      success: false,
      error: error.message,
      source: 'database'
    };
  }
};

// Database-first analytics fetcher
const getAnalyticsFromDatabase = async (tenantId, period = 'today') => {
  try {
    let dateFilter = '';
    const params = [tenantId];

    switch (period) {
      case 'today':
        dateFilter = "AND DATE(created_at) = CURRENT_DATE";
        break;
      case 'week':
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'month':
        dateFilter = "AND created_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      default:
        dateFilter = "AND DATE(created_at) = CURRENT_DATE";
    }

    const query = `
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_sales,
        COALESCE(AVG(total_amount), 0) as average_order_value,
        COUNT(DISTINCT customer_id) as unique_customers
      FROM orders
      WHERE tenant_id = $1 
      AND status IN ('served', 'completed')
      ${dateFilter}
    `;

    const result = await pool.query(query, params);

    return {
      success: true,
      data: {
        period,
        ...result.rows[0]
      },
      source: 'database'
    };
  } catch (error) {
    console.error('❌ Database query failed for analytics:', error.message);
    return {
      success: false,
      error: error.message,
      source: 'database'
    };
  }
};

// Middleware to prefer database over mock data
const databaseFirstMiddleware = (req, res, next) => {
  // Add database-first helpers to request object
  req.dbFirst = {
    getProducts: (tenantId) => getProductsFromDatabase(tenantId),
    getCategories: (tenantId) => getCategoriesFromDatabase(tenantId),
    getOrders: (tenantId, filters) => getOrdersFromDatabase(tenantId, filters),
    getTenants: (filters) => getTenantsFromDatabase(filters),
    getAnalytics: (tenantId, period) => getAnalyticsFromDatabase(tenantId, period)
  };

  next();
};

// Response helper that handles database-first responses
const sendDatabaseFirstResponse = (res, dbResult, mockFallback = null) => {
  if (dbResult.success) {
    return res.json({
      success: true,
      data: dbResult.data,
      source: 'database',
      count: Array.isArray(dbResult.data) ? dbResult.data.length : 1
    });
  } else {
    console.warn('⚠️  Database query failed, no mock fallback provided');
    return res.status(500).json({
      success: false,
      error: 'Database query failed',
      message: dbResult.error,
      source: 'database'
    });
  }
};

module.exports = {
  initializePool,
  databaseFirstMiddleware,
  sendDatabaseFirstResponse,
  getProductsFromDatabase,
  getCategoriesFromDatabase,
  getOrdersFromDatabase,
  getTenantsFromDatabase,
  getAnalyticsFromDatabase
};
