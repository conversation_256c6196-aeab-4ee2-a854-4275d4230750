const http = require('http');

// Test error handling for invalid data
async function testErrorHandling() {
  // 1. Test invalid product (missing required fields)
  const invalidProduct = JSON.stringify({
    // Missing name and price
    category: "beer",
    description: "Test invalid product"
  });

  // 2. Test invalid employee (missing required fields)
  const invalidEmployee = JSON.stringify({
    // Missing pin
    name: "Test Employee",
    role: "invalid_role"
  });

  // 3. Test invalid order (malformed items array)
  const invalidOrder = JSON.stringify({
    items: "not-a-json-array", // Invalid JSONB format
    timestamp: new Date().toISOString(),
    status: 'completed',
    total: "invalid",
    subtotal: "invalid",
    tax: "invalid"
  });

  const testInvalidProduct = () => new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/products',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': invalidProduct.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n1. Invalid Product Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', data);
        resolve();
      });
    });

    req.write(invalidProduct);
    req.end();
  });

  const testInvalidEmployee = () => new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/employees',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': invalidEmployee.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n2. Invalid Employee Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', data);
        resolve();
      });
    });

    req.write(invalidEmployee);
    req.end();
  });

  const testInvalidOrder = () => new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: '/orders',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': invalidOrder.length
      }
    };

    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => { data += chunk; });
      res.on('end', () => {
        console.log('\n3. Invalid Order Test:');
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', data);
        resolve();
      });
    });

    req.write(invalidOrder);
    req.end();
  });

  // Run all error handling tests
  try {
    await testInvalidProduct();
    await testInvalidEmployee();
    await testInvalidOrder();
    console.log('\nError handling tests completed successfully!');
  } catch (error) {
    console.error('\nError handling tests failed:', error);
  }
}

// Run the tests
testErrorHandling();
