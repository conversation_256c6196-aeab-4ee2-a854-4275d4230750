import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { CreditCard, User, Clock, DollarSign, Plus, Minus, X, Check, AlertTriangle, RefreshCw } from 'lucide-react';

interface BarTab {
  id: string;
  tab_name: string;
  customer_name?: string;
  table_number?: number;
  phone_number?: string;
  email?: string;
  status: 'open' | 'closed' | 'suspended';
  opened_at: string;
  closed_at?: string;
  items: Array<{
    id: string;
    name: string;
    price: number;
    quantity: number;
    added_at: string;
    category: 'beverage' | 'food' | 'other';
  }>;
  subtotal: number;
  tax: number;
  tip: number;
  total: number;
  payment_method?: 'cash' | 'card' | 'mobile';
  notes?: string;
  credit_limit?: number;
}

const BarTabManagement: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [tabs, setTabs] = useState<BarTab[]>([]);
  const [selectedTab, setSelectedTab] = useState<BarTab | null>(null);
  const [showNewTabModal, setShowNewTabModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newTabData, setNewTabData] = useState({
    tab_name: '',
    customer_name: '',
    table_number: '',
    phone_number: '',
    email: '',
    credit_limit: 100
  });

  // Load bar tabs
  useEffect(() => {
    const loadBarTabs = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🍺 Loading bar tabs...');
        
        const response = await apiCall('/api/bar/tabs');
        if (response.ok) {
          const data = await response.json();
          setTabs(data);
          console.log('✅ Bar tabs loaded:', data.length);
        }
      } catch (error) {
        console.error('❌ Error loading bar tabs:', error);
        setError('Failed to load bar tabs. Using mock data.');
        
        // Fallback to mock data
        const mockTabs: BarTab[] = [
          {
            id: 'tab_1',
            tab_name: 'John\'s Tab',
            customer_name: 'John Smith',
            table_number: 5,
            phone_number: '555-0123',
            status: 'open',
            opened_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            items: [
              { id: '1', name: 'Beer', price: 6.50, quantity: 3, added_at: new Date().toISOString(), category: 'beverage' },
              { id: '2', name: 'Wings', price: 12.99, quantity: 1, added_at: new Date().toISOString(), category: 'food' }
            ],
            subtotal: 32.49,
            tax: 2.60,
            tip: 0,
            total: 35.09,
            credit_limit: 100
          },
          {
            id: 'tab_2',
            tab_name: 'Sarah\'s Tab',
            customer_name: 'Sarah Johnson',
            table_number: 12,
            status: 'open',
            opened_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            items: [
              { id: '3', name: 'Wine', price: 9.00, quantity: 2, added_at: new Date().toISOString(), category: 'beverage' },
              { id: '4', name: 'Salad', price: 14.50, quantity: 1, added_at: new Date().toISOString(), category: 'food' }
            ],
            subtotal: 32.50,
            tax: 2.60,
            tip: 0,
            total: 35.10,
            credit_limit: 150
          },
          {
            id: 'tab_3',
            tab_name: 'Mike\'s Tab',
            customer_name: 'Mike Wilson',
            status: 'closed',
            opened_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
            closed_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            items: [
              { id: '5', name: 'Cocktail', price: 12.00, quantity: 2, added_at: new Date().toISOString(), category: 'beverage' }
            ],
            subtotal: 24.00,
            tax: 1.92,
            tip: 4.80,
            total: 30.72,
            payment_method: 'card',
            credit_limit: 75
          }
        ];
        setTabs(mockTabs);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadBarTabs();
  }, [apiCall]);

  const createNewTab = async () => {
    try {
      console.log('🍺 Creating new bar tab:', newTabData.tab_name);
      
      const response = await apiCall('/api/bar/tabs', {
        method: 'POST',
        body: JSON.stringify({
          ...newTabData,
          table_number: newTabData.table_number ? parseInt(newTabData.table_number) : undefined
        })
      });
      
      if (response.ok) {
        const newTab = await response.json();
        setTabs(prev => [...prev, newTab]);
        setShowNewTabModal(false);
        setNewTabData({
          tab_name: '',
          customer_name: '',
          table_number: '',
          phone_number: '',
          email: '',
          credit_limit: 100
        });
        console.log('✅ New tab created successfully');
      }
    } catch (error) {
      console.error('❌ Error creating new tab:', error);
      alert('Failed to create new tab. Please try again.');
    }
  };

  const addItemToTab = async (tabId: string, item: { name: string; price: number; category: 'beverage' | 'food' | 'other' }) => {
    try {
      console.log(`🍺 Adding item to tab ${tabId}:`, item.name);
      
      const response = await apiCall(`/api/bar/tabs/${tabId}/items`, {
        method: 'POST',
        body: JSON.stringify(item)
      });
      
      if (response.ok) {
        const updatedTab = await response.json();
        setTabs(prev => prev.map(tab => tab.id === tabId ? updatedTab : tab));
        if (selectedTab?.id === tabId) {
          setSelectedTab(updatedTab);
        }
        console.log('✅ Item added to tab successfully');
      }
    } catch (error) {
      console.error('❌ Error adding item to tab:', error);
      alert('Failed to add item to tab. Please try again.');
    }
  };

  const updateItemQuantity = async (tabId: string, itemId: string, newQuantity: number) => {
    try {
      if (newQuantity <= 0) {
        // Remove item
        const response = await apiCall(`/api/bar/tabs/${tabId}/items/${itemId}`, {
          method: 'DELETE'
        });
        
        if (response.ok) {
          const updatedTab = await response.json();
          setTabs(prev => prev.map(tab => tab.id === tabId ? updatedTab : tab));
          if (selectedTab?.id === tabId) {
            setSelectedTab(updatedTab);
          }
        }
      } else {
        // Update quantity
        const response = await apiCall(`/api/bar/tabs/${tabId}/items/${itemId}`, {
          method: 'PUT',
          body: JSON.stringify({ quantity: newQuantity })
        });
        
        if (response.ok) {
          const updatedTab = await response.json();
          setTabs(prev => prev.map(tab => tab.id === tabId ? updatedTab : tab));
          if (selectedTab?.id === tabId) {
            setSelectedTab(updatedTab);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error updating item quantity:', error);
    }
  };

  const closeTab = async (tabId: string, paymentMethod: 'cash' | 'card' | 'mobile', tip: number) => {
    try {
      console.log(`🍺 Closing tab ${tabId} with ${paymentMethod}`);
      
      const response = await apiCall(`/api/bar/tabs/${tabId}/close`, {
        method: 'POST',
        body: JSON.stringify({ payment_method: paymentMethod, tip })
      });
      
      if (response.ok) {
        const closedTab = await response.json();
        setTabs(prev => prev.map(tab => tab.id === tabId ? closedTab : tab));
        setSelectedTab(null);
        setShowPaymentModal(false);
        console.log('✅ Tab closed successfully');
      }
    } catch (error) {
      console.error('❌ Error closing tab:', error);
      alert('Failed to close tab. Please try again.');
    }
  };

  const getTabDuration = (openedAt: string, closedAt?: string): string => {
    const start = new Date(openedAt);
    const end = closedAt ? new Date(closedAt) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMins}m`;
    }
    return `${diffMins}m`;
  };

  const getStatusColor = (status: BarTab['status']) => {
    switch (status) {
      case 'open': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const openTabs = tabs.filter(tab => tab.status === 'open');
  const closedTabs = tabs.filter(tab => tab.status === 'closed');

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading bar tabs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Bar Tab Management</h2>
            <p className="text-sm text-gray-500">
              {openTabs.length} open tabs • {closedTabs.length} closed today
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowNewTabModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>New Tab</span>
            </button>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Tabs"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-hidden flex">
        {/* Tabs List */}
        <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Open Tabs ({openTabs.length})</h3>
            <div className="space-y-2">
              {openTabs.map((tab) => (
                <div
                  key={tab.id}
                  onClick={() => setSelectedTab(tab)}
                  className={`p-3 border border-gray-200 rounded-lg cursor-pointer transition-colors ${
                    selectedTab?.id === tab.id ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900">{tab.tab_name}</h4>
                      {tab.customer_name && (
                        <p className="text-sm text-gray-600">{tab.customer_name}</p>
                      )}
                      {tab.table_number && (
                        <p className="text-sm text-gray-500">Table {tab.table_number}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(tab.status)}`}>
                        {tab.status.toUpperCase()}
                      </span>
                      <p className="text-sm font-semibold text-gray-900 mt-1">${tab.total.toFixed(2)}</p>
                      <p className="text-xs text-gray-500">{getTabDuration(tab.opened_at)}</p>
                    </div>
                  </div>
                  
                  {/* Credit limit warning */}
                  {tab.credit_limit && tab.total > tab.credit_limit * 0.8 && (
                    <div className="mt-2 flex items-center text-orange-600">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      <span className="text-xs">Near credit limit</span>
                    </div>
                  )}
                </div>
              ))}
              
              {openTabs.length === 0 && (
                <p className="text-gray-500 text-center py-8">No open tabs</p>
              )}
            </div>
          </div>
        </div>

        {/* Tab Details */}
        <div className="flex-1 overflow-y-auto">
          {selectedTab ? (
            <div className="p-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{selectedTab.tab_name}</h3>
                  {selectedTab.customer_name && (
                    <p className="text-gray-600">{selectedTab.customer_name}</p>
                  )}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                    {selectedTab.table_number && <span>Table {selectedTab.table_number}</span>}
                    <span>Opened {getTabDuration(selectedTab.opened_at)} ago</span>
                    {selectedTab.credit_limit && (
                      <span>Credit limit: ${selectedTab.credit_limit}</span>
                    )}
                  </div>
                </div>
                {selectedTab.status === 'open' && (
                  <button
                    onClick={() => setShowPaymentModal(true)}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
                  >
                    <CreditCard className="h-4 w-4" />
                    <span>Close Tab</span>
                  </button>
                )}
              </div>

              {/* Tab Items */}
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium text-gray-900 mb-3">Items</h4>
                <div className="space-y-2">
                  {selectedTab.items.map((item) => (
                    <div key={item.id} className="flex items-center justify-between bg-white p-3 rounded border">
                      <div>
                        <span className="font-medium text-gray-900">{item.name}</span>
                        <span className="text-sm text-gray-500 ml-2">${item.price.toFixed(2)} each</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {selectedTab.status === 'open' && (
                          <>
                            <button
                              onClick={() => updateItemQuantity(selectedTab.id, item.id, item.quantity - 1)}
                              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <span className="w-8 text-center font-medium">{item.quantity}</span>
                            <button
                              onClick={() => updateItemQuantity(selectedTab.id, item.id, item.quantity + 1)}
                              className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        <span className="font-semibold text-gray-900 ml-4">
                          ${(item.price * item.quantity).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tab Totals */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="text-gray-900">${selectedTab.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tax</span>
                    <span className="text-gray-900">${selectedTab.tax.toFixed(2)}</span>
                  </div>
                  {selectedTab.tip > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tip</span>
                      <span className="text-gray-900">${selectedTab.tip.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t border-gray-300 my-2"></div>
                  <div className="flex justify-between font-bold">
                    <span className="text-gray-900">Total</span>
                    <span className="text-gray-900">${selectedTab.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Select a tab to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* New Tab Modal */}
      {showNewTabModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Tab</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tab Name *</label>
                <input
                  type="text"
                  value={newTabData.tab_name}
                  onChange={(e) => setNewTabData({...newTabData, tab_name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter tab name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                <input
                  type="text"
                  value={newTabData.customer_name}
                  onChange={(e) => setNewTabData({...newTabData, customer_name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter customer name"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Table Number</label>
                  <input
                    type="number"
                    value={newTabData.table_number}
                    onChange={(e) => setNewTabData({...newTabData, table_number: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Table #"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Credit Limit</label>
                  <input
                    type="number"
                    value={newTabData.credit_limit}
                    onChange={(e) => setNewTabData({...newTabData, credit_limit: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="$100"
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowNewTabModal(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={createNewTab}
                disabled={!newTabData.tab_name}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                Create Tab
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && selectedTab && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Close Tab - {selectedTab.tab_name}</h3>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex justify-between mb-2">
                  <span>Subtotal</span>
                  <span>${selectedTab.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Tax</span>
                  <span>${selectedTab.tax.toFixed(2)}</span>
                </div>
                <div className="border-t border-gray-300 my-2"></div>
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>${selectedTab.total.toFixed(2)}</span>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => closeTab(selectedTab.id, 'cash', 0)}
                  className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors"
                >
                  <DollarSign className="h-6 w-6 text-green-500 mb-2" />
                  <span className="text-sm">Cash</span>
                </button>
                <button
                  onClick={() => closeTab(selectedTab.id, 'card', 0)}
                  className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors"
                >
                  <CreditCard className="h-6 w-6 text-blue-500 mb-2" />
                  <span className="text-sm">Card</span>
                </button>
                <button
                  onClick={() => closeTab(selectedTab.id, 'mobile', 0)}
                  className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-500 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
                    <path d="M12 18h.01" />
                  </svg>
                  <span className="text-sm">Mobile</span>
                </button>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BarTabManagement;
