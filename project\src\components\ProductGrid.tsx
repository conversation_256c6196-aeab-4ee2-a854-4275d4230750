import React, { useState, useEffect } from 'react';
import { useAppContext } from '../hooks/useAppContext';
import { Beer, Wine, FileLock as Cocktail, GlassWater, Utensils, Flame } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  inStock: boolean;
}

const ProductGrid: React.FC = () => {
  const { state, dispatch, fetchProducts, fetchCategories } = useAppContext();
  const [selectedCategory, setSelectedCategory] = useState<string>('beer');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        await Promise.all([fetchProducts(), fetchCategories()]);
        setError(null);
      } catch (error) {
        setError('Failed to load products or categories');
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, [fetchProducts, fetchCategories]);

  const categories = state.categories.map(name => ({
    name,
    count: state.products.filter(p => p.category === name && p.inStock).length
  }));
  
  const getCategoryIcon = (category: string) => {
    switch(category.toLowerCase()) {
      case 'beer':
        return <Beer className="h-5 w-5" />;
      case 'wine':
        return <Wine className="h-5 w-5" />;
      case 'cocktails':
        return <Cocktail className="h-5 w-5" />;
      case 'spirits':
        return <Wine className="h-5 w-5" />;
      case 'non-alcoholic':
        return <GlassWater className="h-5 w-5" />;
      case 'food':
        return <Utensils className="h-5 w-5" />;
      default:
        return <Flame className="h-5 w-5" />;
    }
  };
  
  const handleAddToOrder = (product: Product) => {
    dispatch({ type: 'ADD_PRODUCT_TO_ORDER', payload: product });
  };
  
  const filteredProducts = state.products.filter(
    product => product.category === selectedCategory && product.inStock
  );

  const getCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center space-y-4">
          <svg className="animate-spin h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p className="text-gray-400">Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500">
          <p>{error}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="h-full flex flex-col max-w-full">
      {/* Category tabs */}
      <div className="flex overflow-x-auto bg-gray-800 p-2 space-x-2 mb-4 rounded-md scrollbar-thin scrollbar-thumb-gray-700">
        {categories.map(category => (
          <button
            key={category.name}
            className={`flex items-center space-x-2 py-2 px-3 sm:px-4 rounded-md transition-colors whitespace-nowrap ${
              selectedCategory === category.name
              ? 'bg-purple-600 text-white'
              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => setSelectedCategory(category.name)}
          >
            {getCategoryIcon(category.name)}
            <span className="text-sm sm:text-base">{getCategoryName(category.name)}</span>
            <span className="bg-gray-800 text-xs px-2 py-1 rounded-full min-w-[1.5rem] text-center">
              {category.count}
            </span>
          </button>
        ))}
      </div>
      
      {/* Products grid */}
      <div className="flex-1 overflow-y-auto pb-4 scrollbar-thin scrollbar-thumb-gray-700">
        <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 auto-rows-fr">
          {filteredProducts.length === 0 ? (
            <div className="col-span-full flex items-center justify-center py-12 text-gray-400">
              <div className="text-center">
                <div className="text-4xl mb-2">📦</div>
                <p className="text-lg font-medium mb-1">No products available</p>
                <p className="text-sm">in {getCategoryName(selectedCategory)} category</p>
              </div>
            </div>
          ) : (
            filteredProducts.map(product => (
              <button
                key={product.id}
                className="bg-gray-800 hover:bg-gray-700 rounded-lg p-3 text-left transition-all duration-200 active:scale-95 hover:shadow-lg border border-gray-700 hover:border-gray-600 min-h-[120px] flex flex-col"
                onClick={() => handleAddToOrder(product)}
              >
                <div className="flex flex-col h-full">
                  <h3 className="font-medium text-white mb-2 text-sm sm:text-base line-clamp-2">
                    {product.name}
                  </h3>
                  {product.description && (
                    <p className="text-gray-400 text-xs sm:text-sm mb-2 flex-grow line-clamp-2">
                      {product.description}
                    </p>
                  )}
                  <div className="mt-auto">
                    <p className="text-amber-400 font-semibold text-sm sm:text-base">
                      ${product.price.toFixed(2)}
                    </p>
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductGrid;
