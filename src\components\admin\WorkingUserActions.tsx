import React, { useState } from 'react';
import { <PERSON>, User<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTriangle } from 'lucide-react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  tenantId: number;
}

interface WorkingUserActionsProps {
  user: User;
  onUserUpdated: () => void;
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
}

export const WorkingUserActions: React.FC<WorkingUserActionsProps> = ({
  user,
  onUserUpdated,
  onError,
  onSuccess
}) => {
  const [loading, setLoading] = useState<string | null>(null);

  const handleResetPassword = async () => {
    try {
      setLoading('reset-password');
      const randomPin = Math.floor(100000 + Math.random() * 900000).toString();
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newPin: randomPin })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess(`Password reset successfully. New PIN: ${randomPin}`);
      onUserUpdated();
    } catch (error) {
      onError('Failed to reset password');
    } finally {
      setLoading(null);
    }
  };

  const handleToggleStatus = async () => {
    const confirmed = window.confirm(`Are you sure you want to ${user.isActive ? 'deactivate' : 'activate'} user "${user.name}"?`);
    if (!confirmed) return;

    try {
      setLoading('toggle-status');
      const newStatus = !user.isActive;
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: newStatus, reason: 'Admin action' })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess(`User ${newStatus ? 'activated' : 'deactivated'} successfully`);
      onUserUpdated();
    } catch (error) {
      onError(`Failed to ${user.isActive ? 'deactivate' : 'activate'} user`);
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteUser = async () => {
    const confirmed = window.confirm(`Are you sure you want to delete user "${user.name}"? This will deactivate the user.`);
    if (!confirmed) return;

    try {
      setLoading('delete-user');
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: 'Admin deletion' })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess('User deactivated successfully');
      onUserUpdated();
    } catch (error) {
      onError('Failed to delete user');
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="flex flex-wrap gap-2">
      {/* Reset Password Button */}
      <button
        onClick={handleResetPassword}
        disabled={loading === 'reset-password'}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          padding: '6px 12px',
          backgroundColor: '#dbeafe',
          color: '#1d4ed8',
          border: 'none',
          borderRadius: '6px',
          fontSize: '14px',
          cursor: loading === 'reset-password' ? 'not-allowed' : 'pointer',
          opacity: loading === 'reset-password' ? 0.5 : 1
        }}
        title="Reset Password"
      >
        {loading === 'reset-password' ? (
          <RefreshCw size={12} style={{ animation: 'spin 1s linear infinite' }} />
        ) : (
          <Key size={12} />
        )}
        <span>Reset PIN</span>
      </button>

      {/* Toggle Status Button */}
      <button
        onClick={handleToggleStatus}
        disabled={loading === 'toggle-status'}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          padding: '6px 12px',
          backgroundColor: user.isActive ? '#fef3c7' : '#d1fae5',
          color: user.isActive ? '#d97706' : '#059669',
          border: 'none',
          borderRadius: '6px',
          fontSize: '14px',
          cursor: loading === 'toggle-status' ? 'not-allowed' : 'pointer',
          opacity: loading === 'toggle-status' ? 0.5 : 1
        }}
        title={user.isActive ? 'Deactivate User' : 'Activate User'}
      >
        {loading === 'toggle-status' ? (
          <RefreshCw size={12} style={{ animation: 'spin 1s linear infinite' }} />
        ) : user.isActive ? (
          <UserX size={12} />
        ) : (
          <UserCheck size={12} />
        )}
        <span>{user.isActive ? 'Deactivate' : 'Activate'}</span>
      </button>

      {/* Delete User Button */}
      {user.role !== 'super_admin' && (
        <button
          onClick={handleDeleteUser}
          disabled={loading === 'delete-user'}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '6px 12px',
            backgroundColor: '#fee2e2',
            color: '#dc2626',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: loading === 'delete-user' ? 'not-allowed' : 'pointer',
            opacity: loading === 'delete-user' ? 0.5 : 1
          }}
          title="Delete User"
        >
          {loading === 'delete-user' ? (
            <RefreshCw size={12} style={{ animation: 'spin 1s linear infinite' }} />
          ) : (
            <Trash2 size={12} />
          )}
          <span>Delete</span>
        </button>
      )}

      {/* Status Indicator */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          padding: '4px 8px',
          backgroundColor: user.isActive ? '#d1fae5' : '#fee2e2',
          color: user.isActive ? '#065f46' : '#991b1b',
          borderRadius: '12px',
          fontSize: '12px'
        }}
      >
        {user.isActive ? '✓' : '✗'}
        <span>{user.isActive ? 'Active' : 'Inactive'}</span>
      </div>

      <style>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default WorkingUserActions;
