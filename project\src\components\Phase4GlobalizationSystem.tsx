import React, { useState, useEffect } from 'react';
import { 
  Globe, 
  Languages, 
  MapPin, 
  DollarSign, 
  CreditCard, 
  Smartphone, 
  Settings, 
  Users, 
  Building,
  Flag,
  Calendar,
  Clock,
  Palette,
  Type,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Download,
  Upload,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
  completeness: number;
}

interface Currency {
  code: string;
  name: string;
  symbol: string;
  rate: number;
  format: string;
}

interface Region {
  code: string;
  name: string;
  languages: string[];
  currencies: string[];
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  taxRules: {
    defaultRate: number;
    categories: Record<string, number>;
  };
}

interface LocalizationSettings {
  language: string;
  region: string;
  currency: string;
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  rtlSupport: boolean;
  voiceLanguage: string;
  customTranslations: Record<string, string>;
}

const Phase4GlobalizationSystem: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'languages' | 'regions' | 'currencies' | 'voice' | 'customization'>('languages');
  const [currentSettings, setCurrentSettings] = useState<LocalizationSettings>({
    language: 'en',
    region: 'US',
    currency: 'USD',
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    numberFormat: 'en-US',
    rtlSupport: false,
    voiceLanguage: 'en-US',
    customTranslations: {}
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [translationProgress, setTranslationProgress] = useState<Record<string, number>>({});

  const supportedLanguages: Language[] = [
    { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸', rtl: false, completeness: 100 },
    { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸', rtl: false, completeness: 95 },
    { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷', rtl: false, completeness: 90 },
    { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', rtl: false, completeness: 88 },
    { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹', rtl: false, completeness: 85 },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹', rtl: false, completeness: 82 },
    { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳', rtl: false, completeness: 80 },
    { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵', rtl: false, completeness: 78 },
    { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷', rtl: false, completeness: 75 },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true, completeness: 70 },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳', rtl: false, completeness: 65 },
    { code: 'ru', name: 'Russian', nativeName: 'Русский', flag: '🇷🇺', rtl: false, completeness: 60 }
  ];

  const supportedRegions: Region[] = [
    {
      code: 'US',
      name: 'United States',
      languages: ['en', 'es'],
      currencies: ['USD'],
      timezone: 'America/New_York',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: 'en-US',
      taxRules: { defaultRate: 8.25, categories: { food: 0, alcohol: 10 } }
    },
    {
      code: 'CA',
      name: 'Canada',
      languages: ['en', 'fr'],
      currencies: ['CAD'],
      timezone: 'America/Toronto',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'en-CA',
      taxRules: { defaultRate: 13, categories: { food: 5, alcohol: 15 } }
    },
    {
      code: 'GB',
      name: 'United Kingdom',
      languages: ['en'],
      currencies: ['GBP'],
      timezone: 'Europe/London',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'en-GB',
      taxRules: { defaultRate: 20, categories: { food: 0, alcohol: 20 } }
    },
    {
      code: 'EU',
      name: 'European Union',
      languages: ['en', 'de', 'fr', 'es', 'it'],
      currencies: ['EUR'],
      timezone: 'Europe/Berlin',
      dateFormat: 'DD.MM.YYYY',
      numberFormat: 'de-DE',
      taxRules: { defaultRate: 19, categories: { food: 7, alcohol: 19 } }
    },
    {
      code: 'JP',
      name: 'Japan',
      languages: ['ja', 'en'],
      currencies: ['JPY'],
      timezone: 'Asia/Tokyo',
      dateFormat: 'YYYY/MM/DD',
      numberFormat: 'ja-JP',
      taxRules: { defaultRate: 10, categories: { food: 8, alcohol: 10 } }
    },
    {
      code: 'CN',
      name: 'China',
      languages: ['zh', 'en'],
      currencies: ['CNY'],
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      numberFormat: 'zh-CN',
      taxRules: { defaultRate: 13, categories: { food: 6, alcohol: 13 } }
    }
  ];

  const supportedCurrencies: Currency[] = [
    { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.0, format: '$#,##0.00' },
    { code: 'EUR', name: 'Euro', symbol: '€', rate: 0.85, format: '#,##0.00 €' },
    { code: 'GBP', name: 'British Pound', symbol: '£', rate: 0.73, format: '£#,##0.00' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', rate: 1.25, format: 'C$#,##0.00' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥', rate: 110.0, format: '¥#,##0' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', rate: 6.45, format: '¥#,##0.00' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', rate: 1.35, format: 'A$#,##0.00' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', rate: 0.92, format: 'CHF #,##0.00' },
    { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', rate: 8.5, format: '#,##0.00 kr' },
    { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', rate: 8.8, format: 'kr #,##0.00' }
  ];

  useEffect(() => {
    loadLocalizationSettings();
    initializeVoiceRecognition();
  }, []);

  const loadLocalizationSettings = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('authToken');
      
      // In production, this would fetch from API
      const response = await fetch('http://localhost:4000/api/localization/settings', {
        headers: { 'Authorization': `Bearer ${token}` }
      }).catch(() => null);
      
      if (response?.ok) {
        const settings = await response.json();
        setCurrentSettings(settings);
      }
    } catch (error) {
      console.error('Error loading localization settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveLocalizationSettings = async (settings: LocalizationSettings) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('authToken');
      
      const response = await fetch('http://localhost:4000/api/localization/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      }).catch(() => null);
      
      if (response?.ok) {
        setCurrentSettings(settings);
        // Apply settings to the application
        applyLocalizationSettings(settings);
      }
    } catch (error) {
      console.error('Error saving localization settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyLocalizationSettings = (settings: LocalizationSettings) => {
    // Apply language
    document.documentElement.lang = settings.language;
    
    // Apply RTL support
    if (settings.rtlSupport) {
      document.documentElement.dir = 'rtl';
      document.body.classList.add('rtl');
    } else {
      document.documentElement.dir = 'ltr';
      document.body.classList.remove('rtl');
    }
    
    // Apply timezone
    localStorage.setItem('timezone', settings.timezone);
    
    // Apply number and date formats
    localStorage.setItem('numberFormat', settings.numberFormat);
    localStorage.setItem('dateFormat', settings.dateFormat);
    
    // Trigger app-wide localization update
    window.dispatchEvent(new CustomEvent('localizationChanged', { detail: settings }));
  };

  const initializeVoiceRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setVoiceEnabled(true);
    }
  };

  const startVoiceRecognition = () => {
    if (!voiceEnabled) return;
    
    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.lang = currentSettings.voiceLanguage;
    recognition.continuous = false;
    recognition.interimResults = false;
    
    recognition.onstart = () => {
      setIsListening(true);
    };
    
    recognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript;
      handleVoiceCommand(transcript);
    };
    
    recognition.onerror = (event: any) => {
      console.error('Voice recognition error:', event.error);
      setIsListening(false);
    };
    
    recognition.onend = () => {
      setIsListening(false);
    };
    
    recognition.start();
  };

  const handleVoiceCommand = (command: string) => {
    // Process voice commands for POS operations
    const lowerCommand = command.toLowerCase();
    
    if (lowerCommand.includes('add') || lowerCommand.includes('order')) {
      // Handle add item commands
      console.log('Voice command: Add item -', command);
    } else if (lowerCommand.includes('total') || lowerCommand.includes('checkout')) {
      // Handle checkout commands
      console.log('Voice command: Checkout -', command);
    } else if (lowerCommand.includes('cancel') || lowerCommand.includes('remove')) {
      // Handle cancel/remove commands
      console.log('Voice command: Cancel/Remove -', command);
    }
    
    // Emit voice command event for other components to handle
    window.dispatchEvent(new CustomEvent('voiceCommand', { detail: { command, language: currentSettings.voiceLanguage } }));
  };

  const exportTranslations = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/localization/export', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `translations_${currentSettings.language}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error exporting translations:', error);
    }
  };

  const importTranslations = async (file: File) => {
    try {
      const token = localStorage.getItem('authToken');
      const formData = new FormData();
      formData.append('translations', file);
      formData.append('language', currentSettings.language);
      
      const response = await fetch('http://localhost:4000/api/localization/import', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
        body: formData
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('Translations imported successfully:', result);
        loadLocalizationSettings(); // Reload settings
      }
    } catch (error) {
      console.error('Error importing translations:', error);
    }
  };

  const generateAutoTranslations = async (targetLanguage: string) => {
    try {
      setTranslationProgress({ [targetLanguage]: 0 });
      const token = localStorage.getItem('authToken');
      
      const response = await fetch('http://localhost:4000/api/localization/auto-translate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sourceLanguage: 'en',
          targetLanguage: targetLanguage
        })
      });
      
      if (response.ok) {
        // Simulate progress updates
        for (let i = 0; i <= 100; i += 10) {
          setTimeout(() => {
            setTranslationProgress({ [targetLanguage]: i });
          }, i * 50);
        }
        
        setTimeout(() => {
          setTranslationProgress({});
          loadLocalizationSettings();
        }, 5500);
      }
    } catch (error) {
      console.error('Error generating auto translations:', error);
      setTranslationProgress({});
    }
  };

  const formatCurrency = (amount: number, currencyCode: string = currentSettings.currency) => {
    const currency = supportedCurrencies.find(c => c.code === currencyCode);
    if (!currency) return `${amount}`;
    
    const convertedAmount = amount * currency.rate;
    return new Intl.NumberFormat(currentSettings.numberFormat, {
      style: 'currency',
      currency: currencyCode
    }).format(convertedAmount);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(currentSettings.numberFormat, {
      timeZone: currentSettings.timezone
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading globalization settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Globe className="h-6 w-6 mr-2 text-blue-600" />
              Global Expansion & Localization
            </h1>
            <p className="text-gray-600 mt-1">
              Configure multi-language support, regional settings, and voice AI for global markets
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-md">
              <Flag className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">
                {supportedLanguages.find(l => l.code === currentSettings.language)?.nativeName}
              </span>
            </div>
            
            <div className="flex items-center space-x-2 bg-gray-100 px-3 py-2 rounded-md">
              <DollarSign className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">{currentSettings.currency}</span>
            </div>
            
            <button
              onClick={voiceEnabled ? startVoiceRecognition : undefined}
              disabled={!voiceEnabled}
              className={`p-2 rounded-md ${
                voiceEnabled 
                  ? (isListening ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600 hover:bg-blue-200')
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
              title={voiceEnabled ? (isListening ? 'Listening...' : 'Start Voice Recognition') : 'Voice not supported'}
            >
              {isListening ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {[
            { id: 'languages', label: 'Languages', icon: Languages },
            { id: 'regions', label: 'Regions', icon: MapPin },
            { id: 'currencies', label: 'Currencies', icon: CreditCard },
            { id: 'voice', label: 'Voice AI', icon: Volume2 },
            { id: 'customization', label: 'Customization', icon: Palette }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeTab === 'languages' && (
          <div className="space-y-6">
            {/* Current Language Settings */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Language Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Primary Language</label>
                  <select
                    value={currentSettings.language}
                    onChange={(e) => setCurrentSettings({ ...currentSettings, language: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {supportedLanguages.map(lang => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name} ({lang.nativeName})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Voice Language</label>
                  <select
                    value={currentSettings.voiceLanguage}
                    onChange={(e) => setCurrentSettings({ ...currentSettings, voiceLanguage: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="en-US">English (US)</option>
                    <option value="en-GB">English (UK)</option>
                    <option value="es-ES">Spanish (Spain)</option>
                    <option value="es-MX">Spanish (Mexico)</option>
                    <option value="fr-FR">French (France)</option>
                    <option value="de-DE">German (Germany)</option>
                    <option value="it-IT">Italian (Italy)</option>
                    <option value="pt-BR">Portuguese (Brazil)</option>
                    <option value="zh-CN">Chinese (Simplified)</option>
                    <option value="ja-JP">Japanese (Japan)</option>
                  </select>
                </div>
                
                <div className="flex items-center">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={currentSettings.rtlSupport}
                      onChange={(e) => setCurrentSettings({ ...currentSettings, rtlSupport: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Enable RTL Support</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Supported Languages Grid */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Supported Languages</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={exportTranslations}
                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>Export</span>
                  </button>
                  <label className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2 cursor-pointer">
                    <Upload className="h-4 w-4" />
                    <span>Import</span>
                    <input
                      type="file"
                      accept=".json"
                      className="hidden"
                      onChange={(e) => e.target.files?.[0] && importTranslations(e.target.files[0])}
                    />
                  </label>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {supportedLanguages.map(language => (
                  <div key={language.code} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{language.flag}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{language.name}</h4>
                          <p className="text-sm text-gray-500">{language.nativeName}</p>
                        </div>
                      </div>
                      {language.rtl && (
                        <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">RTL</span>
                      )}
                    </div>
                    
                    <div className="mb-3">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Translation Progress</span>
                        <span>{language.completeness}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            language.completeness >= 90 ? 'bg-green-500' :
                            language.completeness >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${language.completeness}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {translationProgress[language.code] !== undefined ? (
                      <div className="flex items-center space-x-2">
                        <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />
                        <span className="text-sm text-blue-600">
                          Translating... {translationProgress[language.code]}%
                        </span>
                      </div>
                    ) : (
                      <button
                        onClick={() => generateAutoTranslations(language.code)}
                        disabled={language.completeness >= 95}
                        className={`w-full py-2 px-3 rounded-md text-sm font-medium ${
                          language.completeness >= 95
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        {language.completeness >= 95 ? 'Complete' : 'Auto-Translate'}
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Other tabs content would be implemented here */}
        {activeTab !== 'languages' && (
          <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Globe className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Configuration
              </h3>
              <p className="text-gray-600 mb-4">
                Advanced {activeTab} management system is being implemented with comprehensive global support.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">
                  This section will include full {activeTab} configuration with real-time updates and global compliance.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Save Button */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex justify-end">
          <button
            onClick={() => saveLocalizationSettings(currentSettings)}
            disabled={isLoading}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {isLoading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Phase4GlobalizationSystem;
