import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Clock, 
  Users, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Target,
  Zap,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface OperationalInsightsData {
  capacity_utilization: {
    current: number;
    peak_today: number;
    avg_weekly: number;
    by_hour: Array<{
      hour: number;
      utilization: number;
      available_tables: number;
      occupied_tables: number;
    }>;
  };
  wait_time_analysis: {
    avg_wait_time: number;
    accuracy_rate: number;
    by_party_size: Array<{
      size: number | string;
      avg_wait: number;
      accuracy: number;
    }>;
    peak_wait_times: Array<{
      time: string;
      avg_wait: number;
      max_wait: number;
    }>;
  };
  service_speed: {
    kitchen_to_table: number;
    order_to_kitchen: number;
    payment_processing: number;
    table_turnover: number;
    by_meal_type: Array<{
      type: string;
      avg_time: number;
      target: number;
    }>;
  };
  revenue_optimization: {
    high_performing_tables: Array<{
      table: string;
      revenue_per_hour: number;
      turns_per_day: number;
    }>;
    underperforming_tables: Array<{
      table: string;
      revenue_per_hour: number;
      turns_per_day: number;
      issue: string;
    }>;
    optimization_suggestions: Array<{
      type: string;
      suggestion: string;
      potential_impact: string;
      priority: string;
    }>;
  };
  staffing_optimization: {
    current_efficiency: number;
    optimal_staffing: Array<{
      time: string;
      current: number;
      optimal: number;
      efficiency_gain?: string;
      cost_saving?: string;
    }>;
    server_workload: Array<{
      server: string;
      current_load: number;
      optimal_load: number;
      status: string;
    }>;
  };
}

const OperationalInsightsDashboard: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [insightsData, setInsightsData] = useState<OperationalInsightsData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'today' | '7d' | '30d'>('today');
  const [activeTab, setActiveTab] = useState<'capacity' | 'wait-times' | 'service-speed' | 'revenue' | 'staffing'>('capacity');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchOperationalInsights();
    const interval = setInterval(fetchOperationalInsights, 60000); // Update every minute
    return () => clearInterval(interval);
  }, [selectedTimeRange]);

  const fetchOperationalInsights = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall(`/api/analytics/operational-insights?range=${selectedTimeRange}`);
      
      if (response.ok) {
        const data = await response.json();
        setInsightsData(data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching operational insights:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'text-red-600 bg-red-100';
    if (utilization >= 75) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getWorkloadStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'overloaded': return 'text-red-600 bg-red-100';
      case 'underutilized': return 'text-blue-600 bg-blue-100';
      case 'optimal': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading && !insightsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading operational insights...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Operational Insights Dashboard</h2>
          <p className="text-gray-600">Monitor capacity, service speed, and operational efficiency</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="today">Today</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          <button
            onClick={fetchOperationalInsights}
            className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Key Metrics Cards */}
      {insightsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Current Capacity</p>
                <p className="text-2xl font-bold text-gray-900">{insightsData.capacity_utilization.current.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">Peak: {insightsData.capacity_utilization.peak_today.toFixed(1)}%</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Wait Time</p>
                <p className="text-2xl font-bold text-gray-900">{formatTime(insightsData.wait_time_analysis.avg_wait_time)}</p>
                <p className="text-xs text-gray-500">Accuracy: {insightsData.wait_time_analysis.accuracy_rate.toFixed(1)}%</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Zap className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Service Speed</p>
                <p className="text-2xl font-bold text-gray-900">{formatTime(insightsData.service_speed.kitchen_to_table)}</p>
                <p className="text-xs text-gray-500">Kitchen to Table</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Staff Efficiency</p>
                <p className="text-2xl font-bold text-gray-900">{insightsData.staffing_optimization.current_efficiency.toFixed(1)}%</p>
                <p className="text-xs text-gray-500">Current Level</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'capacity', label: 'Capacity Utilization', icon: BarChart3 },
              { id: 'wait-times', label: 'Wait Time Analysis', icon: Clock },
              { id: 'service-speed', label: 'Service Speed', icon: Zap },
              { id: 'revenue', label: 'Revenue Optimization', icon: TrendingUp },
              { id: 'staffing', label: 'Staffing Optimization', icon: Users }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Capacity Utilization Tab */}
          {activeTab === 'capacity' && insightsData && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Hourly Utilization</h3>
                  <div className="space-y-2">
                    {insightsData.capacity_utilization.by_hour.slice(8, 23).map((hour) => (
                      <div key={hour.hour} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{hour.hour}:00</span>
                        <div className="flex items-center space-x-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${hour.utilization >= 90 ? 'bg-red-500' : hour.utilization >= 75 ? 'bg-yellow-500' : 'bg-green-500'}`}
                              style={{ width: `${Math.min(hour.utilization, 100)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-12">{hour.utilization.toFixed(0)}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Capacity Summary</h3>
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-600">Current Utilization</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUtilizationColor(insightsData.capacity_utilization.current)}`}>
                          {insightsData.capacity_utilization.current.toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-600">Peak Today</span>
                        <span className="text-sm text-gray-900">{insightsData.capacity_utilization.peak_today.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600">Weekly Average</span>
                        <span className="text-sm text-gray-900">{insightsData.capacity_utilization.avg_weekly.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Wait Time Analysis Tab */}
          {activeTab === 'wait-times' && insightsData && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Wait Times by Party Size</h3>
                  <div className="space-y-3">
                    {insightsData.wait_time_analysis.by_party_size.map((party) => (
                      <div key={party.size} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium">{party.size} {typeof party.size === 'number' && party.size === 1 ? 'person' : 'people'}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{formatTime(party.avg_wait)}</div>
                          <div className="text-xs text-gray-500">{party.accuracy}% accuracy</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Peak Wait Times</h3>
                  <div className="space-y-3">
                    {insightsData.wait_time_analysis.peak_wait_times.map((peak) => (
                      <div key={peak.time} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium">{peak.time}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">Avg: {formatTime(peak.avg_wait)}</div>
                          <div className="text-xs text-gray-500">Max: {formatTime(peak.max_wait)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Service Speed Tab */}
          {activeTab === 'service-speed' && insightsData && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Service Timing Breakdown</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium">Order to Kitchen</span>
                      <span className="text-sm font-medium">{formatTime(insightsData.service_speed.order_to_kitchen)}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium">Kitchen to Table</span>
                      <span className="text-sm font-medium">{formatTime(insightsData.service_speed.kitchen_to_table)}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium">Payment Processing</span>
                      <span className="text-sm font-medium">{formatTime(insightsData.service_speed.payment_processing)}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium">Table Turnover</span>
                      <span className="text-sm font-medium">{formatTime(insightsData.service_speed.table_turnover)}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Service Times by Meal Type</h3>
                  <div className="space-y-3">
                    {insightsData.service_speed.by_meal_type.map((meal) => (
                      <div key={meal.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="font-medium">{meal.type}</span>
                        <div className="text-right">
                          <div className="text-sm font-medium">{formatTime(meal.avg_time)}</div>
                          <div className="text-xs text-gray-500">Target: {formatTime(meal.target)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Revenue Optimization Tab */}
          {activeTab === 'revenue' && insightsData && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">High Performing Tables</h3>
                  <div className="space-y-3">
                    {insightsData.revenue_optimization.high_performing_tables.map((table) => (
                      <div key={table.table} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                        <span className="font-medium">{table.table}</span>
                        <div className="text-right">
                          <div className="text-sm font-medium">${table.revenue_per_hour.toFixed(2)}/hr</div>
                          <div className="text-xs text-gray-500">{table.turns_per_day} turns/day</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Optimization Suggestions</h3>
                  <div className="space-y-3">
                    {insightsData.revenue_optimization.optimization_suggestions.map((suggestion, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{suggestion.type}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(suggestion.priority)}`}>
                            {suggestion.priority}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">{suggestion.suggestion}</p>
                        <p className="text-xs text-green-600 font-medium">{suggestion.potential_impact}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Staffing Optimization Tab */}
          {activeTab === 'staffing' && insightsData && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Optimal Staffing Recommendations</h3>
                  <div className="space-y-3">
                    {insightsData.staffing_optimization.optimal_staffing.map((staffing) => (
                      <div key={staffing.time} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{staffing.time}</span>
                          <span className="text-sm text-gray-600">
                            {staffing.current} → {staffing.optimal} servers
                          </span>
                        </div>
                        {staffing.efficiency_gain && (
                          <p className="text-xs text-green-600 font-medium">Efficiency gain: {staffing.efficiency_gain}</p>
                        )}
                        {staffing.cost_saving && (
                          <p className="text-xs text-blue-600 font-medium">Cost saving: {staffing.cost_saving}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Server Workload Analysis</h3>
                  <div className="space-y-3">
                    {insightsData.staffing_optimization.server_workload.map((server) => (
                      <div key={server.server} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="font-medium">{server.server}</span>
                        <div className="text-right">
                          <div className="text-sm font-medium">{server.current_load}% load</div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getWorkloadStatus(server.status)}`}>
                            {server.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OperationalInsightsDashboard;
