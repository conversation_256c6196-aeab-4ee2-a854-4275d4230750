const express = require('express');
const router = express.Router();

// Phase 3J: Voice Recognition & NLP API

// Voice recognition processing
router.post('/recognize', async (req, res) => {
  try {
    const { audioData, language = 'en', context = 'restaurant' } = req.body;
    console.log(`🎤 Voice recognition request - Language: ${language}, Context: ${context}`);

    if (!audioData) {
      return res.status(400).json({ error: 'Audio data is required' });
    }

    // Mock speech recognition with restaurant-specific vocabulary
    const restaurantPhrases = [
      'I would like to order a large pepperoni pizza',
      'Add extra cheese to that order',
      'Can I get a medium Coke with that',
      'Table five is ready for their order',
      'Process payment for forty-five dollars',
      'Show me today\'s sales report',
      'Send this order to the kitchen',
      'Apply fifteen percent discount',
      'Split the bill three ways',
      'Mark table two as occupied'
    ];

    const recognizedText = restaurantPhrases[Math.floor(Math.random() * restaurantPhrases.length)];
    const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence

    // Extract intent and entities
    const intent = extractIntent(recognizedText);
    const entities = extractEntities(recognizedText);

    res.json({
      success: true,
      recognizedText,
      confidence: Math.round(confidence * 100),
      language,
      intent,
      entities,
      processingTime: Math.round(Math.random() * 500 + 200), // 200-700ms
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Voice recognition error:', error);
    res.status(500).json({ error: 'Voice recognition failed' });
  }
});

// Voice authentication
router.post('/authenticate', async (req, res) => {
  try {
    const { voiceSample, userId, enrollmentMode = false } = req.body;
    console.log(`🔐 Voice authentication - User: ${userId}, Enrollment: ${enrollmentMode}`);

    if (!voiceSample || !userId) {
      return res.status(400).json({ error: 'Voice sample and user ID are required' });
    }

    // Mock voice authentication
    const confidence = Math.random() * 0.4 + 0.6; // 60-100% confidence
    const authenticated = confidence > 0.75;

    res.json({
      success: true,
      authenticated,
      confidence: Math.round(confidence * 100),
      userId,
      enrollmentMode,
      voicePrint: enrollmentMode ? `vp_${userId}_${Date.now()}` : null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Voice authentication error:', error);
    res.status(500).json({ error: 'Voice authentication failed' });
  }
});

// Conversational AI processing
router.post('/conversation', async (req, res) => {
  try {
    const { conversationId, message, context = 'restaurant', userId } = req.body;
    console.log(`💬 Conversation - ID: ${conversationId}, Message: "${message}"`);

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Mock conversational AI responses
    const responses = {
      greeting: [
        "Hello! How can I help you with your restaurant today?",
        "Welcome! I'm here to assist with your POS system.",
        "Hi there! What would you like to do?"
      ],
      order: [
        "I'll help you process that order. Let me add those items.",
        "Great choice! I'm adding that to the current order.",
        "Order updated! Anything else you'd like to add?"
      ],
      payment: [
        "I'll process the payment now. Please wait a moment.",
        "Payment processing initiated. The total is confirmed.",
        "Payment completed successfully! Receipt will be printed."
      ],
      help: [
        "I can help you with orders, payments, menu items, and table management.",
        "You can ask me to add items, process payments, or check table status.",
        "Try saying things like 'add pizza to order' or 'process payment for table 5'."
      ]
    };

    // Determine response category
    const category = determineCategory(message);
    const responseOptions = responses[category] || responses.help;
    const response = responseOptions[Math.floor(Math.random() * responseOptions.length)];

    const newConversationId = conversationId || `conv_${Date.now()}`;
    const confidence = Math.random() * 0.3 + 0.7;

    res.json({
      success: true,
      conversationId: newConversationId,
      response,
      confidence: Math.round(confidence * 100),
      context,
      userId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Conversation error:', error);
    res.status(500).json({ error: 'Conversation processing failed' });
  }
});

// Voice analytics dashboard
router.get('/analytics', async (req, res) => {
  try {
    console.log('📊 Loading voice analytics...');

    // Mock voice analytics data
    const analytics = {
      usage: {
        totalCommands: Math.floor(Math.random() * 1000) + 500,
        successfulRecognitions: Math.floor(Math.random() * 800) + 400,
        averageConfidence: Math.round((Math.random() * 20 + 80) * 100) / 100,
        mostUsedCommands: [
          { command: 'add to order', count: 156, percentage: 23.4 },
          { command: 'process payment', count: 134, percentage: 20.1 },
          { command: 'show menu', count: 98, percentage: 14.7 },
          { command: 'table status', count: 87, percentage: 13.0 },
          { command: 'remove item', count: 76, percentage: 11.4 }
        ]
      },
      performance: {
        averageResponseTime: Math.round(Math.random() * 200 + 300), // 300-500ms
        errorRate: Math.round((Math.random() * 5 + 2) * 100) / 100, // 2-7%
        languageAccuracy: {
          'en': 94.5,
          'es': 89.2,
          'fr': 87.8,
          'de': 85.1
        }
      },
      trends: {
        dailyUsage: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          commands: Math.floor(Math.random() * 100) + 50,
          accuracy: Math.round((Math.random() * 10 + 85) * 100) / 100
        }))
      }
    };

    res.json({
      success: true,
      analytics,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Voice analytics error:', error);
    res.status(500).json({ error: 'Failed to load voice analytics' });
  }
});

// Helper functions
function extractIntent(text) {
  const intents = {
    'order': ['order', 'add', 'get', 'want'],
    'payment': ['payment', 'pay', 'process', 'charge'],
    'menu': ['menu', 'show', 'display'],
    'table': ['table', 'status', 'occupied', 'available'],
    'help': ['help', 'assist', 'support']
  };

  for (const [intent, keywords] of Object.entries(intents)) {
    if (keywords.some(keyword => text.toLowerCase().includes(keyword))) {
      return intent;
    }
  }
  return 'unknown';
}

function extractEntities(text) {
  const entities = [];
  
  // Extract numbers
  const numbers = text.match(/\d+/g);
  if (numbers) {
    entities.push(...numbers.map(num => ({ type: 'number', value: num })));
  }
  
  // Extract food items (simplified)
  const foodItems = ['pizza', 'burger', 'salad', 'coke', 'water', 'cheese'];
  foodItems.forEach(item => {
    if (text.toLowerCase().includes(item)) {
      entities.push({ type: 'food_item', value: item });
    }
  });
  
  return entities;
}

function determineCategory(message) {
  const msg = message.toLowerCase();
  if (msg.includes('hello') || msg.includes('hi') || msg.includes('hey')) return 'greeting';
  if (msg.includes('order') || msg.includes('add') || msg.includes('menu')) return 'order';
  if (msg.includes('payment') || msg.includes('pay') || msg.includes('bill')) return 'payment';
  return 'help';
}

module.exports = router;
