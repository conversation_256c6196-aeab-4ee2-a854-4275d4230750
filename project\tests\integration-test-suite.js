/**
 * RestroFlow Comprehensive Integration Test Suite
 * ==============================================
 * 
 * This test suite validates the complete integration between all RestroFlow components:
 * - Super Admin Dashboard
 * - Tenant Admin System
 * - POS System
 * - Authentication Service
 * - Database Operations
 * - WebSocket Real-time Updates
 */

const axios = require('axios');
const WebSocket = require('ws');
const { Pool } = require('pg');

class RestroFlowIntegrationTester {
  constructor() {
    this.baseURL = process.env.TEST_BASE_URL || 'http://localhost:4000';
    this.wsURL = process.env.TEST_WS_URL || 'ws://localhost:4001';
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'RESTROFLOW',
      user: process.env.DB_USER || 'BARPOS',
      password: process.env.DB_PASSWORD || 'Chaand@0319'
    };
    
    this.testResults = [];
    this.tokens = {};
    this.testData = {};
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      test: '🧪'
    }[type] || '📋';

    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async runTest(testName, testFunction) {
    this.log(`Running test: ${testName}`, 'test');
    const startTime = Date.now();
    
    try {
      await testFunction();
      const duration = Date.now() - startTime;
      this.testResults.push({
        name: testName,
        status: 'PASS',
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      });
      this.log(`✅ ${testName} - PASSED (${duration}ms)`, 'success');
    } catch (error) {
      const duration = Date.now() - startTime;
      this.testResults.push({
        name: testName,
        status: 'FAIL',
        duration: `${duration}ms`,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      this.log(`❌ ${testName} - FAILED: ${error.message}`, 'error');
    }
  }

  // Database Connection Test
  async testDatabaseConnection() {
    const pool = new Pool(this.dbConfig);
    try {
      const client = await pool.connect();
      const result = await client.query('SELECT NOW() as current_time');
      client.release();
      await pool.end();
      
      if (!result.rows[0].current_time) {
        throw new Error('Database query returned no results');
      }
    } catch (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  // Super Admin Authentication Test
  async testSuperAdminAuth() {
    const response = await axios.post(`${this.baseURL}/api/admin/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (response.status !== 200 || !response.data.token) {
      throw new Error('Super admin authentication failed');
    }

    this.tokens.superAdmin = response.data.token;
    this.testData.superAdminUser = response.data.user;
  }

  // Tenant Verification Test
  async testTenantVerification() {
    const response = await axios.get(`${this.baseURL}/api/tenant/verify/demo-restaurant`);

    if (response.status !== 200 || !response.data.success) {
      throw new Error('Tenant verification failed');
    }

    this.testData.tenant = response.data;
  }

  // Tenant Admin Authentication Test
  async testTenantAdminAuth() {
    const response = await axios.post(`${this.baseURL}/api/tenant/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
      tenantSlug: 'demo-restaurant',
      rememberMe: false
    });

    if (response.status !== 200 || !response.data.token) {
      throw new Error('Tenant admin authentication failed');
    }

    this.tokens.tenantAdmin = response.data.token;
    this.testData.tenantAdminUser = response.data.user;
  }

  // API Endpoints Test
  async testAPIEndpoints() {
    const endpoints = [
      { method: 'GET', url: '/api/health', auth: false },
      { method: 'GET', url: '/api/admin/dashboard/stats', auth: 'superAdmin' },
      { method: 'GET', url: '/api/tenant/1/metrics', auth: 'tenantAdmin' },
      { method: 'GET', url: '/api/admin/tenants', auth: 'superAdmin' },
      { method: 'GET', url: '/api/admin/users', auth: 'superAdmin' }
    ];

    for (const endpoint of endpoints) {
      const config = {
        method: endpoint.method,
        url: `${this.baseURL}${endpoint.url}`
      };

      if (endpoint.auth) {
        config.headers = {
          'Authorization': `Bearer ${this.tokens[endpoint.auth]}`
        };
      }

      const response = await axios(config);
      
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`API endpoint ${endpoint.url} returned status ${response.status}`);
      }
    }
  }

  // WebSocket Connection Test
  async testWebSocketConnection() {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(`${this.wsURL}/ws/admin?token=${this.tokens.superAdmin}`);
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('WebSocket connection timeout'));
      }, 5000);

      ws.on('open', () => {
        clearTimeout(timeout);
        ws.close();
        resolve();
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`WebSocket connection failed: ${error.message}`));
      });
    });
  }

  // Real-time Updates Test
  async testRealTimeUpdates() {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(`${this.wsURL}/ws/tenant?token=${this.tokens.tenantAdmin}`);
      let messageReceived = false;

      const timeout = setTimeout(() => {
        ws.close();
        if (!messageReceived) {
          reject(new Error('No real-time updates received'));
        }
      }, 10000);

      ws.on('open', () => {
        // Send a test message to trigger real-time update
        ws.send(JSON.stringify({
          type: 'test_update',
          data: { test: true }
        }));
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.type) {
            messageReceived = true;
            clearTimeout(timeout);
            ws.close();
            resolve();
          }
        } catch (error) {
          // Ignore parsing errors for non-JSON messages
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`WebSocket error: ${error.message}`));
      });
    });
  }

  // Data Consistency Test
  async testDataConsistency() {
    // Create a test order via API
    const orderData = {
      table_number: 'TEST-001',
      customer_name: 'Test Customer',
      items: [
        { name: 'Test Item', price: 10.99, quantity: 2 }
      ],
      total_amount: 21.98
    };

    const createResponse = await axios.post(
      `${this.baseURL}/api/tenant/1/orders`,
      orderData,
      {
        headers: {
          'Authorization': `Bearer ${this.tokens.tenantAdmin}`
        }
      }
    );

    if (createResponse.status !== 201) {
      throw new Error('Failed to create test order');
    }

    const orderId = createResponse.data.id;

    // Verify order exists in database
    const pool = new Pool(this.dbConfig);
    const client = await pool.connect();
    
    try {
      const result = await client.query(
        'SELECT * FROM orders WHERE id = $1',
        [orderId]
      );

      if (result.rows.length === 0) {
        throw new Error('Order not found in database');
      }

      const dbOrder = result.rows[0];
      if (dbOrder.total_amount != orderData.total_amount) {
        throw new Error('Order data inconsistency between API and database');
      }

      // Clean up test data
      await client.query('DELETE FROM orders WHERE id = $1', [orderId]);
      
    } finally {
      client.release();
      await pool.end();
    }
  }

  // Performance Test
  async testPerformance() {
    const startTime = Date.now();
    const requests = [];

    // Make 10 concurrent requests
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.get(`${this.baseURL}/api/admin/dashboard/stats`, {
          headers: {
            'Authorization': `Bearer ${this.tokens.superAdmin}`
          }
        })
      );
    }

    await Promise.all(requests);
    const duration = Date.now() - startTime;

    if (duration > 5000) { // 5 seconds threshold
      throw new Error(`Performance test failed: ${duration}ms (threshold: 5000ms)`);
    }
  }

  // Security Test
  async testSecurity() {
    // Test unauthorized access
    try {
      await axios.get(`${this.baseURL}/api/admin/dashboard/stats`);
      throw new Error('Unauthorized access should be blocked');
    } catch (error) {
      if (error.response && error.response.status !== 401) {
        throw new Error('Expected 401 Unauthorized response');
      }
    }

    // Test invalid token
    try {
      await axios.get(`${this.baseURL}/api/admin/dashboard/stats`, {
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      });
      throw new Error('Invalid token should be rejected');
    } catch (error) {
      if (error.response && error.response.status !== 401) {
        throw new Error('Expected 401 Unauthorized response for invalid token');
      }
    }

    // Test cross-tenant access
    try {
      await axios.get(`${this.baseURL}/api/tenant/999/metrics`, {
        headers: {
          'Authorization': `Bearer ${this.tokens.tenantAdmin}`
        }
      });
      throw new Error('Cross-tenant access should be blocked');
    } catch (error) {
      if (error.response && error.response.status !== 403) {
        throw new Error('Expected 403 Forbidden response for cross-tenant access');
      }
    }
  }

  // Integration Flow Test
  async testIntegrationFlow() {
    // Test complete flow: Super Admin creates tenant → Tenant Admin logs in → Creates order
    
    // 1. Super Admin creates a new tenant
    const tenantData = {
      name: 'Test Integration Restaurant',
      slug: 'test-integration',
      email: '<EMAIL>',
      phone: '******-TEST'
    };

    const createTenantResponse = await axios.post(
      `${this.baseURL}/api/admin/tenants`,
      tenantData,
      {
        headers: {
          'Authorization': `Bearer ${this.tokens.superAdmin}`
        }
      }
    );

    if (createTenantResponse.status !== 201) {
      throw new Error('Failed to create test tenant');
    }

    const tenantId = createTenantResponse.data.id;

    // 2. Create tenant admin user
    const adminData = {
      name: 'Test Admin',
      email: '<EMAIL>',
      password: 'testpass123',
      role: 'admin',
      tenant_id: tenantId
    };

    const createAdminResponse = await axios.post(
      `${this.baseURL}/api/admin/users`,
      adminData,
      {
        headers: {
          'Authorization': `Bearer ${this.tokens.superAdmin}`
        }
      }
    );

    if (createAdminResponse.status !== 201) {
      throw new Error('Failed to create tenant admin user');
    }

    // 3. Tenant admin logs in
    const loginResponse = await axios.post(`${this.baseURL}/api/tenant/auth/login`, {
      email: '<EMAIL>',
      password: 'testpass123',
      tenantSlug: 'test-integration'
    });

    if (loginResponse.status !== 200) {
      throw new Error('Tenant admin login failed');
    }

    const testTenantToken = loginResponse.data.token;

    // 4. Tenant admin accesses metrics
    const metricsResponse = await axios.get(
      `${this.baseURL}/api/tenant/${tenantId}/metrics`,
      {
        headers: {
          'Authorization': `Bearer ${testTenantToken}`
        }
      }
    );

    if (metricsResponse.status !== 200) {
      throw new Error('Failed to access tenant metrics');
    }

    // Cleanup: Delete test tenant
    await axios.delete(
      `${this.baseURL}/api/admin/tenants/${tenantId}`,
      {
        headers: {
          'Authorization': `Bearer ${this.tokens.superAdmin}`
        }
      }
    );
  }

  // Run all tests
  async runAllTests() {
    this.log('🚀 Starting RestroFlow Integration Test Suite...', 'info');
    const startTime = Date.now();

    const tests = [
      { name: 'Database Connection', fn: () => this.testDatabaseConnection() },
      { name: 'Super Admin Authentication', fn: () => this.testSuperAdminAuth() },
      { name: 'Tenant Verification', fn: () => this.testTenantVerification() },
      { name: 'Tenant Admin Authentication', fn: () => this.testTenantAdminAuth() },
      { name: 'API Endpoints', fn: () => this.testAPIEndpoints() },
      { name: 'WebSocket Connection', fn: () => this.testWebSocketConnection() },
      { name: 'Real-time Updates', fn: () => this.testRealTimeUpdates() },
      { name: 'Data Consistency', fn: () => this.testDataConsistency() },
      { name: 'Performance', fn: () => this.testPerformance() },
      { name: 'Security', fn: () => this.testSecurity() },
      { name: 'Integration Flow', fn: () => this.testIntegrationFlow() }
    ];

    for (const test of tests) {
      await this.runTest(test.name, test.fn);
    }

    const totalTime = Date.now() - startTime;
    const passedTests = this.testResults.filter(t => t.status === 'PASS').length;
    const failedTests = this.testResults.filter(t => t.status === 'FAIL').length;

    this.log(`\n📊 Test Suite Complete!`, 'info');
    this.log(`Total Tests: ${this.testResults.length}`, 'info');
    this.log(`Passed: ${passedTests}`, 'success');
    this.log(`Failed: ${failedTests}`, failedTests > 0 ? 'error' : 'info');
    this.log(`Total Time: ${Math.round(totalTime / 1000)}s`, 'info');

    // Generate test report
    const report = {
      summary: {
        total: this.testResults.length,
        passed: passedTests,
        failed: failedTests,
        successRate: `${Math.round((passedTests / this.testResults.length) * 100)}%`,
        totalTime: `${Math.round(totalTime / 1000)}s`,
        timestamp: new Date().toISOString()
      },
      results: this.testResults,
      environment: {
        baseURL: this.baseURL,
        wsURL: this.wsURL,
        database: this.dbConfig.database
      }
    };

    // Save test report
    const fs = require('fs');
    fs.writeFileSync('./tests/integration-test-report.json', JSON.stringify(report, null, 2));
    this.log('Test report saved: ./tests/integration-test-report.json', 'info');

    return report;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new RestroFlowIntegrationTester();
  tester.runAllTests()
    .then(report => {
      if (report.summary.failed === 0) {
        console.log('\n🎉 All tests passed! RestroFlow integration is working correctly.');
        process.exit(0);
      } else {
        console.log(`\n⚠️  ${report.summary.failed} test(s) failed. Please review the test report.`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Test suite failed:', error.message);
      process.exit(1);
    });
}

module.exports = RestroFlowIntegrationTester;
