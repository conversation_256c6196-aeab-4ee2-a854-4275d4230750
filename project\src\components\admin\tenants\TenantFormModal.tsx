import React, { useState, useEffect } from 'react';
import { X, Building, Mail, Phone, MapPin, Key, AlertCircle, CheckCircle, Utensils } from 'lucide-react';
import { BusinessType, INDUSTRY_THEMES } from '../../contexts/IndustryThemeContext';
import BusinessTypeSelector from '../../BusinessTypeSelector';

interface Tenant {
  id?: number;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  status: 'active' | 'suspended' | 'inactive';
  planType?: string;
  businessType?: BusinessType;
  businessTypeCode?: string;
}

interface TenantFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (tenant: Tenant) => Promise<void>;
  tenant?: Tenant | null;
  mode: 'create' | 'edit';
}

export const TenantFormModal: React.FC<TenantFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  tenant,
  mode
}) => {
  const [formData, setFormData] = useState<Tenant>({
    name: '',
    slug: '',
    email: '',
    phone: '',
    address: '',
    status: 'active',
    planType: 'starter',
    businessType: undefined,
    businessTypeCode: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [slugGenerated, setSlugGenerated] = useState(false);
  const [currentStep, setCurrentStep] = useState<'basic' | 'business-type' | 'plan'>('basic');

  useEffect(() => {
    if (tenant && mode === 'edit') {
      setFormData({
        ...tenant,
        phone: tenant.phone || '',
        address: tenant.address || '',
        planType: tenant.planType || 'starter',
        businessType: tenant.businessType,
        businessTypeCode: tenant.businessTypeCode || ''
      });
      setSlugGenerated(false);
      setCurrentStep('basic');
    } else {
      setFormData({
        name: '',
        slug: '',
        email: '',
        phone: '',
        address: '',
        status: 'active',
        planType: 'starter',
        businessType: undefined,
        businessTypeCode: ''
      });
      setSlugGenerated(false);
      setCurrentStep('basic');
    }
    setErrors({});
  }, [tenant, mode, isOpen]);

  // Auto-generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (name: string) => {
    setFormData(prev => ({ ...prev, name }));
    
    // Auto-generate slug only for new tenants or if slug hasn't been manually edited
    if (mode === 'create' || !slugGenerated) {
      const newSlug = generateSlug(name);
      setFormData(prev => ({ ...prev, slug: newSlug }));
    }
  };

  const handleSlugChange = (slug: string) => {
    setFormData(prev => ({ ...prev, slug }));
    setSlugGenerated(true);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Restaurant name is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (mode === 'create' && !formData.businessType) {
      newErrors.businessType = 'Business type is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleBusinessTypeSelect = (businessType: BusinessType) => {
    setFormData(prev => ({
      ...prev,
      businessType,
      businessTypeCode: businessType.code
    }));
  };

  const handleNextStep = () => {
    if (currentStep === 'basic') {
      // Validate basic info
      const basicErrors: Record<string, string> = {};
      if (!formData.name.trim()) basicErrors.name = 'Restaurant name is required';
      if (!formData.email.trim()) basicErrors.email = 'Email is required';
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) basicErrors.email = 'Email is invalid';

      if (Object.keys(basicErrors).length > 0) {
        setErrors(basicErrors);
        return;
      }
      setCurrentStep('business-type');
    } else if (currentStep === 'business-type') {
      if (!formData.businessType) {
        setErrors({ businessType: 'Please select a business type' });
        return;
      }
      setCurrentStep('plan');
    }
    setErrors({});
  };

  const handlePrevStep = () => {
    if (currentStep === 'plan') {
      setCurrentStep('business-type');
    } else if (currentStep === 'business-type') {
      setCurrentStep('basic');
    }
    setErrors({});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error submitting tenant:', error);
      setErrors({ submit: 'Failed to save tenant. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Building className="h-6 w-6 text-blue-600 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Add New Tenant' : 'Edit Tenant'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps for Create Mode */}
        {mode === 'create' && (
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              {[
                { id: 'basic', label: 'Basic Info', icon: Building },
                { id: 'business-type', label: 'Business Type', icon: Utensils },
                { id: 'plan', label: 'Plan & Billing', icon: Key }
              ].map((step, index) => {
                const Icon = step.icon;
                const isActive = currentStep === step.id;
                const isCompleted =
                  (step.id === 'basic' && (currentStep === 'business-type' || currentStep === 'plan')) ||
                  (step.id === 'business-type' && currentStep === 'plan');

                return (
                  <React.Fragment key={step.id}>
                    <div className={`flex items-center space-x-3 ${
                      isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                    }`}>
                      <div className={`p-2 rounded-full ${
                        isActive ? 'bg-blue-100' : isCompleted ? 'bg-green-100' : 'bg-gray-100'
                      }`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <span className="font-medium">{step.label}</span>
                    </div>
                    {index < 2 && (
                      <div className={`flex-1 h-0.5 mx-4 ${
                        isCompleted ? 'bg-green-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-700">{errors.submit}</span>
            </div>
          )}

          {/* Step 1: Basic Information */}
          {(mode === 'edit' || currentStep === 'basic') && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Restaurant Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter restaurant name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Slug *
                  <span className="text-xs text-gray-500 ml-1">(URL identifier)</span>
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => handleSlugChange(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.slug ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="restaurant-slug"
                />
                {errors.slug && (
                  <p className="mt-1 text-sm text-red-600">{errors.slug}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Preview: {window.location.origin}/{formData.slug}
                </p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.email ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <Phone className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.phone ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="+****************"
                  />
                </div>
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="suspended">Suspended</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <div className="relative">
                <MapPin className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="123 Main Street, City, State, ZIP"
                />
              </div>
            </div>
            </div>
          )}

          {/* Step 2: Business Type Selection */}
          {mode === 'create' && currentStep === 'business-type' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Select Business Type</h3>
              <p className="text-gray-600">Choose the type that best describes your restaurant or hospitality business</p>

              {errors.businessType && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-red-700">{errors.businessType}</span>
                </div>
              )}

              <BusinessTypeSelector
                onSelect={handleBusinessTypeSelect}
                selectedType={formData.businessType}
                showDetails={false}
              />
            </div>
          )}

          {/* Step 3: Plan Information */}
          {(mode === 'edit' || currentStep === 'plan') && (

            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Plan & Billing</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plan Type
              </label>
              <select
                value={formData.planType}
                onChange={(e) => setFormData(prev => ({ ...prev, planType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="starter">Starter - $29/month</option>
                <option value="professional">Professional - $79/month</option>
                <option value="enterprise">Enterprise - $199/month</option>
              </select>
              </div>
            </div>
          )}

          {/* Default Credentials Notice */}
          {mode === 'create' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <Key className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">Default Admin Credentials</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    A default admin account will be created with PIN: <strong>123456</strong>
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    The tenant admin should change this PIN after first login.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              {mode === 'create' && currentStep !== 'basic' && (
                <button
                  type="button"
                  onClick={handlePrevStep}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Previous
                </button>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>

              {mode === 'create' && currentStep !== 'plan' ? (
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      {mode === 'create' ? 'Creating...' : 'Updating...'}
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      {mode === 'create' ? 'Create Tenant' : 'Update Tenant'}
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};
