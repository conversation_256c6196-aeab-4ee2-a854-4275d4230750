import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Bell,
  Settings,
  Sun,
  Moon,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  AlertCircle,
  CheckCircle,
  Info,
  Zap
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Button, Card, Badge, Flex, Text, Heading } from './ui/DesignSystem';
import OptimizedSidebar from './navigation/OptimizedSidebar';
import OptimizedProductGrid from './pos/OptimizedProductGrid';
import EnhancedOrderPanel from './pos/EnhancedOrderPanel';
import ModernPaymentProcessor from './ModernPaymentProcessor';
// Temporarily comment out components that might have issues
// import EnhancedFloorLayoutManager from './EnhancedFloorLayoutManager';
// import Phase3InventoryManagement from './Phase3InventoryManagement';
// import Phase3AdvancedAnalytics from './Phase3AdvancedAnalytics';
// import UnifiedStaffScheduling from './UnifiedStaffScheduling';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string;
  description?: string;
  isAvailable: boolean;
  preparationTime?: number;
  allergens?: string[];
  nutritionalInfo?: any;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
  popularity?: number;
  isNew?: boolean;
  isRecommended?: boolean;
}

interface ProductVariant {
  id: string;
  name: string;
  priceAdjustment: number;
}

interface ProductModifier {
  id: string;
  name: string;
  price: number;
  category: string;
  required: boolean;
  maxSelections?: number;
}

interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
  specialInstructions?: string;
  total: number;
}

interface Order {
  id: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  total: number;
  tableNumber?: string;
  customerName?: string;
  orderType: 'dine-in' | 'takeout' | 'delivery';
  status: 'draft' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  timestamp: Date;
  paymentStatus: 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
  specialInstructions?: string;
}

interface RestructuredIndustryPOSProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const RestructuredIndustryPOS: React.FC<RestructuredIndustryPOSProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const { state, dispatch } = useEnhancedAppContext();

  // Log that the restructured interface is loading
  console.log('🚀 RESTRUCTURED INDUSTRY POS LOADING!');
  console.log('isDarkMode:', isDarkMode);
  console.log('state:', state);
  
  // State management
  const [activeTab, setActiveTab] = useState('pos');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order>({
    id: `order_${Date.now()}`,
    items: [],
    subtotal: 0,
    tax: 0,
    tip: 0,
    discount: 0,
    total: 0,
    orderType: 'dine-in',
    status: 'draft',
    timestamp: new Date(),
    paymentStatus: 'pending'
  });
  
  // UI State
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isOnline, setIsOnline] = useState(true);
  const [loading, setLoading] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    isOnline: true,
    printer: 'online' as const,
    payment: 'online' as const,
    kitchen: 'online' as const,
    inventory: 'synced' as const
  });

  // Load data on component mount
  useEffect(() => {
    loadProducts();
    loadCategories();
    
    // Simulate network status monitoring
    const interval = setInterval(() => {
      setIsOnline(Math.random() > 0.05); // 95% uptime simulation
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Load products from API
  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/products', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        const enhancedProducts = data.map((p: any) => ({
          ...p,
          isAvailable: p.is_active !== false,
          preparationTime: Math.floor(Math.random() * 20) + 5,
          allergens: ['gluten', 'dairy', 'nuts'].filter(() => Math.random() > 0.7),
          popularity: Math.floor(Math.random() * 100),
          isNew: Math.random() > 0.8,
          isRecommended: Math.random() > 0.7
        }));
        setProducts(enhancedProducts);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      showNotification('error', 'Failed to load products');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load categories from API
  const loadCategories = useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/categories', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCategories(data.map((c: any) => c.name));
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }, []);

  // Show notification
  const showNotification = useCallback((type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    const notification = {
      id: Date.now(),
      type,
      message,
      timestamp: new Date()
    };
    setNotifications(prev => [notification, ...prev.slice(0, 4)]);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 5000);
  }, []);

  // Handle add product to order
  const handleAddToOrder = useCallback((product: Product, quantity: number = 1) => {
    const existingItem = currentOrder.items.find(item => item.productId === product.id);
    
    if (existingItem) {
      const updatedItems = currentOrder.items.map(item => 
        item.id === existingItem.id 
          ? { ...item, quantity: item.quantity + quantity, total: (item.quantity + quantity) * item.price }
          : item
      );
      setCurrentOrder(prev => ({ ...prev, items: updatedItems }));
    } else {
      const newItem: OrderItem = {
        id: `item_${Date.now()}_${Math.random()}`,
        productId: product.id,
        name: product.name,
        price: product.price,
        quantity,
        total: product.price * quantity
      };
      setCurrentOrder(prev => ({ ...prev, items: [...prev.items, newItem] }));
    }
    
    showNotification('success', `${product.name} added to order`);
  }, [currentOrder.items, showNotification]);

  // Handle order updates
  const handleUpdateOrder = useCallback((updatedOrder: Order) => {
    setCurrentOrder(updatedOrder);
  }, []);

  // Handle payment processing
  const handleProcessPayment = useCallback(() => {
    if (currentOrder.items.length === 0) {
      showNotification('warning', 'Cannot process payment for empty order');
      return;
    }
    setShowPayment(true);
  }, [currentOrder.items.length, showNotification]);

  // Handle payment completion
  const handlePaymentComplete = useCallback((paymentData: any) => {
    console.log('Payment completed:', paymentData);
    setShowPayment(false);
    
    // Reset order after successful payment
    setCurrentOrder({
      id: `order_${Date.now()}`,
      items: [],
      subtotal: 0,
      tax: 0,
      tip: 0,
      discount: 0,
      total: 0,
      orderType: 'dine-in',
      status: 'draft',
      timestamp: new Date(),
      paymentStatus: 'pending'
    });
    
    showNotification('success', `Payment of $${paymentData.total?.toFixed(2)} processed successfully`);
  }, [showNotification]);

  // Handle clear order
  const handleClearOrder = useCallback(() => {
    setCurrentOrder(prev => ({
      ...prev,
      items: [],
      subtotal: 0,
      tax: 0,
      total: 0
    }));
    showNotification('info', 'Order cleared');
  }, [showNotification]);

  // Handle save order
  const handleSaveOrder = useCallback(() => {
    // Save order logic here
    const orderData = {
      ...currentOrder,
      lastSaved: new Date().toISOString()
    };
    localStorage.setItem('restructuredPOS_currentOrder', JSON.stringify(orderData));
    showNotification('success', 'Order saved');
  }, [currentOrder, showNotification]);

  // Handle logout
  const handleLogout = useCallback(() => {
    // Logout logic here
    localStorage.removeItem('authToken');
    window.location.reload();
  }, []);

  // Tab content mapping
  const tabContent = useMemo(() => {
    const content: { [key: string]: React.ReactNode } = {
      pos: (
        <div className="flex h-full">
          <div className="flex-1">
            <OptimizedProductGrid
              products={products}
              categories={categories}
              onAddToOrder={handleAddToOrder}
              loading={loading}
              isDarkMode={isDarkMode}
            />
          </div>
          <EnhancedOrderPanel
            order={currentOrder}
            onUpdateOrder={handleUpdateOrder}
            onProcessPayment={handleProcessPayment}
            onClearOrder={handleClearOrder}
            onSaveOrder={handleSaveOrder}
            isDarkMode={isDarkMode}
          />
        </div>
      ),
      floor: (
        <div className="p-6">
          <Heading level={2} className="mb-4">🚀 Floor Layout (Restructured)</Heading>
          <Card padding="lg">
            <Text variant="body">Enhanced Floor Layout Manager - Coming Soon in Restructured Interface</Text>
          </Card>
        </div>
      ),
      orders: (
        <div className="p-6">
          <Heading level={2} className="mb-4">Order Management</Heading>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card padding="lg">
              <Heading level={3} className="mb-2">Pending Orders</Heading>
              <Text variant="body" className="text-3xl font-bold text-orange-500">12</Text>
            </Card>
            <Card padding="lg">
              <Heading level={3} className="mb-2">In Progress</Heading>
              <Text variant="body" className="text-3xl font-bold text-blue-500">8</Text>
            </Card>
            <Card padding="lg">
              <Heading level={3} className="mb-2">Ready</Heading>
              <Text variant="body" className="text-3xl font-bold text-green-500">5</Text>
            </Card>
          </div>
        </div>
      ),
      inventory: (
        <div className="p-6">
          <Heading level={2} className="mb-4">🚀 Inventory Management (Restructured)</Heading>
          <Card padding="lg">
            <Text variant="body">Enhanced Inventory Management - Coming Soon in Restructured Interface</Text>
          </Card>
        </div>
      ),
      analytics: (
        <div className="p-6">
          <Heading level={2} className="mb-4">🚀 Advanced Analytics (Restructured)</Heading>
          <Card padding="lg">
            <Text variant="body">Enhanced Analytics Dashboard - Coming Soon in Restructured Interface</Text>
          </Card>
        </div>
      ),
      staff: (
        <div className="p-6">
          <Heading level={2} className="mb-4">🚀 Staff Management (Restructured)</Heading>
          <Card padding="lg">
            <Text variant="body">Enhanced Staff Scheduling - Coming Soon in Restructured Interface</Text>
          </Card>
        </div>
      ),
      reports: (
        <div className="p-6">
          <Heading level={2} className="mb-4">Business Reports</Heading>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card padding="lg">
              <Heading level={3} className="mb-2">Daily Sales</Heading>
              <Text variant="body" className="text-2xl font-bold text-green-500">$2,847.50</Text>
              <Text variant="caption" color="muted">+12% from yesterday</Text>
            </Card>
            <Card padding="lg">
              <Heading level={3} className="mb-2">Orders Today</Heading>
              <Text variant="body" className="text-2xl font-bold text-blue-500">156</Text>
              <Text variant="caption" color="muted">+8% from yesterday</Text>
            </Card>
            <Card padding="lg">
              <Heading level={3} className="mb-2">Avg Order Value</Heading>
              <Text variant="body" className="text-2xl font-bold text-purple-500">$18.25</Text>
              <Text variant="caption" color="muted">+3% from yesterday</Text>
            </Card>
          </div>
        </div>
      ),
      settings: (
        <div className="p-6">
          <Heading level={2} className="mb-6">System Settings</Heading>
          <div className="space-y-6">
            <Card padding="lg">
              <Heading level={3} className="mb-4">Restaurant Information</Heading>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Restaurant Name</label>
                  <input 
                    type="text" 
                    value={state.currentTenant?.name || ''} 
                    className={`w-full px-3 py-2 rounded-lg border ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Phone Number</label>
                  <input 
                    type="tel" 
                    className={`w-full px-3 py-2 rounded-lg border ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>
            </Card>

            <Card padding="lg">
              <Heading level={3} className="mb-4">System Status</Heading>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(systemStatus).filter(([key]) => key !== 'isOnline').map(([key, status]) => (
                  <Flex key={key} align="center" gap="sm">
                    <div className={`w-3 h-3 rounded-full ${
                      status === 'online' || status === 'synced' ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <Text variant="caption" className="capitalize">{key}</Text>
                  </Flex>
                ))}
              </div>
            </Card>
          </div>
        </div>
      )
    };
    return content;
  }, [products, categories, currentOrder, loading, isDarkMode, state.currentTenant, systemStatus, handleAddToOrder, handleUpdateOrder, handleProcessPayment, handleClearOrder, handleSaveOrder]);

  return (
    <div className={`h-screen flex transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gray-900 text-white'
        : 'bg-gray-50 text-gray-900'
    }`} data-restructured="true" id="restructured-pos-interface">
      {/* Optimized Sidebar */}
      <OptimizedSidebar
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        isDarkMode={isDarkMode}
        currentUser={{
          name: state.currentEmployee?.name || 'Employee',
          role: state.currentEmployee?.role || 'Staff'
        }}
        systemStatus={systemStatus}
        notifications={notifications}
        onLogout={handleLogout}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header Bar */}
        <header className={`flex items-center justify-between px-6 py-4 border-b transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full text-sm font-bold">
                🚀 RESTRUCTURED
              </div>
              <Heading level={2}>
                {activeTab === 'pos' && 'Point of Sale'}
                {activeTab === 'floor' && 'Floor Layout'}
                {activeTab === 'orders' && 'Order Queue'}
                {activeTab === 'inventory' && 'Inventory Management'}
                {activeTab === 'analytics' && 'Business Analytics'}
                {activeTab === 'staff' && 'Staff Management'}
                {activeTab === 'reports' && 'Business Reports'}
                {activeTab === 'settings' && 'System Settings'}
              </Heading>
            </div>
          </div>

          <Flex gap="sm" align="center">
            {/* System Status Indicators */}
            <Flex gap="sm" align="center">
              {isOnline ? (
                <Wifi className="w-5 h-5 text-green-500" />
              ) : (
                <WifiOff className="w-5 h-5 text-red-500" />
              )}
              <Signal className="w-5 h-5 text-green-500" />
              <Battery className="w-5 h-5 text-green-500" />
            </Flex>

            {/* Notifications */}
            {notifications.length > 0 && (
              <div className="relative">
                <Button variant="ghost" size="sm" icon={Bell}>
                  <Badge variant="danger" size="sm" className="absolute -top-1 -right-1">
                    {notifications.length}
                  </Badge>
                </Button>
              </div>
            )}

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onThemeToggle}
              icon={isDarkMode ? Sun : Moon}
            />
          </Flex>
        </header>

        {/* Tab Content */}
        <main className="flex-1 overflow-hidden">
          {tabContent[activeTab]}
        </main>
      </div>

      {/* Modern Payment Processor Modal */}
      <ModernPaymentProcessor
        order={currentOrder}
        isOpen={showPayment}
        onClose={() => setShowPayment(false)}
        onPaymentComplete={handlePaymentComplete}
        isDarkMode={isDarkMode}
      />

      {/* Notification Toast */}
      {notifications.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2">
          {notifications.slice(0, 3).map((notification) => (
            <Card
              key={notification.id}
              variant="elevated"
              padding="sm"
              className={`min-w-80 ${
                notification.type === 'success' ? 'border-l-4 border-green-500' :
                notification.type === 'error' ? 'border-l-4 border-red-500' :
                notification.type === 'warning' ? 'border-l-4 border-yellow-500' :
                'border-l-4 border-blue-500'
              }`}
            >
              <Flex align="center" gap="sm">
                {notification.type === 'success' && <CheckCircle className="w-5 h-5 text-green-500" />}
                {notification.type === 'error' && <AlertCircle className="w-5 h-5 text-red-500" />}
                {notification.type === 'warning' && <AlertCircle className="w-5 h-5 text-yellow-500" />}
                {notification.type === 'info' && <Info className="w-5 h-5 text-blue-500" />}
                <Text variant="caption" className="font-medium">{notification.message}</Text>
              </Flex>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default RestructuredIndustryPOS;
