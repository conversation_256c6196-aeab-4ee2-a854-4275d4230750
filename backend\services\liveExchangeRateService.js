// Live Exchange Rate Service for Production
// Phase 7: Real exchange rate API integration

const axios = require('axios');
const { Pool } = require('pg');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class LiveExchangeRateService {
  constructor() {
    this.providers = {
      primary: {
        name: 'ExchangeRate-API',
        url: 'https://api.exchangerate-api.com/v4/latest',
        apiKey: process.env.EXCHANGE_RATE_API_KEY,
        rateLimit: 1500,
        timeout: 5000,
        priority: 1
      },
      
      secondary: {
        name: 'Fixer.io',
        url: 'https://api.fixer.io/latest',
        apiKey: process.env.FIXER_API_KEY,
        rateLimit: 1000,
        timeout: 5000,
        priority: 2
      },
      
      tertiary: {
        name: 'Open Exchange Rates',
        url: 'https://openexchangerates.org/api/latest.json',
        apiKey: process.env.OPEN_EXCHANGE_RATES_API_KEY,
        rateLimit: 1000,
        timeout: 5000,
        priority: 3
      }
    };
    
    this.cache = new Map();
    this.lastUpdate = new Map();
    this.updateInterval = 300000; // 5 minutes
    this.retryAttempts = 3;
    this.retryDelay = 1000;
    
    // Start automatic updates
    this.startLiveUpdates();
  }

  // =====================================================
  // LIVE EXCHANGE RATE FETCHING
  // =====================================================

  async fetchLiveExchangeRate(baseCurrency, targetCurrency) {
    const cacheKey = `${baseCurrency}_${targetCurrency}`;
    
    try {
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        console.log(`📊 Using cached rate for ${baseCurrency}/${targetCurrency}`);
        return {
          success: true,
          rate: this.cache.get(cacheKey).rate,
          source: 'cache',
          last_updated: this.cache.get(cacheKey).timestamp,
          provider: this.cache.get(cacheKey).provider
        };
      }

      // Try providers in priority order
      for (const [key, provider] of Object.entries(this.providers)) {
        try {
          console.log(`🌐 Fetching live rate from ${provider.name}...`);
          
          const rate = await this.fetchFromProvider(provider, baseCurrency, targetCurrency);
          
          if (rate) {
            // Cache the result
            this.cache.set(cacheKey, {
              rate: rate,
              timestamp: new Date(),
              provider: provider.name
            });
            
            // Store in database
            await this.storeLiveExchangeRate(baseCurrency, targetCurrency, rate, provider.name);
            
            console.log(`✅ Live rate fetched: ${baseCurrency}/${targetCurrency} = ${rate} (${provider.name})`);
            
            return {
              success: true,
              rate: rate,
              source: 'live',
              last_updated: new Date(),
              provider: provider.name
            };
          }
        } catch (error) {
          console.warn(`⚠️ Provider ${provider.name} failed:`, error.message);
          continue;
        }
      }
      
      // If all providers fail, try database fallback
      return await this.getDatabaseFallback(baseCurrency, targetCurrency);

    } catch (error) {
      console.error('❌ Error fetching live exchange rate:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async fetchFromProvider(provider, baseCurrency, targetCurrency) {
    try {
      let url, headers = {};
      
      switch (provider.name) {
        case 'ExchangeRate-API':
          url = `${provider.url}/${baseCurrency}`;
          break;
          
        case 'Fixer.io':
          url = `${provider.url}?access_key=${provider.apiKey}&base=${baseCurrency}&symbols=${targetCurrency}`;
          break;
          
        case 'Open Exchange Rates':
          url = `${provider.url}?app_id=${provider.apiKey}&base=${baseCurrency}&symbols=${targetCurrency}`;
          break;
          
        default:
          throw new Error(`Unknown provider: ${provider.name}`);
      }
      
      const response = await axios.get(url, {
        headers,
        timeout: provider.timeout,
        validateStatus: (status) => status === 200
      });
      
      return this.parseProviderResponse(provider.name, response.data, targetCurrency);
      
    } catch (error) {
      if (error.response?.status === 429) {
        throw new Error(`Rate limit exceeded for ${provider.name}`);
      } else if (error.code === 'ECONNABORTED') {
        throw new Error(`Timeout for ${provider.name}`);
      } else {
        throw new Error(`API error for ${provider.name}: ${error.message}`);
      }
    }
  }

  parseProviderResponse(providerName, data, targetCurrency) {
    try {
      switch (providerName) {
        case 'ExchangeRate-API':
          return data.rates[targetCurrency];
          
        case 'Fixer.io':
          return data.rates[targetCurrency];
          
        case 'Open Exchange Rates':
          return data.rates[targetCurrency];
          
        default:
          throw new Error(`Unknown provider response format: ${providerName}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse response from ${providerName}: ${error.message}`);
    }
  }

  // =====================================================
  // CACHE MANAGEMENT
  // =====================================================

  isCacheValid(cacheKey) {
    if (!this.cache.has(cacheKey)) {
      return false;
    }
    
    const cached = this.cache.get(cacheKey);
    const age = Date.now() - cached.timestamp.getTime();
    
    return age < this.updateInterval;
  }

  clearCache() {
    this.cache.clear();
    this.lastUpdate.clear();
    console.log('🗑️ Exchange rate cache cleared');
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys()),
      lastUpdate: this.lastUpdate
    };
  }

  // =====================================================
  // DATABASE OPERATIONS
  // =====================================================

  async storeLiveExchangeRate(baseCurrency, targetCurrency, rate, provider) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO global_exchange_rates (
          base_currency, target_currency, exchange_rate, rate_source, 
          rate_type, api_response_time_ms, last_updated
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
        ON CONFLICT (base_currency, target_currency, rate_type, rate_source)
        DO UPDATE SET
          exchange_rate = EXCLUDED.exchange_rate,
          last_updated = CURRENT_TIMESTAMP,
          api_response_time_ms = EXCLUDED.api_response_time_ms
      `, [baseCurrency, targetCurrency, rate, provider, 'spot', 100]);
      
      client.release();
      
    } catch (error) {
      console.error('❌ Error storing live exchange rate:', error);
    }
  }

  async getDatabaseFallback(baseCurrency, targetCurrency) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT exchange_rate, rate_source, last_updated
        FROM global_exchange_rates 
        WHERE base_currency = $1 
          AND target_currency = $2 
          AND rate_type = 'spot'
          AND is_active = true
        ORDER BY last_updated DESC
        LIMIT 1
      `, [baseCurrency, targetCurrency]);
      
      client.release();
      
      if (result.rows.length > 0) {
        const rate = result.rows[0];
        console.log(`📊 Using database fallback for ${baseCurrency}/${targetCurrency}`);
        
        return {
          success: true,
          rate: parseFloat(rate.exchange_rate),
          source: 'database_fallback',
          last_updated: rate.last_updated,
          provider: rate.rate_source,
          warning: 'Using stale exchange rate from database'
        };
      }
      
      return {
        success: false,
        error: 'No exchange rate available'
      };

    } catch (error) {
      console.error('❌ Error getting database fallback:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // AUTOMATIC UPDATES
  // =====================================================

  startLiveUpdates() {
    console.log('🔄 Starting live exchange rate updates');
    
    // Update immediately
    this.updateAllMajorRates();
    
    // Set up periodic updates
    this.updateInterval = setInterval(() => {
      this.updateAllMajorRates();
    }, this.updateInterval);
  }

  stopLiveUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      console.log('🛑 Stopped live exchange rate updates');
    }
  }

  async updateAllMajorRates() {
    try {
      console.log('🔄 Updating all major exchange rates...');
      
      const majorPairs = [
        { base: 'USD', target: 'EUR' },
        { base: 'USD', target: 'GBP' },
        { base: 'USD', target: 'JPY' },
        { base: 'USD', target: 'CNY' },
        { base: 'USD', target: 'CAD' },
        { base: 'USD', target: 'AUD' },
        { base: 'USD', target: 'CHF' },
        { base: 'EUR', target: 'GBP' },
        { base: 'EUR', target: 'JPY' },
        { base: 'GBP', target: 'JPY' }
      ];
      
      let updatedCount = 0;
      let failedCount = 0;
      
      for (const pair of majorPairs) {
        try {
          const result = await this.fetchLiveExchangeRate(pair.base, pair.target);
          if (result.success) {
            updatedCount++;
          } else {
            failedCount++;
          }
          
          // Rate limiting - wait between requests
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          console.warn(`⚠️ Failed to update ${pair.base}/${pair.target}:`, error.message);
          failedCount++;
        }
      }
      
      console.log(`✅ Exchange rate update complete: ${updatedCount} updated, ${failedCount} failed`);
      
      // Store update statistics
      await this.storeUpdateStats(updatedCount, failedCount);

    } catch (error) {
      console.error('❌ Error updating exchange rates:', error);
    }
  }

  async storeUpdateStats(updated, failed) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO global_performance_metrics (
          tenant_id, region_code, currency_code, metric_date, metric_hour,
          ai_recommendation_acceptance_rate, prediction_accuracy
        ) VALUES (1, 'GLOBAL', 'USD', CURRENT_DATE, EXTRACT(hour FROM CURRENT_TIME), $1, $2)
        ON CONFLICT (tenant_id, region_code, currency_code, metric_date, metric_hour)
        DO UPDATE SET
          ai_recommendation_acceptance_rate = EXCLUDED.ai_recommendation_acceptance_rate,
          prediction_accuracy = EXCLUDED.prediction_accuracy
      `, [updated / (updated + failed), updated]);
      
      client.release();
      
    } catch (error) {
      console.error('❌ Error storing update stats:', error);
    }
  }

  // =====================================================
  // HEALTH CHECK & MONITORING
  // =====================================================

  async getServiceHealth() {
    const health = {
      status: 'healthy',
      providers: {},
      cache: this.getCacheStats(),
      lastUpdate: this.lastUpdate.get('global') || 'never',
      uptime: process.uptime()
    };
    
    // Check each provider
    for (const [key, provider] of Object.entries(this.providers)) {
      try {
        const start = Date.now();
        await axios.get(provider.url.replace('/latest', '/health'), {
          timeout: 2000,
          validateStatus: () => true
        });
        const responseTime = Date.now() - start;
        
        health.providers[key] = {
          name: provider.name,
          status: 'healthy',
          responseTime: responseTime,
          priority: provider.priority
        };
      } catch (error) {
        health.providers[key] = {
          name: provider.name,
          status: 'unhealthy',
          error: error.message,
          priority: provider.priority
        };
        
        if (provider.priority === 1) {
          health.status = 'degraded';
        }
      }
    }
    
    return health;
  }

  async getProviderUsageStats() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          rate_source,
          COUNT(*) as request_count,
          AVG(api_response_time_ms) as avg_response_time,
          MAX(last_updated) as last_used
        FROM global_exchange_rates 
        WHERE last_updated >= NOW() - INTERVAL '24 hours'
        GROUP BY rate_source
        ORDER BY request_count DESC
      `);
      
      client.release();
      
      return {
        success: true,
        stats: result.rows,
        period: '24 hours'
      };

    } catch (error) {
      console.error('❌ Error getting provider usage stats:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async convertCurrencyLive(amount, fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) {
      return {
        success: true,
        original_amount: amount,
        converted_amount: amount,
        exchange_rate: 1.0,
        conversion_fee: 0,
        total_amount: amount,
        source: 'direct'
      };
    }

    const rateResult = await this.fetchLiveExchangeRate(fromCurrency, toCurrency);
    
    if (!rateResult.success) {
      return {
        success: false,
        error: 'Live exchange rate not available'
      };
    }

    const convertedAmount = amount * rateResult.rate;
    const conversionFee = amount * 0.01; // 1% fee
    const totalAmount = convertedAmount + conversionFee;

    return {
      success: true,
      original_amount: amount,
      converted_amount: convertedAmount,
      exchange_rate: rateResult.rate,
      conversion_fee: conversionFee,
      total_amount: totalAmount,
      source: rateResult.source,
      provider: rateResult.provider,
      last_updated: rateResult.last_updated
    };
  }

  formatCurrency(amount, currencyCode) {
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode
      }).format(amount);
    } catch (error) {
      return `${currencyCode} ${amount.toFixed(2)}`;
    }
  }
}

module.exports = LiveExchangeRateService;
