const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function debugPin() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Debugging PIN Authentication...');
    console.log('=' .repeat(50));
    
    // Get the Enhanced Admin user
    const result = await client.query(`
      SELECT e.*, t.name as tenant_name, t.slug as tenant_slug
      FROM employees e
      JOIN tenants t ON e.tenant_id = t.id
      WHERE e.name = 'Enhanced Admin'
    `);
    
    if (result.rows.length === 0) {
      console.log('❌ Enhanced Admin user not found');
      return;
    }
    
    const user = result.rows[0];
    console.log('✅ User found:');
    console.log('   Name:', user.name);
    console.log('   Role:', user.role);
    console.log('   Tenant:', user.tenant_name);
    console.log('   Tenant Slug:', user.tenant_slug);
    console.log('   Stored PIN Hash:', user.pin);
    console.log('   Is Active:', user.is_active);
    
    // Test PIN comparison
    const testPin = '123456';
    console.log('\n🔐 Testing PIN comparison...');
    console.log('   Test PIN:', testPin);
    
    const isValid = await bcrypt.compare(testPin, user.pin);
    console.log('   bcrypt.compare result:', isValid);
    
    // Test with different PINs
    const testPins = ['123456', '1234', '567890', '5678'];
    console.log('\n🧪 Testing multiple PINs...');
    
    for (const pin of testPins) {
      const valid = await bcrypt.compare(pin, user.pin);
      console.log(`   PIN ${pin}: ${valid ? '✅ VALID' : '❌ INVALID'}`);
    }
    
    // Create a fresh hash for testing
    console.log('\n🔄 Creating fresh hash for 123456...');
    const freshHash = await bcrypt.hash('123456', 10);
    console.log('   Fresh hash:', freshHash);
    
    const freshTest = await bcrypt.compare('123456', freshHash);
    console.log('   Fresh hash test:', freshTest ? '✅ VALID' : '❌ INVALID');
    
    // Update the user with fresh hash
    console.log('\n🔧 Updating user with fresh hash...');
    await client.query(
      'UPDATE employees SET pin = $1 WHERE id = $2',
      [freshHash, user.id]
    );
    
    console.log('✅ User updated with fresh hash');
    
    // Test again
    const finalTest = await bcrypt.compare('123456', freshHash);
    console.log('   Final test:', finalTest ? '✅ VALID' : '❌ INVALID');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

debugPin().catch(console.error);
