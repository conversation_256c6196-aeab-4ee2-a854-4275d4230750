import React from 'react';
import { Tenant, TenantStatus, SubscriptionPlan } from '../types/tenant';

interface TenantCardProps {
  tenant: Tenant;
  onStatusChange: (tenantId: string, status: TenantStatus) => Promise<void>;
  onDelete: (tenantId: string) => Promise<void>;
  onViewDetails: (tenant: Tenant) => void;
  getStatusColor: (status: TenantStatus) => string;
  getPlanColor: (plan: SubscriptionPlan) => string;
}

const TenantCard: React.FC<TenantCardProps> = ({
  tenant,
  onStatusChange,
  onDelete,
  onViewDetails,
  getStatusColor,
  getPlanColor,
}) => {
  const statusOptions: TenantStatus[] = ['active', 'suspended', 'pending', 'cancelled'];

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{tenant.name}</h3>
            <p className="text-sm text-gray-500">{tenant.slug}</p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onViewDetails(tenant)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="View Details"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
            <button
              onClick={() => onDelete(tenant.id)}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors"
              title="Delete Tenant"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Status and Plan */}
        <div className="flex flex-wrap gap-2 mb-4">
          <select
            value={tenant.status}
            onChange={(e) => onStatusChange(tenant.id, e.target.value as TenantStatus)}
            className={`text-sm font-medium px-3 py-1 rounded-full ${getStatusColor(tenant.status)}`}
          >
            {statusOptions.map((status) => (
              <option key={status} value={status}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </option>
            ))}
          </select>
          <span className={`text-sm font-medium px-3 py-1 rounded-full ${getPlanColor(tenant.subscription_plan)}`}>
            {tenant.subscription_plan.charAt(0).toUpperCase() + tenant.subscription_plan.slice(1)}
          </span>
        </div>

        {/* Features */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Active Features</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(tenant.features)
              .filter(([_, enabled]) => enabled)
              .map(([feature]) => (
                <span
                  key={feature}
                  className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                >
                  {feature.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
              ))}
          </div>
        </div>

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex justify-between text-sm text-gray-500">
            <span>Created: {new Date(tenant.created_at).toLocaleDateString()}</span>
            <span>Last Updated: {new Date(tenant.updated_at).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenantCard;
