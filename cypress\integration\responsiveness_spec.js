describe('Mobile and Tablet Responsiveness Tests', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.get('input[type="text"]', { timeout: 10000 }).should('be.visible').type('1234'); // Login PIN
    cy.get('button', { timeout: 10000 }).contains('Login').click();
    cy.contains('POS', { timeout: 10000 }).should('be.visible');
  });

  it('should display correctly on mobile viewport', () => {
    cy.viewport('iphone-6');
    cy.get('.product-grid').should('be.visible');
    cy.get('button').contains('Checkout').should('be.visible');
    // Check that navigation is accessible
    cy.get('button').contains('POS').should('be.visible');
  });

  it('should display correctly on tablet viewport', () => {
    cy.viewport('ipad-2');
    cy.get('.product-grid').should('be.visible');
    cy.get('button').contains('Checkout').should('be.visible');
    cy.get('button').contains('POS').should('be.visible');
  });

  it('should display correctly on desktop viewport', () => {
    cy.viewport(1280, 800);
    cy.get('.product-grid').should('be.visible');
    cy.get('button').contains('Checkout').should('be.visible');
    cy.get('button').contains('POS').should('be.visible');
  });
});
