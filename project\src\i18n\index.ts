// Internationalization (i18n) System for RestroFlow POS
// Supports multiple languages, localization, and regional customization

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

// Language resources
import enTranslations from './locales/en.json';
import esTranslations from './locales/es.json';
import frTranslations from './locales/fr.json';
import deTranslations from './locales/de.json';
import itTranslations from './locales/it.json';
import ptTranslations from './locales/pt.json';
import zhTranslations from './locales/zh.json';
import jaTranslations from './locales/ja.json';
import koTranslations from './locales/ko.json';
import arTranslations from './locales/ar.json';

// Supported languages configuration
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸', rtl: false },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸', rtl: false },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷', rtl: false },
  { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', rtl: false },
  { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹', rtl: false },
  { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹', rtl: false },
  { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳', rtl: false },
  { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵', rtl: false },
  { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷', rtl: false },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true }
];

// Regional configurations
export const REGIONAL_CONFIGS = {
  'en-US': {
    currency: 'USD',
    currencySymbol: '$',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h',
    numberFormat: '1,234.56',
    timezone: 'America/New_York',
    firstDayOfWeek: 0, // Sunday
    measurementSystem: 'imperial'
  },
  'en-GB': {
    currency: 'GBP',
    currencySymbol: '£',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    numberFormat: '1,234.56',
    timezone: 'Europe/London',
    firstDayOfWeek: 1, // Monday
    measurementSystem: 'metric'
  },
  'es-ES': {
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    numberFormat: '1.234,56',
    timezone: 'Europe/Madrid',
    firstDayOfWeek: 1,
    measurementSystem: 'metric'
  },
  'fr-FR': {
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    numberFormat: '1 234,56',
    timezone: 'Europe/Paris',
    firstDayOfWeek: 1,
    measurementSystem: 'metric'
  },
  'de-DE': {
    currency: 'EUR',
    currencySymbol: '€',
    dateFormat: 'DD.MM.YYYY',
    timeFormat: '24h',
    numberFormat: '1.234,56',
    timezone: 'Europe/Berlin',
    firstDayOfWeek: 1,
    measurementSystem: 'metric'
  },
  'zh-CN': {
    currency: 'CNY',
    currencySymbol: '¥',
    dateFormat: 'YYYY/MM/DD',
    timeFormat: '24h',
    numberFormat: '1,234.56',
    timezone: 'Asia/Shanghai',
    firstDayOfWeek: 1,
    measurementSystem: 'metric'
  },
  'ja-JP': {
    currency: 'JPY',
    currencySymbol: '¥',
    dateFormat: 'YYYY/MM/DD',
    timeFormat: '24h',
    numberFormat: '1,234',
    timezone: 'Asia/Tokyo',
    firstDayOfWeek: 0,
    measurementSystem: 'metric'
  },
  'ar-SA': {
    currency: 'SAR',
    currencySymbol: 'ر.س',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '12h',
    numberFormat: '1,234.56',
    timezone: 'Asia/Riyadh',
    firstDayOfWeek: 6, // Saturday
    measurementSystem: 'metric'
  }
};

// Initialize i18n
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false, // React already escapes values
      format: function(value, format, lng) {
        if (format === 'currency') {
          return formatCurrency(value, lng);
        }
        if (format === 'date') {
          return formatDate(value, lng);
        }
        if (format === 'number') {
          return formatNumber(value, lng);
        }
        return value;
      }
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'restroflow_language'
    },

    backend: {
      loadPath: '/locales/{{lng}}.json',
      addPath: '/locales/add/{{lng}}/{{ns}}'
    },

    resources: {
      en: { translation: enTranslations },
      es: { translation: esTranslations },
      fr: { translation: frTranslations },
      de: { translation: deTranslations },
      it: { translation: itTranslations },
      pt: { translation: ptTranslations },
      zh: { translation: zhTranslations },
      ja: { translation: jaTranslations },
      ko: { translation: koTranslations },
      ar: { translation: arTranslations }
    },

    react: {
      useSuspense: false,
      bindI18n: 'languageChanged loaded',
      bindI18nStore: 'added removed',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span']
    }
  });

// Localization utility functions
export const formatCurrency = (amount: number, locale?: string): string => {
  const currentLocale = locale || i18n.language;
  const config = REGIONAL_CONFIGS[currentLocale as keyof typeof REGIONAL_CONFIGS] || REGIONAL_CONFIGS['en-US'];
  
  try {
    return new Intl.NumberFormat(currentLocale, {
      style: 'currency',
      currency: config.currency,
      minimumFractionDigits: config.currency === 'JPY' ? 0 : 2
    }).format(amount);
  } catch (error) {
    return `${config.currencySymbol}${amount.toFixed(2)}`;
  }
};

export const formatDate = (date: Date | string, locale?: string, options?: Intl.DateTimeFormatOptions): string => {
  const currentLocale = locale || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options
  };
  
  try {
    return new Intl.DateTimeFormat(currentLocale, defaultOptions).format(dateObj);
  } catch (error) {
    return dateObj.toLocaleDateString();
  }
};

export const formatTime = (date: Date | string, locale?: string): string => {
  const currentLocale = locale || i18n.language;
  const config = REGIONAL_CONFIGS[currentLocale as keyof typeof REGIONAL_CONFIGS] || REGIONAL_CONFIGS['en-US'];
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const options: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: config.timeFormat === '12h'
  };
  
  try {
    return new Intl.DateTimeFormat(currentLocale, options).format(dateObj);
  } catch (error) {
    return dateObj.toLocaleTimeString();
  }
};

export const formatNumber = (number: number, locale?: string): string => {
  const currentLocale = locale || i18n.language;
  
  try {
    return new Intl.NumberFormat(currentLocale).format(number);
  } catch (error) {
    return number.toString();
  }
};

export const formatPercentage = (number: number, locale?: string): string => {
  const currentLocale = locale || i18n.language;
  
  try {
    return new Intl.NumberFormat(currentLocale, {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(number / 100);
  } catch (error) {
    return `${number.toFixed(1)}%`;
  }
};

// Language management functions
export const changeLanguage = async (languageCode: string): Promise<void> => {
  try {
    await i18n.changeLanguage(languageCode);
    localStorage.setItem('restroflow_language', languageCode);
    
    // Update document direction for RTL languages
    const language = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
    if (language) {
      document.documentElement.dir = language.rtl ? 'rtl' : 'ltr';
      document.documentElement.lang = languageCode;
    }
    
    // Notify backend about language change
    await updateUserLanguagePreference(languageCode);
    
    console.log(`🌐 Language changed to: ${languageCode}`);
  } catch (error) {
    console.error('❌ Error changing language:', error);
  }
};

export const getCurrentLanguage = (): string => {
  return i18n.language || 'en';
};

export const getCurrentRegionalConfig = () => {
  const currentLocale = i18n.language;
  return REGIONAL_CONFIGS[currentLocale as keyof typeof REGIONAL_CONFIGS] || REGIONAL_CONFIGS['en-US'];
};

export const isRTL = (): boolean => {
  const currentLang = getCurrentLanguage();
  const language = SUPPORTED_LANGUAGES.find(lang => lang.code === currentLang);
  return language?.rtl || false;
};

// Translation helpers
export const t = (key: string, options?: any): string => {
  return i18n.t(key, options);
};

export const translateWithFallback = (key: string, fallback: string, options?: any): string => {
  const translation = i18n.t(key, options);
  return translation === key ? fallback : translation;
};

// Pluralization helper
export const pluralize = (count: number, singular: string, plural?: string): string => {
  if (count === 1) {
    return t(singular);
  }
  return t(plural || `${singular}_plural`, { count });
};

// Dynamic translation loading
export const loadTranslations = async (namespace: string, language?: string): Promise<void> => {
  const lang = language || getCurrentLanguage();
  
  try {
    await i18n.loadNamespaces(namespace);
    console.log(`📚 Loaded translations for namespace: ${namespace} (${lang})`);
  } catch (error) {
    console.error(`❌ Error loading translations for ${namespace}:`, error);
  }
};

// Update user language preference on backend
const updateUserLanguagePreference = async (languageCode: string): Promise<void> => {
  try {
    const token = localStorage.getItem('authToken');
    if (!token) return;
    
    await fetch('http://localhost:4000/api/user/preferences', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        language: languageCode,
        locale: `${languageCode}-${languageCode.toUpperCase()}`
      })
    });
  } catch (error) {
    console.error('❌ Error updating language preference:', error);
  }
};

// Business-specific translation helpers
export const translateMenuCategory = (category: string): string => {
  return t(`menu.categories.${category.toLowerCase()}`, category);
};

export const translateOrderStatus = (status: string): string => {
  return t(`orders.status.${status.toLowerCase()}`, status);
};

export const translatePaymentMethod = (method: string): string => {
  return t(`payments.methods.${method.toLowerCase()}`, method);
};

export const translateUserRole = (role: string): string => {
  return t(`users.roles.${role.toLowerCase()}`, role);
};

// Validation messages
export const getValidationMessage = (field: string, rule: string, value?: any): string => {
  return t(`validation.${rule}`, { field: t(`fields.${field}`), value });
};

// Error messages
export const getErrorMessage = (errorCode: string, context?: any): string => {
  return t(`errors.${errorCode}`, context);
};

// Success messages
export const getSuccessMessage = (action: string, entity?: string): string => {
  return t(`success.${action}`, { entity: entity ? t(`entities.${entity}`) : '' });
};

// Date and time helpers with localization
export const getLocalizedDayNames = (): string[] => {
  const locale = getCurrentLanguage();
  const days = [];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(2024, 0, i + 1); // January 1-7, 2024 (Mon-Sun)
    days.push(new Intl.DateTimeFormat(locale, { weekday: 'long' }).format(date));
  }
  
  return days;
};

export const getLocalizedMonthNames = (): string[] => {
  const locale = getCurrentLanguage();
  const months = [];
  
  for (let i = 0; i < 12; i++) {
    const date = new Date(2024, i, 1);
    months.push(new Intl.DateTimeFormat(locale, { month: 'long' }).format(date));
  }
  
  return months;
};

// Export i18n instance
export default i18n;
