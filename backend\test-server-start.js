// Simple test to start the RestroFlow server
console.log('🚀 Starting RestroFlow Server Test...');
console.log('📍 Current directory:', process.cwd());
console.log('📁 Files in directory:');

const fs = require('fs');
const files = fs.readdirSync('.');
files.forEach(file => {
  if (file.endsWith('.js')) {
    console.log(`  📄 ${file}`);
  }
});

console.log('\n🔍 Checking for working-server.js...');
if (fs.existsSync('./working-server.js')) {
  console.log('✅ working-server.js found!');
  console.log('📊 File size:', fs.statSync('./working-server.js').size, 'bytes');
  
  try {
    console.log('🔄 Attempting to require working-server.js...');
    require('./working-server.js');
  } catch (error) {
    console.log('💥 Error loading working-server.js:', error.message);
  }
} else {
  console.log('❌ working-server.js not found!');
}

console.log('🏁 Test completed.');
