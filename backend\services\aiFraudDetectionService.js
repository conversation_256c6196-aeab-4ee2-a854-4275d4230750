// AI Fraud Detection Service for Phase 5
// Real-time transaction monitoring and risk assessment

const { Pool } = require('pg');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class AIFraudDetectionService {
  constructor() {
    this.riskThresholds = {
      low: 0.25,
      medium: 0.50,
      high: 0.75,
      critical: 0.90
    };
    this.fraudIndicators = {
      unusual_amount: 0.3,
      unusual_time: 0.2,
      unusual_location: 0.4,
      rapid_transactions: 0.5,
      payment_method_mismatch: 0.3,
      customer_behavior_anomaly: 0.4,
      velocity_check: 0.6
    };
  }

  // =====================================================
  // REAL-TIME FRAUD ANALYSIS
  // =====================================================

  async analyzeTransaction(transactionData) {
    const startTime = Date.now();
    
    try {
      console.log('🔍 Analyzing transaction for fraud:', transactionData.transaction_id);

      // Get active fraud detection model
      const model = await this.getActiveFraudModel(transactionData.tenant_id);
      if (!model) {
        throw new Error('No active fraud detection model found');
      }

      // Perform multi-layered fraud analysis
      const riskFactors = await this.calculateRiskFactors(transactionData);
      const customerProfile = await this.getCustomerRiskProfile(transactionData);
      const velocityCheck = await this.performVelocityCheck(transactionData);
      const anomalyScore = await this.detectAnomalies(transactionData, customerProfile);

      // Calculate composite risk score
      const riskScore = this.calculateCompositeRiskScore({
        riskFactors,
        customerProfile,
        velocityCheck,
        anomalyScore
      });

      // Determine risk level and action
      const riskLevel = this.determineRiskLevel(riskScore);
      const recommendedAction = this.determineAction(riskLevel, riskScore);

      // Store risk assessment
      const riskAssessment = await this.storeRiskAssessment({
        transaction_id: transactionData.transaction_id,
        risk_score: riskScore,
        risk_level: riskLevel,
        fraud_indicators: riskFactors.indicators,
        risk_factors: {
          customer_risk: customerProfile.risk_score,
          velocity_risk: velocityCheck.risk_score,
          anomaly_risk: anomalyScore,
          transaction_risk: riskFactors.transaction_risk
        },
        ai_model_id: model.id,
        processing_time_ms: Date.now() - startTime,
        action_taken: recommendedAction,
        confidence_score: riskFactors.confidence
      });

      // Update system metrics
      await this.updateFraudMetrics(transactionData.tenant_id, riskLevel, recommendedAction);

      return {
        success: true,
        risk_assessment_id: riskAssessment.id,
        risk_score: riskScore,
        risk_level: riskLevel,
        recommended_action: recommendedAction,
        fraud_indicators: riskFactors.indicators,
        confidence_score: riskFactors.confidence,
        processing_time: Date.now() - startTime,
        model_version: model.model_version
      };

    } catch (error) {
      console.error('❌ Fraud analysis error:', error);
      return {
        success: false,
        error: error.message,
        risk_score: 0.5, // Default medium risk on error
        risk_level: 'medium',
        recommended_action: 'manual_review',
        processing_time: Date.now() - startTime
      };
    }
  }

  // =====================================================
  // RISK FACTOR CALCULATION
  // =====================================================

  async calculateRiskFactors(transactionData) {
    const indicators = {};
    let totalRisk = 0;
    let confidence = 1.0;

    // Amount-based risk analysis
    const amountRisk = await this.analyzeTransactionAmount(transactionData);
    if (amountRisk.is_unusual) {
      indicators.unusual_amount = amountRisk.risk_score;
      totalRisk += amountRisk.risk_score * this.fraudIndicators.unusual_amount;
    }

    // Time-based risk analysis
    const timeRisk = this.analyzeTransactionTime(transactionData);
    if (timeRisk.is_unusual) {
      indicators.unusual_time = timeRisk.risk_score;
      totalRisk += timeRisk.risk_score * this.fraudIndicators.unusual_time;
    }

    // Payment method risk analysis
    const paymentRisk = await this.analyzePaymentMethod(transactionData);
    if (paymentRisk.is_suspicious) {
      indicators.payment_method_mismatch = paymentRisk.risk_score;
      totalRisk += paymentRisk.risk_score * this.fraudIndicators.payment_method_mismatch;
    }

    // Location-based risk (if available)
    if (transactionData.location_data) {
      const locationRisk = await this.analyzeLocation(transactionData);
      if (locationRisk.is_unusual) {
        indicators.unusual_location = locationRisk.risk_score;
        totalRisk += locationRisk.risk_score * this.fraudIndicators.unusual_location;
      }
    }

    return {
      transaction_risk: Math.min(totalRisk, 1.0),
      indicators,
      confidence: confidence * 0.9 // Slight confidence reduction for complexity
    };
  }

  async analyzeTransactionAmount(transactionData) {
    try {
      // Get historical transaction amounts for this customer/tenant
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          AVG(total_amount) as avg_amount,
          STDDEV(total_amount) as stddev_amount,
          MAX(total_amount) as max_amount,
          COUNT(*) as transaction_count
        FROM payment_transactions 
        WHERE tenant_id = $1 
          AND created_at >= NOW() - INTERVAL '30 days'
          AND status = 'completed'
      `, [transactionData.tenant_id]);
      
      client.release();

      const stats = result.rows[0];
      const currentAmount = transactionData.total_amount;

      if (!stats.avg_amount || stats.transaction_count < 10) {
        return { is_unusual: false, risk_score: 0.1 };
      }

      // Calculate z-score for amount
      const zScore = Math.abs((currentAmount - stats.avg_amount) / (stats.stddev_amount || 1));
      
      // Risk increases with z-score
      let riskScore = 0;
      if (zScore > 3) riskScore = 0.9; // Very unusual
      else if (zScore > 2) riskScore = 0.7; // Unusual
      else if (zScore > 1.5) riskScore = 0.4; // Somewhat unusual
      else riskScore = 0.1; // Normal

      // Additional risk for extremely high amounts
      if (currentAmount > stats.max_amount * 2) {
        riskScore = Math.min(riskScore + 0.3, 1.0);
      }

      return {
        is_unusual: riskScore > 0.3,
        risk_score: riskScore,
        z_score: zScore,
        avg_amount: stats.avg_amount,
        current_amount: currentAmount
      };

    } catch (error) {
      console.error('❌ Amount analysis error:', error);
      return { is_unusual: false, risk_score: 0.1 };
    }
  }

  analyzeTransactionTime(transactionData) {
    const transactionTime = new Date(transactionData.created_at || Date.now());
    const hour = transactionTime.getHours();
    const dayOfWeek = transactionTime.getDay();

    // Define normal business hours (can be customized per tenant)
    const normalHours = {
      weekday: { start: 6, end: 22 }, // 6 AM to 10 PM
      weekend: { start: 8, end: 23 }  // 8 AM to 11 PM
    };

    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const businessHours = isWeekend ? normalHours.weekend : normalHours.weekday;

    let riskScore = 0;
    let isUnusual = false;

    // Check if transaction is outside normal hours
    if (hour < businessHours.start || hour > businessHours.end) {
      riskScore = 0.6;
      isUnusual = true;
    }
    // Very late night transactions (2 AM - 5 AM) are highly suspicious
    else if (hour >= 2 && hour <= 5) {
      riskScore = 0.8;
      isUnusual = true;
    }
    // Early morning transactions have moderate risk
    else if (hour >= 5 && hour <= 7) {
      riskScore = 0.3;
      isUnusual = true;
    }

    return {
      is_unusual: isUnusual,
      risk_score: riskScore,
      transaction_hour: hour,
      day_of_week: dayOfWeek,
      is_weekend: isWeekend
    };
  }

  async analyzePaymentMethod(transactionData) {
    try {
      // Get customer's typical payment methods
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          pm.name as payment_method,
          COUNT(*) as usage_count,
          COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as usage_percentage
        FROM payment_transactions pt
        JOIN payment_methods pm ON pt.payment_method_id = pm.id
        WHERE pt.tenant_id = $1 
          AND pt.created_at >= NOW() - INTERVAL '90 days'
          AND pt.status = 'completed'
          AND pt.customer_info->>'email' = $2
        GROUP BY pm.name
        ORDER BY usage_count DESC
      `, [transactionData.tenant_id, transactionData.customer_info?.email || '']);
      
      client.release();

      const paymentHistory = result.rows;
      const currentPaymentMethod = transactionData.payment_method_name;

      if (paymentHistory.length === 0) {
        // New customer - moderate risk
        return { is_suspicious: true, risk_score: 0.3, reason: 'new_customer' };
      }

      // Check if current payment method is typical for this customer
      const methodUsage = paymentHistory.find(pm => pm.payment_method === currentPaymentMethod);
      
      if (!methodUsage) {
        // Customer using a completely new payment method
        return { is_suspicious: true, risk_score: 0.5, reason: 'new_payment_method' };
      }

      // Low usage of this payment method is suspicious
      if (methodUsage.usage_percentage < 10) {
        return { is_suspicious: true, risk_score: 0.4, reason: 'unusual_payment_method' };
      }

      return { is_suspicious: false, risk_score: 0.1 };

    } catch (error) {
      console.error('❌ Payment method analysis error:', error);
      return { is_suspicious: false, risk_score: 0.1 };
    }
  }

  // =====================================================
  // CUSTOMER RISK PROFILING
  // =====================================================

  async getCustomerRiskProfile(transactionData) {
    try {
      const client = await pool.connect();
      
      // Get or create customer profile
      let profile = await client.query(`
        SELECT * FROM ai_customer_profiles 
        WHERE tenant_id = $1 AND customer_identifier = $2
      `, [transactionData.tenant_id, transactionData.customer_info?.email || 'anonymous']);

      if (profile.rows.length === 0) {
        // Create new customer profile
        const newProfile = await client.query(`
          INSERT INTO ai_customer_profiles (
            tenant_id, customer_identifier, customer_type, behavior_score
          ) VALUES ($1, $2, 'new', 0.5)
          RETURNING *
        `, [transactionData.tenant_id, transactionData.customer_info?.email || 'anonymous']);
        
        profile = newProfile;
      }

      client.release();

      const customerProfile = profile.rows[0];
      
      // Calculate risk based on customer history
      let riskScore = customerProfile.behavior_score || 0.5;
      
      // Adjust risk based on customer type
      switch (customerProfile.customer_type) {
        case 'new':
          riskScore += 0.2; // New customers have higher risk
          break;
        case 'vip':
          riskScore -= 0.3; // VIP customers have lower risk
          break;
        case 'at_risk':
          riskScore += 0.4; // At-risk customers have higher risk
          break;
      }

      // Ensure risk score is within bounds
      riskScore = Math.max(0, Math.min(1, riskScore));

      return {
        customer_id: customerProfile.id,
        customer_type: customerProfile.customer_type,
        risk_score: riskScore,
        behavior_score: customerProfile.behavior_score,
        total_orders: customerProfile.total_orders || 0,
        total_spent: customerProfile.total_spent || 0,
        churn_probability: customerProfile.churn_probability || 0
      };

    } catch (error) {
      console.error('❌ Customer profile error:', error);
      return {
        customer_type: 'unknown',
        risk_score: 0.5,
        behavior_score: 0.5
      };
    }
  }

  // =====================================================
  // VELOCITY CHECKS
  // =====================================================

  async performVelocityCheck(transactionData) {
    try {
      const client = await pool.connect();
      
      // Check for rapid transactions in the last hour
      const rapidTransactions = await client.query(`
        SELECT COUNT(*) as transaction_count
        FROM payment_transactions 
        WHERE tenant_id = $1 
          AND customer_info->>'email' = $2
          AND created_at >= NOW() - INTERVAL '1 hour'
          AND status IN ('completed', 'processing')
      `, [transactionData.tenant_id, transactionData.customer_info?.email || '']);

      // Check for high-value transactions in the last 24 hours
      const highValueTransactions = await client.query(`
        SELECT 
          COUNT(*) as count,
          SUM(total_amount) as total_amount
        FROM payment_transactions 
        WHERE tenant_id = $1 
          AND customer_info->>'email' = $2
          AND created_at >= NOW() - INTERVAL '24 hours'
          AND total_amount > $3
          AND status IN ('completed', 'processing')
      `, [transactionData.tenant_id, transactionData.customer_info?.email || '', 100]);

      client.release();

      const rapidCount = parseInt(rapidTransactions.rows[0].transaction_count);
      const highValueCount = parseInt(highValueTransactions.rows[0].count);
      const highValueTotal = parseFloat(highValueTransactions.rows[0].total_amount || 0);

      let riskScore = 0;
      const riskFactors = [];

      // Rapid transaction risk
      if (rapidCount > 5) {
        riskScore += 0.8;
        riskFactors.push('excessive_rapid_transactions');
      } else if (rapidCount > 3) {
        riskScore += 0.5;
        riskFactors.push('multiple_rapid_transactions');
      }

      // High-value transaction risk
      if (highValueCount > 3 || highValueTotal > 1000) {
        riskScore += 0.6;
        riskFactors.push('high_value_velocity');
      }

      return {
        risk_score: Math.min(riskScore, 1.0),
        rapid_transaction_count: rapidCount,
        high_value_count: highValueCount,
        high_value_total: highValueTotal,
        risk_factors: riskFactors
      };

    } catch (error) {
      console.error('❌ Velocity check error:', error);
      return { risk_score: 0.1, risk_factors: [] };
    }
  }

  // =====================================================
  // ANOMALY DETECTION
  // =====================================================

  async detectAnomalies(transactionData, customerProfile) {
    // Simple anomaly detection based on customer behavior patterns
    let anomalyScore = 0;

    // Check if transaction amount is anomalous for this customer
    const avgOrderValue = customerProfile.avg_order_value || 50;
    const currentAmount = transactionData.total_amount;
    
    if (currentAmount > avgOrderValue * 3) {
      anomalyScore += 0.4;
    } else if (currentAmount > avgOrderValue * 2) {
      anomalyScore += 0.2;
    }

    // Check if transaction time is anomalous
    const hour = new Date().getHours();
    if (hour < 6 || hour > 23) {
      anomalyScore += 0.3;
    }

    // Additional anomaly checks can be added here
    // (e.g., geolocation, device fingerprinting, etc.)

    return Math.min(anomalyScore, 1.0);
  }

  // =====================================================
  // RISK SCORING AND DECISION MAKING
  // =====================================================

  calculateCompositeRiskScore({ riskFactors, customerProfile, velocityCheck, anomalyScore }) {
    // Weighted combination of different risk factors
    const weights = {
      transaction_risk: 0.25,
      customer_risk: 0.30,
      velocity_risk: 0.25,
      anomaly_risk: 0.20
    };

    // Ensure all values are valid numbers
    const transactionRisk = isNaN(riskFactors.transaction_risk) ? 0.1 : riskFactors.transaction_risk;
    const customerRisk = isNaN(customerProfile.risk_score) ? 0.5 : customerProfile.risk_score;
    const velocityRisk = isNaN(velocityCheck.risk_score) ? 0.1 : velocityCheck.risk_score;
    const anomalyRisk = isNaN(anomalyScore) ? 0.1 : anomalyScore;

    const compositeScore =
      (transactionRisk * weights.transaction_risk) +
      (customerRisk * weights.customer_risk) +
      (velocityRisk * weights.velocity_risk) +
      (anomalyRisk * weights.anomaly_risk);

    return Math.min(Math.max(compositeScore, 0), 1.0);
  }

  determineRiskLevel(riskScore) {
    if (riskScore >= this.riskThresholds.critical) return 'critical';
    if (riskScore >= this.riskThresholds.high) return 'high';
    if (riskScore >= this.riskThresholds.medium) return 'medium';
    return 'low';
  }

  determineAction(riskLevel, riskScore) {
    switch (riskLevel) {
      case 'critical':
        return 'blocked';
      case 'high':
        return 'manual_review';
      case 'medium':
        return riskScore > 0.6 ? 'flagged' : 'approved';
      case 'low':
      default:
        return 'approved';
    }
  }

  // =====================================================
  // DATA STORAGE AND METRICS
  // =====================================================

  async storeRiskAssessment(assessmentData) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        INSERT INTO ai_transaction_risks (
          transaction_id, risk_score, risk_level, fraud_indicators,
          risk_factors, ai_model_id, processing_time_ms, action_taken,
          confidence_score, action_reason
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id
      `, [
        assessmentData.transaction_id,
        assessmentData.risk_score,
        assessmentData.risk_level,
        JSON.stringify(assessmentData.fraud_indicators),
        JSON.stringify(assessmentData.risk_factors),
        assessmentData.ai_model_id,
        assessmentData.processing_time_ms,
        assessmentData.action_taken,
        assessmentData.confidence_score,
        `Risk level: ${assessmentData.risk_level}, Score: ${assessmentData.risk_score.toFixed(4)}`
      ]);
      
      client.release();
      
      return { id: result.rows[0].id };

    } catch (error) {
      console.error('❌ Error storing risk assessment:', error);
      throw error;
    }
  }

  async getActiveFraudModel(tenantId) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT * FROM ai_fraud_models 
        WHERE tenant_id = $1 AND is_active = true 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [tenantId]);
      
      client.release();
      
      return result.rows[0] || null;

    } catch (error) {
      console.error('❌ Error getting fraud model:', error);
      return null;
    }
  }

  async updateFraudMetrics(tenantId, riskLevel, action) {
    try {
      const client = await pool.connect();
      const today = new Date().toISOString().split('T')[0];
      const currentHour = new Date().getHours();
      
      await client.query(`
        INSERT INTO ai_system_metrics (
          tenant_id, metric_date, metric_hour, fraud_checks_performed,
          fraud_detected, fraud_detection_accuracy
        ) VALUES ($1, $2, $3, 1, $4, 0.95)
        ON CONFLICT (tenant_id, metric_date, metric_hour)
        DO UPDATE SET
          fraud_checks_performed = ai_system_metrics.fraud_checks_performed + 1,
          fraud_detected = ai_system_metrics.fraud_detected + $4,
          fraud_detection_accuracy = 0.95
      `, [tenantId, today, currentHour, riskLevel === 'high' || riskLevel === 'critical' ? 1 : 0]);
      
      client.release();

    } catch (error) {
      console.error('❌ Error updating fraud metrics:', error);
    }
  }
}

module.exports = AIFraudDetectionService;
