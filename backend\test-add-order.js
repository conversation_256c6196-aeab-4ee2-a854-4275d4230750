const http = require('http');

const orderData = JSON.stringify({
  items: JSON.stringify([{
    id: '9a271a1d-6797-47c5-8cfe-bf8d58fdc3fb',
    name: 'Test Beer',
    price: 5.99,
    quantity: 2
  }]),
  timestamp: new Date().toISOString(),
  status: 'completed',
  total: 11.98,
  subtotal: 11.98,
  tax: 0,
  payment_method: 'cash',
  employee_id: 'daef8aa3-c660-449b-b199-b84a4ac97004'
});

const options = {
  hostname: 'localhost',
  port: 4000,
  path: '/orders',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': orderData.length
  }
};

const req = http.request(options, res => {
  console.log(`Add Order Status: ${res.statusCode}`);
  let data = '';
  
  res.on('data', chunk => {
    data += chunk;
  });

  res.on('end', () => {
    if (res.statusCode === 201) {
      console.log('Response:', JSON.parse(data));
      
      // Now try to get all orders
      http.get('http://localhost:4000/orders', (getRes) => {
        let getAllData = '';
        
        getRes.on('data', (chunk) => {
          getAllData += chunk;
        });
        
        getRes.on('end', () => {
          console.log('\nAll Orders:', JSON.parse(getAllData));
        });
      });
    } else {
      console.error('Error Response:', data);
    }
  });
});

req.on('error', error => {
  console.error('Error:', error);
});

req.write(orderData);
req.end();
