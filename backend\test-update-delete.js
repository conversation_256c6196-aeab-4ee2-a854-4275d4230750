const http = require('http');

const employeeId = '367042b7-e2d1-41b8-8928-f1950ab05bae';

// Update employee
const updateData = JSON.stringify({
  name: "Updated Employee",
  role: "manager"
});

const updateOptions = {
  hostname: 'localhost',
  port: 4000,
  path: `/employees/${employeeId}`,
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': updateData.length
  }
};

const updateReq = http.request(updateOptions, res => {
  console.log(`Update Status: ${res.statusCode}`);
  res.on('data', d => {
    process.stdout.write(d);
  });

  res.on('end', () => {
    // Delete employee after update
    const deleteOptions = {
      hostname: 'localhost',
      port: 4000,
      path: `/employees/${employeeId}`,
      method: 'DELETE'
    };

    const deleteReq = http.request(deleteOptions, delRes => {
      console.log(`\nDelete Status: ${delRes.statusCode}`);
      delRes.on('data', d => {
        process.stdout.write(d);
      });
    });

    deleteReq.on('error', error => {
      console.error('Delete Error:', error);
    });

    deleteReq.end();
  });
});

updateReq.on('error', error => {
  console.error('Update Error:', error);
});

updateReq.write(updateData);
updateReq.end();
