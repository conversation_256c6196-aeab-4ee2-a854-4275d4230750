import React, { useEffect, useState } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

const HealthStatus: React.FC = () => {
  const { fetchHealthStatus } = useEnhancedAppContext();
  const [health, setHealth] = useState<string>('Loading...');

  useEffect(() => {
    const getHealth = async () => {
      const status = await fetchHealthStatus();
      if (status && status.status) {
        setHealth(status.status);
      } else {
        setHealth('Unavailable');
      }
    };
    getHealth();
  }, [fetchHealthStatus]);

  return (
    <div className="p-3 bg-gray-800 text-white rounded-md text-center text-sm font-medium">
      Backend Health: <span className={health === 'healthy' ? 'text-green-400' : 'text-red-400'}>{health}</span>
    </div>
  );
};

export default HealthStatus;
