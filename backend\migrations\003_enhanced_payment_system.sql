-- Enhanced Payment System Database Schema
-- This migration adds comprehensive payment processing, receipt generation, and order completion tracking

-- Create payment methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL, -- 'cash', 'card', 'apple_pay', 'google_pay', etc.
    display_name VARCHAR(100) NOT NULL,
    icon VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    processing_fee_percentage DECIMAL(5,4) DEFAULT 0, -- e.g., 0.029 for 2.9%
    processing_fee_fixed DECIMAL(10,2) DEFAULT 0, -- e.g., 0.30 for $0.30
    requires_authorization BOOLEAN DEFAULT false,
    supports_tips BOOLEAN DEFAULT true,
    supports_split_payment BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    transaction_id VARCHAR(255) UNIQUE NOT NULL, -- External payment processor ID
    payment_method_id UUID REFERENCES payment_methods(id),
    payment_method_name VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    processing_fee DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'partially_refunded')),
    gateway_provider VARCHAR(50), -- 'stripe', 'moneris', 'square', etc.
    gateway_transaction_id VARCHAR(255),
    authorization_code VARCHAR(100),
    gateway_response JSONB,
    failure_reason TEXT,
    processed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create split payments table for handling multiple payment methods per order
CREATE TABLE IF NOT EXISTS split_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    payment_transaction_id UUID REFERENCES payment_transactions(id) ON DELETE CASCADE,
    split_amount DECIMAL(10,2) NOT NULL,
    split_percentage DECIMAL(5,2) NOT NULL,
    sequence_number INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create receipts table
CREATE TABLE IF NOT EXISTS receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    receipt_number VARCHAR(50) UNIQUE NOT NULL,
    receipt_type VARCHAR(20) DEFAULT 'customer' CHECK (receipt_type IN ('customer', 'merchant', 'kitchen', 'gift')),
    template_id UUID,
    receipt_data JSONB NOT NULL,
    print_status VARCHAR(20) DEFAULT 'pending' CHECK (print_status IN ('pending', 'printed', 'failed', 'skipped')),
    email_status VARCHAR(20) DEFAULT 'not_sent' CHECK (email_status IN ('not_sent', 'sent', 'failed', 'bounced')),
    sms_status VARCHAR(20) DEFAULT 'not_sent' CHECK (sms_status IN ('not_sent', 'sent', 'failed')),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    printed_at TIMESTAMP,
    emailed_at TIMESTAMP,
    sms_sent_at TIMESTAMP,
    reprint_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create receipt templates table
CREATE TABLE IF NOT EXISTS receipt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    template_type VARCHAR(20) DEFAULT 'standard' CHECK (template_type IN ('standard', 'gift', 'refund', 'kitchen')),
    header_config JSONB DEFAULT '{}',
    body_config JSONB DEFAULT '{}',
    footer_config JSONB DEFAULT '{}',
    paper_size VARCHAR(20) DEFAULT '80mm' CHECK (paper_size IN ('58mm', '80mm', 'A4', 'letter')),
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payment refunds table
CREATE TABLE IF NOT EXISTS payment_refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    original_transaction_id UUID REFERENCES payment_transactions(id) ON DELETE CASCADE,
    refund_transaction_id VARCHAR(255) UNIQUE NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_reason VARCHAR(255),
    refund_type VARCHAR(20) DEFAULT 'full' CHECK (refund_type IN ('full', 'partial')),
    gateway_refund_id VARCHAR(255),
    gateway_response JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    processed_by UUID REFERENCES employees(id) ON DELETE SET NULL,
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payment analytics table
CREATE TABLE IF NOT EXISTS payment_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    date DATE NOT NULL,
    hour INTEGER CHECK (hour BETWEEN 0 AND 23),
    payment_method VARCHAR(50) NOT NULL,
    transaction_count INTEGER DEFAULT 0,
    total_amount DECIMAL(10,2) DEFAULT 0,
    total_tips DECIMAL(10,2) DEFAULT 0,
    total_fees DECIMAL(10,2) DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    average_transaction_amount DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, location_id, date, hour, payment_method)
);

-- Create customer payment preferences table
CREATE TABLE IF NOT EXISTS customer_payment_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    preferred_payment_method VARCHAR(50),
    receipt_preference VARCHAR(20) DEFAULT 'print' CHECK (receipt_preference IN ('print', 'email', 'sms', 'both')),
    email_receipts BOOLEAN DEFAULT false,
    sms_receipts BOOLEAN DEFAULT false,
    auto_tip_percentage DECIMAL(5,2),
    saved_payment_methods JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create order completion tracking table
CREATE TABLE IF NOT EXISTS order_completion_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    step_name VARCHAR(50) NOT NULL, -- 'order_created', 'payment_processed', 'receipt_generated', 'kitchen_notified', etc.
    step_status VARCHAR(20) DEFAULT 'pending' CHECK (step_status IN ('pending', 'in_progress', 'completed', 'failed', 'skipped')),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payment gateway configurations table
CREATE TABLE IF NOT EXISTS payment_gateway_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    gateway_name VARCHAR(50) NOT NULL, -- 'stripe', 'moneris', 'square', etc.
    is_active BOOLEAN DEFAULT false,
    is_test_mode BOOLEAN DEFAULT true,
    api_credentials JSONB NOT NULL, -- Encrypted credentials
    webhook_endpoints JSONB DEFAULT '[]',
    supported_payment_methods JSONB DEFAULT '[]',
    fee_structure JSONB DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, gateway_name)
);

-- Create tip tracking table
CREATE TABLE IF NOT EXISTS tip_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    server_id UUID REFERENCES employees(id) ON DELETE SET NULL,
    tip_amount DECIMAL(10,2) NOT NULL,
    tip_percentage DECIMAL(5,2),
    tip_method VARCHAR(20) DEFAULT 'percentage' CHECK (tip_method IN ('percentage', 'fixed', 'custom')),
    order_total DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default payment methods
INSERT INTO payment_methods (tenant_id, name, display_name, icon, processing_fee_percentage, processing_fee_fixed, requires_authorization, supports_tips, supports_split_payment) VALUES
(NULL, 'cash', 'Cash', 'banknote', 0, 0, false, true, true),
(NULL, 'card', 'Credit/Debit Card', 'credit-card', 0.029, 0.30, true, true, true),
(NULL, 'apple_pay', 'Apple Pay', 'smartphone', 0.029, 0.30, true, true, false),
(NULL, 'google_pay', 'Google Pay', 'smartphone', 0.029, 0.30, true, true, false),
(NULL, 'tap', 'Tap to Pay', 'contactless', 0.029, 0.30, true, true, false),
(NULL, 'gift_card', 'Gift Card', 'gift', 0, 0, false, false, true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_payment_transactions_order_id ON payment_transactions(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_created_at ON payment_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_receipts_order_id ON receipts(order_id);
CREATE INDEX IF NOT EXISTS idx_receipts_receipt_number ON receipts(receipt_number);
CREATE INDEX IF NOT EXISTS idx_payment_analytics_date ON payment_analytics(date);
CREATE INDEX IF NOT EXISTS idx_payment_analytics_tenant_location ON payment_analytics(tenant_id, location_id);
CREATE INDEX IF NOT EXISTS idx_order_completion_tracking_order_id ON order_completion_tracking(order_id);
CREATE INDEX IF NOT EXISTS idx_tip_tracking_server_date ON tip_tracking(server_id, date);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_receipt_templates_updated_at BEFORE UPDATE ON receipt_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_gateway_configs_updated_at BEFORE UPDATE ON payment_gateway_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_payment_preferences_updated_at BEFORE UPDATE ON customer_payment_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
