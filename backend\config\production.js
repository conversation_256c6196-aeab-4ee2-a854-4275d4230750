// Production Environment Configuration
// Phase 7: Production Deployment & Live Integration

const production = {
  // Environment
  NODE_ENV: 'production',
  PORT: process.env.PORT || 4000,
  
  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'BARPOS',
    username: process.env.DB_USER || 'BARPOS',
    password: process.env.DB_PASSWORD || 'Chaand@0319',
    
    // Production Pool Settings
    pool: {
      min: 10,
      max: 100,
      acquire: 30000,
      idle: 10000,
      evict: 1000
    },
    
    // SSL Configuration for Production
    ssl: process.env.NODE_ENV === 'production' ? {
      require: true,
      rejectUnauthorized: false
    } : false,
    
    // Connection Timeout
    dialectOptions: {
      connectTimeout: 60000,
      acquireTimeout: 60000,
      timeout: 60000
    }
  },
  
  // Redis Cache Configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    db: 0,
    
    // Production Settings
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: 3,
    
    // Memory Management
    maxMemoryPolicy: 'allkeys-lru',
    keyPrefix: 'barpos:',
    
    // TTL Settings
    defaultTTL: 3600, // 1 hour
    sessionTTL: 86400, // 24 hours
    cacheTTL: 1800 // 30 minutes
  },
  
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    
    // Security Settings
    algorithm: 'HS256',
    issuer: 'barpos-api',
    audience: 'barpos-clients'
  },
  
  // Live Exchange Rate APIs
  exchangeRates: {
    primary: {
      name: 'ExchangeRate-API',
      url: 'https://api.exchangerate-api.com/v4/latest',
      apiKey: process.env.EXCHANGE_RATE_API_KEY,
      rateLimit: 1500, // requests per month
      timeout: 5000
    },
    
    secondary: {
      name: 'Fixer.io',
      url: 'https://api.fixer.io/latest',
      apiKey: process.env.FIXER_API_KEY,
      rateLimit: 1000,
      timeout: 5000
    },
    
    tertiary: {
      name: 'Open Exchange Rates',
      url: 'https://openexchangerates.org/api/latest.json',
      apiKey: process.env.OPEN_EXCHANGE_RATES_API_KEY,
      rateLimit: 1000,
      timeout: 5000
    },
    
    // Fallback Settings
    updateInterval: 300000, // 5 minutes
    retryAttempts: 3,
    retryDelay: 1000
  },
  
  // Production Payment Gateways
  paymentGateways: {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
      apiVersion: '2023-10-16',
      
      // Production Settings
      environment: 'production',
      timeout: 30000,
      maxRetries: 3,
      
      // Supported Features
      supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
      supportedMethods: ['card', 'bank_transfer', 'digital_wallet'],
      
      // Fee Structure
      fees: {
        card: { percentage: 2.9, fixed: 0.30 },
        international: { percentage: 1.5, fixed: 0 }
      }
    },
    
    paypal: {
      clientId: process.env.PAYPAL_CLIENT_ID,
      clientSecret: process.env.PAYPAL_CLIENT_SECRET,
      environment: process.env.PAYPAL_ENVIRONMENT || 'production',
      
      // API Configuration
      baseUrl: process.env.PAYPAL_ENVIRONMENT === 'production' 
        ? 'https://api.paypal.com'
        : 'https://api.sandbox.paypal.com',
      
      // Supported Features
      supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY'],
      supportedMethods: ['paypal', 'credit_card', 'bank_transfer'],
      
      // Settings
      timeout: 30000,
      maxRetries: 3
    },
    
    square: {
      accessToken: process.env.SQUARE_ACCESS_TOKEN,
      applicationId: process.env.SQUARE_APPLICATION_ID,
      environment: process.env.SQUARE_ENVIRONMENT || 'production',
      
      // API Configuration
      baseUrl: process.env.SQUARE_ENVIRONMENT === 'production'
        ? 'https://connect.squareup.com'
        : 'https://connect.squareupsandbox.com',
      
      // Supported Features
      supportedCurrencies: ['USD', 'CAD', 'AUD', 'GBP', 'JPY'],
      supportedMethods: ['card', 'cash', 'gift_card'],
      
      // Settings
      timeout: 30000,
      maxRetries: 3
    }
  },
  
  // Compliance Services
  compliance: {
    gdpr: {
      provider: 'OneTrust',
      apiKey: process.env.ONETRUST_API_KEY,
      baseUrl: 'https://app.onetrust.com/api',
      
      features: {
        consentManagement: true,
        dataMapping: true,
        breachNotification: true,
        rightToErasure: true,
        dataPortability: true
      },
      
      settings: {
        timeout: 10000,
        retryAttempts: 3
      }
    },
    
    ccpa: {
      provider: 'TrustArc',
      apiKey: process.env.TRUSTARC_API_KEY,
      baseUrl: 'https://api.trustarc.com',
      
      features: {
        privacyRights: true,
        optOutManagement: true,
        dataInventory: true
      },
      
      settings: {
        timeout: 10000,
        retryAttempts: 3
      }
    },
    
    pciDss: {
      level: 1,
      tokenization: true,
      encryption: 'end-to-end',
      
      requirements: {
        firewall: true,
        defaultPasswords: false,
        cardholderDataProtection: true,
        encryptedTransmission: true,
        antivirusUpdated: true,
        secureSystemsDevelopment: true,
        accessControlMeasures: true,
        uniqueUserIds: true,
        physicalDataAccess: true,
        networkMonitoring: true,
        regularTesting: true,
        informationSecurityPolicy: true
      }
    }
  },
  
  // Security Configuration
  security: {
    // Encryption
    encryption: {
      algorithm: 'AES-256-GCM',
      keyLength: 32,
      ivLength: 16,
      tagLength: 16,
      
      // Key Management
      keyRotationDays: 90,
      masterKey: process.env.MASTER_ENCRYPTION_KEY
    },
    
    // Rate Limiting
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // requests per window
      standardHeaders: true,
      legacyHeaders: false,
      
      // Skip successful requests
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      
      // Custom key generator
      keyGenerator: (req) => req.ip,
      
      // Handler for rate limit exceeded
      handler: (req, res) => {
        res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
      }
    },
    
    // CORS Configuration
    cors: {
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
      optionsSuccessStatus: 200,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    },
    
    // Helmet Security Headers
    helmet: {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }
  },
  
  // Monitoring & Logging
  monitoring: {
    // Application Performance Monitoring
    apm: {
      serviceName: 'barpos-api',
      environment: 'production',
      
      // New Relic
      newRelic: {
        licenseKey: process.env.NEW_RELIC_LICENSE_KEY,
        appName: 'BARPOS API Production'
      },
      
      // DataDog
      datadog: {
        apiKey: process.env.DATADOG_API_KEY,
        service: 'barpos-api',
        version: process.env.APP_VERSION || '1.0.0'
      }
    },
    
    // Error Tracking
    errorTracking: {
      sentry: {
        dsn: process.env.SENTRY_DSN,
        environment: 'production',
        release: process.env.APP_VERSION || '1.0.0',
        
        // Performance Monitoring
        tracesSampleRate: 0.1,
        profilesSampleRate: 0.1
      }
    },
    
    // Logging
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      format: 'json',
      
      // File Logging
      file: {
        enabled: true,
        filename: 'logs/barpos-api.log',
        maxSize: '100MB',
        maxFiles: 10
      },
      
      // Console Logging
      console: {
        enabled: process.env.NODE_ENV !== 'production',
        colorize: false
      },
      
      // External Logging
      external: {
        enabled: true,
        service: 'elasticsearch',
        host: process.env.ELASTICSEARCH_HOST,
        index: 'barpos-logs'
      }
    }
  },
  
  // Performance Configuration
  performance: {
    // Compression
    compression: {
      enabled: true,
      level: 6,
      threshold: 1024,
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return true;
      }
    },
    
    // Caching
    caching: {
      enabled: true,
      
      // Memory Cache
      memory: {
        max: 1000,
        ttl: 300000 // 5 minutes
      },
      
      // HTTP Cache Headers
      http: {
        maxAge: 3600, // 1 hour
        mustRevalidate: true,
        public: false
      }
    },
    
    // Connection Limits
    limits: {
      maxConnections: 1000,
      timeout: 30000,
      keepAlive: true,
      keepAliveTimeout: 5000
    }
  },
  
  // Feature Flags
  features: {
    aiEnabled: true,
    globalPayments: true,
    complianceMonitoring: true,
    realTimeAnalytics: true,
    advancedReporting: true,
    multiCurrency: true,
    fraudDetection: true,
    predictiveAnalytics: true,
    automationWorkflows: true
  },
  
  // Health Check Configuration
  healthCheck: {
    enabled: true,
    endpoint: '/health',
    
    checks: {
      database: true,
      redis: true,
      externalAPIs: true,
      diskSpace: true,
      memory: true
    },
    
    thresholds: {
      responseTime: 1000, // ms
      memoryUsage: 0.8, // 80%
      diskUsage: 0.85, // 85%
      cpuUsage: 0.7 // 70%
    }
  }
};

module.exports = production;
