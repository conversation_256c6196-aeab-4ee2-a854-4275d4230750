import React, { useState, useEffect } from 'react';
import {
  Building,
  Users,
  DollarSign,
  Activity,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Search,
  Filter,
  Download,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Clock,
  X,
  Star,
  TrendingUp,
  Calendar,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';
import { comprehensiveAdminApiService } from '../services/comprehensiveAdminApiService';

interface Tenant {
  id: string;
  name: string;
  slug: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
  employee_count?: number;
  order_count?: number;
  total_revenue?: number;
  last_order_date?: string;
  last_employee_login?: string;
}

interface TenantStatistics {
  overview: {
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
    suspendedTenants: number;
    newTenantsThisMonth: number;
    newTenantsThisWeek: number;
    avgEmployeesPerTenant: number;
    totalOrders: number;
    totalRevenue: number;
  };
  recentActivity: Array<{
    id: string;
    name: string;
    slug: string;
    status: string;
    employeeCount: number;
    orderCount: number;
    totalRevenue: number;
    lastOrderDate: string;
    lastEmployeeLogin: string;
    isNewTenant: boolean;
  }>;
}

interface TenantActivity {
  type: string;
  date: string;
  description: string;
  metadata: any;
}

interface EnhancedTenantManagerProps {
  tenants?: any[];
  metrics?: any;
  isDarkMode?: boolean;
}

export const EnhancedTenantManager: React.FC<EnhancedTenantManagerProps> = ({ 
  tenants: propTenants = [], 
  metrics: propMetrics = null, 
  isDarkMode = false 
}) => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Enhanced state for new features
  const [tenantStatistics, setTenantStatistics] = useState<TenantStatistics | null>(null);
  const [selectedTenantActivity, setSelectedTenantActivity] = useState<TenantActivity[]>([]);
  const [showTenantProfile, setShowTenantProfile] = useState<string | null>(null);
  const [showActivityModal, setShowActivityModal] = useState<string | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedTenants, setSelectedTenants] = useState<string[]>([]);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [advancedFilters, setAdvancedFilters] = useState({
    createdDateRange: '',
    revenueRange: '',
    employeeCountRange: ''
  });
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Enhanced data loading - use props if available, otherwise fetch from API
  useEffect(() => {
    console.log('🚀 Enhanced TenantManager component mounted, loading comprehensive data...');
    if (propTenants && propTenants.length > 0) {
      console.log('📊 Using provided tenants data:', propTenants);
      loadTenantsFromProps();
    } else {
      console.log('📡 No props data, fetching from API...');
      loadTenantsData();
    }
    loadTenantStatistics();
  }, [propTenants]);

  // Load comprehensive tenant statistics
  const loadTenantStatistics = async () => {
    try {
      console.log('📊 Loading tenant statistics...');
      const response = await fetch('http://localhost:4000/api/admin/tenants/statistics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const stats = await response.json();
        setTenantStatistics(stats);
        console.log('✅ Tenant statistics loaded:', stats);
      } else {
        console.warn('⚠️ Failed to load tenant statistics, using fallback data');
        setFallbackStatistics();
      }
    } catch (error) {
      console.error('💥 Error loading tenant statistics:', error);
      console.log('🔄 Using fallback statistics...');
      setFallbackStatistics();
    }
  };

  // Fallback statistics to prevent crashes
  const setFallbackStatistics = () => {
    const fallbackStats = {
      overview: {
        totalTenants: 1,
        activeTenants: 1,
        inactiveTenants: 0,
        suspendedTenants: 0,
        newTenantsThisMonth: 1,
        newTenantsThisWeek: 0,
        avgEmployeesPerTenant: 5,
        totalOrders: 150,
        totalRevenue: 12500.00
      },
      recentActivity: [
        {
          id: '1',
          name: 'Demo Restaurant',
          slug: 'demo-restaurant',
          status: 'active',
          employeeCount: 5,
          orderCount: 150,
          totalRevenue: 12500.00,
          lastOrderDate: new Date().toISOString(),
          lastEmployeeLogin: new Date().toISOString(),
          isNewTenant: false
        }
      ]
    };
    setTenantStatistics(fallbackStats);
  };

  // Load tenant activity timeline
  const loadTenantActivity = async (tenantId: string) => {
    try {
      console.log(`📋 Loading activity for tenant ${tenantId}...`);
      const response = await fetch(`http://localhost:4000/api/admin/tenants/${tenantId}/activity`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedTenantActivity(data.activities);
        console.log(`✅ Loaded ${data.activities.length} activities for tenant ${tenantId}`);
      } else {
        console.warn(`⚠️ Failed to load activity for tenant ${tenantId}`);
        setSelectedTenantActivity([]);
      }
    } catch (error) {
      console.error('💥 Error loading tenant activity:', error);
      setSelectedTenantActivity([]);
    }
  };

  const loadTenantsFromProps = () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('📊 Loading tenants from props:', propTenants);
      
      // Transform the prop data to match frontend expectations
      const transformedTenants = propTenants.map((tenant: any) => ({
        id: tenant.id.toString(),
        name: tenant.name,
        slug: tenant.slug,
        status: tenant.status,
        created_at: tenant.createdAt || tenant.created_at || '',
        updated_at: tenant.updatedAt || tenant.updated_at || '',
        employee_count: tenant.employeeCount || tenant.employee_count || 0,
        order_count: tenant.orderCount || tenant.order_count || 0,
        total_revenue: tenant.totalRevenue || tenant.total_revenue || 0,
        last_order_date: tenant.lastOrderDate || tenant.last_order_date || '',
        last_employee_login: tenant.lastEmployeeLogin || tenant.last_employee_login || ''
      }));

      console.log('🔄 Transformed tenants from props:', transformedTenants);
      setTenants(transformedTenants);
    } catch (error) {
      console.error('💥 Error loading tenants from props:', error);
      setError('Failed to load tenants data from props.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadTenantsData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('🏢 Loading tenants data...');
      const tenantsData = await comprehensiveAdminApiService.getTenants();
      console.log('📊 Raw API response:', tenantsData);

      // Transform the data to match frontend expectations
      const transformedTenants = tenantsData.map((tenant: any) => ({
        id: tenant.id.toString(),
        name: tenant.name,
        slug: tenant.slug,
        status: tenant.status,
        created_at: tenant.createdAt || tenant.created_at || '',
        updated_at: tenant.updatedAt || tenant.updated_at || '',
        employee_count: tenant.employeeCount || tenant.employee_count || 0,
        order_count: tenant.orderCount || tenant.order_count || 0,
        total_revenue: tenant.totalRevenue || tenant.total_revenue || 0,
        last_order_date: tenant.lastOrderDate || tenant.last_order_date || '',
        last_employee_login: tenant.lastEmployeeLogin || tenant.last_employee_login || ''
      }));

      console.log('🔄 Transformed tenants:', transformedTenants);
      setTenants(transformedTenants);
    } catch (error) {
      console.error('💥 Error loading tenants:', error);
      console.log('🔄 Using fallback tenant data...');

      // Fallback data to prevent crashes
      const fallbackTenants = [
        {
          id: '1',
          name: 'Demo Restaurant',
          slug: 'demo-restaurant',
          status: 'active' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          employee_count: 5,
          order_count: 150,
          total_revenue: 12500.00,
          last_order_date: new Date().toISOString(),
          last_employee_login: new Date().toISOString()
        }
      ];

      setTenants(fallbackTenants);
      setError('Using demo data. Please check your database connection.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter tenants
  useEffect(() => {
    let filtered = tenants.filter(tenant => {
      const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tenant.slug.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || tenant.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });

    setFilteredTenants(filtered);
  }, [tenants, searchTerm, filterStatus]);

  // Enhanced tenant profile view
  const handleViewTenantProfile = async (tenantId: string) => {
    console.log('🏢 Opening tenant profile for:', tenantId);
    setShowTenantProfile(tenantId);
    await loadTenantActivity(tenantId);
  };

  // Enhanced tenant activity view
  const handleViewTenantActivity = async (tenantId: string) => {
    console.log('📋 Opening activity timeline for:', tenantId);
    setShowActivityModal(tenantId);
    await loadTenantActivity(tenantId);
  };

  // Export tenants functionality
  const handleExportTenants = async (format: string = 'csv') => {
    try {
      setActionLoading('export');
      console.log(`📤 Exporting tenants in ${format} format...`);

      const response = await fetch(`http://localhost:4000/api/admin/tenants/export?format=${format}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        if (format === 'csv') {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `tenants-export-${new Date().toISOString().split('T')[0]}.csv`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        } else {
          const data = await response.json();
          console.log('📊 Export data:', data);
        }
        
        setSuccess(`Tenants exported successfully in ${format} format`);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error('💥 Error exporting tenants:', error);
      setError('Failed to export tenants');
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading tenants...</span>
      </div>
    );
  }

  return (
    <div className={`rounded-lg shadow-lg p-6 transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-800 border border-gray-700' 
        : 'bg-white'
    }`}>
      {/* Enhanced Header with Statistics */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className={`text-2xl font-bold transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Enhanced Tenant Management
          </h2>
          <p className={`transition-colors duration-300 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Comprehensive tenant management with analytics, activity tracking, and advanced operations
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowBulkActions(!showBulkActions)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors duration-200 ${
              showBulkActions 
                ? 'bg-blue-600 text-white' 
                : isDarkMode 
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Settings className="h-4 w-4" />
            <span>Bulk Actions</span>
          </button>
          <button
            onClick={() => handleExportTenants('csv')}
            disabled={actionLoading === 'export'}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {actionLoading === 'export' ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            <span>Export CSV</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            <span>Add Tenant</span>
          </button>
        </div>
      </div>

      {/* Real-time Statistics Dashboard */}
      {tenantStatistics && (
        <div className="mb-6">
          <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Tenant Statistics Dashboard
          </h3>

          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-4">
            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-blue-50 border-blue-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Building className="h-8 w-8 text-blue-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenantStatistics.overview.totalTenants}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Total Tenants
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenantStatistics.overview.activeTenants}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Active Tenants
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-yellow-50 border-yellow-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Star className="h-8 w-8 text-yellow-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenantStatistics.overview.newTenantsThisMonth}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    New This Month
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-purple-50 border-purple-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-purple-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {Math.round(tenantStatistics.overview.avgEmployeesPerTenant)}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Avg Employees
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-indigo-50 border-indigo-200'
            }`}>
              <div className="flex items-center space-x-3">
                <DollarSign className="h-8 w-8 text-indigo-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    ${(tenantStatistics.overview.totalRevenue / 1000).toFixed(1)}K
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Total Revenue
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          {tenantStatistics.recentActivity.length > 0 && (
            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-3 transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Recent Tenant Activity
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {tenantStatistics.recentActivity.slice(0, 6).map((activity, index) => (
                  <div key={index} className={`p-3 rounded border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
                  }`}>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${activity.isNewTenant ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                      <span className={`font-medium transition-colors duration-300 ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {activity.name}
                      </span>
                    </div>
                    <p className={`text-sm transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {activity.employeeCount} employees • {activity.orderCount} orders
                    </p>
                    <p className={`text-xs transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-500' : 'text-gray-500'
                    }`}>
                      Revenue: ${activity.totalRevenue.toFixed(2)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Filters */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search className={`absolute left-3 top-3 h-4 w-4 transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-400'
            }`} />
            <input
              type="text"
              placeholder="Search tenants by name, slug..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
          </div>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>

          <button
            onClick={() => {
              setSearchTerm('');
              setFilterStatus('all');
              setAdvancedFilters({ createdDateRange: '', revenueRange: '', employeeCountRange: '' });
            }}
            className={`px-4 py-2 border rounded-lg transition-colors duration-200 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Enhanced Bulk Actions */}
      {showBulkActions && selectedTenants.length > 0 && (
        <div className={`border rounded-lg p-4 mb-6 transition-colors duration-300 ${
          isDarkMode
            ? 'bg-gray-700 border-gray-600'
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-center justify-between mb-3">
            <span className={`font-medium transition-colors duration-300 ${
              isDarkMode ? 'text-white' : 'text-blue-800'
            }`}>
              {selectedTenants.length} tenant(s) selected
            </span>
            <button
              onClick={() => setSelectedTenants([])}
              className={`text-sm transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Clear Selection
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => console.log('Bulk activate tenants:', selectedTenants)}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              Activate All
            </button>
            <button
              onClick={() => console.log('Bulk suspend tenants:', selectedTenants)}
              className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
            >
              Suspend All
            </button>
            <button
              onClick={() => console.log('Bulk deactivate tenants:', selectedTenants)}
              className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
            >
              Deactivate All
            </button>
          </div>
        </div>
      )}

      {/* Tenant Table */}
      <div className={`rounded-lg border transition-colors duration-300 ${
        isDarkMode
          ? 'bg-gray-800 border-gray-700'
          : 'bg-white border-gray-200'
      }`}>
        <div className="p-6">
          <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            All Tenants ({filteredTenants.length})
          </h3>

          {/* Tenant Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className={`border-b transition-colors duration-300 ${
                  isDarkMode ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    <input
                      type="checkbox"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTenants(filteredTenants.map(t => t.id));
                        } else {
                          setSelectedTenants([]);
                        }
                      }}
                      className="mr-2"
                    />
                    Tenant
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Status
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Employees
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Orders
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Revenue
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Created
                  </th>
                  <th className={`text-left py-3 px-4 font-medium transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredTenants.map((tenant) => (
                  <tr key={tenant.id} className={`border-b transition-colors duration-300 ${
                    isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'
                  }`}>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedTenants.includes(tenant.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedTenants(prev => [...prev, tenant.id]);
                            } else {
                              setSelectedTenants(prev => prev.filter(id => id !== tenant.id));
                            }
                          }}
                          className="mr-2"
                        />
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center bg-blue-100`}>
                          <Building className="w-4 h-4 text-blue-600" />
                        </div>
                        <div>
                          <p className={`font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {tenant.name}
                          </p>
                          <p className={`text-sm transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            {tenant.slug}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tenant.status)}`}>
                        {tenant.status.toUpperCase()}
                      </span>
                    </td>
                    <td className={`py-3 px-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      {tenant.employee_count || 0}
                    </td>
                    <td className={`py-3 px-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      {tenant.order_count || 0}
                    </td>
                    <td className={`py-3 px-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      ${(tenant.total_revenue || 0).toFixed(2)}
                    </td>
                    <td className={`py-3 px-4 text-sm transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      {tenant.created_at ? new Date(tenant.created_at).toLocaleDateString() : 'Unknown'}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewTenantProfile(tenant.id)}
                          className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-blue-600 hover:bg-blue-700 text-white"
                          title="View Profile"
                        >
                          <Eye className="h-3 w-3" />
                          <span className="hidden sm:inline">Profile</span>
                        </button>

                        <button
                          onClick={() => handleViewTenantActivity(tenant.id)}
                          className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-indigo-600 hover:bg-indigo-700 text-white"
                          title="View Activity"
                        >
                          <Clock className="h-3 w-3" />
                          <span className="hidden sm:inline">Activity</span>
                        </button>

                        <button
                          onClick={() => console.log('Edit tenant:', tenant.id)}
                          className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-green-600 hover:bg-green-700 text-white"
                          title="Edit Tenant"
                        >
                          <Edit className="h-3 w-3" />
                          <span className="hidden sm:inline">Edit</span>
                        </button>

                        <button
                          onClick={() => console.log('Delete tenant:', tenant.id)}
                          className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-red-600 hover:bg-red-700 text-white"
                          title="Delete Tenant"
                        >
                          <Trash2 className="h-3 w-3" />
                          <span className="hidden sm:inline">Delete</span>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Enhanced Tenant Profile Modal */}
      {showTenantProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-colors duration-300 ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-semibold transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Tenant Profile Details
              </h3>
              <button
                onClick={() => setShowTenantProfile(null)}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {(() => {
              const tenant = tenants.find(t => t.id === showTenantProfile);
              if (!tenant) return <div>Tenant not found</div>;

              return (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Tenant Information */}
                  <div className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-semibold mb-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Basic Information
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <Building className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className={`font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {tenant.name}
                          </p>
                          <p className={`text-sm transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {tenant.slug}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Status
                          </label>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tenant.status)}`}>
                            {tenant.status.toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Employees
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {tenant.employee_count || 0}
                          </p>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Total Orders
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {tenant.order_count || 0}
                          </p>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Total Revenue
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            ${(tenant.total_revenue || 0).toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Activity Timeline */}
                  <div className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-semibold mb-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Recent Activity
                    </h4>
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {selectedTenantActivity.length > 0 ? (
                        selectedTenantActivity.map((activity, index) => (
                          <div key={index} className={`p-3 rounded border transition-colors duration-300 ${
                            isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
                          }`}>
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${
                                activity.type === 'tenant_created' ? 'bg-green-500' :
                                activity.type === 'order_created' ? 'bg-blue-500' :
                                activity.type === 'employee_added' ? 'bg-purple-500' : 'bg-yellow-500'
                              }`}></div>
                              <span className={`font-medium transition-colors duration-300 ${
                                isDarkMode ? 'text-white' : 'text-gray-900'
                              }`}>
                                {activity.description}
                              </span>
                            </div>
                            <p className={`text-sm mt-1 transition-colors duration-300 ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {new Date(activity.date).toLocaleString()}
                            </p>
                          </div>
                        ))
                      ) : (
                        <p className={`text-center transition-colors duration-300 ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          No recent activity
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      )}

      {/* Tenant Activity Modal */}
      {showActivityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-colors duration-300 ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-semibold transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Tenant Activity Timeline
              </h3>
              <button
                onClick={() => setShowActivityModal(null)}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              {selectedTenantActivity.length > 0 ? (
                selectedTenantActivity.map((activity, index) => (
                  <div key={index} className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-start space-x-3">
                      <div className={`w-3 h-3 rounded-full mt-2 ${
                        activity.type === 'tenant_created' ? 'bg-green-500' :
                        activity.type === 'order_created' ? 'bg-blue-500' :
                        activity.type === 'employee_added' ? 'bg-purple-500' :
                        activity.type === 'tenant_updated' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`}></div>
                      <div className="flex-1">
                        <h4 className={`font-medium transition-colors duration-300 ${
                          isDarkMode ? 'text-white' : 'text-gray-900'
                        }`}>
                          {activity.description}
                        </h4>
                        <p className={`text-sm mt-1 transition-colors duration-300 ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {new Date(activity.date).toLocaleString()}
                        </p>
                        {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                          <div className={`mt-2 p-2 rounded text-xs transition-colors duration-300 ${
                            isDarkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700'
                          }`}>
                            <pre>{JSON.stringify(activity.metadata, null, 2)}</pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className={`h-12 w-12 mx-auto mb-3 transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-600' : 'text-gray-400'
                  }`} />
                  <p className={`transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    No activity records found
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Success/Error Messages */}
      {success && (
        <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {success}
        </div>
      )}

      {error && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {error}
        </div>
      )}
    </div>
  );
};
