import React, { useState, useEffect } from 'react';
import { X, Plus, Minus, CreditCard, DollarSign, Smartphone, Users, Calculator } from 'lucide-react';
import { Button } from '../ui/button';

interface PaymentSplit {
  id: string;
  method: 'cash' | 'card' | 'mobile_wallet';
  amount: number;
  customer_name?: string;
  card_last_four?: string;
}

interface SplitPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderTotal: number;
  onProcessSplit: (splits: PaymentSplit[]) => void;
}

export const SplitPaymentModal: React.FC<SplitPaymentModalProps> = ({
  isOpen,
  onClose,
  orderTotal,
  onProcessSplit
}) => {
  const [splits, setSplits] = useState<PaymentSplit[]>([
    {
      id: '1',
      method: 'card',
      amount: orderTotal / 2,
      customer_name: 'Customer 1'
    },
    {
      id: '2',
      method: 'card',
      amount: orderTotal / 2,
      customer_name: 'Customer 2'
    }
  ]);

  const [splitMode, setSplitMode] = useState<'equal' | 'custom' | 'by_item'>('equal');
  const [numberOfSplits, setNumberOfSplits] = useState(2);
  const [isProcessing, setIsProcessing] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Recalculate splits when mode or number changes
  useEffect(() => {
    if (splitMode === 'equal') {
      const equalAmount = orderTotal / numberOfSplits;
      const newSplits = Array.from({ length: numberOfSplits }, (_, index) => ({
        id: (index + 1).toString(),
        method: 'card' as const,
        amount: equalAmount,
        customer_name: `Customer ${index + 1}`
      }));
      setSplits(newSplits);
    }
  }, [splitMode, numberOfSplits, orderTotal]);

  const paymentMethods = [
    { id: 'cash', name: 'Cash', icon: DollarSign, color: 'green' },
    { id: 'card', name: 'Card', icon: CreditCard, color: 'blue' },
    { id: 'mobile_wallet', name: 'Mobile', icon: Smartphone, color: 'purple' }
  ];

  const getTotalSplitAmount = () => {
    return splits.reduce((total, split) => total + split.amount, 0);
  };

  const getRemainingAmount = () => {
    return orderTotal - getTotalSplitAmount();
  };

  const validateSplits = () => {
    const newErrors: Record<string, string> = {};
    const totalSplit = getTotalSplitAmount();
    const remaining = Math.abs(orderTotal - totalSplit);

    if (remaining > 0.01) {
      newErrors.total = `Split amounts must equal order total. Remaining: $${remaining.toFixed(2)}`;
    }

    splits.forEach((split, index) => {
      if (split.amount <= 0) {
        newErrors[`split_${index}`] = 'Amount must be greater than 0';
      }
      if (!split.customer_name?.trim()) {
        newErrors[`name_${index}`] = 'Customer name is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSplitChange = (index: number, field: keyof PaymentSplit, value: any) => {
    setSplits(prev => prev.map((split, i) => 
      i === index ? { ...split, [field]: value } : split
    ));
    
    // Clear errors when user makes changes
    if (errors[`split_${index}`] || errors[`name_${index}`]) {
      setErrors(prev => ({
        ...prev,
        [`split_${index}`]: '',
        [`name_${index}`]: ''
      }));
    }
  };

  const addSplit = () => {
    const newSplit: PaymentSplit = {
      id: (splits.length + 1).toString(),
      method: 'card',
      amount: 0,
      customer_name: `Customer ${splits.length + 1}`
    };
    setSplits(prev => [...prev, newSplit]);
  };

  const removeSplit = (index: number) => {
    if (splits.length > 2) {
      setSplits(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleEqualSplit = () => {
    const equalAmount = orderTotal / splits.length;
    setSplits(prev => prev.map(split => ({ ...split, amount: equalAmount })));
  };

  const handleProcessSplit = async () => {
    if (!validateSplits()) {
      return;
    }

    setIsProcessing(true);
    try {
      await onProcessSplit(splits);
      onClose();
    } catch (error) {
      setErrors({ submit: 'Failed to process split payment. Please try again.' });
    } finally {
      setIsProcessing(false);
    }
  };

  const getMethodIcon = (method: string) => {
    const methodConfig = paymentMethods.find(m => m.id === method);
    return methodConfig?.icon || CreditCard;
  };

  const getMethodColor = (method: string) => {
    const methodConfig = paymentMethods.find(m => m.id === method);
    switch (methodConfig?.color) {
      case 'green': return 'text-green-600 bg-green-100';
      case 'blue': return 'text-blue-600 bg-blue-100';
      case 'purple': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Split Payment</h2>
              <p className="text-sm text-gray-500">Order Total: ${orderTotal.toFixed(2)}</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Split Mode Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Split Method</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { id: 'equal', name: 'Equal Split', description: 'Split equally among customers' },
                { id: 'custom', name: 'Custom Amounts', description: 'Set custom amount for each customer' },
                { id: 'by_item', name: 'By Items', description: 'Split by individual items (Coming Soon)', disabled: true }
              ].map((mode) => (
                <button
                  key={mode.id}
                  onClick={() => !mode.disabled && setSplitMode(mode.id as any)}
                  disabled={mode.disabled}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    splitMode === mode.id
                      ? 'border-purple-500 bg-purple-50'
                      : mode.disabled
                      ? 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <h4 className="font-medium text-gray-900">{mode.name}</h4>
                  <p className="text-sm text-gray-600 mt-1">{mode.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Number of Splits (for equal mode) */}
          {splitMode === 'equal' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Number of Customers</h3>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setNumberOfSplits(Math.max(2, numberOfSplits - 1))}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="text-xl font-semibold w-12 text-center">{numberOfSplits}</span>
                <button
                  onClick={() => setNumberOfSplits(Math.min(10, numberOfSplits + 1))}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          {/* Payment Splits */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Payment Details</h3>
              {splitMode === 'custom' && (
                <div className="flex space-x-2">
                  <button
                    onClick={handleEqualSplit}
                    className="flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg"
                  >
                    <Calculator className="h-4 w-4" />
                    <span>Equal Split</span>
                  </button>
                  <button
                    onClick={addSplit}
                    className="flex items-center space-x-2 px-3 py-1 text-sm bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg"
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add Customer</span>
                  </button>
                </div>
              )}
            </div>

            <div className="space-y-4">
              {splits.map((split, index) => {
                const MethodIcon = getMethodIcon(split.method);
                return (
                  <div key={split.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      {/* Customer Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Customer Name
                        </label>
                        <input
                          type="text"
                          value={split.customer_name || ''}
                          onChange={(e) => handleSplitChange(index, 'customer_name', e.target.value)}
                          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                            errors[`name_${index}`] ? 'border-red-500' : 'border-gray-300'
                          }`}
                          placeholder="Enter name"
                        />
                        {errors[`name_${index}`] && (
                          <p className="text-sm text-red-600 mt-1">{errors[`name_${index}`]}</p>
                        )}
                      </div>

                      {/* Payment Method */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Method
                        </label>
                        <select
                          value={split.method}
                          onChange={(e) => handleSplitChange(index, 'method', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          {paymentMethods.map((method) => (
                            <option key={method.id} value={method.id}>
                              {method.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Amount */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Amount
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-2 text-gray-500">$</span>
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={split.amount.toFixed(2)}
                            onChange={(e) => handleSplitChange(index, 'amount', parseFloat(e.target.value) || 0)}
                            disabled={splitMode === 'equal'}
                            className={`w-full pl-8 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                              errors[`split_${index}`] ? 'border-red-500' : 'border-gray-300'
                            } ${splitMode === 'equal' ? 'bg-gray-50' : ''}`}
                          />
                        </div>
                        {errors[`split_${index}`] && (
                          <p className="text-sm text-red-600 mt-1">{errors[`split_${index}`]}</p>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between">
                        <div className={`p-2 rounded-lg ${getMethodColor(split.method)}`}>
                          <MethodIcon className="h-5 w-5" />
                        </div>
                        {splits.length > 2 && splitMode === 'custom' && (
                          <button
                            onClick={() => removeSplit(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium text-gray-900">Order Total:</span>
              <span className="font-bold text-gray-900">${orderTotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium text-gray-900">Split Total:</span>
              <span className="font-bold text-gray-900">${getTotalSplitAmount().toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-900">Remaining:</span>
              <span className={`font-bold ${getRemainingAmount() === 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${getRemainingAmount().toFixed(2)}
              </span>
            </div>
          </div>

          {/* Error Message */}
          {errors.total && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.total}</p>
            </div>
          )}

          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleProcessSplit} 
              disabled={isProcessing || getRemainingAmount() !== 0}
            >
              {isProcessing ? 'Processing...' : `Process Split Payment`}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
