const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const queries = require('./db/queries');

const app = express();
const port = 4000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Rate limiting (disabled for testing)
// const limiter = rateLimit({
//   windowMs: 1 * 60 * 1000, // 1 minute
//   max: 10 // limit each IP to 10 requests per windowMs
// });
// app.use(limiter);

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Database health check endpoint
app.get('/api/admin/health/database', (req, res) => {
  try {
    // Since this server uses queries module, let's check if it's available
    if (queries) {
      res.json({
        status: 'healthy',
        database: {
          connected: true,
          timestamp: new Date().toISOString(),
          message: 'Database connection active'
        }
      });
    } else {
      res.status(500).json({
        status: 'unhealthy',
        database: {
          connected: false,
          timestamp: new Date().toISOString(),
          message: 'Database queries module not available'
        }
      });
    }
  } catch (error) {
    console.error('Database health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      database: {
        connected: false,
        timestamp: new Date().toISOString(),
        error: error.message
      }
    });
  }
});

// Admin metrics endpoint
app.get('/api/admin/metrics', (req, res) => {
  try {
    const metrics = {
      totalTenants: 3,
      activeTenants: 3,
      totalUsers: 6,
      activeUsers: 4,
      totalRevenue: 15420.50,
      monthlyRevenue: 8750.25,
      transactionsToday: 45,
      systemUptime: 99.8,
      responseTime: 125,
      errorRate: 0.02,
      databaseConnections: 8,
      apiRequests: 12450,
      memoryUsage: 67.8,
      cpuUsage: 34.2,
      diskUsage: 45.1,
      lastUpdated: new Date().toISOString()
    };
    res.json(metrics);
  } catch (error) {
    console.error('Error fetching admin metrics:', error);
    res.status(500).json({ error: 'Failed to fetch metrics' });
  }
});

// Security status endpoint
app.get('/api/admin/security/status', (req, res) => {
  try {
    const securityStatus = {
      threatLevel: 'low',
      activeThreats: 0,
      blockedAttempts: 0,
      complianceScore: 98.5,
      lastScan: new Date().toISOString(),
      accessAttempts: 0,
      maxAccessAttempts: 3,
      systemStatus: 'secure',
      firewallStatus: 'active',
      encryptionStatus: 'enabled',
      backupStatus: 'current',
      auditStatus: 'compliant'
    };
    res.json(securityStatus);
  } catch (error) {
    console.error('Error fetching security status:', error);
    res.status(500).json({ error: 'Failed to fetch security status' });
  }
});

// Critical dashboard data endpoint
app.get('/api/admin/dashboard/critical', (req, res) => {
  try {
    const criticalData = {
      systemAlerts: [],
      criticalErrors: 0,
      emergencyNotifications: [],
      systemLoad: 45.2,
      memoryUsage: 67.8,
      diskUsage: 34.1,
      networkStatus: 'healthy',
      databaseStatus: 'connected',
      backupStatus: 'current',
      securityStatus: 'secure'
    };
    res.json(criticalData);
  } catch (error) {
    console.error('Error fetching critical dashboard data:', error);
    res.status(500).json({ error: 'Failed to fetch critical dashboard data' });
  }
});

// Authentication middleware
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization;
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Protected endpoint example
app.post('/api/server/restart', authMiddleware, (req, res) => {
  res.status(200).json({ message: 'Server restart initiated' });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { pin, tenant_slug } = req.body;

    if (!pin) {
      return res.status(400).json({ error: 'PIN is required' });
    }

    // Development authentication bypass for demo credentials
    const developmentCredentials = {
      '888888': { id: 1, name: 'Super Admin', role: 'super_admin', tenant_id: 1, tenant_name: 'Demo Restaurant', tenant_slug: 'demo-restaurant' },
      '999999': { id: 2, name: 'Super Admin 2', role: 'super_admin', tenant_id: 1, tenant_name: 'Demo Restaurant', tenant_slug: 'demo-restaurant' },
      '123456': { id: 3, name: 'Demo Employee', role: 'employee', tenant_id: 1, tenant_name: 'Demo Restaurant', tenant_slug: 'demo-restaurant' },
      '234567': { id: 4, name: 'Demo Employee 2', role: 'employee', tenant_id: 1, tenant_name: 'Demo Restaurant', tenant_slug: 'demo-restaurant' },
      '567890': { id: 5, name: 'Demo Manager', role: 'manager', tenant_id: 1, tenant_name: 'Demo Restaurant', tenant_slug: 'demo-restaurant' }
    };

    // Check development credentials first
    if (developmentCredentials[pin]) {
      console.log(`Development login successful for PIN: ${pin}`);
      return res.json({
        token: `jwt-token-${pin}-${Date.now()}`,
        user: developmentCredentials[pin]
      });
    }

    // Fallback to database authentication
    const authData = await queries.getEmployeeByPin(pin, tenant_slug);

    if (!authData) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    res.json({
      token: `jwt-token-${pin}-${Date.now()}`,
      user: authData
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Token verification endpoint
app.get('/api/auth/verify', (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  // Simple token validation (in production, use proper JWT verification)
  if (token.startsWith('jwt-token-') || token === 'mock-jwt-token') {
    res.json({ valid: true, message: 'Token is valid' });
  } else {
    res.status(401).json({ error: 'Invalid token' });
  }
});

// API routes
app.get('/api/employees', async (req, res) => {
  try {
    const employees = await queries.getAllEmployees();
    res.json(employees);
  } catch (error) {
    console.error('Error fetching employees:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/settings', async (req, res) => {
  try {
    const settings = await queries.getSettings();
    res.json(settings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/products', async (req, res) => {
  try {
    const products = await queries.getProducts();
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/categories', async (req, res) => {
  try {
    const categories = await queries.getCategories();
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/floor-layout', async (req, res) => {
  try {
    const floorLayout = await queries.getFloorLayout();
    res.json(floorLayout);
  } catch (error) {
    console.error('Error fetching floor layout:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Orders API endpoints
app.get('/api/orders', async (req, res) => {
  try {
    console.log('📋 Fetching orders...');
    // Mock orders data for now
    const orders = [
      {
        id: 'order_1',
        items: [
          { name: 'Coffee', price: 3.50, quantity: 2 },
          { name: 'Sandwich', price: 8.99, quantity: 1 }
        ],
        subtotal: 15.99,
        tax: 1.60,
        total: 17.59,
        status: 'completed',
        timestamp: new Date().toISOString(),
        employee_id: 1,
        tenant_id: 1
      }
    ];
    res.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/orders', async (req, res) => {
  try {
    console.log('📝 Creating new order:', req.body);
    const orderData = req.body;

    // Generate order ID
    const orderId = `order_${Date.now()}`;

    // Create order object
    const order = {
      id: orderId,
      ...orderData,
      status: 'completed',
      timestamp: new Date().toISOString(),
      created_at: new Date().toISOString()
    };

    console.log('✅ Order created successfully:', orderId);

    // In a real implementation, save to database here
    // await queries.createOrder(order);

    res.status(201).json({
      success: true,
      order: order,
      message: 'Order created successfully'
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ error: 'Failed to create order' });
  }
});

// Floor layout API endpoints
app.get('/api/floor/layout', async (req, res) => {
  try {
    console.log('🏢 Fetching floor layout...');

    // Mock floor layout data
    const floorLayout = {
      tables: [
        {
          id: '1',
          number: 1,
          seats: 4,
          x: 50,
          y: 50,
          width: 80,
          height: 80,
          shape: 'rectangle',
          status: 'available',
          section: 'main',
          tableType: 'regular'
        },
        {
          id: '2',
          number: 2,
          seats: 2,
          x: 200,
          y: 50,
          width: 60,
          height: 60,
          shape: 'circle',
          status: 'occupied',
          section: 'main',
          tableType: 'regular',
          guestCount: 2,
          orderTotal: 45.50,
          orderItems: 3
        },
        {
          id: '3',
          number: 3,
          seats: 6,
          x: 350,
          y: 50,
          width: 100,
          height: 80,
          shape: 'rectangle',
          status: 'reserved',
          section: 'main',
          tableType: 'regular'
        }
      ],
      sections: [
        {
          id: 'main',
          name: 'Main Dining',
          color: '#3B82F6',
          x: 25,
          y: 25,
          width: 450,
          height: 200
        },
        {
          id: 'bar',
          name: 'Bar Area',
          color: '#10B981',
          x: 25,
          y: 250,
          width: 200,
          height: 100
        }
      ]
    };

    res.json(floorLayout);
  } catch (error) {
    console.error('Error fetching floor layout:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Payment processing endpoint
app.post('/api/payments/process', async (req, res) => {
  try {
    console.log('💳 Processing payment:', req.body);
    const { amount, method, orderId } = req.body;

    // Simulate payment processing
    const paymentId = `payment_${Date.now()}`;

    // Mock payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const payment = {
      id: paymentId,
      orderId: orderId,
      amount: amount,
      method: method,
      status: 'completed',
      timestamp: new Date().toISOString(),
      transactionId: `txn_${Date.now()}`
    };

    console.log('✅ Payment processed successfully:', paymentId);

    res.json({
      success: true,
      payment: payment,
      message: 'Payment processed successfully'
    });
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({ error: 'Payment processing failed' });
  }
});

// Phase 2D API Endpoints

// Enterprise Integrations API
app.get('/api/integrations', (req, res) => {
  const integrations = [
    {
      id: 'stripe',
      name: 'Stripe',
      category: 'payment',
      status: 'connected',
      lastSync: new Date(Date.now() - 300000),
      usage: 15420,
      successRate: 98.5,
      features: ['Credit Cards', 'Digital Wallets', 'Subscriptions', 'Refunds']
    },
    {
      id: 'moneris',
      name: 'Moneris',
      category: 'payment',
      status: 'connected',
      lastSync: new Date(Date.now() - 600000),
      usage: 8930,
      successRate: 97.2,
      features: ['Interac', 'Credit Cards', 'Contactless', 'EMV']
    },
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      category: 'accounting',
      status: 'pending',
      usage: 0,
      successRate: 0,
      features: ['Invoicing', 'Expense Tracking', 'Tax Reports', 'Payroll']
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      category: 'marketing',
      status: 'disconnected',
      usage: 0,
      successRate: 0,
      features: ['Email Campaigns', 'Customer Segmentation', 'Analytics', 'Automation']
    },
    {
      id: 'twilio',
      name: 'Twilio',
      category: 'communication',
      status: 'connected',
      lastSync: new Date(Date.now() - 120000),
      usage: 5670,
      successRate: 99.1,
      features: ['SMS Alerts', 'Order Updates', 'Marketing Messages', 'Two-Factor Auth']
    },
    {
      id: 'ubereats',
      name: 'Uber Eats',
      category: 'delivery',
      status: 'connected',
      lastSync: new Date(Date.now() - 180000),
      usage: 3450,
      successRate: 96.8,
      features: ['Order Sync', 'Menu Management', 'Delivery Tracking', 'Analytics']
    }
  ];
  res.json({ integrations, total: integrations.length, connected: integrations.filter(i => i.status === 'connected').length });
});

// Advanced Automation API
app.get('/api/automation/rules', (req, res) => {
  const automationRules = [
    {
      id: 'auto-1',
      name: 'Low Stock Alert',
      category: 'inventory',
      status: 'active',
      triggerCount: 45,
      successRate: 98.5,
      lastTriggered: new Date(Date.now() - 3600000),
      description: 'Automatically notify managers when inventory falls below threshold'
    },
    {
      id: 'auto-2',
      name: 'Happy Hour Pricing',
      category: 'pricing',
      status: 'active',
      triggerCount: 156,
      successRate: 100,
      lastTriggered: new Date(Date.now() - 86400000),
      description: 'Automatically apply discounts during specified hours'
    },
    {
      id: 'auto-3',
      name: 'Customer Birthday Rewards',
      category: 'marketing',
      status: 'active',
      triggerCount: 23,
      successRate: 95.7,
      lastTriggered: new Date(Date.now() - 7200000),
      description: 'Send birthday offers to customers automatically'
    },
    {
      id: 'auto-4',
      name: 'Peak Hour Staffing',
      category: 'staffing',
      status: 'active',
      triggerCount: 89,
      successRate: 92.1,
      lastTriggered: new Date(Date.now() - 10800000),
      description: 'Automatically adjust staffing recommendations based on predicted demand'
    }
  ];
  const totalTriggers = automationRules.reduce((sum, rule) => sum + rule.triggerCount, 0);
  const avgSuccessRate = automationRules.reduce((sum, rule) => sum + rule.successRate, 0) / automationRules.length;
  res.json({
    rules: automationRules,
    total: automationRules.length,
    totalTriggers,
    avgSuccessRate: Math.round(avgSuccessRate * 10) / 10,
    activeRules: automationRules.filter(r => r.status === 'active').length
  });
});

// Mobile & API Management API
app.get('/api/mobile/apps', (req, res) => {
  const mobileApps = [
    {
      id: 'pos-ios',
      name: 'POS Manager',
      platform: 'ios',
      version: '2.1.4',
      downloads: 12500,
      activeUsers: 8900,
      rating: 4.7,
      status: 'live',
      lastUpdate: new Date(Date.now() - 604800000)
    },
    {
      id: 'pos-android',
      name: 'POS Manager',
      platform: 'android',
      version: '2.1.3',
      downloads: 18700,
      activeUsers: 13200,
      rating: 4.5,
      status: 'live',
      lastUpdate: new Date(Date.now() - 1209600000)
    },
    {
      id: 'customer-ios',
      name: 'Customer App',
      platform: 'ios',
      version: '1.8.2',
      downloads: 45600,
      activeUsers: 28900,
      rating: 4.3,
      status: 'live',
      lastUpdate: new Date(Date.now() - 432000000)
    },
    {
      id: 'customer-android',
      name: 'Customer App',
      platform: 'android',
      version: '1.8.1',
      downloads: 67800,
      activeUsers: 41200,
      rating: 4.1,
      status: 'live',
      lastUpdate: new Date(Date.now() - 864000000)
    }
  ];
  const totalDownloads = mobileApps.reduce((sum, app) => sum + app.downloads, 0);
  const totalActiveUsers = mobileApps.reduce((sum, app) => sum + app.activeUsers, 0);
  const avgRating = mobileApps.filter(app => app.rating > 0).reduce((sum, app) => sum + app.rating, 0) / mobileApps.filter(app => app.rating > 0).length;
  res.json({
    apps: mobileApps,
    total: mobileApps.length,
    totalDownloads,
    totalActiveUsers,
    avgRating: Math.round(avgRating * 10) / 10,
    liveApps: mobileApps.filter(app => app.status === 'live').length
  });
});

// API Endpoints Management
app.get('/api/endpoints', (req, res) => {
  const apiEndpoints = [
    {
      id: 'auth-login',
      path: '/api/auth/login',
      method: 'POST',
      usage: 15420,
      responseTime: 145,
      errorRate: 0.2,
      status: 'healthy',
      description: 'User authentication endpoint'
    },
    {
      id: 'orders-get',
      path: '/api/orders',
      method: 'GET',
      usage: 8930,
      responseTime: 89,
      errorRate: 0.1,
      status: 'healthy',
      description: 'Retrieve order information'
    },
    {
      id: 'payments-process',
      path: '/api/payments/process',
      method: 'POST',
      usage: 5670,
      responseTime: 320,
      errorRate: 1.2,
      status: 'warning',
      description: 'Process payment transactions'
    },
    {
      id: 'inventory-get',
      path: '/api/inventory',
      method: 'GET',
      usage: 3450,
      responseTime: 67,
      errorRate: 0.05,
      status: 'healthy',
      description: 'Inventory management data'
    }
  ];
  const totalRequests = apiEndpoints.reduce((sum, endpoint) => sum + endpoint.usage, 0);
  const avgResponseTime = Math.round(apiEndpoints.reduce((sum, endpoint) => sum + endpoint.responseTime, 0) / apiEndpoints.length);
  const avgErrorRate = Math.round((apiEndpoints.reduce((sum, endpoint) => sum + endpoint.errorRate, 0) / apiEndpoints.length) * 100) / 100;
  res.json({
    endpoints: apiEndpoints,
    total: apiEndpoints.length,
    totalRequests,
    avgResponseTime,
    avgErrorRate,
    healthyEndpoints: apiEndpoints.filter(e => e.status === 'healthy').length
  });
});

// Omnichannel Experience API
app.get('/api/channels', (req, res) => {
  const channels = [
    {
      id: 'web',
      name: 'Website',
      type: 'online',
      status: 'active',
      orders: 1250,
      revenue: 45600,
      customers: 3400,
      satisfaction: 4.3,
      conversionRate: 3.2
    },
    {
      id: 'mobile',
      name: 'Mobile App',
      type: 'mobile',
      status: 'active',
      orders: 890,
      revenue: 32100,
      customers: 2100,
      satisfaction: 4.5,
      conversionRate: 4.1
    },
    {
      id: 'instore',
      name: 'In-Store POS',
      type: 'instore',
      status: 'active',
      orders: 2340,
      revenue: 78900,
      customers: 1890,
      satisfaction: 4.7,
      conversionRate: 12.5
    },
    {
      id: 'ubereats',
      name: 'Uber Eats',
      type: 'delivery',
      status: 'active',
      orders: 567,
      revenue: 18900,
      customers: 1200,
      satisfaction: 4.1,
      conversionRate: 2.8
    }
  ];
  const totalOrders = channels.reduce((sum, channel) => sum + channel.orders, 0);
  const totalRevenue = channels.reduce((sum, channel) => sum + channel.revenue, 0);
  const totalCustomers = channels.reduce((sum, channel) => sum + channel.customers, 0);
  const avgSatisfaction = Math.round((channels.reduce((sum, channel) => sum + channel.satisfaction, 0) / channels.length) * 10) / 10;
  res.json({
    channels,
    total: channels.length,
    totalOrders,
    totalRevenue,
    totalCustomers,
    avgSatisfaction,
    activeChannels: channels.filter(c => c.status === 'active').length
  });
});

// Customer Journey API
app.get('/api/customer-journeys', (req, res) => {
  const customerJourneys = [
    {
      id: 'journey-1',
      customerId: 'cust-001',
      customerName: 'Sarah Johnson',
      touchpoints: [
        { channel: 'social', action: 'viewed_post', timestamp: new Date(Date.now() - 7200000) },
        { channel: 'web', action: 'visited_menu', timestamp: new Date(Date.now() - 3600000) },
        { channel: 'mobile', action: 'placed_order', timestamp: new Date(Date.now() - 1800000), value: 45.50 }
      ],
      totalValue: 45.50,
      status: 'completed'
    },
    {
      id: 'journey-2',
      customerId: 'cust-002',
      customerName: 'Mike Chen',
      touchpoints: [
        { channel: 'web', action: 'browsed_menu', timestamp: new Date(Date.now() - 10800000) },
        { channel: 'instore', action: 'visited_location', timestamp: new Date(Date.now() - 7200000) },
        { channel: 'instore', action: 'placed_order', timestamp: new Date(Date.now() - 7000000), value: 78.25 }
      ],
      totalValue: 78.25,
      status: 'completed'
    },
    {
      id: 'journey-3',
      customerId: 'cust-003',
      customerName: 'Emma Davis',
      touchpoints: [
        { channel: 'mobile', action: 'downloaded_app', timestamp: new Date(Date.now() - 14400000) },
        { channel: 'mobile', action: 'browsed_menu', timestamp: new Date(Date.now() - 3600000) },
        { channel: 'mobile', action: 'added_to_cart', timestamp: new Date(Date.now() - 1800000) }
      ],
      totalValue: 0,
      status: 'abandoned'
    }
  ];
  res.json({
    journeys: customerJourneys,
    total: customerJourneys.length,
    completed: customerJourneys.filter(j => j.status === 'completed').length,
    abandoned: customerJourneys.filter(j => j.status === 'abandoned').length,
    totalValue: customerJourneys.reduce((sum, journey) => sum + journey.totalValue, 0)
  });
});

// Deployment Readiness API
app.get('/api/deployment/environments', (req, res) => {
  const environments = [
    {
      id: 'dev',
      name: 'Development',
      type: 'development',
      status: 'healthy',
      version: '2.4.0-dev.123',
      uptime: 99.2,
      cpu: 45,
      memory: 62,
      disk: 34,
      requests: 1250,
      errors: 3,
      lastDeployment: new Date(Date.now() - 3600000)
    },
    {
      id: 'staging',
      name: 'Staging',
      type: 'staging',
      status: 'warning',
      version: '2.3.8-rc.2',
      uptime: 98.7,
      cpu: 67,
      memory: 78,
      disk: 45,
      requests: 890,
      errors: 12,
      lastDeployment: new Date(Date.now() - 7200000)
    },
    {
      id: 'prod',
      name: 'Production',
      type: 'production',
      status: 'healthy',
      version: '2.3.7',
      uptime: 99.9,
      cpu: 34,
      memory: 56,
      disk: 23,
      requests: 15600,
      errors: 8,
      lastDeployment: new Date(Date.now() - 86400000)
    }
  ];
  res.json({
    environments,
    total: environments.length,
    healthy: environments.filter(e => e.status === 'healthy').length,
    avgUptime: Math.round((environments.reduce((sum, env) => sum + env.uptime, 0) / environments.length) * 10) / 10
  });
});

app.get('/api/deployment/checks', (req, res) => {
  const deploymentChecks = [
    {
      id: 'security-1',
      name: 'Security Scan',
      category: 'security',
      status: 'passed',
      lastRun: new Date(Date.now() - 1800000),
      description: 'Vulnerability assessment and security audit',
      details: 'No critical vulnerabilities found. 2 low-risk issues identified.'
    },
    {
      id: 'performance-1',
      name: 'Load Testing',
      category: 'performance',
      status: 'passed',
      lastRun: new Date(Date.now() - 3600000),
      description: 'Performance and load testing validation',
      details: 'Average response time: 89ms. Peak load: 1000 concurrent users.'
    },
    {
      id: 'database-1',
      name: 'Database Migration',
      category: 'database',
      status: 'passed',
      lastRun: new Date(Date.now() - 7200000),
      description: 'Database schema and migration validation'
    },
    {
      id: 'integration-1',
      name: 'API Integration Tests',
      category: 'integration',
      status: 'warning',
      lastRun: new Date(Date.now() - 1800000),
      description: 'Third-party API integration validation',
      details: 'Payment gateway timeout detected. Retry mechanism working.'
    },
    {
      id: 'compliance-1',
      name: 'Compliance Check',
      category: 'compliance',
      status: 'failed',
      lastRun: new Date(Date.now() - 7200000),
      description: 'PCI-DSS and GDPR compliance validation',
      details: 'Missing data retention policy documentation.'
    }
  ];
  res.json({
    checks: deploymentChecks,
    total: deploymentChecks.length,
    passed: deploymentChecks.filter(c => c.status === 'passed').length,
    warnings: deploymentChecks.filter(c => c.status === 'warning').length,
    failed: deploymentChecks.filter(c => c.status === 'failed').length,
    successRate: Math.round((deploymentChecks.filter(c => c.status === 'passed').length / deploymentChecks.length) * 100)
  });
});

// Phase 3D: AI Customer Behavior Analysis API
app.get('/api/customer-segments', (req, res) => {
  const customerSegments = [
    {
      id: 'vip-customers',
      name: 'VIP Customers',
      description: 'High-value customers with frequent visits and large orders',
      customerCount: 156,
      avgOrderValue: 89.50,
      frequency: 3.2,
      churnRisk: 'low',
      lifetimeValue: 2850.00,
      characteristics: ['High AOV', 'Frequent visits', 'Premium items', 'Low price sensitivity'],
      color: 'bg-purple-500',
      growthRate: 12.5
    },
    {
      id: 'regular-customers',
      name: 'Regular Customers',
      description: 'Consistent customers with moderate spending and regular visits',
      customerCount: 423,
      avgOrderValue: 45.20,
      frequency: 2.1,
      churnRisk: 'low',
      lifetimeValue: 1250.00,
      characteristics: ['Consistent visits', 'Moderate AOV', 'Loyal to favorites', 'Price conscious'],
      color: 'bg-green-500',
      growthRate: 8.3
    },
    {
      id: 'occasional-customers',
      name: 'Occasional Customers',
      description: 'Infrequent visitors with variable spending patterns',
      customerCount: 789,
      avgOrderValue: 32.80,
      frequency: 0.8,
      churnRisk: 'medium',
      lifetimeValue: 420.00,
      characteristics: ['Infrequent visits', 'Variable AOV', 'Event-driven', 'Promotion sensitive'],
      color: 'bg-yellow-500',
      growthRate: -2.1
    },
    {
      id: 'at-risk-customers',
      name: 'At-Risk Customers',
      description: 'Previously active customers showing declining engagement',
      customerCount: 234,
      avgOrderValue: 38.90,
      frequency: 0.3,
      churnRisk: 'high',
      lifetimeValue: 680.00,
      characteristics: ['Declining visits', 'Reduced AOV', 'Long gaps', 'Needs re-engagement'],
      color: 'bg-red-500',
      growthRate: -15.7
    },
    {
      id: 'new-customers',
      name: 'New Customers',
      description: 'Recent customers in their first 30 days',
      customerCount: 167,
      avgOrderValue: 28.60,
      frequency: 1.4,
      churnRisk: 'medium',
      lifetimeValue: 180.00,
      characteristics: ['Recent signup', 'Exploring menu', 'First impressions', 'Conversion critical'],
      color: 'bg-blue-500',
      growthRate: 25.8
    }
  ];

  const totalCustomers = customerSegments.reduce((sum, segment) => sum + segment.customerCount, 0);
  const avgLifetimeValue = Math.round(customerSegments.reduce((sum, segment) => sum + (segment.lifetimeValue * segment.customerCount), 0) / totalCustomers);
  const atRiskCustomers = customerSegments.find(s => s.id === 'at-risk-customers')?.customerCount || 0;

  res.json({
    segments: customerSegments,
    total: customerSegments.length,
    totalCustomers,
    avgLifetimeValue,
    atRiskCustomers,
    vipCustomers: customerSegments.find(s => s.id === 'vip-customers')?.customerCount || 0
  });
});

app.get('/api/behavior-insights', (req, res) => {
  const behaviorInsights = [
    {
      id: 'insight-1',
      type: 'opportunity',
      title: 'Weekend Dinner Upselling Opportunity',
      description: 'VIP customers show 34% higher willingness to order premium items on weekend evenings',
      confidence: 92.4,
      impact: 'high',
      affectedCustomers: 156,
      potentialValue: 2840.00,
      actionRecommendation: 'Implement targeted premium menu recommendations for VIP customers on weekends',
      timestamp: new Date(Date.now() - 1800000)
    },
    {
      id: 'insight-2',
      type: 'risk',
      title: 'Churn Risk Pattern Detected',
      description: 'Regular customers with 3+ week gaps between visits have 67% churn probability',
      confidence: 89.7,
      impact: 'high',
      affectedCustomers: 89,
      potentialValue: -5670.00,
      actionRecommendation: 'Send personalized re-engagement offers to customers with 2+ week gaps',
      timestamp: new Date(Date.now() - 3600000)
    },
    {
      id: 'insight-3',
      type: 'pattern',
      title: 'Lunch Rush Behavior Shift',
      description: 'Office workers now prefer quick-service items during 12-1 PM peak hours',
      confidence: 94.1,
      impact: 'medium',
      affectedCustomers: 312,
      potentialValue: 1890.00,
      actionRecommendation: 'Optimize lunch menu for speed and introduce express ordering options',
      timestamp: new Date(Date.now() - 7200000)
    },
    {
      id: 'insight-4',
      type: 'anomaly',
      title: 'Unusual Ordering Pattern',
      description: 'Significant increase in vegetarian orders among 25-35 age group (+45% this month)',
      confidence: 87.3,
      impact: 'medium',
      affectedCustomers: 198,
      potentialValue: 1240.00,
      actionRecommendation: 'Expand vegetarian menu options and promote plant-based alternatives',
      timestamp: new Date(Date.now() - 10800000)
    }
  ];

  res.json({
    insights: behaviorInsights,
    total: behaviorInsights.length,
    opportunities: behaviorInsights.filter(i => i.type === 'opportunity').length,
    risks: behaviorInsights.filter(i => i.type === 'risk').length,
    patterns: behaviorInsights.filter(i => i.type === 'pattern').length,
    anomalies: behaviorInsights.filter(i => i.type === 'anomaly').length,
    avgConfidence: Math.round(behaviorInsights.reduce((sum, insight) => sum + insight.confidence, 0) / behaviorInsights.length * 10) / 10
  });
});

app.get('/api/customer-profiles', (req, res) => {
  const customerProfiles = [
    {
      id: 'cust-001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      segment: 'VIP Customers',
      totalOrders: 47,
      totalSpent: 2890.50,
      avgOrderValue: 61.50,
      lastVisit: new Date(Date.now() - 172800000),
      churnProbability: 8.2,
      lifetimeValue: 3450.00,
      preferredItems: ['Truffle Pasta', 'Premium Wine', 'Chocolate Dessert'],
      visitPattern: 'Weekend evenings',
      satisfactionScore: 4.8,
      loyaltyTier: 'platinum'
    },
    {
      id: 'cust-002',
      name: 'Mike Chen',
      email: '<EMAIL>',
      segment: 'Regular Customers',
      totalOrders: 23,
      totalSpent: 1240.80,
      avgOrderValue: 54.00,
      lastVisit: new Date(Date.now() - 432000000),
      churnProbability: 15.7,
      lifetimeValue: 1890.00,
      preferredItems: ['Burger', 'Craft Beer', 'Fries'],
      visitPattern: 'Weekday lunch',
      satisfactionScore: 4.3,
      loyaltyTier: 'gold'
    },
    {
      id: 'cust-003',
      name: 'Emma Davis',
      email: '<EMAIL>',
      segment: 'At-Risk Customers',
      totalOrders: 12,
      totalSpent: 456.70,
      avgOrderValue: 38.00,
      lastVisit: new Date(Date.now() - 1814400000),
      churnProbability: 73.4,
      lifetimeValue: 680.00,
      preferredItems: ['Salad', 'Smoothie', 'Soup'],
      visitPattern: 'Irregular',
      satisfactionScore: 3.9,
      loyaltyTier: 'silver'
    },
    {
      id: 'cust-004',
      name: 'David Wilson',
      email: '<EMAIL>',
      segment: 'New Customers',
      totalOrders: 3,
      totalSpent: 89.40,
      avgOrderValue: 29.80,
      lastVisit: new Date(Date.now() - 86400000),
      churnProbability: 45.2,
      lifetimeValue: 180.00,
      preferredItems: ['Pizza', 'Soda', 'Garlic Bread'],
      visitPattern: 'Weekend lunch',
      satisfactionScore: 4.1,
      loyaltyTier: 'bronze'
    }
  ];

  res.json({
    profiles: customerProfiles,
    total: customerProfiles.length,
    highValue: customerProfiles.filter(p => p.lifetimeValue > 1000).length,
    atRisk: customerProfiles.filter(p => p.churnProbability > 50).length,
    avgSatisfaction: Math.round(customerProfiles.reduce((sum, profile) => sum + profile.satisfactionScore, 0) / customerProfiles.length * 10) / 10
  });
});

// Phase 3E: Smart Pricing Optimization API
app.get('/api/pricing-rules', (req, res) => {
  const pricingRules = [
    {
      id: 'rule-1',
      name: 'Peak Hour Premium',
      description: 'Increase prices by 10-15% during peak dining hours (6-8 PM)',
      type: 'time_based',
      status: 'active',
      confidence: 94.2,
      impact: 'high',
      itemsAffected: 45,
      revenueImpact: 2840.00,
      lastUpdated: new Date(Date.now() - 1800000),
      conditions: ['Time: 6-8 PM', 'Day: Fri-Sun', 'Occupancy > 80%'],
      adjustmentRange: { min: 10, max: 15 }
    },
    {
      id: 'rule-2',
      name: 'Demand-Based Pricing',
      description: 'Adjust prices based on real-time demand and inventory levels',
      type: 'demand_based',
      status: 'active',
      confidence: 89.7,
      impact: 'high',
      itemsAffected: 67,
      revenueImpact: 3250.00,
      lastUpdated: new Date(Date.now() - 3600000),
      conditions: ['High demand items', 'Low inventory', 'Popular categories'],
      adjustmentRange: { min: 5, max: 20 }
    },
    {
      id: 'rule-3',
      name: 'Competitor Price Matching',
      description: 'Maintain competitive pricing while maximizing profit margins',
      type: 'competitor_based',
      status: 'testing',
      confidence: 87.3,
      impact: 'medium',
      itemsAffected: 23,
      revenueImpact: 1890.00,
      lastUpdated: new Date(Date.now() - 7200000),
      conditions: ['Competitor price < current', 'Margin > 25%', 'Popular items'],
      adjustmentRange: { min: -10, max: 5 }
    },
    {
      id: 'rule-4',
      name: 'Slow-Moving Inventory',
      description: 'Reduce prices for items with low turnover to increase sales velocity',
      type: 'inventory_based',
      status: 'active',
      confidence: 91.8,
      impact: 'medium',
      itemsAffected: 34,
      revenueImpact: -890.00,
      lastUpdated: new Date(Date.now() - 10800000),
      conditions: ['Low turnover', 'High inventory', 'Expiration risk'],
      adjustmentRange: { min: -20, max: -5 }
    },
    {
      id: 'rule-5',
      name: 'VIP Customer Pricing',
      description: 'Special pricing tiers for high-value customers and loyalty members',
      type: 'customer_based',
      status: 'inactive',
      confidence: 85.6,
      impact: 'low',
      itemsAffected: 12,
      revenueImpact: 450.00,
      lastUpdated: new Date(Date.now() - 14400000),
      conditions: ['VIP customers', 'Loyalty tier: Gold+', 'Order value > $50'],
      adjustmentRange: { min: -15, max: -5 }
    }
  ];

  res.json({
    rules: pricingRules,
    total: pricingRules.length,
    active: pricingRules.filter(r => r.status === 'active').length,
    testing: pricingRules.filter(r => r.status === 'testing').length,
    inactive: pricingRules.filter(r => r.status === 'inactive').length,
    totalRevenueImpact: pricingRules.reduce((sum, rule) => sum + rule.revenueImpact, 0)
  });
});

app.get('/api/price-optimizations', (req, res) => {
  const priceOptimizations = [
    {
      id: 'opt-1',
      itemName: 'Truffle Pasta',
      category: 'Main Course',
      currentPrice: 28.00,
      suggestedPrice: 32.00,
      priceChange: 4.00,
      priceChangePercent: 14.3,
      reason: 'High demand, low competitor pricing, premium positioning opportunity',
      confidence: 94.5,
      expectedImpact: {
        revenue: 1250.00,
        demand: -8.5,
        profit: 890.00
      },
      demandForecast: 85,
      competitorPrice: 35.00,
      lastSold: new Date(Date.now() - 3600000),
      popularity: 92,
      profitMargin: 68.5
    },
    {
      id: 'opt-2',
      itemName: 'Craft Beer Selection',
      category: 'Beverages',
      currentPrice: 8.50,
      suggestedPrice: 9.25,
      priceChange: 0.75,
      priceChangePercent: 8.8,
      reason: 'Peak hour demand surge, limited inventory, high customer willingness to pay',
      confidence: 89.2,
      expectedImpact: {
        revenue: 680.00,
        demand: -5.2,
        profit: 520.00
      },
      demandForecast: 78,
      competitorPrice: 9.50,
      lastSold: new Date(Date.now() - 1800000),
      popularity: 87,
      profitMargin: 72.3
    },
    {
      id: 'opt-3',
      itemName: 'Caesar Salad',
      category: 'Appetizers',
      currentPrice: 14.00,
      suggestedPrice: 12.50,
      priceChange: -1.50,
      priceChangePercent: -10.7,
      reason: 'Low demand, high inventory, competitor underpricing, boost sales velocity',
      confidence: 87.8,
      expectedImpact: {
        revenue: -340.00,
        demand: 15.8,
        profit: -180.00
      },
      demandForecast: 45,
      competitorPrice: 11.00,
      lastSold: new Date(Date.now() - 86400000),
      popularity: 34,
      profitMargin: 45.2
    },
    {
      id: 'opt-4',
      itemName: 'Chocolate Dessert',
      category: 'Desserts',
      currentPrice: 12.00,
      suggestedPrice: 13.50,
      priceChange: 1.50,
      priceChangePercent: 12.5,
      reason: 'High customer satisfaction, unique offering, premium dessert positioning',
      confidence: 91.3,
      expectedImpact: {
        revenue: 420.00,
        demand: -6.8,
        profit: 350.00
      },
      demandForecast: 62,
      competitorPrice: 15.00,
      lastSold: new Date(Date.now() - 7200000),
      popularity: 76,
      profitMargin: 58.9
    },
    {
      id: 'opt-5',
      itemName: 'House Wine',
      category: 'Beverages',
      currentPrice: 24.00,
      suggestedPrice: 26.50,
      priceChange: 2.50,
      priceChangePercent: 10.4,
      reason: 'Weekend premium, high-margin item, customer price insensitivity',
      confidence: 88.7,
      expectedImpact: {
        revenue: 890.00,
        demand: -4.2,
        profit: 720.00
      },
      demandForecast: 71,
      competitorPrice: 28.00,
      lastSold: new Date(Date.now() - 5400000),
      popularity: 81,
      profitMargin: 75.8
    }
  ];

  res.json({
    optimizations: priceOptimizations,
    total: priceOptimizations.length,
    increases: priceOptimizations.filter(o => o.priceChange > 0).length,
    decreases: priceOptimizations.filter(o => o.priceChange < 0).length,
    totalRevenueImpact: priceOptimizations.reduce((sum, opt) => sum + opt.expectedImpact.revenue, 0),
    avgConfidence: Math.round(priceOptimizations.reduce((sum, opt) => sum + opt.confidence, 0) / priceOptimizations.length * 10) / 10
  });
});

app.get('/api/pricing-analytics', (req, res) => {
  const analytics = {
    totalRevenue: 284750.00,
    revenueIncrease: 18.7,
    avgPriceOptimization: 8.3,
    itemsOptimized: 181,
    activePricingRules: 3,
    demandElasticity: -0.85,
    competitorGap: 12.4,
    profitMarginImprovement: 15.2
  };

  res.json(analytics);
});

// Phase 3F: Automated Staff Scheduling API
app.get('/api/staff-members', (req, res) => {
  const staffMembers = [
    {
      id: 'staff-1',
      name: 'Sarah Johnson',
      role: 'manager',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 25.00,
      availability: {
        monday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        tuesday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        wednesday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        thursday: { available: true, startTime: '09:00', endTime: '18:00', preferences: ['morning', 'afternoon'] },
        friday: { available: true, startTime: '09:00', endTime: '22:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['evening'] },
        sunday: { available: false }
      },
      skills: ['leadership', 'customer_service', 'inventory_management', 'training'],
      performance: {
        rating: 4.8,
        efficiency: 94.2,
        customerSatisfaction: 96.5,
        punctuality: 98.1
      },
      totalHours: 45,
      status: 'active',
      lastWorked: new Date(Date.now() - 86400000)
    },
    {
      id: 'staff-2',
      name: 'Mike Rodriguez',
      role: 'chef',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 22.00,
      availability: {
        monday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        tuesday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        wednesday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        thursday: { available: true, startTime: '10:00', endTime: '22:00', preferences: ['lunch', 'dinner'] },
        friday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['dinner'] },
        saturday: { available: true, startTime: '10:00', endTime: '23:00', preferences: ['dinner'] },
        sunday: { available: true, startTime: '11:00', endTime: '21:00', preferences: ['brunch', 'dinner'] }
      },
      skills: ['cooking', 'food_prep', 'kitchen_management', 'menu_development'],
      performance: {
        rating: 4.6,
        efficiency: 91.8,
        customerSatisfaction: 94.2,
        punctuality: 95.7
      },
      totalHours: 42,
      status: 'active',
      lastWorked: new Date(Date.now() - 172800000)
    },
    {
      id: 'staff-3',
      name: 'Emma Thompson',
      role: 'server',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 15.00,
      availability: {
        monday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        tuesday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        wednesday: { available: false },
        thursday: { available: true, startTime: '11:00', endTime: '20:00', preferences: ['lunch', 'afternoon'] },
        friday: { available: true, startTime: '17:00', endTime: '23:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '17:00', endTime: '23:00', preferences: ['evening'] },
        sunday: { available: true, startTime: '10:00', endTime: '16:00', preferences: ['brunch'] }
      },
      skills: ['customer_service', 'order_taking', 'upselling', 'wine_knowledge'],
      performance: {
        rating: 4.4,
        efficiency: 88.5,
        customerSatisfaction: 92.8,
        punctuality: 94.3
      },
      totalHours: 35,
      status: 'active',
      lastWorked: new Date(Date.now() - 259200000)
    },
    {
      id: 'staff-4',
      name: 'David Chen',
      role: 'bartender',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 18.00,
      availability: {
        monday: { available: false },
        tuesday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        wednesday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        thursday: { available: true, startTime: '16:00', endTime: '24:00', preferences: ['evening', 'night'] },
        friday: { available: true, startTime: '16:00', endTime: '02:00', preferences: ['evening', 'night'] },
        saturday: { available: true, startTime: '16:00', endTime: '02:00', preferences: ['evening', 'night'] },
        sunday: { available: true, startTime: '14:00', endTime: '22:00', preferences: ['afternoon', 'evening'] }
      },
      skills: ['mixology', 'customer_service', 'inventory_management', 'cash_handling'],
      performance: {
        rating: 4.7,
        efficiency: 92.3,
        customerSatisfaction: 95.1,
        punctuality: 96.8
      },
      totalHours: 38,
      status: 'active',
      lastWorked: new Date(Date.now() - 345600000)
    },
    {
      id: 'staff-5',
      name: 'Lisa Park',
      role: 'host',
      email: '<EMAIL>',
      phone: '+****************',
      hourlyRate: 14.00,
      availability: {
        monday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        tuesday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        wednesday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        thursday: { available: true, startTime: '11:00', endTime: '19:00', preferences: ['lunch', 'afternoon'] },
        friday: { available: true, startTime: '17:00', endTime: '22:00', preferences: ['evening'] },
        saturday: { available: true, startTime: '17:00', endTime: '22:00', preferences: ['evening'] },
        sunday: { available: true, startTime: '10:00', endTime: '15:00', preferences: ['brunch'] }
      },
      skills: ['customer_service', 'reservation_management', 'phone_etiquette', 'organization'],
      performance: {
        rating: 4.5,
        efficiency: 89.7,
        customerSatisfaction: 93.4,
        punctuality: 97.2
      },
      totalHours: 32,
      status: 'active',
      lastWorked: new Date(Date.now() - 432000000)
    }
  ];

  res.json({
    staff: staffMembers,
    total: staffMembers.length,
    active: staffMembers.filter(s => s.status === 'active').length,
    totalHours: staffMembers.reduce((sum, staff) => sum + staff.totalHours, 0),
    avgRating: Math.round(staffMembers.reduce((sum, staff) => sum + staff.performance.rating, 0) / staffMembers.length * 10) / 10
  });
});

app.get('/api/staff-schedule', (req, res) => {
  const schedule = [
    {
      id: 'shift-1',
      staffId: 'staff-1',
      staffName: 'Sarah Johnson',
      role: 'manager',
      date: new Date(),
      startTime: '09:00',
      endTime: '18:00',
      duration: 9,
      position: 'Floor Manager',
      status: 'scheduled',
      estimatedRevenue: 2840.00,
      aiGenerated: true,
      confidence: 94.2
    },
    {
      id: 'shift-2',
      staffId: 'staff-2',
      staffName: 'Mike Rodriguez',
      role: 'chef',
      date: new Date(),
      startTime: '10:00',
      endTime: '22:00',
      duration: 12,
      position: 'Head Chef',
      status: 'confirmed',
      estimatedRevenue: 3250.00,
      aiGenerated: true,
      confidence: 91.8
    },
    {
      id: 'shift-3',
      staffId: 'staff-3',
      staffName: 'Emma Thompson',
      role: 'server',
      date: new Date(),
      startTime: '11:00',
      endTime: '20:00',
      duration: 9,
      position: 'Dining Room Server',
      status: 'scheduled',
      estimatedRevenue: 1890.00,
      aiGenerated: true,
      confidence: 88.5
    },
    {
      id: 'shift-4',
      staffId: 'staff-4',
      staffName: 'David Chen',
      role: 'bartender',
      date: new Date(),
      startTime: '16:00',
      endTime: '24:00',
      duration: 8,
      position: 'Main Bar',
      status: 'confirmed',
      estimatedRevenue: 1650.00,
      aiGenerated: true,
      confidence: 92.3
    },
    {
      id: 'shift-5',
      staffId: 'staff-5',
      staffName: 'Lisa Park',
      role: 'host',
      date: new Date(),
      startTime: '11:00',
      endTime: '19:00',
      duration: 8,
      position: 'Front Desk',
      status: 'scheduled',
      estimatedRevenue: 980.00,
      aiGenerated: true,
      confidence: 89.7
    }
  ];

  res.json({
    schedule: schedule,
    total: schedule.length,
    totalHours: schedule.reduce((sum, shift) => sum + shift.duration, 0),
    totalRevenue: schedule.reduce((sum, shift) => sum + shift.estimatedRevenue, 0),
    aiGenerated: schedule.filter(s => s.aiGenerated).length,
    avgConfidence: Math.round(schedule.reduce((sum, shift) => sum + shift.confidence, 0) / schedule.length * 10) / 10
  });
});

app.get('/api/workload-predictions', (req, res) => {
  const predictions = [
    {
      date: new Date(),
      dayOfWeek: 'Today',
      expectedCustomers: 245,
      expectedRevenue: 8940.00,
      peakHours: ['12:00-14:00', '18:00-21:00'],
      requiredStaff: {
        managers: 1,
        servers: 4,
        chefs: 2,
        bartenders: 2,
        hosts: 1,
        cashiers: 1
      },
      confidence: 94.2,
      factors: ['Weekend traffic', 'Local events', 'Weather forecast', 'Historical data'],
      recommendations: [
        'Schedule additional server for lunch rush',
        'Ensure bartender coverage for evening peak',
        'Consider prep chef for kitchen support'
      ]
    },
    {
      date: new Date(Date.now() + 86400000),
      dayOfWeek: 'Tomorrow',
      expectedCustomers: 189,
      expectedRevenue: 6780.00,
      peakHours: ['12:00-14:00', '19:00-21:00'],
      requiredStaff: {
        managers: 1,
        servers: 3,
        chefs: 2,
        bartenders: 1,
        hosts: 1,
        cashiers: 1
      },
      confidence: 91.7,
      factors: ['Weekday pattern', 'Regular customer base', 'No special events'],
      recommendations: [
        'Standard weekday staffing sufficient',
        'Monitor lunch rush for potential overflow',
        'Single bartender adequate for expected volume'
      ]
    }
  ];

  res.json({
    predictions: predictions,
    total: predictions.length,
    avgConfidence: Math.round(predictions.reduce((sum, pred) => sum + pred.confidence, 0) / predictions.length * 10) / 10,
    totalExpectedRevenue: predictions.reduce((sum, pred) => sum + pred.expectedRevenue, 0),
    totalExpectedCustomers: predictions.reduce((sum, pred) => sum + pred.expectedCustomers, 0)
  });
});

app.get('/api/scheduling-analytics', (req, res) => {
  const analytics = {
    totalStaff: 5,
    activeStaff: 5,
    scheduledHours: 192,
    laborCost: 3840.00,
    efficiency: 92.4,
    coverage: 98.7,
    aiOptimization: 15.3,
    costSavings: 580.00
  };

  res.json(analytics);
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
let server;

if (require.main === module) {
  // Start server if run directly
  server = app.listen(port, () => {
    console.log(`🚀 Server running on port ${port}`);
  });
} else {
  // Export app for testing
  server = app.listen(port, () => {
    console.log(`🚀 Test server running on port ${port}`);
  });
}

module.exports = { app, server };
