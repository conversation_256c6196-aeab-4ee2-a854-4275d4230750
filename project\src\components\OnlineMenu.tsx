import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { Category } from '../types';
import { Beer, Wine, FileLock as Cocktail, GlassWater, Utensils } from 'lucide-react';

const OnlineMenu: React.FC = () => {
  const { state, fetchProducts, fetchCategories } = useAppContext();
  const [selectedCategory, setSelectedCategory] = useState<Category>('beer');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        await Promise.all([fetchProducts(), fetchCategories()]);
      } catch (error) {
        console.error('Failed to load menu data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, [fetchProducts, fetchCategories]);

  const getCategoryIcon = (category: Category) => {
    switch(category) {
      case 'beer':
        return <Beer className="h-5 w-5" />;
      case 'wine':
        return <Wine className="h-5 w-5" />;
      case 'cocktails':
        return <Cocktail className="h-5 w-5" />;
      case 'spirits':
        return <Wine className="h-5 w-5" />;
      case 'non-alcoholic':
        return <GlassWater className="h-5 w-5" />;
      case 'food':
        return <Utensils className="h-5 w-5" />;
      default:
        return <Beer className="h-5 w-5" />;
    }
  };

  const getCategoryName = (category: Category): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  };

  const filteredProducts = state.products.filter(product => 
    product.category.toLowerCase() === selectedCategory.toLowerCase() && product.inStock
  );

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="flex flex-col items-center space-y-4">
            <svg className="animate-spin h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
            <p className="text-gray-400">Loading menu...</p>
          </div>
        </div>
      );
    }

    return (
      <>
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Our Menu</h1>
          <p className="text-gray-400">Explore our selection of drinks and food</p>
        </header>

        <div className="max-w-4xl mx-auto">
          {/* Category tabs */}
          <div className="flex overflow-x-auto bg-gray-800 p-2 space-x-2 mb-8 rounded-md scrollbar-thin scrollbar-thumb-gray-700">
            {state.categories.map(category => (
              <button
                key={category}
                className={`flex items-center space-x-2 py-2 px-4 rounded-md transition-colors whitespace-nowrap ${
                  selectedCategory === category
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
                onClick={() => setSelectedCategory(category)}
              >
                {getCategoryIcon(category)}
                <span>{getCategoryName(category)}</span>
              </button>
            ))}
          </div>

          {/* Products grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredProducts.length === 0 ? (
              <div className="col-span-full flex items-center justify-center py-8 text-gray-400">
                No items available in this category
              </div>
            ) : (
              filteredProducts.map(product => (
                <div key={product.id} className="bg-gray-800 rounded-lg p-4">
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-gray-400 mb-3">{product.description}</p>
                  <p className="text-2xl font-bold text-amber-400">${product.price.toFixed(2)}</p>
                </div>
              ))
            )}
          </div>
        </div>
      </>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      {renderContent()}
    </div>
  );
};

export default OnlineMenu;
