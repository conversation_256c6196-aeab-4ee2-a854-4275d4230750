import { Socket } from 'socket.io-client';

// Core entity types
export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string | null;
  description?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
  product_count?: number;
  sort_order?: number;
  created_at?: string;
  updated_at?: string;
}

export interface Employee {
  id: string;
  name: string;
  role: string;
  pin?: string;
  permissions?: string[];
  tenant_id?: string;
  location_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  business_name?: string;
  business_address?: string;
  business_phone?: string;
  business_email?: string;
  features?: Record<string, boolean>;
  settings?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface Location {
  id: string;
  name: string;
  address?: string;
  tenant_id: string;
  settings?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  modifiers?: any[];
}

export interface Order {
  id: string;
  items: OrderItem[];
  timestamp: number;
  status: 'open' | 'completed' | 'cancelled';
  total: number;
  subtotal: number;
  tax: number;
  tenant_id?: string;
  location_id?: string;
  employee_id?: string;
  customer_id?: string;
  table_id?: string;
  tabName?: string;
  payment_method?: string;
  created_at?: string;
  updated_at?: string;
}

export interface KitchenOrder {
  id: string;
  order_id: string;
  items: OrderItem[];
  status: 'pending' | 'preparing' | 'ready' | 'served';
  priority: 'low' | 'normal' | 'high';
  estimated_time?: number;
  actual_time?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Table {
  id: string;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'needs-cleaning' | 'out-of-order';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  section?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  current_order_id?: string;
  reservation_id?: string;
  name?: string;
  shape?: 'rectangle' | 'circle' | 'oval';
  tableType?: 'regular' | 'bar' | 'booth' | 'private' | 'outdoor';
  rotation?: number;
  guestCount?: number;
  orderTotal?: number;
  orderItems?: number;
}

export interface FloorLayout {
  id: string;
  name: string;
  tables: Table[];
  sections?: string[];
  width: number;
  height: number;
  background_image?: string;
}

export interface SystemConfig {
  tax_rate: number;
  receipt_header: string;
  receipt_footer: string;
  business_name: string;
  business_address: string;
  business_phone: string;
  theme_primary_color: string;
  theme_secondary_color: string;
}

export interface KitchenSettings {
  audioEnabled: boolean;
  audioVolume: number;
  autoRefresh: boolean;
  refreshInterval: number;
  showMetrics: boolean;
  compactView: boolean;
  maxOrdersPerColumn: number;
  overdueThreshold: number;
  priorityColors: {
    low: string;
    normal: string;
    high: string;
  };
}

export interface KitchenMetrics {
  totalOrders: number;
  pendingOrders: number;
  averageTime: number;
  overdueOrders: number;
  completedToday: number;
}

// App state interface
export interface AppState {
  // Core data
  products: Product[];
  orders: Order[];
  currentOrder: Order | null;
  employees: Employee[];
  currentEmployee: Employee | null;
  isAuthenticated: boolean;
  categories: Category[];
  systemConfig: SystemConfig;
  
  // Floor layout
  floorLayout: FloorLayout | null;
  tables: Table[];
  selectedTable: Table | null;
  
  // Kitchen
  kitchenOrders: KitchenOrder[];
  kitchenMetrics: KitchenMetrics | null;
  kitchenSettings: KitchenSettings;
  kitchenStaff: Employee[];
  
  // Multi-tenancy
  currentTenant: Tenant | null;
  currentLocation: Location | null;
  locations: Location[];
  tenantSettings: any;
  authToken: string | null;
  
  // Real-time
  socket: Socket | null;
  realTimeUpdates: boolean;
  
  // Additional features
  schedules: any[];
  customers: any[];
  loyaltyRewards: any[];
}

// Action types
export type AppAction =
  | { type: 'SET_AUTH_TOKEN'; payload: string | null }
  | { type: 'SET_CURRENT_TENANT'; payload: Tenant | null }
  | { type: 'SET_CURRENT_LOCATION'; payload: Location | null }
  | { type: 'SET_LOCATIONS'; payload: Location[] }
  | { type: 'SET_TENANT_SETTINGS'; payload: any }
  | { type: 'SET_SOCKET'; payload: Socket | null }
  | { type: 'LOGIN'; payload: { employee: Employee; tenant: Tenant; location?: Location; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'SET_EMPLOYEES'; payload: Employee[] }
  | { type: 'ADD_EMPLOYEE'; payload: Employee }
  | { type: 'UPDATE_EMPLOYEE'; payload: Employee }
  | { type: 'DELETE_EMPLOYEE'; payload: string }
  | { type: 'SET_ORDERS'; payload: Order[] }
  | { type: 'SET_KITCHEN_ORDERS'; payload: KitchenOrder[] }
  | { type: 'ADD_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'UPDATE_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'SET_KITCHEN_METRICS'; payload: KitchenMetrics }
  | { type: 'UPDATE_KITCHEN_SETTINGS'; payload: Partial<KitchenSettings> }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'ADD_CATEGORY'; payload: Category }
  | { type: 'UPDATE_SYSTEM_CONFIG'; payload: Partial<SystemConfig> }
  | { type: 'ADD_PRODUCT_TO_ORDER'; payload: Product }
  | { type: 'REMOVE_ITEM_FROM_ORDER'; payload: string }
  | { type: 'UPDATE_ITEM_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CURRENT_ORDER' }
  | { type: 'SET_CURRENT_ORDER'; payload: Order | null }
  | { type: 'SET_TAB_NAME'; payload: string }
  | { type: 'SELECT_TABLE'; payload: { tableId: string; tableNumber: string; guestCount?: number } }
  | { type: 'CLEAR_TABLE_SELECTION' }
  | { type: 'SET_FLOOR_LAYOUT'; payload: any }
  | { type: 'UPDATE_TABLE_STATUS'; payload: { tableId: string; status: Table['status']; substatus?: Table['substatus'] } }
  | { type: 'ADD_ORDER_TO_HISTORY'; payload: Order };

// Context type
export interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}
