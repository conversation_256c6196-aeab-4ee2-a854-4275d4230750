// Database Initialization Script for Super Admin Dashboard
// Creates required tables and sample data for BARPOS database

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

async function initializeDatabase() {
  console.log('🚀 INITIALIZING SUPER ADMIN DASHBOARD DATABASE');
  console.log('==============================================');
  
  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL database');
    
    // Create users table
    console.log('\n📋 Creating users table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255),
          role VARCHAR(50) DEFAULT 'employee' CHECK (role IN ('super_admin', 'tenant_admin', 'manager', 'employee')),
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          last_login TIMESTAMP,
          permissions TEXT[] DEFAULT ARRAY['pos_access'],
          settings JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ Users table created');
    
    // Create transactions table
    console.log('\n💰 Creating transactions table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS transactions (
          id SERIAL PRIMARY KEY,
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          user_id INTEGER REFERENCES users(id),
          amount DECIMAL(10,2) NOT NULL,
          type VARCHAR(50) DEFAULT 'sale',
          status VARCHAR(20) DEFAULT 'completed',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ Transactions table created');
    
    // Create security_audits table
    console.log('\n🔒 Creating security_audits table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS security_audits (
          id SERIAL PRIMARY KEY,
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          user_id INTEGER REFERENCES users(id),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          event VARCHAR(255) NOT NULL,
          severity VARCHAR(20) DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
          ip_address INET,
          user_agent TEXT,
          details TEXT,
          status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved')),
          metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ Security audits table created');
    
    // Create system_activity table
    console.log('\n📊 Creating system_activity table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS system_activity (
          id SERIAL PRIMARY KEY,
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          user_id INTEGER REFERENCES users(id),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          action VARCHAR(255) NOT NULL,
          type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('success', 'warning', 'error', 'info')),
          details TEXT,
          metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ System activity table created');
    
    // Create system_metrics table
    console.log('\n📈 Creating system_metrics table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS system_metrics (
          id SERIAL PRIMARY KEY,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          metric_name VARCHAR(100) NOT NULL,
          metric_value DECIMAL(15,4),
          metric_unit VARCHAR(20),
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ System metrics table created');
    
    // Create ai_analytics table
    console.log('\n🤖 Creating ai_analytics table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS ai_analytics (
          id SERIAL PRIMARY KEY,
          tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          analysis_type VARCHAR(100) NOT NULL,
          predictions JSONB DEFAULT '{}',
          confidence_score DECIMAL(5,2),
          recommendations JSONB DEFAULT '{}',
          metadata JSONB DEFAULT '{}'
      )
    `);
    console.log('✅ AI analytics table created');
    
    // Create indexes for better performance
    console.log('\n🔍 Creating database indexes...');
    await client.query('CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_tenant_id ON transactions(tenant_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_security_audits_timestamp ON security_audits(timestamp)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_security_audits_severity ON security_audits(severity)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_system_activity_timestamp ON system_activity(timestamp)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp)');
    console.log('✅ Database indexes created');
    
    // Insert sample data
    console.log('\n📝 Inserting sample data...');
    
    // Sample users (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO users (tenant_id, name, email, role, status, permissions) VALUES
      (NULL, 'Super Administrator', '<EMAIL>', 'super_admin', 'active', ARRAY['all']),
      (1, 'John Manager', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'user_management', 'pos_access']),
      (1, 'Sarah Barista', '<EMAIL>', 'employee', 'active', ARRAY['pos_access']),
      (4, 'Mike Owner', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'user_management', 'pos_access']),
      (4, 'Lisa Manager', '<EMAIL>', 'manager', 'active', ARRAY['user_management', 'pos_access']),
      (5, 'Tom Baker', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'pos_access'])
      ON CONFLICT (email) DO NOTHING
    `);
    console.log('✅ Sample users inserted');
    
    // Sample transactions (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO transactions (tenant_id, user_id, amount, type, status) VALUES
      (1, 2, 15.50, 'sale', 'completed'),
      (1, 3, 8.75, 'sale', 'completed'),
      (1, 2, 22.00, 'sale', 'completed'),
      (4, 4, 45.25, 'sale', 'completed'),
      (4, 5, 67.80, 'sale', 'completed'),
      (4, 4, 33.50, 'sale', 'completed'),
      (5, 6, 12.25, 'sale', 'completed'),
      (5, 6, 18.90, 'sale', 'completed')
    `);
    console.log('✅ Sample transactions inserted');
    
    // Sample security audits (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO security_audits (tenant_id, user_id, event, severity, ip_address, details, status) VALUES
      (NULL, 1, 'Super admin login', 'low', '*************', 'Successful super admin authentication', 'resolved'),
      (1, 2, 'Failed login attempt', 'medium', '*************', 'Multiple failed login attempts detected', 'investigating'),
      (4, 4, 'Password changed', 'low', '*************', 'User password successfully updated', 'resolved'),
      (NULL, NULL, 'Suspicious API access', 'high', '*********', 'Unusual API access pattern detected', 'open'),
      (5, 6, 'Account locked', 'medium', '************', 'Account locked due to failed attempts', 'resolved')
    `);
    console.log('✅ Sample security audits inserted');
    
    // Sample system activity (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO system_activity (tenant_id, user_id, action, type, details) VALUES
      (NULL, 1, 'System backup completed', 'success', 'Daily automated backup completed successfully'),
      (1, 2, 'New user created', 'info', 'New employee account created for Demo Restaurant'),
      (4, 4, 'Menu updated', 'info', 'Restaurant menu items updated'),
      (NULL, NULL, 'Database maintenance', 'info', 'Routine database optimization completed'),
      (5, 6, 'Payment processed', 'success', 'Customer payment processed successfully')
    `);
    console.log('✅ Sample system activity inserted');
    
    // Sample system metrics (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO system_metrics (metric_name, metric_value, metric_unit, tenant_id) VALUES
      ('cpu_usage', 45.2, 'percent', NULL),
      ('memory_usage', 67.8, 'percent', NULL),
      ('disk_usage', 78.5, 'percent', NULL),
      ('response_time', 125.3, 'milliseconds', NULL),
      ('active_connections', 15, 'count', NULL),
      ('daily_transactions', 156, 'count', 1),
      ('daily_revenue', 1250.75, 'dollars', 1),
      ('daily_transactions', 89, 'count', 4),
      ('daily_revenue', 2150.50, 'dollars', 4)
    `);
    console.log('✅ Sample system metrics inserted');
    
    // Sample AI analytics (using existing tenant IDs: 1, 4, 5)
    await client.query(`
      INSERT INTO ai_analytics (tenant_id, analysis_type, predictions, confidence_score, recommendations) VALUES
      (1, 'revenue_forecast', '{"next_month": 15000, "growth_rate": 12.5}', 87.5, '{"pricing": "increase_coffee_by_5_percent", "inventory": "stock_more_pastries"}'),
      (4, 'customer_behavior', '{"churn_risk": 8.2, "lifetime_value": 1250}', 92.1, '{"retention": "loyalty_program", "upsell": "premium_menu_items"}'),
      (5, 'inventory_optimization', '{"overstock": 12, "understock": 5}', 89.3, '{"reduce": ["sauce_packets"], "increase": ["pizza_dough"]}')
    `);
    console.log('✅ Sample AI analytics inserted');
    
    // Update timestamps
    await client.query(`UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '1 hour' WHERE id IN (1, 2, 4, 6)`);
    await client.query(`UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '3 hours' WHERE id IN (3, 5)`);
    console.log('✅ User timestamps updated');
    
    client.release();
    
    console.log('\n🎉 DATABASE INITIALIZATION COMPLETED SUCCESSFULLY!');
    console.log('✅ All required tables created');
    console.log('✅ Sample data inserted');
    console.log('✅ Database indexes created');
    console.log('✅ Super Admin Dashboard ready for PostgreSQL integration');
    
  } catch (error) {
    console.error('\n💥 DATABASE INITIALIZATION FAILED!');
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the initialization
initializeDatabase()
  .then(() => {
    console.log('\n🚀 Database is ready for Super Admin Dashboard!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Initialization failed:', error);
    process.exit(1);
  });
