// Phase 3J: Advanced Voice Recognition & Natural Language Processing
// Voice Analytics Dashboard Component

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  MicrophoneIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  SpeakerWaveIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

const VoiceAnalyticsDashboard = ({ className = '' }) => {
  const { t, formatNumber } = useTranslation();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('overview');

  useEffect(() => {
    loadVoiceAnalytics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadVoiceAnalytics, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadVoiceAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/voice/analytics');
      const data = await response.json();
      setAnalytics(data.analytics);
      setError(null);
    } catch (error) {
      console.error('Failed to load voice analytics:', error);
      setError('Failed to load voice analytics');
    } finally {
      setLoading(false);
    }
  };

  const MetricCard = ({ title, value, subtitle, icon: Icon, color = 'blue', trend = null }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400`}>
            {typeof value === 'number' ? formatNumber(value) : value}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/20 rounded-lg`}>
          <Icon className={`w-6 h-6 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
      {trend && (
        <div className="flex items-center mt-2">
          {trend.direction === 'up' ? (
            <TrendingUpIcon className="w-4 h-4 text-green-500 mr-1" />
          ) : (
            <TrendingDownIcon className="w-4 h-4 text-red-500 mr-1" />
          )}
          <span className={`text-xs ${trend.direction === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            {trend.value}
          </span>
        </div>
      )}
    </div>
  );

  const AccuracyChart = ({ data, title }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <div className="space-y-3">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
              {key.replace(/_/g, ' ')}
            </span>
            <div className="flex items-center space-x-2">
              <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${value}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white w-12 text-right">
                {value}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const UsageChart = ({ data, title }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{title}</h3>
      <div className="space-y-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="flex-1">
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {item.command || item.date || `Hour ${item.hour}`}
              </span>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {item.count || item.interactions} interactions
                </span>
                <span className="text-xs text-green-600 dark:text-green-400">
                  {item.accuracy}% accuracy
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const ImprovementSuggestions = ({ suggestions }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {t('voice.improvement_suggestions', 'Improvement Suggestions')}
      </h3>
      <div className="space-y-3">
        {suggestions.map((suggestion, index) => (
          <div key={index} className="border-l-4 border-blue-500 pl-4">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {suggestion.area}
              </h4>
              <span className={`px-2 py-1 text-xs rounded-full ${
                suggestion.priority === 'high' 
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                  : suggestion.priority === 'medium'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                  : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
              }`}>
                {suggestion.priority}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {suggestion.issue}
            </p>
            <p className="text-sm text-blue-600 dark:text-blue-400">
              {suggestion.recommendation}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Expected improvement: {suggestion.estimatedImprovement}
            </p>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <CpuChipIcon className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">
            {t('voice.loading_analytics', 'Loading voice analytics...')}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2 text-red-500">
          <ExclamationTriangleIcon className="w-5 h-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('voice.analytics_dashboard', 'Voice Analytics Dashboard')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('voice.analytics_subtitle', 'Advanced voice recognition performance and insights')}
          </p>
        </div>
        <button
          onClick={loadVoiceAnalytics}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          {t('common.refresh', 'Refresh')}
        </button>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title={t('voice.total_interactions', 'Total Interactions')}
          value={analytics.overview.totalVoiceInteractions}
          subtitle={t('voice.this_week', 'This week')}
          icon={MicrophoneIcon}
          color="blue"
          trend={{ direction: 'up', value: '+15%' }}
        />
        <MetricCard
          title={t('voice.average_accuracy', 'Average Accuracy')}
          value={`${analytics.overview.averageAccuracy}%`}
          subtitle={t('voice.speech_recognition', 'Speech recognition')}
          icon={CheckCircleIcon}
          color="green"
          trend={{ direction: 'up', value: '****%' }}
        />
        <MetricCard
          title={t('voice.response_time', 'Response Time')}
          value={`${analytics.overview.averageResponseTime}ms`}
          subtitle={t('voice.average_processing', 'Average processing')}
          icon={ClockIcon}
          color="purple"
          trend={{ direction: 'down', value: '-12ms' }}
        />
        <MetricCard
          title={t('voice.authentication_rate', 'Auth Success Rate')}
          value={`${Math.round((analytics.overview.successfulAuthentications / (analytics.overview.successfulAuthentications + analytics.overview.failedAuthentications)) * 100)}%`}
          subtitle={t('voice.voice_authentication', 'Voice authentication')}
          icon={ShieldCheckIcon}
          color="emerald"
          trend={{ direction: 'up', value: '+1.2%' }}
        />
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AccuracyChart
          data={analytics.performance.speechRecognitionAccuracy.byLanguage}
          title={t('voice.accuracy_by_language', 'Accuracy by Language')}
        />
        <AccuracyChart
          data={analytics.performance.speechRecognitionAccuracy.byContext}
          title={t('voice.accuracy_by_context', 'Accuracy by Context')}
        />
      </div>

      {/* Intent Recognition */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AccuracyChart
          data={analytics.performance.intentRecognition.byIntent}
          title={t('voice.intent_recognition', 'Intent Recognition Accuracy')}
        />
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('voice.authentication_metrics', 'Voice Authentication Metrics')}
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('voice.accuracy', 'Accuracy')}
              </span>
              <span className="text-lg font-semibold text-green-600">
                {analytics.performance.voiceAuthentication.accuracy}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('voice.false_positive_rate', 'False Positive Rate')}
              </span>
              <span className="text-lg font-semibold text-red-600">
                {analytics.performance.voiceAuthentication.falsePositiveRate}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('voice.false_negative_rate', 'False Negative Rate')}
              </span>
              <span className="text-lg font-semibold text-orange-600">
                {analytics.performance.voiceAuthentication.falseNegativeRate}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('voice.avg_verification_time', 'Avg Verification Time')}
              </span>
              <span className="text-lg font-semibold text-blue-600">
                {analytics.performance.voiceAuthentication.averageVerificationTime}s
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Usage Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <UsageChart
          data={analytics.usage.dailyInteractions.slice(-7)}
          title={t('voice.daily_interactions', 'Daily Interactions')}
        />
        <UsageChart
          data={analytics.usage.peakHours.slice(0, 6)}
          title={t('voice.peak_hours', 'Peak Hours')}
        />
        <UsageChart
          data={analytics.usage.commandFrequency.slice(0, 6)}
          title={t('voice.top_commands', 'Top Commands')}
        />
      </div>

      {/* Improvement Suggestions */}
      <ImprovementSuggestions suggestions={analytics.improvements.suggestions} />

      {/* Trends Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('voice.trends_summary', 'Trends Summary')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <TrendingUpIcon className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {t('voice.accuracy_trend', 'Accuracy Trend')}
              </p>
              <p className="text-xs text-green-600 capitalize">
                {analytics.improvements.trends.accuracyTrend}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <ChartBarIcon className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {t('voice.usage_trend', 'Usage Trend')}
              </p>
              <p className="text-xs text-blue-600 capitalize">
                {analytics.improvements.trends.usageTrend}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <SpeakerWaveIcon className="w-5 h-5 text-purple-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {t('voice.satisfaction_trend', 'Satisfaction Trend')}
              </p>
              <p className="text-xs text-purple-600 capitalize">
                {analytics.improvements.trends.satisfactionTrend}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceAnalyticsDashboard;
