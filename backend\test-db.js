const db = require('./db/pool');
const queries = require('./db/queries');

async function testConnection() {
  try {
    // Test basic query using the shared pool
    console.log('Testing database connection...');
    const result = await db.query(`
      SELECT 
        id,
        name,
        role,
        email
      FROM employees
      LIMIT 5
    `);
    console.log('Successfully connected to database');
    console.log('Sample employees:', result.rows);

    // Test prepared statement query
    console.log('\nTesting prepared statement...');
    const employeeId = 1; // Example ID
    const employee = await queries.getEmployeeById(employeeId);
    if (employee) {
      console.log('Found employee by ID:', employee);
    } else {
      console.log('No employee found with ID:', employeeId);
    }

    // Test caching
    console.log('\nTesting cache (second query should be faster)...');
    console.time('First query');
    await queries.getEmployeeById(employeeId);
    console.timeEnd('First query');

    console.time('Second query (cached)');
    await queries.getEmployeeById(employeeId);
    console.timeEnd('Second query (cached)');

  } catch (error) {
    console.error('Error during database tests:', error);
  }
}

// Run tests and handle cleanup
testConnection()
  .catch(console.error)
  .finally(() => {
    // Note: We don't call db.end() here because the pool is shared
    // The pool should only be ended when the application shuts down
    console.log('Tests completed');
  });
