// Use built-in fetch (Node.js 18+) or polyfill
const fetch = globalThis.fetch || require('node-fetch');

async function testLogin() {
  try {
    console.log('🔐 Testing login with PIN 123456 and tenant barpos-system...');
    
    const response = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '123456',
        tenant_slug: 'barpos-system'
      })
    });

    console.log('📥 Response status:', response.status);
    const data = await response.json();
    console.log('📄 Response data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('🎫 Token:', data.token);
      console.log('👤 Employee:', data.employee.name, '(' + data.employee.role + ')');
      console.log('🏢 Tenant:', data.tenant.name, '(' + data.tenant.slug + ')');
      
      // Test category creation
      await testCategoryCreation(data.token);
    } else {
      console.log('❌ Login failed:', data.error);
    }
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

async function testCategoryCreation(token) {
  try {
    console.log('\n📂 Testing category creation...');
    
    const categoryData = {
      name: 'Test Category',
      description: 'A test category created via API',
      color: '#FF6B6B',
      icon: 'utensils'
    };

    const response = await fetch('http://localhost:4000/api/tenant/categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(categoryData)
    });

    console.log('📥 Category creation response status:', response.status);
    const data = await response.json();
    console.log('📄 Category creation response:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('✅ Category created successfully!');
      console.log('🆔 Category ID:', data.id);
      console.log('📛 Category Name:', data.name);
    } else {
      console.log('❌ Category creation failed:', data.error);
    }
  } catch (error) {
    console.error('💥 Category creation error:', error.message);
  }
}

// Run the test
testLogin();
