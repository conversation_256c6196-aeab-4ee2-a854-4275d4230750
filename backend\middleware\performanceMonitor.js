// Performance Monitoring Middleware
// Tracks API performance, database queries, and system metrics

const { logger } = require('../services/loggingService');

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requests: new Map(),
      database: new Map(),
      memory: [],
      cpu: [],
      errors: []
    };
    
    this.thresholds = {
      slowRequest: 1000, // 1 second
      slowQuery: 500,    // 500ms
      highMemory: 80,    // 80% memory usage
      highCpu: 70        // 70% CPU usage
    };
    
    // Start system monitoring
    this.startSystemMonitoring();
  }

  // Middleware for request performance monitoring
  requestMonitoringMiddleware() {
    return (req, res, next) => {
      const startTime = process.hrtime.bigint();
      const startMemory = process.memoryUsage();
      
      // Add request ID for tracking
      req.requestId = req.requestId || require('crypto').randomUUID();
      
      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = function(...args) {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        const endMemory = process.memoryUsage();
        
        // Calculate memory delta
        const memoryDelta = {
          rss: endMemory.rss - startMemory.rss,
          heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          heapTotal: endMemory.heapTotal - startMemory.heapTotal
        };
        
        // Store metrics
        const requestMetrics = {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration,
          memoryDelta,
          timestamp: new Date(),
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          userId: req.user?.employeeId,
          tenantId: req.user?.tenantId
        };
        
        // Log performance
        logger.logPerformance(
          `${req.method} ${req.url}`,
          duration,
          this.thresholds.slowRequest
        );
        
        // Store in metrics
        this.storeRequestMetrics(requestMetrics);
        
        // Check for performance issues
        this.checkPerformanceThresholds(requestMetrics);
        
        originalEnd.apply(this, args);
      }.bind(this);
      
      next();
    };
  }

  // Store request metrics
  storeRequestMetrics(metrics) {
    const key = `${metrics.method}:${metrics.url}`;
    
    if (!this.metrics.requests.has(key)) {
      this.metrics.requests.set(key, {
        count: 0,
        totalDuration: 0,
        minDuration: Infinity,
        maxDuration: 0,
        avgDuration: 0,
        errors: 0,
        lastAccess: null
      });
    }
    
    const existing = this.metrics.requests.get(key);
    existing.count++;
    existing.totalDuration += metrics.duration;
    existing.minDuration = Math.min(existing.minDuration, metrics.duration);
    existing.maxDuration = Math.max(existing.maxDuration, metrics.duration);
    existing.avgDuration = existing.totalDuration / existing.count;
    existing.lastAccess = metrics.timestamp;
    
    if (metrics.statusCode >= 400) {
      existing.errors++;
    }
    
    // Keep only last 1000 requests to prevent memory leaks
    if (this.metrics.requests.size > 1000) {
      const oldestKey = Array.from(this.metrics.requests.keys())[0];
      this.metrics.requests.delete(oldestKey);
    }
  }

  // Database query monitoring
  monitorDatabaseQuery(query, duration, error = null) {
    const queryKey = this.normalizeQuery(query);
    
    if (!this.metrics.database.has(queryKey)) {
      this.metrics.database.set(queryKey, {
        count: 0,
        totalDuration: 0,
        minDuration: Infinity,
        maxDuration: 0,
        avgDuration: 0,
        errors: 0,
        lastExecuted: null
      });
    }
    
    const existing = this.metrics.database.get(queryKey);
    existing.count++;
    existing.totalDuration += duration;
    existing.minDuration = Math.min(existing.minDuration, duration);
    existing.maxDuration = Math.max(existing.maxDuration, duration);
    existing.avgDuration = existing.totalDuration / existing.count;
    existing.lastExecuted = new Date();
    
    if (error) {
      existing.errors++;
    }
    
    // Log slow queries
    if (duration > this.thresholds.slowQuery) {
      logger.warn(`Slow database query detected`, {
        query: queryKey,
        duration: `${duration}ms`,
        threshold: `${this.thresholds.slowQuery}ms`
      });
    }
    
    // Log database errors
    if (error) {
      logger.logDatabase('query', 'unknown', duration, error);
    }
  }

  // Normalize SQL query for grouping
  normalizeQuery(query) {
    return query
      .replace(/\$\d+/g, '$?')           // Replace parameters
      .replace(/\d+/g, '?')             // Replace numbers
      .replace(/'.+?'/g, "'?'")         // Replace string literals
      .replace(/\s+/g, ' ')             // Normalize whitespace
      .trim()
      .substring(0, 100);               // Limit length
  }

  // System monitoring
  startSystemMonitoring() {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // Every 30 seconds
  }

  // Collect system metrics
  collectSystemMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // Calculate memory percentage
    const totalMemory = require('os').totalmem();
    const memoryPercent = (memoryUsage.rss / totalMemory) * 100;
    
    // Store metrics
    this.metrics.memory.push({
      timestamp: new Date(),
      rss: memoryUsage.rss,
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external,
      percent: memoryPercent
    });
    
    this.metrics.cpu.push({
      timestamp: new Date(),
      user: cpuUsage.user,
      system: cpuUsage.system
    });
    
    // Keep only last 100 entries
    if (this.metrics.memory.length > 100) {
      this.metrics.memory = this.metrics.memory.slice(-100);
    }
    if (this.metrics.cpu.length > 100) {
      this.metrics.cpu = this.metrics.cpu.slice(-100);
    }
    
    // Check thresholds
    if (memoryPercent > this.thresholds.highMemory) {
      logger.warn(`High memory usage detected: ${memoryPercent.toFixed(1)}%`);
    }
  }

  // Check performance thresholds
  checkPerformanceThresholds(metrics) {
    // Slow request threshold
    if (metrics.duration > this.thresholds.slowRequest) {
      logger.warn(`Slow request detected`, {
        url: metrics.url,
        method: metrics.method,
        duration: `${metrics.duration}ms`,
        threshold: `${this.thresholds.slowRequest}ms`
      });
    }
    
    // Error rate threshold
    const key = `${metrics.method}:${metrics.url}`;
    const requestStats = this.metrics.requests.get(key);
    
    if (requestStats && requestStats.count > 10) {
      const errorRate = (requestStats.errors / requestStats.count) * 100;
      if (errorRate > 10) { // 10% error rate
        logger.warn(`High error rate detected`, {
          url: metrics.url,
          method: metrics.method,
          errorRate: `${errorRate.toFixed(1)}%`,
          errors: requestStats.errors,
          total: requestStats.count
        });
      }
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    // Request statistics
    const requestStats = Array.from(this.metrics.requests.entries()).map(([key, stats]) => ({
      endpoint: key,
      ...stats
    })).sort((a, b) => b.avgDuration - a.avgDuration);
    
    // Database statistics
    const dbStats = Array.from(this.metrics.database.entries()).map(([key, stats]) => ({
      query: key,
      ...stats
    })).sort((a, b) => b.avgDuration - a.avgDuration);
    
    // Recent memory usage
    const recentMemory = this.metrics.memory.filter(m => m.timestamp > oneHourAgo);
    const avgMemory = recentMemory.length > 0 
      ? recentMemory.reduce((sum, m) => sum + m.percent, 0) / recentMemory.length 
      : 0;
    
    return {
      requests: {
        total: requestStats.reduce((sum, s) => sum + s.count, 0),
        slowest: requestStats.slice(0, 5),
        errorRate: requestStats.reduce((sum, s) => sum + s.errors, 0) / 
                  Math.max(requestStats.reduce((sum, s) => sum + s.count, 0), 1) * 100
      },
      database: {
        total: dbStats.reduce((sum, s) => sum + s.count, 0),
        slowest: dbStats.slice(0, 5),
        errorRate: dbStats.reduce((sum, s) => sum + s.errors, 0) / 
                  Math.max(dbStats.reduce((sum, s) => sum + s.count, 0), 1) * 100
      },
      system: {
        avgMemoryUsage: avgMemory,
        currentMemoryUsage: this.metrics.memory.length > 0 
          ? this.metrics.memory[this.metrics.memory.length - 1].percent 
          : 0,
        uptime: process.uptime()
      },
      timestamp: now
    };
  }

  // Get detailed metrics for specific endpoint
  getEndpointMetrics(method, url) {
    const key = `${method}:${url}`;
    return this.metrics.requests.get(key) || null;
  }

  // Reset metrics
  resetMetrics() {
    this.metrics.requests.clear();
    this.metrics.database.clear();
    this.metrics.memory = [];
    this.metrics.cpu = [];
    this.metrics.errors = [];
    
    logger.info('Performance metrics reset');
  }

  // Export metrics for external monitoring
  exportMetrics() {
    return {
      requests: Object.fromEntries(this.metrics.requests),
      database: Object.fromEntries(this.metrics.database),
      memory: this.metrics.memory,
      cpu: this.metrics.cpu,
      summary: this.getPerformanceSummary()
    };
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  performanceMonitor,
  PerformanceMonitor
};
