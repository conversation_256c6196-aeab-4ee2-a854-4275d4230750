<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Super Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #dcfce7;
            border: 1px solid #22c55e;
        }
        .status.error {
            background: #fef2f2;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🔍 Super Admin Debug Tool</h1>
    
    <div class="test-section">
        <h2>🌐 Current Page Information</h2>
        <div id="page-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Navigation Tests</h2>
        <button onclick="testSuperAdminPage()">Test Super Admin Page</button>
        <button onclick="testMainPage()">Test Main Page</button>
        <button onclick="testPOSPage()">Test POS Page</button>
        <div id="navigation-status"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication Test</h2>
        <div>
            <label>PIN: </label>
            <input type="text" id="pin-input" value="888888" placeholder="Enter PIN">
            <button onclick="testAuthentication()">Test Authentication</button>
        </div>
        <div id="auth-status"></div>
    </div>

    <div class="test-section">
        <h2>📊 API Endpoints Test</h2>
        <button onclick="testHealthEndpoint()">Test Health</button>
        <button onclick="testAuthEndpoint()">Test Auth Endpoint</button>
        <button onclick="testTenantEndpoint()">Test Tenant Endpoint</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Local Storage Information</h2>
        <button onclick="checkLocalStorage()">Check Local Storage</button>
        <button onclick="clearLocalStorage()">Clear Local Storage</button>
        <div id="storage-status"></div>
    </div>

    <div class="test-section">
        <h2>📋 Console Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="console-logs" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;"></div>
    </div>

    <script>
        // Initialize page
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toISOString();

        // Console log capture
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];

        function captureLog(type, args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = Array.from(args).map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logs.push({ type, timestamp, message });
            updateConsoleLogs();
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            captureLog('log', args);
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            captureLog('error', args);
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            captureLog('warn', args);
        };

        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.innerHTML = logs.slice(-20).map(log => 
                `<div style="color: ${log.type === 'error' ? 'red' : log.type === 'warn' ? 'orange' : 'black'}">
                    [${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}
                </div>`
            ).join('');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            logs.length = 0;
            updateConsoleLogs();
        }

        // Utility functions
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Navigation tests
        function testSuperAdminPage() {
            showStatus('navigation-status', '🔄 Testing Super Admin page...', 'info');
            window.open('http://localhost:5173/super-admin.html', '_blank');
        }

        function testMainPage() {
            showStatus('navigation-status', '🔄 Testing Main page...', 'info');
            window.open('http://localhost:5173/', '_blank');
        }

        function testPOSPage() {
            showStatus('navigation-status', '🔄 Testing POS page...', 'info');
            window.open('http://localhost:5173/unified-pos.html', '_blank');
        }

        // Authentication test
        async function testAuthentication() {
            const pin = document.getElementById('pin-input').value;
            showStatus('auth-status', `🔄 Testing authentication with PIN: ${pin}...`, 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pin: pin })
                });

                const data = await response.json();
                
                if (response.ok) {
                    if (data.employee && data.employee.role === 'super_admin') {
                        showStatus('auth-status', `✅ Super Admin authentication successful!<br>
                            <strong>Name:</strong> ${data.employee.name}<br>
                            <strong>Role:</strong> ${data.employee.role}<br>
                            <strong>Token:</strong> ${data.token ? 'Present' : 'Missing'}`, 'success');
                        
                        // Store token for further tests
                        localStorage.setItem('authToken', data.token);
                        localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
                    } else {
                        showStatus('auth-status', `⚠️ Authentication successful but not super admin!<br>
                            <strong>Role:</strong> ${data.employee?.role || 'unknown'}`, 'error');
                    }
                } else {
                    showStatus('auth-status', `❌ Authentication failed!<br>
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Message:</strong> ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showStatus('auth-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Authentication error:', error);
            }
        }

        // API endpoint tests
        async function testHealthEndpoint() {
            showStatus('api-status', '🔄 Testing health endpoint...', 'info');
            
            try {
                const response = await fetch('/api/admin/health');
                const data = await response.json();
                
                if (response.ok) {
                    showStatus('api-status', `✅ Health endpoint working!<br>
                        <strong>Status:</strong> ${data.status}<br>
                        <strong>Uptime:</strong> ${data.uptime}s`, 'success');
                } else {
                    showStatus('api-status', `❌ Health endpoint failed!<br>
                        <strong>Status:</strong> ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Health endpoint error: ${error.message}`, 'error');
            }
        }

        async function testAuthEndpoint() {
            showStatus('api-status', '🔄 Testing auth endpoint...', 'info');
            
            try {
                const response = await fetch('/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus('api-status', `✅ Auth endpoint working!<br>
                        <strong>Valid:</strong> ${data.valid}`, 'success');
                } else {
                    showStatus('api-status', `❌ Auth endpoint failed!<br>
                        <strong>Status:</strong> ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Auth endpoint error: ${error.message}`, 'error');
            }
        }

        async function testTenantEndpoint() {
            showStatus('api-status', '🔄 Testing tenant endpoint...', 'info');
            
            try {
                const response = await fetch('/api/admin/tenants', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus('api-status', `✅ Tenant endpoint working!<br>
                        <strong>Tenants:</strong> ${Array.isArray(data) ? data.length : 'Unknown'}`, 'success');
                } else {
                    showStatus('api-status', `❌ Tenant endpoint failed!<br>
                        <strong>Status:</strong> ${response.status}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Tenant endpoint error: ${error.message}`, 'error');
            }
        }

        // Local storage functions
        function checkLocalStorage() {
            const authToken = localStorage.getItem('authToken');
            const currentEmployee = localStorage.getItem('currentEmployee');
            const currentTenant = localStorage.getItem('currentTenant');
            
            showStatus('storage-status', `
                <strong>Auth Token:</strong> ${authToken ? 'Present' : 'Missing'}<br>
                <strong>Current Employee:</strong> ${currentEmployee ? 'Present' : 'Missing'}<br>
                <strong>Current Tenant:</strong> ${currentTenant ? 'Present' : 'Missing'}<br>
                ${currentEmployee ? `<br><strong>Employee Data:</strong><pre>${JSON.stringify(JSON.parse(currentEmployee), null, 2)}</pre>` : ''}
            `, authToken ? 'success' : 'error');
        }

        function clearLocalStorage() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentEmployee');
            localStorage.removeItem('currentTenant');
            showStatus('storage-status', '✅ Local storage cleared!', 'success');
        }

        // Auto-run some tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testHealthEndpoint();
                checkLocalStorage();
            }, 1000);
        });
    </script>
</body>
</html>
