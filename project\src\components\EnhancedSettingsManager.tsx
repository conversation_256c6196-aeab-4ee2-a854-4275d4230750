import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Database, 
  Receipt, 
  Bell, 
  Shield, 
  Globe,
  Palette,
  Clock,
  DollarSign,
  Mail,
  Phone,
  MapPin,
  Building,
  Key,
  Lock,
  Eye,
  EyeOff,
  CheckCircle,
  AlertTriangle,
  Download,
  Upload,
  Trash2,
  Copy,
  Edit3,
  Plus,
  Minus
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface SettingsCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
}

interface SettingsAction {
  id: string;
  type: 'save' | 'reset' | 'backup' | 'restore' | 'export' | 'import' | 'test';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresConfirmation?: boolean;
}

export const EnhancedSettingsManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [activeCategory, setActiveCategory] = useState('general');
  const [settings, setSettings] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Settings categories
  const settingsCategories: SettingsCategory[] = [
    {
      id: 'general',
      name: 'General Settings',
      description: 'Basic system configuration',
      icon: Settings,
      color: 'blue'
    },
    {
      id: 'business',
      name: 'Business Information',
      description: 'Restaurant details and branding',
      icon: Building,
      color: 'green'
    },
    {
      id: 'financial',
      name: 'Financial Settings',
      description: 'Tax rates, currency, and pricing',
      icon: DollarSign,
      color: 'yellow'
    },
    {
      id: 'receipts',
      name: 'Receipt Configuration',
      description: 'Receipt templates and printing',
      icon: Receipt,
      color: 'purple'
    },
    {
      id: 'notifications',
      name: 'Notifications',
      description: 'Alerts and communication settings',
      icon: Bell,
      color: 'orange'
    },
    {
      id: 'security',
      name: 'Security & Access',
      description: 'Authentication and permissions',
      icon: Shield,
      color: 'red'
    },
    {
      id: 'integrations',
      name: 'Integrations',
      description: 'Third-party services and APIs',
      icon: Globe,
      color: 'indigo'
    },
    {
      id: 'appearance',
      name: 'Appearance',
      description: 'Themes and interface customization',
      icon: Palette,
      color: 'pink'
    }
  ];

  // Settings actions
  const settingsActions: SettingsAction[] = [
    {
      id: 'save_settings',
      type: 'save',
      label: 'Save Changes',
      icon: Save,
      color: 'blue'
    },
    {
      id: 'reset_settings',
      type: 'reset',
      label: 'Reset to Default',
      icon: RefreshCw,
      color: 'gray',
      requiresConfirmation: true
    },
    {
      id: 'backup_settings',
      type: 'backup',
      label: 'Backup Settings',
      icon: Download,
      color: 'green'
    },
    {
      id: 'restore_settings',
      type: 'restore',
      label: 'Restore Settings',
      icon: Upload,
      color: 'orange',
      requiresConfirmation: true
    },
    {
      id: 'export_config',
      type: 'export',
      label: 'Export Config',
      icon: Copy,
      color: 'purple'
    },
    {
      id: 'test_settings',
      type: 'test',
      label: 'Test Configuration',
      icon: CheckCircle,
      color: 'green'
    }
  ];

  // Load settings
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiCall('/api/settings/all');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        throw new Error('Failed to load settings');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setError('Failed to load settings');
      
      // Mock settings data
      setSettings({
        general: {
          system_name: 'BARPOS Restaurant System',
          timezone: 'America/New_York',
          language: 'en',
          date_format: 'MM/DD/YYYY',
          time_format: '12h',
          auto_backup: true,
          backup_frequency: 'daily'
        },
        business: {
          business_name: 'Bella Vista Restaurant',
          business_address: '123 Main Street, City, State 12345',
          business_phone: '+****************',
          business_email: '<EMAIL>',
          business_website: 'www.bellavista.com',
          logo_url: '',
          business_hours: {
            monday: { open: '09:00', close: '22:00', closed: false },
            tuesday: { open: '09:00', close: '22:00', closed: false },
            wednesday: { open: '09:00', close: '22:00', closed: false },
            thursday: { open: '09:00', close: '22:00', closed: false },
            friday: { open: '09:00', close: '23:00', closed: false },
            saturday: { open: '10:00', close: '23:00', closed: false },
            sunday: { open: '10:00', close: '21:00', closed: false }
          }
        },
        financial: {
          currency: 'USD',
          tax_rate: 0.08,
          service_charge: 0.18,
          auto_gratuity_threshold: 6,
          payment_methods: ['cash', 'card', 'mobile_wallet'],
          rounding_method: 'nearest_cent'
        },
        receipts: {
          header_text: 'Thank you for dining with us!',
          footer_text: 'Visit us again soon!',
          include_logo: true,
          include_qr_code: true,
          print_customer_copy: true,
          print_kitchen_copy: true,
          auto_print: true,
          paper_size: '80mm'
        },
        notifications: {
          email_notifications: true,
          sms_notifications: false,
          push_notifications: true,
          low_stock_alerts: true,
          order_alerts: true,
          payment_alerts: true,
          system_alerts: true,
          alert_email: '<EMAIL>'
        },
        security: {
          session_timeout: 30,
          password_policy: 'medium',
          two_factor_auth: false,
          login_attempts: 5,
          audit_logging: true,
          data_encryption: true
        },
        integrations: {
          payment_gateway: 'stripe',
          email_service: 'sendgrid',
          sms_service: 'twilio',
          analytics_service: 'google',
          backup_service: 'aws_s3'
        },
        appearance: {
          theme: 'light',
          primary_color: '#3B82F6',
          secondary_color: '#10B981',
          font_family: 'Inter',
          compact_mode: false,
          show_animations: true
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleNestedSettingChange = (category: string, parentKey: string, childKey: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [parentKey]: {
          ...prev[category][parentKey],
          [childKey]: value
        }
      }
    }));
    setHasChanges(true);
  };

  const handleSettingsAction = async (action: SettingsAction) => {
    if (action.requiresConfirmation) {
      setShowConfirmDialog(action.id);
      return;
    }

    await executeSettingsAction(action);
  };

  const executeSettingsAction = async (action: SettingsAction) => {
    setActionLoading(action.id);
    setError(null);
    setSuccess(null);

    try {
      switch (action.type) {
        case 'save':
          await handleSaveSettings();
          break;

        case 'reset':
          await handleResetSettings();
          break;

        case 'backup':
          await handleBackupSettings();
          break;

        case 'restore':
          await handleRestoreSettings();
          break;

        case 'export':
          await handleExportConfig();
          break;

        case 'test':
          await handleTestSettings();
          break;

        default:
          console.warn('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      setError(`Failed to ${action.label.toLowerCase()}`);
    } finally {
      setActionLoading(null);
      setShowConfirmDialog(null);
    }
  };

  const handleSaveSettings = async () => {
    const response = await apiCall('/api/settings/update', {
      method: 'POST',
      body: JSON.stringify(settings)
    });

    if (response.ok) {
      setHasChanges(false);
      setSuccess('Settings saved successfully');
    } else {
      throw new Error('Failed to save settings');
    }
  };

  const handleResetSettings = async () => {
    const response = await apiCall('/api/settings/reset', {
      method: 'POST',
      body: JSON.stringify({ category: activeCategory })
    });

    if (response.ok) {
      await loadSettings();
      setHasChanges(false);
      setSuccess('Settings reset to default values');
    } else {
      throw new Error('Failed to reset settings');
    }
  };

  const handleBackupSettings = async () => {
    const response = await apiCall('/api/settings/backup', {
      method: 'POST'
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `settings-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      setSuccess('Settings backup downloaded');
    } else {
      throw new Error('Failed to backup settings');
    }
  };

  const handleRestoreSettings = async () => {
    // This would typically open a file picker
    setSuccess('Settings restore functionality would open file picker');
  };

  const handleExportConfig = async () => {
    const configData = JSON.stringify(settings, null, 2);
    const blob = new Blob([configData], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `config-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    setSuccess('Configuration exported successfully');
  };

  const handleTestSettings = async () => {
    const response = await apiCall('/api/settings/test', {
      method: 'POST',
      body: JSON.stringify({ category: activeCategory, settings: settings[activeCategory] })
    });

    if (response.ok) {
      const result = await response.json();
      setSuccess(`Configuration test completed: ${result.message}`);
    } else {
      throw new Error('Configuration test failed');
    }
  };

  const getCategoryColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'green': return 'bg-green-100 text-green-800 border-green-200';
      case 'yellow': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'purple': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'orange': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'red': return 'bg-red-100 text-red-800 border-red-200';
      case 'indigo': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'pink': return 'bg-pink-100 text-pink-800 border-pink-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getActionButtonClass = (action: SettingsAction) => {
    const baseClass = "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
    
    switch (action.color) {
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'gray': return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading settings...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">System Settings</h2>
          <p className="text-gray-600">Configure system preferences and behavior</p>
        </div>
        <div className="flex items-center space-x-3">
          {hasChanges && (
            <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
              Unsaved Changes
            </span>
          )}
          <button
            onClick={loadSettings}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-1/4 border-r border-gray-200 p-4">
          <div className="space-y-2">
            {settingsCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`w-full text-left p-3 rounded-lg transition-all ${
                  activeCategory === category.id
                    ? getCategoryColor(category.color)
                    : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <category.icon className="h-5 w-5" />
                  <div>
                    <div className="font-medium">{category.name}</div>
                    <div className="text-sm text-gray-500">{category.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 mb-6">
            {settingsActions.map((action) => (
              <button
                key={action.id}
                onClick={() => handleSettingsAction(action)}
                disabled={actionLoading === action.id || (action.type === 'save' && !hasChanges)}
                className={getActionButtonClass(action)}
              >
                {actionLoading === action.id ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <action.icon className="h-4 w-4" />
                )}
                <span>{action.label}</span>
              </button>
            ))}
          </div>

          {/* Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                <span className="text-red-800">{error}</span>
              </div>
              <button
                onClick={() => setError(null)}
                className="mt-2 text-red-600 hover:text-red-800 font-medium text-sm"
              >
                Dismiss
              </button>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-green-800">{success}</span>
              </div>
              <button
                onClick={() => setSuccess(null)}
                className="mt-2 text-green-600 hover:text-green-800 font-medium text-sm"
              >
                Dismiss
              </button>
            </div>
          )}

          {/* Settings Content */}
          <div className="space-y-6">
            {activeCategory === 'general' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">General Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      System Name
                    </label>
                    <input
                      type="text"
                      value={settings.general?.system_name || ''}
                      onChange={(e) => handleSettingChange('general', 'system_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Timezone
                    </label>
                    <select
                      value={settings.general?.timezone || ''}
                      onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Language
                    </label>
                    <select
                      value={settings.general?.language || ''}
                      onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Date Format
                    </label>
                    <select
                      value={settings.general?.date_format || ''}
                      onChange={(e) => handleSettingChange('general', 'date_format', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={settings.general?.auto_backup || false}
                      onChange={(e) => handleSettingChange('general', 'auto_backup', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">Enable automatic backups</span>
                  </label>

                  {settings.general?.auto_backup && (
                    <div className="ml-7">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Backup Frequency
                      </label>
                      <select
                        value={settings.general?.backup_frequency || ''}
                        onChange={(e) => handleSettingChange('general', 'backup_frequency', e.target.value)}
                        className="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Add other category content here */}
            {activeCategory !== 'general' && (
              <div className="text-center py-8 text-gray-500">
                <Settings className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                <p>Settings for {settingsCategories.find(c => c.id === activeCategory)?.name} coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Action</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to perform this action? This may affect system behavior.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const action = settingsActions.find(a => a.id === showConfirmDialog);
                  if (action) executeSettingsAction(action);
                }}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
