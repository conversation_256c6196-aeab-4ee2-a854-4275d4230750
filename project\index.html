<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enterprise POS System - Multi-Tenant Restaurant Management</title>
    <meta name="description" content="Advanced multi-tenant restaurant point of sale system with real-time capabilities, AI analytics, and comprehensive management tools" />
    <meta name="keywords" content="restaurant, pos, multi-tenant, point of sale, food service, analytics, inventory, staff management" />

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Favicon and app icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

    <!-- Performance and SEO optimizations -->
    <meta name="theme-color" content="#1e40af" />
    <meta name="robots" content="noindex, nofollow" />

    <!-- Critical CSS for loading state -->
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #3730a3 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }

      #root {
        min-height: 100vh;
        position: relative;
      }

      /* Loading spinner */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 50%, #3730a3 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Hide loading when app loads */
      .app-loaded .loading-container {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading state -->
    <div class="loading-container">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading Enterprise POS System...</div>
      </div>
    </div>

    <!-- Main app container -->
    <div id="root"></div>

    <!-- Main application script -->
    <script type="module" src="src/main-unified-pos.tsx"></script>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 500);
      });
    </script>
  </body>
</html>
