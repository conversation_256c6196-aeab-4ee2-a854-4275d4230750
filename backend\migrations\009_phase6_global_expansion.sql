-- Phase 6: Global Expansion Migration
-- Database: BARPOS, PostgreSQL
-- This migration adds comprehensive global expansion capabilities

-- =====================================================
-- GLOBAL CURRENCY MANAGEMENT
-- =====================================================

CREATE TABLE IF NOT EXISTS global_currencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    currency_code VARCHAR(3) NOT NULL UNIQUE, -- ISO 4217
    currency_name VARCHAR(100) NOT NULL,
    currency_symbol VARCHAR(10) NOT NULL,
    decimal_places INTEGER DEFAULT 2,
    is_crypto BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    supported_regions TEXT[] DEFAULT ARRAY[]::TEXT[],
    display_format VARCHAR(50) DEFAULT 'symbol_amount', -- 'symbol_amount', 'amount_symbol', 'code_amount'
    rounding_method VARCHAR(20) DEFAULT 'round', -- 'round', 'floor', 'ceil'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- REAL-TIME EXCHANGE RATES
-- =====================================================

CREATE TABLE IF NOT EXISTS global_exchange_rates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    base_currency VARCHAR(3) NOT NULL REFERENCES global_currencies(currency_code),
    target_currency VARCHAR(3) NOT NULL REFERENCES global_currencies(currency_code),
    exchange_rate DECIMAL(15,8) NOT NULL CHECK (exchange_rate > 0),
    bid_rate DECIMAL(15,8),
    ask_rate DECIMAL(15,8),
    rate_source VARCHAR(50) NOT NULL, -- 'xe', 'fixer', 'openexchangerates', 'ecb'
    rate_type VARCHAR(20) DEFAULT 'spot', -- 'spot', 'forward', 'historical'
    volatility DECIMAL(5,4) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    next_update TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    api_response_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(base_currency, target_currency, rate_type, rate_source)
);

-- =====================================================
-- MULTI-CURRENCY PRICING
-- =====================================================

CREATE TABLE IF NOT EXISTS global_product_pricing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL,
    currency_code VARCHAR(3) REFERENCES global_currencies(currency_code),
    base_price DECIMAL(12,4) NOT NULL CHECK (base_price >= 0),
    local_price DECIMAL(12,4), -- Manually set local price
    auto_convert BOOLEAN DEFAULT true,
    price_strategy VARCHAR(50) DEFAULT 'auto_convert', -- 'auto_convert', 'manual', 'regional', 'dynamic'
    markup_percentage DECIMAL(5,2) DEFAULT 0, -- Additional markup for currency
    minimum_price DECIMAL(12,4),
    maximum_price DECIMAL(12,4),
    price_tier VARCHAR(20) DEFAULT 'standard', -- 'economy', 'standard', 'premium'
    seasonal_adjustment DECIMAL(5,2) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, product_id, currency_code)
);

-- =====================================================
-- INTERNATIONAL PAYMENT GATEWAYS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_payment_gateways (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    gateway_name VARCHAR(100) NOT NULL,
    gateway_code VARCHAR(50) NOT NULL UNIQUE,
    gateway_type VARCHAR(50) NOT NULL, -- 'card_processor', 'digital_wallet', 'bank_transfer', 'crypto'
    supported_currencies TEXT[] DEFAULT ARRAY[]::TEXT[],
    supported_regions TEXT[] DEFAULT ARRAY[]::TEXT[],
    supported_payment_methods TEXT[] DEFAULT ARRAY[]::TEXT[],
    api_endpoint VARCHAR(255),
    sandbox_endpoint VARCHAR(255),
    api_version VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_sandbox BOOLEAN DEFAULT false,
    configuration JSONB DEFAULT '{}',
    fees_structure JSONB DEFAULT '{}',
    settlement_time_hours INTEGER DEFAULT 24,
    max_transaction_amount DECIMAL(15,4),
    min_transaction_amount DECIMAL(15,4),
    security_features TEXT[] DEFAULT ARRAY[]::TEXT[],
    compliance_certifications TEXT[] DEFAULT ARRAY[]::TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- REGIONAL COMPLIANCE RULES
-- =====================================================

CREATE TABLE IF NOT EXISTS global_compliance_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    region_code VARCHAR(10) NOT NULL, -- ISO 3166
    country_code VARCHAR(3) NOT NULL, -- ISO 3166-1 alpha-3
    regulation_type VARCHAR(50) NOT NULL, -- 'gdpr', 'ccpa', 'pipeda', 'pci_dss', 'lgpd'
    regulation_name VARCHAR(200) NOT NULL,
    compliance_rules JSONB NOT NULL DEFAULT '{}',
    data_retention_days INTEGER,
    data_sovereignty_required BOOLEAN DEFAULT false,
    cross_border_transfer_allowed BOOLEAN DEFAULT true,
    consent_required BOOLEAN DEFAULT false,
    right_to_erasure BOOLEAN DEFAULT false,
    right_to_portability BOOLEAN DEFAULT false,
    breach_notification_hours INTEGER DEFAULT 72,
    audit_requirements JSONB DEFAULT '{}',
    penalties JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    effective_date DATE,
    expiry_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(region_code, regulation_type)
);

-- =====================================================
-- REGIONAL PAYMENT METHODS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    method_name VARCHAR(100) NOT NULL,
    method_code VARCHAR(50) NOT NULL UNIQUE,
    method_type VARCHAR(50) NOT NULL, -- 'card', 'wallet', 'bank_transfer', 'cash', 'crypto', 'bnpl'
    provider_name VARCHAR(100),
    supported_regions TEXT[] DEFAULT ARRAY[]::TEXT[],
    supported_currencies TEXT[] DEFAULT ARRAY[]::TEXT[],
    gateway_integration VARCHAR(100),
    configuration JSONB DEFAULT '{}',
    popularity_score DECIMAL(3,2) DEFAULT 0 CHECK (popularity_score >= 0 AND popularity_score <= 1),
    market_share_percentage DECIMAL(5,2) DEFAULT 0,
    user_demographics JSONB DEFAULT '{}',
    transaction_limits JSONB DEFAULT '{}',
    processing_time_minutes INTEGER DEFAULT 0,
    refund_support BOOLEAN DEFAULT true,
    recurring_payment_support BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- GLOBAL TAX CALCULATION
-- =====================================================

CREATE TABLE IF NOT EXISTS global_tax_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    region_code VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    state_province_code VARCHAR(10),
    city_code VARCHAR(10),
    tax_type VARCHAR(50) NOT NULL, -- 'vat', 'gst', 'sales_tax', 'service_tax', 'luxury_tax'
    tax_name VARCHAR(100) NOT NULL,
    tax_code VARCHAR(20),
    tax_rate DECIMAL(5,4) NOT NULL CHECK (tax_rate >= 0),
    applies_to TEXT[] DEFAULT ARRAY[]::TEXT[], -- Product categories
    exemptions TEXT[] DEFAULT ARRAY[]::TEXT[],
    calculation_method VARCHAR(50) DEFAULT 'percentage', -- 'percentage', 'fixed', 'tiered', 'progressive'
    is_inclusive BOOLEAN DEFAULT false,
    is_compound BOOLEAN DEFAULT false, -- Tax on tax
    minimum_threshold DECIMAL(12,4) DEFAULT 0,
    maximum_threshold DECIMAL(12,4),
    effective_date DATE,
    expiry_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- MULTI-CURRENCY TRANSACTIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    original_transaction_id UUID,
    base_currency VARCHAR(3) NOT NULL REFERENCES global_currencies(currency_code),
    transaction_currency VARCHAR(3) NOT NULL REFERENCES global_currencies(currency_code),
    base_amount DECIMAL(12,4) NOT NULL CHECK (base_amount >= 0),
    converted_amount DECIMAL(12,4) NOT NULL CHECK (converted_amount >= 0),
    exchange_rate DECIMAL(15,8) NOT NULL CHECK (exchange_rate > 0),
    conversion_fee DECIMAL(10,4) DEFAULT 0,
    gateway_used VARCHAR(100),
    payment_method_used VARCHAR(100),
    region_code VARCHAR(10),
    country_code VARCHAR(3),
    tax_details JSONB DEFAULT '{}',
    compliance_data JSONB DEFAULT '{}',
    risk_assessment JSONB DEFAULT '{}',
    processing_time_ms INTEGER,
    settlement_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'settled', 'failed', 'refunded'
    settlement_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- GLOBAL AI MODELS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_ai_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_type VARCHAR(50) NOT NULL, -- 'fraud_detection', 'sales_forecast', 'pricing_optimization', 'demand_prediction'
    region_code VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    currency_code VARCHAR(3) REFERENCES global_currencies(currency_code),
    language_code VARCHAR(5) NOT NULL,
    model_name VARCHAR(200) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    model_parameters JSONB NOT NULL DEFAULT '{}',
    training_data_size INTEGER DEFAULT 0,
    accuracy_metrics JSONB DEFAULT '{}',
    cultural_factors JSONB DEFAULT '{}',
    local_preferences JSONB DEFAULT '{}',
    market_conditions JSONB DEFAULT '{}',
    seasonal_patterns JSONB DEFAULT '{}',
    validation_score DECIMAL(5,4) DEFAULT 0,
    bias_score DECIMAL(5,4) DEFAULT 0,
    fairness_metrics JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_trained TIMESTAMP,
    next_training_scheduled TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(model_type, region_code, currency_code, language_code)
);

-- =====================================================
-- LOCALIZED AI RECOMMENDATIONS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_ai_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL, -- 'pricing', 'menu', 'promotion', 'upsell', 'retention'
    region_code VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    currency_code VARCHAR(3) REFERENCES global_currencies(currency_code),
    language_code VARCHAR(5) NOT NULL,
    target_entity_type VARCHAR(50) NOT NULL, -- 'product', 'customer', 'menu', 'promotion'
    target_entity_id VARCHAR(255) NOT NULL,
    recommendation_title VARCHAR(200),
    recommendation_description TEXT,
    recommendation_data JSONB NOT NULL DEFAULT '{}',
    cultural_adaptation JSONB DEFAULT '{}',
    local_market_factors JSONB DEFAULT '{}',
    seasonal_considerations JSONB DEFAULT '{}',
    confidence_score DECIMAL(5,4) DEFAULT 0,
    expected_impact JSONB DEFAULT '{}',
    priority_level VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'rejected', 'implemented'
    expires_at TIMESTAMP,
    implemented_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- GLOBAL PERFORMANCE METRICS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    region_code VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    currency_code VARCHAR(3) REFERENCES global_currencies(currency_code),
    metric_date DATE NOT NULL,
    metric_hour INTEGER CHECK (metric_hour >= 0 AND metric_hour <= 23),
    
    -- Sales metrics
    total_sales DECIMAL(15,4) DEFAULT 0,
    total_sales_base_currency DECIMAL(15,4) DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    avg_order_value DECIMAL(10,4) DEFAULT 0,
    avg_order_value_base_currency DECIMAL(10,4) DEFAULT 0,
    
    -- Performance metrics
    conversion_rate DECIMAL(5,4) DEFAULT 0,
    customer_satisfaction DECIMAL(3,2) DEFAULT 0,
    payment_success_rate DECIMAL(5,4) DEFAULT 0,
    avg_response_time_ms INTEGER DEFAULT 0,
    
    -- AI metrics
    fraud_detection_rate DECIMAL(5,4) DEFAULT 0,
    ai_recommendation_acceptance_rate DECIMAL(5,4) DEFAULT 0,
    prediction_accuracy DECIMAL(5,4) DEFAULT 0,
    
    -- Compliance metrics
    compliance_score DECIMAL(3,2) DEFAULT 0,
    data_breach_incidents INTEGER DEFAULT 0,
    audit_findings INTEGER DEFAULT 0,
    
    -- Financial metrics
    currency_volatility DECIMAL(5,4) DEFAULT 0,
    exchange_rate_impact DECIMAL(10,4) DEFAULT 0,
    international_fees DECIMAL(10,4) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, region_code, currency_code, metric_date, metric_hour)
);

-- =====================================================
-- DATA PROCESSING ACTIVITIES (GDPR)
-- =====================================================

CREATE TABLE IF NOT EXISTS global_data_processing (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    activity_name VARCHAR(200) NOT NULL,
    activity_type VARCHAR(100) NOT NULL,
    data_categories TEXT[] DEFAULT ARRAY[]::TEXT[],
    processing_purposes TEXT[] DEFAULT ARRAY[]::TEXT[],
    lawful_basis VARCHAR(100),
    legitimate_interests TEXT,
    retention_period INTEGER, -- days
    data_subjects TEXT[] DEFAULT ARRAY[]::TEXT[],
    recipients TEXT[] DEFAULT ARRAY[]::TEXT[],
    international_transfers BOOLEAN DEFAULT false,
    transfer_countries TEXT[] DEFAULT ARRAY[]::TEXT[],
    safeguards_applied TEXT[] DEFAULT ARRAY[]::TEXT[],
    automated_decision_making BOOLEAN DEFAULT false,
    profiling BOOLEAN DEFAULT false,
    region_code VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    risk_assessment JSONB DEFAULT '{}',
    mitigation_measures TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- COMPLIANCE AUDIT LOGS
-- =====================================================

CREATE TABLE IF NOT EXISTS global_compliance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    compliance_type VARCHAR(50) NOT NULL,
    audit_date DATE NOT NULL,
    region_code VARCHAR(10) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    compliance_score DECIMAL(5,4) DEFAULT 0,
    findings JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '{}',
    action_items JSONB DEFAULT '{}',
    risk_level VARCHAR(20) DEFAULT 'low', -- 'low', 'medium', 'high', 'critical'
    auditor_name VARCHAR(200),
    auditor_notes TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'compliant', 'non_compliant', 'remediation'
    remediation_deadline DATE,
    remediation_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_global_exchange_rates_currencies ON global_exchange_rates(base_currency, target_currency);
CREATE INDEX IF NOT EXISTS idx_global_exchange_rates_updated ON global_exchange_rates(last_updated);
CREATE INDEX IF NOT EXISTS idx_global_exchange_rates_active ON global_exchange_rates(is_active);

CREATE INDEX IF NOT EXISTS idx_global_product_pricing_tenant ON global_product_pricing(tenant_id);
CREATE INDEX IF NOT EXISTS idx_global_product_pricing_product ON global_product_pricing(product_id);
CREATE INDEX IF NOT EXISTS idx_global_product_pricing_currency ON global_product_pricing(currency_code);

CREATE INDEX IF NOT EXISTS idx_global_transactions_tenant ON global_transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_global_transactions_date ON global_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_global_transactions_currency ON global_transactions(transaction_currency);
CREATE INDEX IF NOT EXISTS idx_global_transactions_region ON global_transactions(region_code);

CREATE INDEX IF NOT EXISTS idx_global_ai_models_type_region ON global_ai_models(model_type, region_code);
CREATE INDEX IF NOT EXISTS idx_global_ai_models_currency ON global_ai_models(currency_code);
CREATE INDEX IF NOT EXISTS idx_global_ai_models_active ON global_ai_models(is_active);

CREATE INDEX IF NOT EXISTS idx_global_performance_tenant_date ON global_performance_metrics(tenant_id, metric_date);
CREATE INDEX IF NOT EXISTS idx_global_performance_region ON global_performance_metrics(region_code);
CREATE INDEX IF NOT EXISTS idx_global_performance_currency ON global_performance_metrics(currency_code);

CREATE INDEX IF NOT EXISTS idx_global_compliance_tenant ON global_compliance_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_global_compliance_type ON global_compliance_logs(compliance_type);
CREATE INDEX IF NOT EXISTS idx_global_compliance_region ON global_compliance_logs(region_code);

-- =====================================================
-- SAMPLE DATA FOR DEMONSTRATION
-- =====================================================

-- Insert major global currencies
INSERT INTO global_currencies (currency_code, currency_name, currency_symbol, decimal_places, supported_regions) VALUES
('USD', 'US Dollar', '$', 2, ARRAY['US', 'EC', 'SV', 'PA']),
('EUR', 'Euro', '€', 2, ARRAY['DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'PT', 'IE', 'FI', 'GR']),
('GBP', 'British Pound', '£', 2, ARRAY['GB']),
('JPY', 'Japanese Yen', '¥', 0, ARRAY['JP']),
('CNY', 'Chinese Yuan', '¥', 2, ARRAY['CN']),
('CAD', 'Canadian Dollar', 'C$', 2, ARRAY['CA']),
('AUD', 'Australian Dollar', 'A$', 2, ARRAY['AU']),
('CHF', 'Swiss Franc', 'CHF', 2, ARRAY['CH']),
('SEK', 'Swedish Krona', 'kr', 2, ARRAY['SE']),
('NOK', 'Norwegian Krone', 'kr', 2, ARRAY['NO']),
('DKK', 'Danish Krone', 'kr', 2, ARRAY['DK']),
('SGD', 'Singapore Dollar', 'S$', 2, ARRAY['SG']),
('HKD', 'Hong Kong Dollar', 'HK$', 2, ARRAY['HK']),
('NZD', 'New Zealand Dollar', 'NZ$', 2, ARRAY['NZ']),
('MXN', 'Mexican Peso', '$', 2, ARRAY['MX'])
ON CONFLICT (currency_code) DO NOTHING;

-- Insert sample exchange rates (USD as base)
INSERT INTO global_exchange_rates (base_currency, target_currency, exchange_rate, rate_source) VALUES
('USD', 'EUR', 0.8500, 'xe'),
('USD', 'GBP', 0.7300, 'xe'),
('USD', 'JPY', 110.0000, 'xe'),
('USD', 'CNY', 6.4500, 'xe'),
('USD', 'CAD', 1.2500, 'xe'),
('USD', 'AUD', 1.3500, 'xe'),
('USD', 'CHF', 0.9200, 'xe'),
('USD', 'SEK', 8.5000, 'xe'),
('USD', 'NOK', 8.8000, 'xe'),
('USD', 'DKK', 6.3500, 'xe'),
('USD', 'SGD', 1.3500, 'xe'),
('USD', 'HKD', 7.8000, 'xe'),
('USD', 'NZD', 1.4200, 'xe'),
('USD', 'MXN', 20.0000, 'xe')
ON CONFLICT (base_currency, target_currency, rate_type, rate_source) DO NOTHING;

-- Insert major payment gateways
INSERT INTO global_payment_gateways (gateway_name, gateway_code, gateway_type, supported_currencies, supported_regions) VALUES
('Stripe', 'stripe', 'card_processor', ARRAY['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'], ARRAY['US', 'CA', 'GB', 'AU', 'JP', 'EU']),
('PayPal', 'paypal', 'digital_wallet', ARRAY['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY'], ARRAY['US', 'CA', 'GB', 'AU', 'JP', 'EU', 'CN']),
('Adyen', 'adyen', 'card_processor', ARRAY['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'SGD'], ARRAY['US', 'EU', 'GB', 'JP', 'CN', 'SG']),
('Alipay', 'alipay', 'digital_wallet', ARRAY['CNY', 'USD', 'EUR'], ARRAY['CN', 'US', 'EU']),
('WeChat Pay', 'wechat_pay', 'digital_wallet', ARRAY['CNY', 'USD'], ARRAY['CN', 'US'])
ON CONFLICT (gateway_code) DO NOTHING;

-- Insert sample compliance rules
INSERT INTO global_compliance_rules (region_code, country_code, regulation_type, regulation_name, data_retention_days) VALUES
('EU', 'EUR', 'gdpr', 'General Data Protection Regulation', 2555), -- 7 years
('US', 'USA', 'ccpa', 'California Consumer Privacy Act', 1095), -- 3 years
('CA', 'CAN', 'pipeda', 'Personal Information Protection and Electronic Documents Act', 2190), -- 6 years
('GB', 'GBR', 'uk_gdpr', 'UK General Data Protection Regulation', 2555), -- 7 years
('AU', 'AUS', 'privacy_act', 'Privacy Act 1988', 2555) -- 7 years
ON CONFLICT (region_code, regulation_type) DO NOTHING;

-- Insert global AI models for each tenant
INSERT INTO global_ai_models (model_type, region_code, country_code, currency_code, language_code, model_name, algorithm_type, accuracy_metrics)
SELECT 
    'fraud_detection' as model_type,
    'US' as region_code,
    'USA' as country_code,
    'USD' as currency_code,
    'en' as language_code,
    'Global Fraud Detection Model - USD' as model_name,
    'ensemble' as algorithm_type,
    '{"accuracy": 0.965, "precision": 0.942, "recall": 0.958}' as accuracy_metrics
FROM tenants
ON CONFLICT (model_type, region_code, currency_code, language_code) DO NOTHING;

-- Grant permissions to BARPOS user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "BARPOS";

-- Migration completed successfully
-- Phase 6: Global Expansion database schema is now ready
