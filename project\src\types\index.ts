// Enterprise POS System Type Definitions

// Core Product Types
export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  inStock: boolean;
  sku?: string;
  barcode?: string;
  cost?: number;
  margin?: number;
  taxable?: boolean;
  modifiers?: ProductModifier[];
  variants?: ProductVariant[];
  tenant_id?: string;
  location_id?: string;
}

export interface ProductModifier {
  id: string;
  name: string;
  price: number;
  required: boolean;
  options: ModifierOption[];
}

export interface ModifierOption {
  id: string;
  name: string;
  price: number;
}

export interface ProductVariant {
  id: string;
  name: string;
  price: number;
  sku?: string;
  inStock: boolean;
}

// Order Types
export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  modifiers?: SelectedModifier[];
  variant?: ProductVariant;
  discount?: number;
  tax?: number;
}

export interface SelectedModifier {
  modifierId: string;
  optionId: string;
  name: string;
  price: number;
}

export interface Order {
  id: string;
  items: OrderItem[];
  tabName?: string;
  tableId?: string;
  timestamp: number;
  status: 'open' | 'paid' | 'canceled' | 'kitchen' | 'ready' | 'served' | 'refunded';
  paymentMethod?: PaymentMethod;
  total: number;
  subtotal: number;
  tax: number;
  tip?: number;
  discount?: number;
  employee_id?: string;
  customer_id?: string;
  kitchenNotes?: string;
  orderNumber?: number;
  tenant_id?: string;
  location_id?: string;
  delivery?: DeliveryInfo;
  payment?: PaymentInfo;
}

export interface DeliveryInfo {
  type: 'pickup' | 'delivery' | 'dine-in';
  address?: string;
  phone?: string;
  estimatedTime?: number;
  driver?: string;
}

export interface PaymentInfo {
  method: PaymentMethod;
  amount: number;
  tip?: number;
  transactionId?: string;
  cardLast4?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  processedAt?: number;
}

export type PaymentMethod = 'cash' | 'card' | 'mobile' | 'gift_card' | 'loyalty_points';

// Employee & Authentication Types
export interface Employee {
  id?: string;
  name: string;
  pin?: string;
  role: EmployeeRole;
  created_at?: string;
  phone?: string;
  email?: string;
  hourlyRate?: number;
  permissions?: Permission[];
  tenant_id?: string;
  location_id?: string;
  isActive?: boolean;
  lastLogin?: string;
}

export type EmployeeRole = 'super_admin' | 'tenant_admin' | 'manager' | 'bartender' | 'server' | 'kitchen' | 'cashier';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

// Multi-tenant Types
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  subscription: Subscription;
  settings: TenantSettings;
  created_at: string;
  updated_at: string;
  status: 'active' | 'suspended' | 'canceled';
}

export interface Subscription {
  id: string;
  plan: SubscriptionPlan;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  stripe_subscription_id?: string;
  trial_end?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  features: PlanFeature[];
  limits: PlanLimits;
}

export interface PlanFeature {
  name: string;
  included: boolean;
  limit?: number;
}

export interface PlanLimits {
  locations: number;
  employees: number;
  products: number;
  orders_per_month: number;
  storage_gb: number;
}

export interface TenantSettings {
  business_name: string;
  business_type: string;
  timezone: string;
  currency: string;
  tax_rate: number;
  receipt_header?: string;
  receipt_footer?: string;
  business_address?: string;
  business_phone?: string;
  theme_primary_color?: string;
  theme_secondary_color?: string;
  features: TenantFeatures;
}

export interface TenantFeatures {
  multi_location: boolean;
  kitchen_display: boolean;
  loyalty_program: boolean;
  inventory_management: boolean;
  advanced_reporting: boolean;
  third_party_integrations: boolean;
  custom_branding: boolean;
}

// Location Types
export interface Location {
  id: string;
  tenant_id: string;
  name: string;
  address: string;
  phone?: string;
  email?: string;
  timezone: string;
  settings: LocationSettings;
  isActive: boolean;
  created_at: string;
}

export interface LocationSettings {
  tax_rate?: number;
  receipt_printer?: string;
  kitchen_printer?: string;
  cash_drawer?: string;
  payment_terminal?: string;
}

export interface SystemConfig {
  tax_rate: number;
  receipt_header?: string;
  receipt_footer?: string;
  business_name?: string;
  business_address?: string;
  business_phone?: string;
  theme_primary_color?: string;
  theme_secondary_color?: string;
}

// Floor Layout Types
export interface Table {
  id: string;
  number: number;
  seats: number;
  x: number;
  y: number;
  width: number;
  height: number;
  shape: 'rectangle' | 'circle' | 'oval';
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order' | 'being-seated';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  currentOrderId?: string;
  reservationTime?: number;
  seatedTime?: Date;
  estimatedTurnTime?: Date;
  serverAssigned?: string;
  serverName?: string;
  guestCount?: number;
  specialRequests?: string[];
  allergies?: string[];
  section?: string;
  tableType: 'regular' | 'bar' | 'private' | 'outdoor' | 'booth';
  combinedWith?: string[]; // IDs of tables combined with this one
  orderTotal?: number;
  orderItems?: number;
  lastOrderTime?: Date;
  averageTurnTime?: number;
  isVip?: boolean;
}

export interface TableSection {
  id: string;
  name: string;
  color: string;
  serverIds: string[];
  tableIds: string[];
  isActive: boolean;
}

export interface Reservation {
  id: string;
  tableId: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  reservationTime: Date;
  duration: number; // in minutes
  status: 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no-show';
  specialRequests?: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WaitlistEntry {
  id: string;
  customerName: string;
  customerPhone: string;
  partySize: number;
  estimatedWaitTime: number; // in minutes
  joinedAt: Date;
  notifiedAt?: Date;
  status: 'waiting' | 'notified' | 'seated' | 'cancelled';
  specialRequests?: string[];
  preferredSection?: string;
}

export interface FloorLayout {
  id: string;
  name: string;
  tables: Table[];
  sections: TableSection[];
  width: number;
  height: number;
  backgroundImage?: string;
  gridSize: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Staff Scheduling Types
export interface Shift {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  startTime: string;
  endTime: string;
  position: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'no-show';
  notes?: string;
}

export interface Schedule {
  id: string;
  weekStartDate: string;
  shifts: Shift[];
  published: boolean;
  createdBy: string;
}

// Loyalty Program Types
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  points: number;
  totalSpent: number;
  visits: number;
  joinDate: string;
  lastVisit?: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
}

export interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: 'discount' | 'free-item' | 'upgrade';
  value: number;
  active: boolean;
}

export interface LoyaltyTransaction {
  id: string;
  customerId: string;
  orderId?: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  timestamp: number;
}

// Kitchen Display Types
export interface KitchenOrder {
  id: string;
  orderNumber: number;
  tableNumber?: number;
  items: OrderItem[];
  status: 'new' | 'preparing' | 'ready' | 'served';
  timestamp: number;
  estimatedTime?: number;
  priority: 'low' | 'normal' | 'high';
  notes?: string;
  assignedTo?: string;
  startedAt?: number;
  readyAt?: number;
  servedAt?: number;
  preparationTime?: number;
  totalTime?: number;
}

// Kitchen Performance Metrics
export interface KitchenMetrics {
  totalOrders: number;
  completedOrders: number;
  averagePreparationTime: number;
  averageTotalTime: number;
  ordersInProgress: number;
  ordersReady: number;
  ordersOverdue: number;
  efficiency: number;
  peakHours: { hour: number; count: number }[];
}

// Kitchen Filter Options
export interface KitchenFilters {
  status?: KitchenOrder['status'][];
  priority?: KitchenOrder['priority'][];
  assignedTo?: string[];
  timeRange?: {
    start: number;
    end: number;
  };
  searchTerm?: string;
  tableNumber?: number;
  showCompleted?: boolean;
}

// Kitchen Settings
export interface KitchenSettings {
  audioEnabled: boolean;
  audioVolume: number;
  autoRefresh: boolean;
  refreshInterval: number;
  showMetrics: boolean;
  compactView: boolean;
  maxOrdersPerColumn: number;
  overdueThreshold: number; // minutes
  priorityColors: {
    low: string;
    normal: string;
    high: string;
  };
}

// Kitchen Staff Assignment
export interface KitchenStaff {
  id: string;
  name: string;
  role: 'chef' | 'cook' | 'prep';
  isActive: boolean;
  currentOrders: string[];
  maxOrders: number;
  efficiency: number;
}

export interface AppState {
  products: Product[];
  orders: Order[];
  currentOrder: Order | null;
  employees: Employee[];
  currentEmployee: Employee | null;
  isAuthenticated: boolean;
  categories: Category[];
  systemConfig: SystemConfig;
  floorLayout: FloorLayout | null;
  tables: Table[];
  schedules: Schedule[];
  customers: Customer[];
  loyaltyRewards: LoyaltyReward[];
  kitchenOrders: KitchenOrder[];
  selectedTable: Table | null;
  kitchenMetrics: KitchenMetrics | null;
  kitchenSettings: KitchenSettings;
  kitchenStaff: KitchenStaff[];
  // Enhanced multi-tenancy state
  currentTenant: Tenant | null;
  currentLocation: Location | null;
  locations: Location[];
  tenantSettings: TenantSettings | null;
  authToken: string | null;
  socket: any | null;
  realTimeUpdates: boolean;
}

// Allow dynamic categories
export type Category = string;

export type AppAction = 
  | { type: 'ADD_PRODUCT_TO_ORDER'; payload: Product }
  | { type: 'REMOVE_ITEM_FROM_ORDER'; payload: string }
  | { type: 'UPDATE_ITEM_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CURRENT_ORDER' }
  | { type: 'SET_CURRENT_ORDER'; payload: Order }
  | { type: 'COMPLETE_ORDER'; payload: { paymentMethod: 'cash' | 'card' | 'mobile'; tip?: number } }
  | { type: 'SET_TAB_NAME'; payload: string }
  | { type: 'LOGIN'; payload: { employee: Employee; tenant: Tenant; location: Location | null; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'ADD_CATEGORY'; payload: Category }
  | { type: 'SET_EMPLOYEES'; payload: Employee[] }
  | { type: 'ADD_EMPLOYEE'; payload: Employee }
  | { type: 'UPDATE_EMPLOYEE'; payload: Employee }
  | { type: 'DELETE_EMPLOYEE'; payload: string }
  | { type: 'SET_ORDERS'; payload: Order[] }
  | { type: 'UPDATE_SYSTEM_CONFIG'; payload: Partial<SystemConfig> }
  | { type: 'SET_FLOOR_LAYOUT'; payload: FloorLayout }
  | { type: 'SET_TABLES'; payload: Table[] }
  | { type: 'UPDATE_TABLE_STATUS'; payload: { tableId: string; status: Table['status']; orderId?: string } }
  | { type: 'SELECT_TABLE'; payload: Table | null }
  | { type: 'SET_SCHEDULES'; payload: Schedule[] }
  | { type: 'ADD_SCHEDULE'; payload: Schedule }
  | { type: 'UPDATE_SCHEDULE'; payload: Schedule }
  | { type: 'SET_CUSTOMERS'; payload: Customer[] }
  | { type: 'ADD_CUSTOMER'; payload: Customer }
  | { type: 'UPDATE_CUSTOMER'; payload: Customer }
  | { type: 'SET_LOYALTY_REWARDS'; payload: LoyaltyReward[] }
  | { type: 'SET_KITCHEN_ORDERS'; payload: KitchenOrder[] }
  | { type: 'ADD_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'UPDATE_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'SEND_ORDER_TO_KITCHEN'; payload: string }
  | { type: 'SET_KITCHEN_METRICS'; payload: KitchenMetrics }
  | { type: 'UPDATE_KITCHEN_SETTINGS'; payload: Partial<KitchenSettings> }
  | { type: 'SET_KITCHEN_STAFF'; payload: KitchenStaff[] }
  // Enhanced multi-tenancy actions
  | { type: 'SET_AUTH_TOKEN'; payload: string }
  | { type: 'SET_CURRENT_TENANT'; payload: Tenant | null }
  | { type: 'SET_CURRENT_LOCATION'; payload: Location | null }
  | { type: 'SET_LOCATIONS'; payload: Location[] }
  | { type: 'SET_TENANT_SETTINGS'; payload: TenantSettings | null }
  | { type: 'SET_SOCKET'; payload: any | null }
  | { type: 'ADD_ORDER_TO_HISTORY'; payload: Order }
  | { type: 'ASSIGN_ORDER_TO_STAFF'; payload: Order };

export type AppContextType = {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  addProduct: (product: Product) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  addCategory: (category: Category) => Promise<void>;
  addEmployee: (employee: Employee) => Promise<void>;
  updateEmployee: (employee: Employee) => Promise<void>;
  deleteEmployee: (employeeId: string) => Promise<void>;
  updateSettings: (settings: Partial<SystemConfig>) => Promise<void>;
  fetchEmployees: () => Promise<Employee[]>;
  fetchSettings: () => Promise<void>;
  validateEmployeePin: (pin: string) => Promise<Employee | null>;
  fetchProducts: () => Promise<Product[]>;
  fetchCategories: () => Promise<Category[]>;
  importProducts: (file: File) => Promise<{ success: any[]; errors: any[] }>;
  exportProducts: () => Promise<void>;
  downloadTemplate: () => Promise<void>;
  // Floor Layout functions
  fetchFloorLayout: () => Promise<FloorLayout | null>;
  updateTableStatus: (tableId: string, status: Table['status'], orderId?: string) => Promise<void>;
  // Scheduling functions
  fetchSchedules: () => Promise<Schedule[]>;
  addSchedule: (schedule: Schedule) => Promise<void>;
  updateSchedule: (schedule: Schedule) => Promise<void>;
  // Loyalty functions
  fetchCustomers: () => Promise<Customer[]>;
  addCustomer: (customer: Customer) => Promise<void>;
  updateCustomer: (customer: Customer) => Promise<void>;
  fetchLoyaltyRewards: () => Promise<LoyaltyReward[]>;
  // Kitchen functions
  fetchKitchenOrders: () => Promise<KitchenOrder[]>;
  sendOrderToKitchen: (orderId: string) => Promise<void>;
  updateKitchenOrderStatus: (orderId: string, status: KitchenOrder['status']) => Promise<void>;
  // Enhanced Kitchen functions
  fetchKitchenMetrics: () => Promise<KitchenMetrics>;
  updateKitchenSettings: (settings: Partial<KitchenSettings>) => Promise<void>;
  fetchKitchenStaff: () => Promise<KitchenStaff[]>;
  assignOrderToStaff: (orderId: string, staffId: string) => Promise<void>;
  updateKitchenOrder: (orderId: string, updates: Partial<KitchenOrder>) => Promise<void>;
  bulkUpdateKitchenOrders: (orderIds: string[], status: KitchenOrder['status']) => Promise<void>;
};
