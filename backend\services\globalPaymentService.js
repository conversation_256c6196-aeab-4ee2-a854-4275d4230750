// Global Payment Service for Phase 6
// International payment gateways, regional compliance, and global transactions

const { Pool } = require('pg');
const GlobalCurrencyService = require('./globalCurrencyService');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class GlobalPaymentService {
  constructor() {
    this.currencyService = new GlobalCurrencyService();
    this.supportedGateways = new Map();
    this.regionCompliance = new Map();
    
    // Initialize gateway configurations
    this.initializeGateways();
    this.loadComplianceRules();
  }

  // =====================================================
  // GATEWAY INITIALIZATION
  // =====================================================

  async initializeGateways() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT * FROM global_payment_gateways 
        WHERE is_active = true
        ORDER BY gateway_name
      `);
      
      result.rows.forEach(gateway => {
        this.supportedGateways.set(gateway.gateway_code, {
          ...gateway,
          configuration: gateway.configuration || {},
          fees_structure: gateway.fees_structure || {}
        });
      });
      
      client.release();
      
      console.log(`✅ Initialized ${this.supportedGateways.size} payment gateways`);

    } catch (error) {
      console.error('❌ Error initializing gateways:', error);
    }
  }

  async loadComplianceRules() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT * FROM global_compliance_rules 
        WHERE is_active = true
        ORDER BY region_code
      `);
      
      result.rows.forEach(rule => {
        const key = `${rule.region_code}_${rule.regulation_type}`;
        this.regionCompliance.set(key, rule);
      });
      
      client.release();
      
      console.log(`✅ Loaded ${this.regionCompliance.size} compliance rules`);

    } catch (error) {
      console.error('❌ Error loading compliance rules:', error);
    }
  }

  // =====================================================
  // INTERNATIONAL PAYMENT PROCESSING
  // =====================================================

  async processInternationalPayment(paymentData) {
    const startTime = Date.now();
    
    try {
      console.log('🌍 Processing international payment:', {
        amount: paymentData.amount,
        currency: paymentData.currency,
        region: paymentData.region,
        gateway: paymentData.preferred_gateway
      });

      // Step 1: Validate payment data
      const validation = await this.validatePaymentData(paymentData);
      if (!validation.success) {
        return {
          success: false,
          error: validation.error,
          processing_time: Date.now() - startTime
        };
      }

      // Step 2: Check regional compliance
      const compliance = await this.validateRegionalCompliance(paymentData);
      if (!compliance.success) {
        return {
          success: false,
          error: compliance.error,
          compliance_issues: compliance.issues,
          processing_time: Date.now() - startTime
        };
      }

      // Step 3: Select optimal payment gateway
      const gateway = await this.selectOptimalGateway(paymentData);
      if (!gateway.success) {
        return {
          success: false,
          error: gateway.error,
          processing_time: Date.now() - startTime
        };
      }

      // Step 4: Calculate fees and taxes
      const feeCalculation = await this.calculateInternationalFees(paymentData, gateway.gateway);
      
      // Step 5: Process currency conversion if needed
      let conversionResult = null;
      if (paymentData.currency !== paymentData.settlement_currency) {
        conversionResult = await this.currencyService.convertCurrency(
          paymentData.amount,
          paymentData.currency,
          paymentData.settlement_currency
        );
        
        if (!conversionResult.success) {
          return {
            success: false,
            error: 'Currency conversion failed',
            processing_time: Date.now() - startTime
          };
        }
      }

      // Step 6: Process payment through selected gateway
      const paymentResult = await this.processPaymentThroughGateway(
        paymentData,
        gateway.gateway,
        feeCalculation,
        conversionResult
      );

      // Step 7: Store global transaction record
      if (paymentResult.success) {
        await this.storeGlobalTransaction({
          ...paymentData,
          gateway_used: gateway.gateway.gateway_code,
          conversion_result: conversionResult,
          fee_calculation: feeCalculation,
          processing_time: Date.now() - startTime,
          transaction_id: paymentResult.transaction_id
        });
      }

      return {
        success: paymentResult.success,
        transaction_id: paymentResult.transaction_id,
        gateway_used: gateway.gateway.gateway_name,
        original_amount: paymentData.amount,
        original_currency: paymentData.currency,
        settlement_amount: conversionResult?.converted_amount || paymentData.amount,
        settlement_currency: paymentData.settlement_currency || paymentData.currency,
        exchange_rate: conversionResult?.exchange_rate || 1.0,
        total_fees: feeCalculation.total_fees,
        compliance_status: compliance.status,
        processing_time: Date.now() - startTime,
        payment_method: paymentData.payment_method,
        region: paymentData.region
      };

    } catch (error) {
      console.error('❌ International payment processing error:', error);
      return {
        success: false,
        error: error.message,
        processing_time: Date.now() - startTime
      };
    }
  }

  async validatePaymentData(paymentData) {
    // Basic validation
    if (!paymentData.amount || paymentData.amount <= 0) {
      return { success: false, error: 'Invalid payment amount' };
    }

    if (!paymentData.currency || paymentData.currency.length !== 3) {
      return { success: false, error: 'Invalid currency code' };
    }

    if (!paymentData.region) {
      return { success: false, error: 'Region code required for international payments' };
    }

    // Check if currency is supported
    const currencies = await this.currencyService.getSupportedCurrencies();
    const supportedCodes = currencies.currencies.map(c => c.currency_code);
    
    if (!supportedCodes.includes(paymentData.currency)) {
      return { success: false, error: `Currency ${paymentData.currency} not supported` };
    }

    return { success: true };
  }

  async validateRegionalCompliance(paymentData) {
    try {
      const region = paymentData.region;
      const complianceRules = [];
      
      // Get all applicable compliance rules for the region
      for (const [key, rule] of this.regionCompliance) {
        if (key.startsWith(region)) {
          complianceRules.push(rule);
        }
      }

      const issues = [];
      
      // Check GDPR compliance
      const gdprRule = complianceRules.find(r => r.regulation_type === 'gdpr');
      if (gdprRule && paymentData.customer_data) {
        if (!paymentData.customer_data.consent_given) {
          issues.push('GDPR: Customer consent required for data processing');
        }
        
        if (!paymentData.customer_data.lawful_basis) {
          issues.push('GDPR: Lawful basis for processing must be specified');
        }
      }

      // Check PCI-DSS compliance
      if (paymentData.payment_method === 'card' && paymentData.card_data) {
        if (!paymentData.card_data.encrypted) {
          issues.push('PCI-DSS: Card data must be encrypted');
        }
      }

      // Check data sovereignty requirements
      const sovereigntyRules = complianceRules.filter(r => r.data_sovereignty_required);
      if (sovereigntyRules.length > 0 && !paymentData.data_localization) {
        issues.push('Data sovereignty: Payment data must be processed locally');
      }

      return {
        success: issues.length === 0,
        status: issues.length === 0 ? 'compliant' : 'non_compliant',
        issues: issues,
        applicable_rules: complianceRules.map(r => r.regulation_type)
      };

    } catch (error) {
      console.error('❌ Compliance validation error:', error);
      return {
        success: false,
        error: 'Compliance validation failed'
      };
    }
  }

  async selectOptimalGateway(paymentData) {
    try {
      const availableGateways = [];
      
      // Filter gateways by region and currency support
      for (const [code, gateway] of this.supportedGateways) {
        const supportsRegion = gateway.supported_regions.includes(paymentData.region) ||
                              gateway.supported_regions.includes('GLOBAL');
        const supportsCurrency = gateway.supported_currencies.includes(paymentData.currency);
        
        if (supportsRegion && supportsCurrency) {
          // Calculate gateway score based on various factors
          const score = this.calculateGatewayScore(gateway, paymentData);
          availableGateways.push({ gateway, score });
        }
      }

      if (availableGateways.length === 0) {
        return {
          success: false,
          error: 'No suitable payment gateway available for this region and currency'
        };
      }

      // Sort by score and select the best gateway
      availableGateways.sort((a, b) => b.score - a.score);
      const selectedGateway = availableGateways[0].gateway;

      console.log(`✅ Selected gateway: ${selectedGateway.gateway_name} (score: ${availableGateways[0].score})`);

      return {
        success: true,
        gateway: selectedGateway,
        alternatives: availableGateways.slice(1, 3).map(g => ({
          name: g.gateway.gateway_name,
          score: g.score
        }))
      };

    } catch (error) {
      console.error('❌ Gateway selection error:', error);
      return {
        success: false,
        error: 'Gateway selection failed'
      };
    }
  }

  calculateGatewayScore(gateway, paymentData) {
    let score = 0;
    
    // Base score
    score += 50;
    
    // Prefer gateways with lower fees
    const feeStructure = gateway.fees_structure;
    if (feeStructure.percentage_fee) {
      score -= feeStructure.percentage_fee * 10; // Lower fees = higher score
    }
    
    // Prefer faster settlement
    if (gateway.settlement_time_hours <= 24) score += 20;
    else if (gateway.settlement_time_hours <= 48) score += 10;
    
    // Prefer gateways with better security features
    score += (gateway.security_features?.length || 0) * 5;
    
    // Prefer gateways with more compliance certifications
    score += (gateway.compliance_certifications?.length || 0) * 3;
    
    // Prefer user's preferred gateway if specified
    if (paymentData.preferred_gateway === gateway.gateway_code) {
      score += 30;
    }
    
    // Regional preference
    if (gateway.supported_regions.includes(paymentData.region)) {
      score += 15;
    }
    
    return score;
  }

  async calculateInternationalFees(paymentData, gateway) {
    try {
      const feeStructure = gateway.fees_structure;
      let totalFees = 0;
      const feeBreakdown = {};

      // Gateway processing fee
      if (feeStructure.percentage_fee) {
        const processingFee = paymentData.amount * (feeStructure.percentage_fee / 100);
        feeBreakdown.processing_fee = processingFee;
        totalFees += processingFee;
      }

      if (feeStructure.fixed_fee) {
        feeBreakdown.fixed_fee = feeStructure.fixed_fee;
        totalFees += feeStructure.fixed_fee;
      }

      // International transaction fee
      if (paymentData.region !== 'US') {
        const internationalFee = paymentData.amount * 0.015; // 1.5% international fee
        feeBreakdown.international_fee = internationalFee;
        totalFees += internationalFee;
      }

      // Currency conversion fee (if applicable)
      if (paymentData.currency !== paymentData.settlement_currency) {
        const conversionFee = paymentData.amount * 0.01; // 1% conversion fee
        feeBreakdown.conversion_fee = conversionFee;
        totalFees += conversionFee;
      }

      // Cross-border fee
      if (paymentData.cross_border) {
        const crossBorderFee = Math.min(paymentData.amount * 0.005, 50); // 0.5% up to $50
        feeBreakdown.cross_border_fee = crossBorderFee;
        totalFees += crossBorderFee;
      }

      return {
        success: true,
        total_fees: Math.round(totalFees * 100) / 100,
        fee_breakdown: feeBreakdown,
        fee_currency: paymentData.currency
      };

    } catch (error) {
      console.error('❌ Fee calculation error:', error);
      return {
        success: false,
        error: 'Fee calculation failed',
        total_fees: 0
      };
    }
  }

  async processPaymentThroughGateway(paymentData, gateway, feeCalculation, conversionResult) {
    try {
      // Mock payment processing for demonstration
      // In production, this would integrate with actual payment gateway APIs
      
      const processingTime = Math.random() * 2000 + 1000; // 1-3 seconds
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      // Simulate payment success/failure
      const successRate = 0.95; // 95% success rate
      const isSuccessful = Math.random() < successRate;
      
      if (!isSuccessful) {
        const errorCodes = ['insufficient_funds', 'card_declined', 'network_error', 'gateway_timeout'];
        const errorCode = errorCodes[Math.floor(Math.random() * errorCodes.length)];
        
        return {
          success: false,
          error_code: errorCode,
          error_message: `Payment failed: ${errorCode.replace('_', ' ')}`,
          gateway_response: {
            status: 'failed',
            error_code: errorCode,
            processing_time_ms: processingTime
          }
        };
      }

      // Generate mock transaction ID
      const transactionId = `${gateway.gateway_code}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      return {
        success: true,
        transaction_id: transactionId,
        gateway_transaction_id: `gtw_${Math.random().toString(36).substr(2, 12)}`,
        status: 'completed',
        gateway_response: {
          status: 'success',
          authorization_code: Math.random().toString(36).substr(2, 8).toUpperCase(),
          processing_time_ms: processingTime,
          settlement_date: new Date(Date.now() + gateway.settlement_time_hours * 60 * 60 * 1000),
          risk_score: Math.random() * 0.3, // Low risk score
          network_transaction_id: `net_${Math.random().toString(36).substr(2, 16)}`
        }
      };

    } catch (error) {
      console.error('❌ Gateway processing error:', error);
      return {
        success: false,
        error: error.message,
        gateway_response: {
          status: 'error',
          error_message: error.message
        }
      };
    }
  }

  async storeGlobalTransaction(transactionData) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO global_transactions (
          tenant_id, original_transaction_id, base_currency, transaction_currency,
          base_amount, converted_amount, exchange_rate, conversion_fee,
          gateway_used, payment_method_used, region_code, country_code,
          tax_details, compliance_data, risk_assessment, processing_time_ms,
          settlement_status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      `, [
        transactionData.tenant_id,
        transactionData.transaction_id,
        transactionData.settlement_currency || transactionData.currency,
        transactionData.currency,
        transactionData.conversion_result?.converted_amount || transactionData.amount,
        transactionData.amount,
        transactionData.conversion_result?.exchange_rate || 1.0,
        transactionData.conversion_result?.conversion_fee || 0,
        transactionData.gateway_used,
        transactionData.payment_method,
        transactionData.region,
        transactionData.country_code || transactionData.region,
        JSON.stringify(transactionData.tax_details || {}),
        JSON.stringify(transactionData.compliance_data || {}),
        JSON.stringify(transactionData.risk_assessment || {}),
        transactionData.processing_time,
        'completed'
      ]);
      
      client.release();

    } catch (error) {
      console.error('❌ Error storing global transaction:', error);
    }
  }

  // =====================================================
  // REGIONAL PAYMENT METHODS
  // =====================================================

  async getRegionalPaymentMethods(region, currency) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          method_name,
          method_code,
          method_type,
          provider_name,
          popularity_score,
          market_share_percentage,
          transaction_limits,
          processing_time_minutes,
          refund_support,
          recurring_payment_support
        FROM global_payment_methods 
        WHERE $1 = ANY(supported_regions) 
          AND $2 = ANY(supported_currencies)
          AND is_active = true
        ORDER BY popularity_score DESC, market_share_percentage DESC
      `, [region, currency]);
      
      client.release();
      
      return {
        success: true,
        payment_methods: result.rows,
        region: region,
        currency: currency,
        count: result.rows.length
      };

    } catch (error) {
      console.error('❌ Error getting regional payment methods:', error);
      return {
        success: false,
        error: error.message,
        payment_methods: []
      };
    }
  }

  // =====================================================
  // GLOBAL TAX CALCULATION
  // =====================================================

  async calculateGlobalTax(amount, region, productCategory, currency) {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          tax_type,
          tax_name,
          tax_rate,
          calculation_method,
          is_inclusive,
          is_compound,
          minimum_threshold
        FROM global_tax_rules 
        WHERE region_code = $1 
          AND (applies_to IS NULL OR $2 = ANY(applies_to))
          AND is_active = true
          AND (effective_date IS NULL OR effective_date <= CURRENT_DATE)
          AND (expiry_date IS NULL OR expiry_date > CURRENT_DATE)
        ORDER BY tax_type
      `, [region, productCategory]);
      
      client.release();
      
      let totalTax = 0;
      const taxBreakdown = [];
      let taxableAmount = amount;
      
      for (const tax of result.rows) {
        if (taxableAmount >= (tax.minimum_threshold || 0)) {
          let taxAmount = 0;
          
          if (tax.calculation_method === 'percentage') {
            taxAmount = taxableAmount * (tax.tax_rate / 100);
          } else if (tax.calculation_method === 'fixed') {
            taxAmount = tax.tax_rate;
          }
          
          taxBreakdown.push({
            tax_type: tax.tax_type,
            tax_name: tax.tax_name,
            tax_rate: tax.tax_rate,
            taxable_amount: taxableAmount,
            tax_amount: Math.round(taxAmount * 100) / 100,
            is_inclusive: tax.is_inclusive
          });
          
          if (!tax.is_inclusive) {
            totalTax += taxAmount;
            if (tax.is_compound) {
              taxableAmount += taxAmount; // Tax on tax
            }
          }
        }
      }
      
      return {
        success: true,
        subtotal: amount,
        total_tax: Math.round(totalTax * 100) / 100,
        total_amount: Math.round((amount + totalTax) * 100) / 100,
        tax_breakdown: taxBreakdown,
        currency: currency,
        region: region
      };

    } catch (error) {
      console.error('❌ Error calculating global tax:', error);
      return {
        success: false,
        error: error.message,
        subtotal: amount,
        total_tax: 0,
        total_amount: amount
      };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async getGatewayStatus() {
    const status = [];
    
    for (const [code, gateway] of this.supportedGateways) {
      status.push({
        gateway_code: code,
        gateway_name: gateway.gateway_name,
        is_active: gateway.is_active,
        supported_regions: gateway.supported_regions,
        supported_currencies: gateway.supported_currencies,
        settlement_time: gateway.settlement_time_hours
      });
    }
    
    return {
      success: true,
      gateways: status,
      total_gateways: status.length,
      active_gateways: status.filter(g => g.is_active).length
    };
  }

  async getComplianceStatus(region) {
    const rules = [];
    
    for (const [key, rule] of this.regionCompliance) {
      if (key.startsWith(region)) {
        rules.push({
          regulation_type: rule.regulation_type,
          regulation_name: rule.regulation_name,
          data_retention_days: rule.data_retention_days,
          data_sovereignty_required: rule.data_sovereignty_required,
          effective_date: rule.effective_date
        });
      }
    }
    
    return {
      success: true,
      region: region,
      compliance_rules: rules,
      total_rules: rules.length
    };
  }
}

module.exports = GlobalPaymentService;
