const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting Super Admin Dashboard migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', '007_super_admin_dashboard_metrics.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    await client.query(migrationSQL);
    
    console.log('✅ Super Admin Dashboard migration completed successfully!');
    console.log('📊 Created tables:');
    console.log('   - system_metrics');
    console.log('   - system_activity_log');
    console.log('   - tenant_daily_analytics');
    console.log('   - system_performance_snapshots');
    console.log('   - api_usage_log');
    console.log('   - tenant_billing_history');
    console.log('   - system_alerts');
    console.log('📈 Created views and functions for real-time metrics');
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runMigration();
