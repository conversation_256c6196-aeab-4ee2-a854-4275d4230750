import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Toggle, 
  Check, 
  X, 
  Info, 
  Star, 
  Zap, 
  Shield, 
  Smartphone,
  Clock,
  Users,
  BarChart3,
  CreditCard,
  Truck,
  Calendar,
  Wine,
  Coffee,
  Music,
  Building
} from 'lucide-react';
import { BusinessType } from '../contexts/IndustryThemeContext';

interface FeatureConfig {
  id: string;
  name: string;
  description: string;
  category: 'core' | 'advanced' | 'integration' | 'analytics' | 'compliance';
  icon: React.ComponentType<any>;
  isEnabled: boolean;
  isPremium: boolean;
  dependencies?: string[];
  businessTypes: string[];
  configuration?: Record<string, any>;
}

interface IndustryFeatureManagerProps {
  businessType: BusinessType;
  onFeatureToggle: (featureId: string, enabled: boolean) => void;
  onConfigurationChange: (featureId: string, config: Record<string, any>) => void;
}

// Comprehensive feature definitions
const AVAILABLE_FEATURES: FeatureConfig[] = [
  // Core Features
  {
    id: 'order_management',
    name: 'Order Management',
    description: 'Basic order creation, modification, and tracking',
    category: 'core',
    icon: Settings,
    isEnabled: true,
    isPremium: false,
    businessTypes: ['FINE_DINING', 'QUICK_SERVICE', 'CAFE', 'BAR', 'FOOD_TRUCK', 'CATERING', 'HOTEL']
  },
  {
    id: 'payment_processing',
    name: 'Payment Processing',
    description: 'Credit card, cash, and digital payment handling',
    category: 'core',
    icon: CreditCard,
    isEnabled: true,
    isPremium: false,
    businessTypes: ['FINE_DINING', 'QUICK_SERVICE', 'CAFE', 'BAR', 'FOOD_TRUCK', 'CATERING', 'HOTEL']
  },
  {
    id: 'inventory_basic',
    name: 'Basic Inventory',
    description: 'Track stock levels and basic inventory management',
    category: 'core',
    icon: BarChart3,
    isEnabled: true,
    isPremium: false,
    businessTypes: ['FINE_DINING', 'QUICK_SERVICE', 'CAFE', 'BAR', 'FOOD_TRUCK', 'CATERING', 'HOTEL']
  },

  // Fine Dining Specific
  {
    id: 'wine_management',
    name: 'Wine Management',
    description: 'Comprehensive wine inventory, pairing suggestions, and cellar management',
    category: 'advanced',
    icon: Wine,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['FINE_DINING', 'HOTEL'],
    configuration: {
      cellarTracking: true,
      vintageManagement: true,
      pairingEngine: true,
      sommelierNotes: true
    }
  },
  {
    id: 'course_timing',
    name: 'Course Timing',
    description: 'Kitchen coordination for multi-course meal timing',
    category: 'advanced',
    icon: Clock,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['FINE_DINING', 'HOTEL'],
    dependencies: ['order_management']
  },
  {
    id: 'guest_profiles',
    name: 'Guest Profiles',
    description: 'Detailed customer preferences and dining history',
    category: 'advanced',
    icon: Users,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['FINE_DINING', 'HOTEL'],
    configuration: {
      dietaryRestrictions: true,
      visitHistory: true,
      preferences: true,
      specialOccasions: true
    }
  },

  // Quick Service Specific
  {
    id: 'order_queue',
    name: 'Order Queue Management',
    description: 'Visual order queue with completion timers and priority handling',
    category: 'advanced',
    icon: Clock,
    isEnabled: false,
    isPremium: false,
    businessTypes: ['QUICK_SERVICE', 'CAFE'],
    configuration: {
      queueDisplay: true,
      completionTimers: true,
      priorityOrders: true,
      customerNotifications: true
    }
  },
  {
    id: 'kitchen_display',
    name: 'Kitchen Display System',
    description: 'Real-time kitchen order management and preparation tracking',
    category: 'integration',
    icon: Settings,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['QUICK_SERVICE', 'CAFE', 'FINE_DINING'],
    dependencies: ['order_management']
  },
  {
    id: 'mobile_ordering',
    name: 'Mobile Ordering',
    description: 'Mobile app integration for online ordering and pickup',
    category: 'integration',
    icon: Smartphone,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['QUICK_SERVICE', 'CAFE', 'FOOD_TRUCK']
  },

  // Cafe Specific
  {
    id: 'beverage_customization',
    name: 'Beverage Customization',
    description: 'Advanced drink modification system with custom pricing',
    category: 'advanced',
    icon: Coffee,
    isEnabled: false,
    isPremium: false,
    businessTypes: ['CAFE'],
    configuration: {
      modifierMatrix: true,
      customPricing: true,
      recipeManagement: true,
      nutritionalInfo: true
    }
  },
  {
    id: 'subscription_service',
    name: 'Subscription Service',
    description: 'Coffee subscriptions and membership management',
    category: 'advanced',
    icon: Star,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['CAFE'],
    dependencies: ['payment_processing']
  },

  // Bar Specific
  {
    id: 'alcohol_tracking',
    name: 'Alcohol Tracking',
    description: 'Pour tracking, inventory monitoring, and compliance reporting',
    category: 'compliance',
    icon: Music,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['BAR', 'FINE_DINING', 'HOTEL'],
    configuration: {
      pourTracking: true,
      wasteMonitoring: true,
      complianceReporting: true,
      inventoryAlerts: true
    }
  },
  {
    id: 'age_verification',
    name: 'Age Verification',
    description: 'ID scanning and age verification workflow',
    category: 'compliance',
    icon: Shield,
    isEnabled: false,
    isPremium: false,
    businessTypes: ['BAR', 'FINE_DINING', 'HOTEL'],
    dependencies: ['alcohol_tracking']
  },
  {
    id: 'tab_management',
    name: 'Tab Management',
    description: 'Open tabs, group ordering, and automatic splitting',
    category: 'advanced',
    icon: Users,
    isEnabled: false,
    isPremium: false,
    businessTypes: ['BAR'],
    dependencies: ['payment_processing']
  },

  // Food Truck Specific
  {
    id: 'offline_capability',
    name: 'Offline Capability',
    description: 'Process orders and payments without internet connection',
    category: 'core',
    icon: Truck,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['FOOD_TRUCK'],
    configuration: {
      offlinePayments: true,
      syncWhenOnline: true,
      localStorage: true
    }
  },
  {
    id: 'location_services',
    name: 'Location Services',
    description: 'GPS tracking and customer location notifications',
    category: 'integration',
    icon: Truck,
    isEnabled: false,
    isPremium: false,
    businessTypes: ['FOOD_TRUCK'],
    dependencies: ['mobile_ordering']
  },

  // Catering Specific
  {
    id: 'event_planning',
    name: 'Event Planning',
    description: 'Timeline management, task scheduling, and event coordination',
    category: 'advanced',
    icon: Calendar,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['CATERING', 'HOTEL'],
    configuration: {
      timelineManagement: true,
      taskScheduling: true,
      staffAssignment: true,
      clientCommunication: true
    }
  },
  {
    id: 'custom_menus',
    name: 'Custom Menu Creation',
    description: 'Per-event menu customization and client approval workflow',
    category: 'advanced',
    icon: Settings,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['CATERING'],
    dependencies: ['event_planning']
  },

  // Hotel Specific
  {
    id: 'pms_integration',
    name: 'Hotel PMS Integration',
    description: 'Integration with hotel property management systems',
    category: 'integration',
    icon: Building,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['HOTEL'],
    configuration: {
      guestBilling: true,
      roomCharges: true,
      folioIntegration: true,
      checkInOut: true
    }
  },
  {
    id: 'room_service',
    name: 'Room Service',
    description: 'Room service ordering, delivery tracking, and guest communication',
    category: 'advanced',
    icon: Building,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['HOTEL'],
    dependencies: ['pms_integration']
  },
  {
    id: 'multi_language',
    name: 'Multi-Language Support',
    description: 'Multiple language interfaces and menu translations',
    category: 'advanced',
    icon: Users,
    isEnabled: false,
    isPremium: true,
    businessTypes: ['HOTEL', 'FINE_DINING']
  }
];

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'core': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'advanced': return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'integration': return 'bg-green-100 text-green-800 border-green-200';
    case 'analytics': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'compliance': return 'bg-red-100 text-red-800 border-red-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const IndustryFeatureManager: React.FC<IndustryFeatureManagerProps> = ({
  businessType,
  onFeatureToggle,
  onConfigurationChange
}) => {
  const [features, setFeatures] = useState<FeatureConfig[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Filter features based on business type
    const relevantFeatures = AVAILABLE_FEATURES.filter(feature =>
      feature.businessTypes.includes(businessType.code)
    );
    
    // Initialize with business type defaults
    const initializedFeatures = relevantFeatures.map(feature => ({
      ...feature,
      isEnabled: businessType.featureSet[feature.id] || feature.category === 'core'
    }));
    
    setFeatures(initializedFeatures);
  }, [businessType]);

  const handleFeatureToggle = (featureId: string) => {
    setFeatures(prev => prev.map(feature => {
      if (feature.id === featureId) {
        const newEnabled = !feature.isEnabled;
        onFeatureToggle(featureId, newEnabled);
        return { ...feature, isEnabled: newEnabled };
      }
      return feature;
    }));
  };

  const filteredFeatures = features.filter(feature => {
    const matchesCategory = selectedCategory === 'all' || feature.category === selectedCategory;
    const matchesSearch = feature.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         feature.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const categories = ['all', 'core', 'advanced', 'integration', 'analytics', 'compliance'];
  const enabledCount = features.filter(f => f.isEnabled).length;
  const premiumCount = features.filter(f => f.isEnabled && f.isPremium).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Feature Management</h2>
            <p className="text-gray-600">Configure features for your {businessType.name}</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{enabledCount}</div>
              <div className="text-sm text-gray-600">Enabled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{premiumCount}</div>
              <div className="text-sm text-gray-600">Premium</div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search features..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex space-x-2">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFeatures.map(feature => {
          const IconComponent = feature.icon;
          const canToggle = feature.category !== 'core';
          
          return (
            <div
              key={feature.id}
              className={`bg-white rounded-xl shadow-sm border-2 p-6 transition-all duration-200 ${
                feature.isEnabled
                  ? 'border-blue-200 bg-blue-50/50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    feature.isEnabled ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.name}</h3>
                    {feature.isPremium && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                        <Star className="w-3 h-3 mr-1" />
                        Premium
                      </span>
                    )}
                  </div>
                </div>
                
                {canToggle && (
                  <button
                    onClick={() => handleFeatureToggle(feature.id)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      feature.isEnabled ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        feature.isEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                )}
              </div>

              <p className="text-gray-600 text-sm mb-4">{feature.description}</p>

              <div className="flex items-center justify-between">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getCategoryColor(feature.category)}`}>
                  {feature.category}
                </span>
                
                {feature.isEnabled ? (
                  <div className="flex items-center text-green-600">
                    <Check className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">Active</span>
                  </div>
                ) : (
                  <div className="flex items-center text-gray-400">
                    <X className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">Disabled</span>
                  </div>
                )}
              </div>

              {feature.dependencies && feature.dependencies.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex items-center text-xs text-gray-500">
                    <Info className="w-3 h-3 mr-1" />
                    <span>Requires: {feature.dependencies.join(', ')}</span>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {filteredFeatures.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Settings className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No features found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria</p>
        </div>
      )}
    </div>
  );
};

export default IndustryFeatureManager;
