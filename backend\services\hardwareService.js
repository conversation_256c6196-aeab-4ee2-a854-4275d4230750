// Hardware Service for Phase 4
// Comprehensive hardware device integration for POS systems

const { Pool } = require('pg');
const EventEmitter = require('events');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class HardwareService extends EventEmitter {
  constructor() {
    super();
    this.devices = new Map();
    this.deviceDrivers = new Map();
    this.heartbeatInterval = 30000; // 30 seconds
    this.initializeDrivers();
    this.startHeartbeatMonitoring();
  }

  // =====================================================
  // DEVICE MANAGEMENT
  // =====================================================

  async registerDevice(deviceConfig) {
    try {
      const client = await pool.connect();
      
      // Validate device configuration
      const validation = this.validateDeviceConfig(deviceConfig);
      if (!validation.valid) {
        throw new Error(`Device validation failed: ${validation.error}`);
      }

      // Insert device into database
      const insertQuery = `
        INSERT INTO hardware_devices (
          tenant_id, location_id, device_type, device_name,
          manufacturer, model, serial_number, firmware_version,
          connection_type, connection_string, ip_address, port,
          configuration, capabilities, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        RETURNING id
      `;
      
      const result = await client.query(insertQuery, [
        deviceConfig.tenant_id,
        deviceConfig.location_id,
        deviceConfig.device_type,
        deviceConfig.device_name,
        deviceConfig.manufacturer || 'Unknown',
        deviceConfig.model || 'Unknown',
        deviceConfig.serial_number || null,
        deviceConfig.firmware_version || null,
        deviceConfig.connection_type,
        deviceConfig.connection_string,
        deviceConfig.ip_address || null,
        deviceConfig.port || null,
        JSON.stringify(deviceConfig.configuration || {}),
        JSON.stringify(deviceConfig.capabilities || {}),
        true
      ]);
      
      const deviceId = result.rows[0].id;
      client.release();

      // Initialize device connection
      const device = await this.initializeDevice(deviceId, deviceConfig);
      this.devices.set(deviceId, device);

      // Log device registration
      await this.logDeviceEvent(deviceId, 'INFO', 'device_registered', 'Device successfully registered');

      return {
        success: true,
        device_id: deviceId,
        device: device
      };

    } catch (error) {
      console.error('❌ Error registering device:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async getDevices(tenantId, locationId = null, deviceType = null) {
    try {
      const client = await pool.connect();
      
      let query = `
        SELECT 
          hd.*,
          CASE 
            WHEN hd.last_heartbeat > NOW() - INTERVAL '2 minutes' THEN true
            ELSE false
          END as is_online,
          CASE 
            WHEN hd.error_count > 5 THEN 'error'
            WHEN hd.is_connected THEN 'connected'
            WHEN hd.is_active THEN 'disconnected'
            ELSE 'inactive'
          END as status
        FROM hardware_devices hd
        WHERE hd.tenant_id = $1
      `;
      
      const params = [tenantId];
      let paramIndex = 2;
      
      if (locationId) {
        query += ` AND hd.location_id = $${paramIndex}`;
        params.push(locationId);
        paramIndex++;
      }
      
      if (deviceType) {
        query += ` AND hd.device_type = $${paramIndex}`;
        params.push(deviceType);
      }
      
      query += ` ORDER BY hd.device_type, hd.device_name`;
      
      const result = await client.query(query, params);
      client.release();
      
      return {
        success: true,
        devices: result.rows,
        count: result.rows.length
      };

    } catch (error) {
      console.error('❌ Error fetching devices:', error);
      return {
        success: false,
        error: error.message,
        devices: []
      };
    }
  }

  // =====================================================
  // RECEIPT PRINTER OPERATIONS
  // =====================================================

  async printReceipt(deviceId, receiptData, options = {}) {
    try {
      const device = this.devices.get(deviceId);
      if (!device || device.device_type !== 'receipt_printer') {
        throw new Error('Receipt printer not found or invalid device type');
      }

      // Check device status
      if (!device.is_connected) {
        throw new Error('Receipt printer is not connected');
      }

      const startTime = Date.now();
      
      // Generate ESC/POS commands
      const escPosCommands = this.generateESCPOSCommands(receiptData, options);
      
      // Send print job to device
      const printResult = await this.sendDeviceCommand(deviceId, 'print', {
        commands: escPosCommands,
        copies: options.copies || 1,
        cut_paper: options.cut_paper !== false
      });

      const processingTime = Date.now() - startTime;

      // Log print job
      await this.logDeviceEvent(deviceId, 'INFO', 'print_job', 
        `Receipt printed successfully in ${processingTime}ms`, {
          receipt_id: receiptData.receipt_number,
          processing_time: processingTime,
          copies: options.copies || 1
        });

      // Update device statistics
      await this.updateDeviceStats(deviceId, 'print_job', true, processingTime);

      return {
        success: true,
        processing_time: processingTime,
        job_id: `print_${Date.now()}`,
        device_response: printResult
      };

    } catch (error) {
      console.error('❌ Print error:', error);
      
      // Log error
      await this.logDeviceEvent(deviceId, 'ERROR', 'print_error', error.message);
      await this.updateDeviceStats(deviceId, 'print_job', false);

      return {
        success: false,
        error: error.message
      };
    }
  }

  generateESCPOSCommands(receiptData, options = {}) {
    // ESC/POS command generation for thermal printers
    const commands = [];
    
    // Initialize printer
    commands.push('\x1B\x40'); // ESC @ - Initialize printer
    
    // Set character set
    commands.push('\x1B\x74\x00'); // ESC t 0 - Select character set
    
    // Header
    if (receiptData.header) {
      commands.push('\x1B\x61\x01'); // ESC a 1 - Center alignment
      commands.push('\x1B\x21\x30'); // ESC ! 48 - Double height and width
      commands.push(receiptData.header);
      commands.push('\n\n');
      commands.push('\x1B\x21\x00'); // ESC ! 0 - Normal text
      commands.push('\x1B\x61\x00'); // ESC a 0 - Left alignment
    }
    
    // Business info
    if (receiptData.business_info) {
      commands.push(receiptData.business_info.name || '');
      commands.push('\n');
      if (receiptData.business_info.address) {
        commands.push(receiptData.business_info.address);
        commands.push('\n');
      }
      if (receiptData.business_info.phone) {
        commands.push(receiptData.business_info.phone);
        commands.push('\n');
      }
      commands.push('\n');
    }
    
    // Receipt details
    commands.push(`Receipt #: ${receiptData.receipt_number}\n`);
    commands.push(`Date: ${new Date().toLocaleString()}\n`);
    if (receiptData.server_name) {
      commands.push(`Server: ${receiptData.server_name}\n`);
    }
    if (receiptData.table_number) {
      commands.push(`Table: ${receiptData.table_number}\n`);
    }
    commands.push('\n');
    
    // Separator line
    commands.push('--------------------------------\n');
    
    // Items
    if (receiptData.items && receiptData.items.length > 0) {
      receiptData.items.forEach(item => {
        const itemLine = `${item.name} x${item.quantity}`;
        const price = `$${item.total.toFixed(2)}`;
        const padding = 32 - itemLine.length - price.length;
        commands.push(itemLine + ' '.repeat(Math.max(1, padding)) + price + '\n');
      });
    }
    
    commands.push('--------------------------------\n');
    
    // Totals
    if (receiptData.subtotal) {
      commands.push(`Subtotal:${' '.repeat(19)}$${receiptData.subtotal.toFixed(2)}\n`);
    }
    if (receiptData.tax) {
      commands.push(`Tax:${' '.repeat(24)}$${receiptData.tax.toFixed(2)}\n`);
    }
    if (receiptData.tip) {
      commands.push(`Tip:${' '.repeat(24)}$${receiptData.tip.toFixed(2)}\n`);
    }
    commands.push(`Total:${' '.repeat(22)}$${receiptData.total.toFixed(2)}\n`);
    
    commands.push('\n');
    commands.push(`Payment: ${receiptData.payment_method}\n`);
    
    // Footer
    if (receiptData.footer) {
      commands.push('\n');
      commands.push('\x1B\x61\x01'); // Center alignment
      commands.push(receiptData.footer);
      commands.push('\x1B\x61\x00'); // Left alignment
    }
    
    commands.push('\n\n\n');
    
    // Cut paper if requested
    if (options.cut_paper !== false) {
      commands.push('\x1D\x56\x41'); // GS V A - Cut paper
    }
    
    return commands.join('');
  }

  // =====================================================
  // BARCODE SCANNER OPERATIONS
  // =====================================================

  async scanBarcode(deviceId, timeout = 10000) {
    try {
      let device = this.devices.get(deviceId);

      // If device not in memory, try to load from database
      if (!device) {
        const dbDevice = await this.getDeviceFromDatabase(deviceId);
        if (dbDevice && dbDevice.device_type === 'barcode_scanner') {
          device = dbDevice;
          this.devices.set(deviceId, device);
        }
      }

      if (!device) {
        throw new Error('Barcode scanner not found');
      }

      // For testing, allow any device type to simulate scanning
      if (device.device_type !== 'barcode_scanner') {
        console.log(`⚠️ Warning: Using ${device.device_type} for barcode scanning test`);
      }

      if (!device.is_connected) {
        throw new Error('Barcode scanner is not connected');
      }

      const startTime = Date.now();
      
      // Send scan command to device
      const scanResult = await this.sendDeviceCommand(deviceId, 'scan', {
        timeout: timeout,
        scan_mode: 'single'
      });

      const processingTime = Date.now() - startTime;

      // Log scan event
      await this.logDeviceEvent(deviceId, 'INFO', 'scan', 
        `Barcode scanned successfully in ${processingTime}ms`, {
          barcode: scanResult.barcode,
          processing_time: processingTime
        });

      return {
        success: true,
        barcode: scanResult.barcode,
        barcode_type: scanResult.barcode_type || 'unknown',
        processing_time: processingTime
      };

    } catch (error) {
      console.error('❌ Scan error:', error);
      
      await this.logDeviceEvent(deviceId, 'ERROR', 'scan_error', error.message);

      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // CASH DRAWER OPERATIONS
  // =====================================================

  async openCashDrawer(deviceId, reason = 'manual') {
    try {
      let device = this.devices.get(deviceId);

      // If device not in memory, try to load from database
      if (!device) {
        const dbDevice = await this.getDeviceFromDatabase(deviceId);
        if (dbDevice) {
          device = dbDevice;
          this.devices.set(deviceId, device);
        }
      }

      if (!device) {
        throw new Error('Cash drawer not found');
      }

      // For testing, allow any device type to simulate cash drawer
      if (device.device_type !== 'cash_drawer') {
        console.log(`⚠️ Warning: Using ${device.device_type} for cash drawer test`);
      }

      if (!device.is_connected) {
        throw new Error('Cash drawer is not connected');
      }

      // Send open command to device
      const openResult = await this.sendDeviceCommand(deviceId, 'open', {
        reason: reason,
        timestamp: new Date().toISOString()
      });

      // Log drawer open event
      await this.logDeviceEvent(deviceId, 'INFO', 'drawer_open', 
        `Cash drawer opened: ${reason}`, {
          reason: reason,
          opened_by: openResult.employee_id
        });

      return {
        success: true,
        opened_at: new Date().toISOString(),
        reason: reason
      };

    } catch (error) {
      console.error('❌ Cash drawer error:', error);
      
      await this.logDeviceEvent(deviceId, 'ERROR', 'drawer_error', error.message);

      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // DEVICE COMMUNICATION
  // =====================================================

  async sendDeviceCommand(deviceId, command, data = {}) {
    const device = this.devices.get(deviceId);
    if (!device) {
      throw new Error('Device not found');
    }

    // Mock device communication - replace with actual device drivers
    console.log(`📡 Sending command '${command}' to device ${deviceId}:`, data);
    
    // Simulate device response time
    await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100));
    
    // Mock responses based on command type
    switch (command) {
      case 'print':
        return {
          status: 'success',
          job_id: `print_${Date.now()}`,
          pages_printed: 1
        };
      
      case 'scan':
        // Mock barcode scan (90% success rate)
        if (Math.random() > 0.1) {
          return {
            barcode: `${Math.random().toString().substr(2, 12)}`,
            barcode_type: 'CODE128',
            scan_time: Date.now()
          };
        } else {
          throw new Error('Scan timeout - no barcode detected');
        }
      
      case 'open':
        return {
          status: 'opened',
          opened_at: new Date().toISOString()
        };
      
      default:
        throw new Error(`Unknown command: ${command}`);
    }
  }

  // =====================================================
  // DEVICE MONITORING
  // =====================================================

  startHeartbeatMonitoring() {
    setInterval(async () => {
      for (const [deviceId, device] of this.devices) {
        try {
          await this.checkDeviceHeartbeat(deviceId);
        } catch (error) {
          console.error(`❌ Heartbeat check failed for device ${deviceId}:`, error);
        }
      }
    }, this.heartbeatInterval);
  }

  async checkDeviceHeartbeat(deviceId) {
    try {
      const client = await pool.connect();
      
      // Update last heartbeat
      await client.query(
        'UPDATE hardware_devices SET last_heartbeat = NOW() WHERE id = $1',
        [deviceId]
      );
      
      client.release();
      
      // Emit heartbeat event
      this.emit('device_heartbeat', { device_id: deviceId, timestamp: new Date() });
      
    } catch (error) {
      console.error(`❌ Heartbeat update failed for device ${deviceId}:`, error);
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  validateDeviceConfig(config) {
    if (!config.tenant_id) {
      return { valid: false, error: 'Tenant ID required' };
    }
    
    if (!config.device_type) {
      return { valid: false, error: 'Device type required' };
    }
    
    if (!config.device_name) {
      return { valid: false, error: 'Device name required' };
    }
    
    if (!config.connection_type) {
      return { valid: false, error: 'Connection type required' };
    }
    
    const validDeviceTypes = ['receipt_printer', 'barcode_scanner', 'cash_drawer', 'card_reader', 'kitchen_display'];
    if (!validDeviceTypes.includes(config.device_type)) {
      return { valid: false, error: 'Invalid device type' };
    }
    
    const validConnectionTypes = ['usb', 'network', 'bluetooth', 'serial', 'wifi'];
    if (!validConnectionTypes.includes(config.connection_type)) {
      return { valid: false, error: 'Invalid connection type' };
    }
    
    return { valid: true };
  }

  async initializeDevice(deviceId, config) {
    // Mock device initialization
    return {
      id: deviceId,
      device_type: config.device_type,
      device_name: config.device_name,
      is_connected: true,
      last_connected: new Date(),
      capabilities: config.capabilities || {}
    };
  }

  initializeDrivers() {
    // Initialize device drivers - placeholder for actual driver implementations
    this.deviceDrivers.set('receipt_printer', {
      connect: async (config) => ({ connected: true }),
      disconnect: async () => ({ disconnected: true }),
      print: async (data) => ({ printed: true })
    });
    
    this.deviceDrivers.set('barcode_scanner', {
      connect: async (config) => ({ connected: true }),
      disconnect: async () => ({ disconnected: true }),
      scan: async (timeout) => ({ scanned: true })
    });
    
    this.deviceDrivers.set('cash_drawer', {
      connect: async (config) => ({ connected: true }),
      disconnect: async () => ({ disconnected: true }),
      open: async () => ({ opened: true })
    });
  }

  async logDeviceEvent(deviceId, level, eventType, message, details = {}) {
    try {
      const client = await pool.connect();
      
      await client.query(`
        INSERT INTO hardware_device_logs (
          device_id, log_level, event_type, message, details
        ) VALUES ($1, $2, $3, $4, $5)
      `, [deviceId, level, eventType, message, JSON.stringify(details)]);
      
      client.release();
    } catch (error) {
      console.error('❌ Error logging device event:', error);
    }
  }

  async updateDeviceStats(deviceId, operation, success, processingTime = null) {
    try {
      const client = await pool.connect();

      let updateQuery = `
        UPDATE hardware_devices
        SET
          total_jobs_processed = total_jobs_processed + 1,
          error_count = CASE WHEN $2 THEN error_count ELSE error_count + 1 END,
          updated_at = NOW()
        WHERE id = $1
      `;

      await client.query(updateQuery, [deviceId, success]);
      client.release();
    } catch (error) {
      console.error('❌ Error updating device stats:', error);
    }
  }

  async getDeviceFromDatabase(deviceId) {
    try {
      const client = await pool.connect();

      const result = await client.query(`
        SELECT
          hd.*,
          CASE
            WHEN hd.last_heartbeat > NOW() - INTERVAL '2 minutes' THEN true
            ELSE false
          END as is_online,
          CASE
            WHEN hd.error_count > 5 THEN 'error'
            WHEN hd.is_connected THEN 'connected'
            WHEN hd.is_active THEN 'disconnected'
            ELSE 'inactive'
          END as status
        FROM hardware_devices hd
        WHERE hd.id = $1
      `, [deviceId]);

      client.release();

      return result.rows[0] || null;
    } catch (error) {
      console.error('❌ Error fetching device from database:', error);
      return null;
    }
  }
}

module.exports = HardwareService;
