import React from 'react';
import { <PERSON>, Moon, Monitor } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { Button } from './button';

// Safe theme hook with fallback
function useSafeTheme() {
  try {
    return useTheme();
  } catch (error) {
    console.warn('ThemeProvider not found, using default theme');
    return { theme: 'light' as const, toggleTheme: () => {}, setTheme: () => {} };
  }
}

export function ThemeToggle() {
  const { theme, toggleTheme } = useSafeTheme();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="relative h-9 w-9 rounded-full transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
    >
      <div className="relative flex items-center justify-center">
        {/* Light mode icon */}
        <Sun 
          className={`h-4 w-4 transition-all duration-300 ${
            theme === 'light' 
              ? 'rotate-0 scale-100 opacity-100' 
              : 'rotate-90 scale-0 opacity-0'
          } absolute`}
        />
        
        {/* Dark mode icon */}
        <Moon 
          className={`h-4 w-4 transition-all duration-300 ${
            theme === 'dark' 
              ? 'rotate-0 scale-100 opacity-100' 
              : '-rotate-90 scale-0 opacity-0'
          } absolute`}
        />
      </div>
    </Button>
  );
}

export function ThemeToggleAdvanced() {
  const { theme, setTheme } = useSafeTheme();

  const themes = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'dark', icon: Moon, label: 'Dark' },
  ] as const;

  return (
    <div className="flex items-center space-x-1 rounded-lg bg-gray-100 dark:bg-gray-800 p-1">
      {themes.map(({ value, icon: Icon, label }) => (
        <button
          key={value}
          onClick={() => setTheme(value)}
          className={`
            relative flex items-center justify-center rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200
            ${theme === value
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-700/50'
            }
          `}
          aria-label={`Switch to ${label} theme`}
        >
          <Icon className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">{label}</span>
        </button>
      ))}
    </div>
  );
}

export function ThemeToggleDropdown() {
  const { theme, setTheme } = useSafeTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  const themes = [
    { value: 'light', icon: Sun, label: 'Light', description: 'Clean and bright interface' },
    { value: 'dark', icon: Moon, label: 'Dark', description: 'Easy on the eyes' },
    { value: 'system', icon: Monitor, label: 'System', description: 'Follow system preference' },
  ] as const;

  const currentTheme = themes.find(t => t.value === theme) || themes[0];

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-9 w-9 rounded-full"
        aria-label="Theme options"
      >
        <currentTheme.icon className="h-4 w-4" />
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full z-50 mt-2 w-56 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5">
            <div className="p-1">
              {themes.map(({ value, icon: Icon, label, description }) => (
                <button
                  key={value}
                  onClick={() => {
                    if (value !== 'system') {
                      setTheme(value);
                    }
                    setIsOpen(false);
                  }}
                  disabled={value === 'system'}
                  className={`
                    w-full flex items-start px-3 py-2 text-left rounded-md transition-colors duration-150
                    ${theme === value
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }
                    ${value === 'system' ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  <Icon className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <div className="text-sm font-medium">{label}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{description}</div>
                  </div>
                  {theme === value && (
                    <div className="ml-auto">
                      <div className="h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// Compact theme toggle for mobile
export function ThemeToggleCompact() {
  const { theme, toggleTheme } = useSafeTheme();

  return (
    <button
      onClick={toggleTheme}
      className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-all duration-200"
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
    </button>
  );
}

// Theme status indicator
export function ThemeIndicator() {
  const { theme } = useSafeTheme();

  return (
    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
      <div className={`w-2 h-2 rounded-full ${
        theme === 'light' ? 'bg-yellow-400' : 'bg-blue-400'
      }`} />
      <span className="capitalize">{theme} mode</span>
    </div>
  );
}
