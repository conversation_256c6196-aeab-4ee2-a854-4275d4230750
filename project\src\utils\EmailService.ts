// Email Service for Authentication and Notifications
// Handles email notifications for login events, security alerts, and user management

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface EmailNotification {
  to: string;
  template: string;
  data: Record<string, any>;
  priority?: 'high' | 'normal' | 'low';
}

interface LoginEvent {
  employeeId: number;
  employeeName: string;
  employeeEmail: string;
  tenantName: string;
  loginTime: string;
  ipAddress: string;
  userAgent: string;
  loginType: 'employee' | 'admin';
  success: boolean;
}

class EmailService {
  private static instance: EmailService;
  private baseUrl = 'http://localhost:4000';

  private constructor() {}

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  // Send login notification email
  async sendLoginNotification(event: LoginEvent): Promise<boolean> {
    try {
      const template = event.success ? 'login_success' : 'login_failed';
      
      const emailData = {
        to: event.employeeEmail,
        template: template,
        data: {
          employeeName: event.employeeName,
          tenantName: event.tenantName,
          loginTime: new Date(event.loginTime).toLocaleString(),
          ipAddress: event.ipAddress,
          userAgent: event.userAgent.substring(0, 100),
          loginType: event.loginType === 'admin' ? 'Administrator' : 'Employee',
          dashboardUrl: event.loginType === 'admin' 
            ? 'http://localhost:5173/super-admin.html'
            : 'http://localhost:5173/pos.html'
        },
        priority: event.success ? 'normal' : 'high'
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send login notification:', error);
      return false;
    }
  }

  // Send security alert email
  async sendSecurityAlert(alert: {
    type: 'failed_login' | 'suspicious_activity' | 'account_locked';
    employeeEmail: string;
    employeeName: string;
    tenantName: string;
    details: string;
    timestamp: string;
  }): Promise<boolean> {
    try {
      const emailData = {
        to: alert.employeeEmail,
        template: 'security_alert',
        data: {
          alertType: alert.type.replace('_', ' ').toUpperCase(),
          employeeName: alert.employeeName,
          tenantName: alert.tenantName,
          details: alert.details,
          timestamp: new Date(alert.timestamp).toLocaleString(),
          supportEmail: '<EMAIL>',
          securityUrl: 'http://localhost:5173/security'
        },
        priority: 'high'
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send security alert:', error);
      return false;
    }
  }

  // Send welcome email for new employees
  async sendWelcomeEmail(employee: {
    name: string;
    email: string;
    role: string;
    tenantName: string;
    temporaryPin: string;
    tenantSlug: string;
  }): Promise<boolean> {
    try {
      const emailData = {
        to: employee.email,
        template: 'welcome_employee',
        data: {
          employeeName: employee.name,
          tenantName: employee.tenantName,
          role: employee.role,
          temporaryPin: employee.temporaryPin,
          loginUrl: `http://localhost:5173/login?tenant=${employee.tenantSlug}`,
          supportEmail: '<EMAIL>',
          changePasswordUrl: 'http://localhost:5173/change-pin'
        },
        priority: 'normal'
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      return false;
    }
  }

  // Send PIN reset email
  async sendPinResetEmail(employee: {
    name: string;
    email: string;
    tenantName: string;
    newPin: string;
    resetBy: string;
  }): Promise<boolean> {
    try {
      const emailData = {
        to: employee.email,
        template: 'pin_reset',
        data: {
          employeeName: employee.name,
          tenantName: employee.tenantName,
          newPin: employee.newPin,
          resetBy: employee.resetBy,
          resetTime: new Date().toLocaleString(),
          loginUrl: 'http://localhost:5173/login',
          supportEmail: '<EMAIL>'
        },
        priority: 'high'
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send PIN reset email:', error);
      return false;
    }
  }

  // Send tenant activation email
  async sendTenantActivationEmail(tenant: {
    businessName: string;
    adminEmail: string;
    adminName: string;
    tenantSlug: string;
    adminPin: string;
  }): Promise<boolean> {
    try {
      const emailData = {
        to: tenant.adminEmail,
        template: 'tenant_activation',
        data: {
          businessName: tenant.businessName,
          adminName: tenant.adminName,
          tenantSlug: tenant.tenantSlug,
          adminPin: tenant.adminPin,
          loginUrl: `http://localhost:5173/login?tenant=${tenant.tenantSlug}`,
          adminUrl: 'http://localhost:5173/admin',
          supportEmail: '<EMAIL>',
          documentationUrl: 'http://localhost:5173/docs'
        },
        priority: 'high'
      };

      return await this.sendEmail(emailData);
    } catch (error) {
      console.error('Failed to send tenant activation email:', error);
      return false;
    }
  }

  // Generic email sending method
  private async sendEmail(emailData: EmailNotification): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/email/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(emailData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Email sent successfully: ${emailData.template} to ${emailData.to}`);
        return true;
      } else {
        const error = await response.json();
        console.error(`❌ Email sending failed: ${error.message}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Email service error:', error);
      return false;
    }
  }

  // Get email templates
  async getEmailTemplates(): Promise<Record<string, EmailTemplate>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/email/templates`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to fetch email templates');
        return {};
      }
    } catch (error) {
      console.error('Error fetching email templates:', error);
      return {};
    }
  }

  // Test email configuration
  async testEmailConfiguration(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/email/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Email configuration test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();

// Export types
export type { EmailTemplate, EmailNotification, LoginEvent };

// Utility functions
export const sendLoginNotification = (event: LoginEvent) => emailService.sendLoginNotification(event);
export const sendSecurityAlert = (alert: any) => emailService.sendSecurityAlert(alert);
export const sendWelcomeEmail = (employee: any) => emailService.sendWelcomeEmail(employee);
export const sendPinResetEmail = (employee: any) => emailService.sendPinResetEmail(employee);
export const sendTenantActivationEmail = (tenant: any) => emailService.sendTenantActivationEmail(tenant);
