import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Users, Plus, Edit, Trash2, Calendar, Clock, RefreshCw } from 'lucide-react';

interface Employee {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  hire_date: string;
}

const UnifiedStaffScheduling: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);

  // Load employees
  useEffect(() => {
    const loadEmployees = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('👥 Loading employees...');
        
        const response = await apiCall('/api/employees');
        if (response.ok) {
          const data = await response.json();
          setEmployees(data);
          console.log('✅ Employees loaded:', data.length);
        }
      } catch (error) {
        console.error('❌ Error loading employees:', error);
        setError('Failed to load employees. Using mock data.');
        
        // Fallback to mock data
        const mockEmployees = [
          {
            id: '1',
            name: 'Super Admin',
            role: 'super_admin',
            email: '<EMAIL>',
            phone: '555-0001',
            status: 'active' as const,
            hire_date: '2024-01-01'
          },
          {
            id: '2',
            name: 'Manager Smith',
            role: 'manager',
            email: '<EMAIL>',
            phone: '555-0002',
            status: 'active' as const,
            hire_date: '2024-01-15'
          },
          {
            id: '3',
            name: 'Employee Jones',
            role: 'employee',
            email: '<EMAIL>',
            phone: '555-0003',
            status: 'active' as const,
            hire_date: '2024-02-01'
          }
        ];
        setEmployees(mockEmployees);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadEmployees();
  }, [apiCall]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin': return 'bg-purple-100 text-purple-800';
      case 'manager': return 'bg-blue-100 text-blue-800';
      case 'employee': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleLabel = (role: string) => {
    return role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading staff...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Staff Management</h2>
            <p className="text-sm text-gray-500">Manage employees and scheduling</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Employee</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Staff</p>
                <p className="text-2xl font-bold text-gray-900">{employees.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Active</p>
                <p className="text-2xl font-bold text-green-600">
                  {employees.filter(e => e.status === 'active').length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">On Duty</p>
                <p className="text-2xl font-bold text-blue-600">
                  {employees.filter(e => e.status === 'active').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Employee List */}
      <div className="flex-1 overflow-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Employee</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Role</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Contact</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Hire Date</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {employees.map((employee) => (
              <tr key={employee.id} className="hover:bg-gray-50">
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {employee.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div className="ml-3">
                      <div className="font-medium text-gray-900">{employee.name}</div>
                      <div className="text-sm text-gray-500">ID: {employee.id}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(employee.role)}`}>
                    {getRoleLabel(employee.role)}
                  </span>
                </td>
                <td className="px-4 py-3">
                  <div className="text-sm text-gray-900">{employee.email}</div>
                  <div className="text-sm text-gray-500">{employee.phone}</div>
                </td>
                <td className="px-4 py-3 text-sm text-gray-900">
                  {new Date(employee.hire_date).toLocaleDateString()}
                </td>
                <td className="px-4 py-3">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    employee.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {employee.status}
                  </span>
                </td>
                <td className="px-4 py-3">
                  <div className="flex space-x-2">
                    <button
                      className="text-blue-600 hover:text-blue-800 text-sm"
                      title="Edit Employee"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      className="text-red-600 hover:text-red-800 text-sm"
                      title="Remove Employee"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Schedule Section */}
      <div className="bg-gray-50 border-t border-gray-200 p-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Weekly Schedule</h3>
          <p className="text-sm text-gray-500 mb-4">Schedule management coming soon</p>
          <div className="grid grid-cols-7 gap-2">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
              <div key={day} className="bg-white p-2 rounded border border-gray-200 text-center">
                <div className="text-xs font-medium text-gray-500">{day}</div>
                <div className="text-sm text-gray-900 mt-1">
                  {employees.filter(e => e.status === 'active').length} staff
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 p-3">
        <p className="text-gray-600 text-sm text-center">
          {employees.length} employees • {employees.filter(e => e.status === 'active').length} active
        </p>
      </div>
    </div>
  );
};

export default UnifiedStaffScheduling;
