const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

console.log('🚀 Starting test server...');

const app = express();
const PORT = 4000;
const JWT_SECRET = 'dev-secret-key-2024';

// Database connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Test database connection
pool.connect((err, client, release) => {
  if (err) {
    console.log('💥 Database connection failed:', err.message);
    console.log('⚠️ Running in mock mode without database');
  } else {
    console.log('✅ Database connected successfully');
    release();
  }
});

// Enable CORS for all origins in development
app.use(cors({
  origin: true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`${timestamp} - ${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
};

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0-test',
    message: 'Test backend server is running successfully!'
  });
});

// Simple login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { pin, tenant } = req.body;
    
    console.log(`🔐 Login attempt - PIN: ${pin?.length || 0} digits, Tenant: ${tenant || 'auto-detect'}`);

    if (!pin || pin.length < 4) {
      console.log('❌ Invalid PIN format');
      return res.status(400).json({ error: 'PIN must be at least 4 digits' });
    }

    // Simple authentication for testing
    let user = null;
    if (pin === '888888') {
      user = {
        id: '1',
        name: 'Enhanced Admin',
        role: 'super_admin',
        tenant_id: '1',
        permissions: ['all']
      };
    } else if (pin === '123456') {
      user = {
        id: '2',
        name: 'Main Admin',
        role: 'super_admin',
        tenant_id: '1',
        permissions: ['all']
      };
    } else {
      console.log(`❌ Invalid PIN: ${pin}`);
      return res.status(401).json({ error: 'Invalid PIN' });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        employeeId: user.id,
        tenantId: user.tenant_id,
        role: user.role,
        permissions: user.permissions
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log(`✅ Login successful - ${user.name} (${user.role})`);

    res.json({
      token,
      user: {
        id: user.id,
        name: user.name,
        role: user.role,
        permissions: user.permissions
      },
      tenant: {
        id: '1',
        name: 'Demo Restaurant',
        slug: 'demo-restaurant'
      }
    });
    
  } catch (error) {
    console.error('💥 Login error:', error);
    res.status(500).json({ error: 'Internal server error during login' });
  }
});

// Admin endpoints
app.get('/api/admin/metrics', authenticateToken, async (req, res) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Access denied: Super admin privileges required' });
  }

  console.log('📊 Admin metrics requested');
  
  const metrics = {
    totalTenants: 2,
    activeTenants: 2,
    totalRevenue: 45678.90,
    systemUptime: 99.8,
    activeUsers: 12,
    transactionsToday: 156,
    responseTime: 245,
    errorRate: 0.02
  };

  res.json(metrics);
});

app.get('/api/admin/tenants', authenticateToken, async (req, res) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Access denied: Super admin privileges required' });
  }

  console.log('🏢 Admin tenants requested');
  
  const tenants = [
    {
      id: '1',
      name: 'Demo Restaurant',
      slug: 'demo-restaurant',
      email: '<EMAIL>',
      phone: '555-0123',
      address: '123 Main St',
      status: 'active',
      employeeCount: 5,
      locationCount: 1,
      totalRevenue: 25000,
      orderCount: 450,
      lastActivity: new Date().toISOString(),
      planType: 'starter',
      subscriptionStatus: 'active'
    }
  ];

  res.json(tenants);
});

app.get('/api/admin/analytics', authenticateToken, (req, res) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Access denied: Super admin privileges required' });
  }

  console.log('📈 Admin analytics requested');
  
  const analytics = {
    revenueTrends: [
      { date: '2024-01-01', revenue: 12000 },
      { date: '2024-01-02', revenue: 15000 },
      { date: '2024-01-03', revenue: 18000 },
      { date: '2024-01-04', revenue: 22000 },
      { date: '2024-01-05', revenue: 25000 }
    ],
    topTenants: [
      { name: 'Demo Restaurant', revenue: 25000, growth: 15.5 },
      { name: 'Test Cafe', revenue: 18000, growth: 8.2 }
    ],
    performanceMetrics: [
      { metric: 'Response Time', value: 245, unit: 'ms' },
      { metric: 'Uptime', value: 99.8, unit: '%' },
      { metric: 'Error Rate', value: 0.02, unit: '%' }
    ]
  };

  res.json(analytics);
});

app.get('/api/admin/activity', authenticateToken, (req, res) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({ error: 'Access denied: Super admin privileges required' });
  }

  console.log('📋 Admin activity requested');
  
  const activities = [
    {
      id: '1',
      action: 'New tenant registered',
      tenant: 'Demo Restaurant',
      user: 'System',
      time: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
      type: 'registration'
    },
    {
      id: '2',
      action: 'Payment processed',
      tenant: 'Demo Restaurant',
      user: 'Enhanced Admin',
      time: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
      type: 'payment'
    },
    {
      id: '3',
      action: 'System maintenance completed',
      tenant: 'System',
      user: 'System',
      time: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      type: 'maintenance'
    }
  ];

  res.json(activities);
});

// 404 handler
app.use((req, res) => {
  console.log(`❓ 404 - Route not found: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 Test POS Backend Server Started!');
  console.log('=' .repeat(50));
  console.log(`📡 Server running on: http://localhost:${PORT}`);
  console.log(`🕐 Started at: ${new Date().toISOString()}`);
  console.log('');
  console.log('📊 Available API Endpoints:');
  console.log('  ✅ GET  /api/health');
  console.log('  🔐 POST /api/auth/login');
  console.log('  👑 GET  /api/admin/metrics (super admin only)');
  console.log('  👑 GET  /api/admin/tenants (super admin only)');
  console.log('  👑 GET  /api/admin/analytics (super admin only)');
  console.log('  👑 GET  /api/admin/activity (super admin only)');
  console.log('');
  console.log('🔑 Test Credentials:');
  console.log('  👑 Enhanced Admin: PIN 888888');
  console.log('  👑 Main Admin: PIN 123456');
  console.log('  🏢 Tenant: demo-restaurant');
  console.log('');
  console.log('🌐 Frontend URL:');
  console.log('  👑 Super Admin: http://localhost:5175/super-admin.html');
  console.log('=' .repeat(50));
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
