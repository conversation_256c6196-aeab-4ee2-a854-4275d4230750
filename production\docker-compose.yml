# 🚀 BARPOS Production Docker Compose
# Multi-Tenant Restaurant POS System - Production Deployment
# Phase 7: Production Deployment & Market Launch

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: barpos-postgres-prod
    environment:
      POSTGRES_DB: BARPOS_PROD
      POSTGRES_USER: barpos_prod
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./production/database/init:/docker-entrypoint-initdb.d
      - ./production/backups:/backups
    restart: unless-stopped
    networks:
      - barpos-network
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U barpos_prod -d BARPOS_PROD"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: barpos-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - barpos-network
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: ../production/backend/Dockerfile
    container_name: barpos-backend-prod
    environment:
      NODE_ENV: production
      PORT: 4000
      DATABASE_URL: postgresql://barpos_prod:${POSTGRES_PASSWORD}@postgres:5432/BARPOS_PROD
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      JWT_SECRET: ${JWT_SECRET}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
      PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASS: ${SMTP_PASS}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      SENTRY_DSN: ${SENTRY_DSN}
      NEW_RELIC_LICENSE_KEY: ${NEW_RELIC_LICENSE_KEY}
      CORS_ORIGIN: ${CORS_ORIGIN}
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      ENABLE_AI_FEATURES: true
      ENABLE_GLOBAL_FEATURES: true
      ENABLE_ANALYTICS: true
    ports:
      - "4000:4000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - barpos-network
    volumes:
      - ./production/logs:/app/logs
      - ./production/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Web Server
  frontend:
    build:
      context: ./project
      dockerfile: ../production/frontend/Dockerfile
    container_name: barpos-frontend-prod
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL}
      REACT_APP_STRIPE_PUBLISHABLE_KEY: ${REACT_APP_STRIPE_PUBLISHABLE_KEY}
      REACT_APP_PAYPAL_CLIENT_ID: ${REACT_APP_PAYPAL_CLIENT_ID}
      REACT_APP_SENTRY_DSN: ${REACT_APP_SENTRY_DSN}
      REACT_APP_ENVIRONMENT: production
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - barpos-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer & SSL Termination
  nginx:
    image: nginx:alpine
    container_name: barpos-nginx-prod
    ports:
      - "443:443"
      - "8080:80"
    volumes:
      - ./production/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./production/ssl:/etc/nginx/ssl
      - ./production/logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - barpos-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: barpos-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ./production/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./production/monitoring/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    restart: unless-stopped
    networks:
      - barpos-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: barpos-grafana-prod
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SECURITY_ADMIN_USER: admin
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_SECURITY_ALLOW_EMBEDDING: true
      GF_AUTH_ANONYMOUS_ENABLED: false
      GF_SMTP_ENABLED: true
      GF_SMTP_HOST: ${SMTP_HOST}:${SMTP_PORT}
      GF_SMTP_USER: ${SMTP_USER}
      GF_SMTP_PASSWORD: ${SMTP_PASS}
      GF_SMTP_FROM_ADDRESS: ${SMTP_USER}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./production/monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./production/monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - barpos-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Node Exporter for System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: barpos-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    networks:
      - barpos-network

  # Postgres Exporter for Database Metrics
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: barpos-postgres-exporter
    environment:
      DATA_SOURCE_NAME: postgresql://barpos_prod:${POSTGRES_PASSWORD}@postgres:5432/BARPOS_PROD?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - barpos-network

  # Redis Exporter for Cache Metrics
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: barpos-redis-exporter
    environment:
      REDIS_ADDR: redis://redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "9121:9121"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - barpos-network

  # Log Aggregation (Optional)
  loki:
    image: grafana/loki:latest
    container_name: barpos-loki-prod
    ports:
      - "3100:3100"
    volumes:
      - ./production/monitoring/loki/loki-config.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped
    networks:
      - barpos-network

  # Log Shipper
  promtail:
    image: grafana/promtail:latest
    container_name: barpos-promtail-prod
    volumes:
      - ./production/logs:/var/log
      - ./production/monitoring/promtail/promtail-config.yml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    restart: unless-stopped
    networks:
      - barpos-network

# Networks
networks:
  barpos-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
