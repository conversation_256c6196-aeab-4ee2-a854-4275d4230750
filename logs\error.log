{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:31:55.678Z","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:32:09.249Z","type":"entity.parse.failed"}
{"code":"22P02","file":"uuid.c","length":143,"level":"error","line":"141","message":"Error adding/updating employee: invalid input syntax for type uuid: \"test-tenant\"","name":"error","routine":"string_to_uuid","service":"pos-backend","severity":"ERROR","stack":"error: invalid input syntax for type uuid: \"test-tenant\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:458:22","timestamp":"2025-05-31T02:32:58.968Z","where":"unnamed portal parameter $5 = '...'"}
{"code":"23503","constraint":"employees_tenant_id_fkey","detail":"Key (tenant_id)=(123e4567-e89b-12d3-a456-************) is not present in table \"tenants\".","file":"ri_triggers.c","length":301,"level":"error","line":"2610","message":"Error adding/updating employee: insert or update on table \"employees\" violates foreign key constraint \"employees_tenant_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"pos-backend","severity":"ERROR","stack":"error: insert or update on table \"employees\" violates foreign key constraint \"employees_tenant_id_fkey\"\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\server-enhanced.js:458:22","table":"employees","timestamp":"2025-05-31T02:34:23.387Z"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:34:48.858Z","type":"entity.parse.failed"}
{"body":"{\\","expose":true,"level":"error","message":"Unhandled error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"pos-backend","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-9ew4zohy (1)\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-05-31T02:49:31.746Z","type":"entity.parse.failed"}
