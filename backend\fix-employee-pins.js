const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
});

async function fixEmployeePins() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Fixing employee PINs in database...');
    
    // Check current employees
    const currentEmployees = await client.query('SELECT id, name, pin, role FROM employees');
    console.log('📋 Current employees:');
    currentEmployees.rows.forEach(emp => {
      console.log(`  ${emp.id}: ${emp.name} (${emp.role}) - PIN: ${emp.pin}`);
    });
    
    // Hash the PINs properly
    const pinMappings = {
      '999999': await bcrypt.hash('999999', 10),
      '123456': await bcrypt.hash('123456', 10),
      '888888': await bcrypt.hash('888888', 10),
      '234567': await bcrypt.hash('234567', 10)
    };
    
    console.log('\n🔐 Updating PINs with proper hashes...');
    
    // Update each employee's PIN
    for (const emp of currentEmployees.rows) {
      if (pinMappings[emp.pin]) {
        await client.query(
          'UPDATE employees SET pin = $1 WHERE id = $2',
          [pinMappings[emp.pin], emp.id]
        );
        console.log(`✅ Updated ${emp.name}: ${emp.pin} -> hashed`);
      }
    }
    
    // Also add a super admin with PIN 123456 for easy access
    const superAdminPin = await bcrypt.hash('123456', 10);
    
    // Check if we already have a super admin with PIN 123456
    const existingSuperAdmin = await client.query(`
      SELECT id FROM employees WHERE role = 'super_admin' AND name = 'Super Admin'
    `);
    
    if (existingSuperAdmin.rows.length === 0) {
      // Get the first tenant ID
      const tenantResult = await client.query('SELECT id FROM tenants ORDER BY id LIMIT 1');
      const tenantId = tenantResult.rows[0]?.id || 1;
      
      await client.query(`
        INSERT INTO employees (name, pin, role, tenant_id, location_id, permissions, is_active)
        VALUES ('Super Admin', $1, 'super_admin', $2, 'loc_1', $3, true)
      `, [superAdminPin, tenantId, JSON.stringify(['all'])]);
      
      console.log('✅ Added Super Admin with PIN 123456');
    }
    
    // Verify the updates
    console.log('\n📋 Updated employees:');
    const updatedEmployees = await client.query('SELECT id, name, pin, role FROM employees');
    updatedEmployees.rows.forEach(emp => {
      console.log(`  ${emp.id}: ${emp.name} (${emp.role}) - PIN: ${emp.pin.substring(0, 10)}...`);
    });
    
    console.log('\n🧪 Testing PIN verification...');
    
    // Test PIN verification
    const testPins = ['123456', '567890', '999999', '888888', '234567'];
    
    for (const testPin of testPins) {
      console.log(`\n🔍 Testing PIN: ${testPin}`);
      
      for (const emp of updatedEmployees.rows) {
        try {
          const isValid = await bcrypt.compare(testPin, emp.pin);
          if (isValid) {
            console.log(`  ✅ PIN ${testPin} matches ${emp.name} (${emp.role})`);
          }
        } catch (error) {
          console.log(`  ❌ Error testing PIN ${testPin} for ${emp.name}: ${error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.error('💥 Failed to fix employee PINs:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

fixEmployeePins();
