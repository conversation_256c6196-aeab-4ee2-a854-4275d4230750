const db = require('./pool');

// Cache for query results
const cache = new Map();
const CACHE_TTL = 60000; // 1 minute TTL for cache entries

// Cache helper functions
const getCacheKey = (queryName, params) => `${queryName}:${JSON.stringify(params)}`;

const setCacheEntry = (key, data) => {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const getCacheEntry = (key) => {
  const entry = cache.get(key);
  if (!entry) return null;
  
  if (Date.now() - entry.timestamp > CACHE_TTL) {
    cache.delete(key);
    return null;
  }
  
  return entry.data;
};

// Prepared statements
const PREPARED_STATEMENTS = {
  getEmployeeByPin: {
    name: 'get_employee_by_pin',
    text: `
      SELECT 
        e.id,
        e.name,
        e.role,
        t.id as tenant_id,
        t.name as tenant_name,
        t.slug as tenant_slug
      FROM employees e
      JOIN tenants t ON e.tenant_id = t.id
      WHERE e.pin = $1 AND t.slug = $2
    `
  },
  getEmployeeById: {
    name: 'get_employee_by_id',
    text: `
      SELECT 
        id,
        name,
        role
      FROM employees
      WHERE id = $1
    `
  }
};

// Initialize prepared statements
const initializePreparedStatements = async () => {
  for (const [key, statement] of Object.entries(PREPARED_STATEMENTS)) {
    await db.prepare(statement.name, statement.text);
  }
};

// Query functions with caching
const queries = {
  async getEmployeeByPin(pin, tenantSlug) {
    const cacheKey = getCacheKey('getEmployeeByPin', [pin, tenantSlug]);
    const cachedResult = getCacheEntry(cacheKey);
    if (cachedResult) return cachedResult;

    const result = await db.query(PREPARED_STATEMENTS.getEmployeeByPin.text, [pin, tenantSlug]);
    if (result.rows.length > 0) {
      setCacheEntry(cacheKey, result.rows[0]);
      return result.rows[0];
    }
    return null;
  },

  async getEmployeeById(id) {
    const cacheKey = getCacheKey('getEmployeeById', [id]);
    const cachedResult = getCacheEntry(cacheKey);
    if (cachedResult) return cachedResult;

    const result = await db.query(PREPARED_STATEMENTS.getEmployeeById.text, [id]);
    if (result.rows.length > 0) {
      setCacheEntry(cacheKey, result.rows[0]);
      return result.rows[0];
    }
    return null;
  },

  // Mock data for development
  async getAllEmployees() {
    return [
      { id: 1, name: 'John Doe', role: 'manager' },
      { id: 2, name: 'Jane Smith', role: 'employee' }
    ];
  },

  async getSettings() {
    return {
      restaurantName: 'Demo Restaurant',
      currency: 'USD',
      taxRate: 0.08,
      theme: 'dark'
    };
  },

  async getProducts() {
    return [
      { id: 1, name: 'Burger', price: 9.99, category: 1 },
      { id: 2, name: 'Pizza', price: 12.99, category: 1 },
      { id: 3, name: 'Cola', price: 2.99, category: 2 }
    ];
  },

  async getCategories() {
    return [
      { id: 1, name: 'Food' },
      { id: 2, name: 'Beverages' }
    ];
  },

  async getFloorLayout() {
    return {
      tables: [
        { id: 1, name: 'Table 1', x: 100, y: 100, seats: 4 },
        { id: 2, name: 'Table 2', x: 200, y: 100, seats: 2 },
        { id: 3, name: 'Table 3', x: 100, y: 200, seats: 6 }
      ],
      zones: [
        { id: 1, name: 'Main Dining', color: '#e6f3ff' },
        { id: 2, name: 'Bar Area', color: '#fff3e6' }
      ]
    };
  },

  // Function to clear cache entries
  clearCache() {
    cache.clear();
  },

  // Function to clear specific cache entry
  clearCacheEntry(queryName, params) {
    const key = getCacheKey(queryName, params);
    cache.delete(key);
  }
};

// Initialize prepared statements when module is loaded
initializePreparedStatements().catch(console.error);

module.exports = queries;
