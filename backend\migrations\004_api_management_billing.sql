-- Phase 4: API Management & Advanced Features Migration
-- Execute this migration to add API management and billing features

-- =====================================================
-- API MANAGEMENT TABLES
-- =====================================================

-- API Keys Management
CREATE TABLE IF NOT EXISTS api_keys (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  key_name VARCHAR(100) NOT NULL,
  api_key VARCHAR(64) UNIQUE NOT NULL,
  api_secret VARCHAR(128),
  permissions JSONB NOT NULL DEFAULT '[]',
  rate_limit_per_hour INTEGER DEFAULT 1000,
  rate_limit_per_day INTEGER DEFAULT 10000,
  allowed_ips TEXT[], -- Array of allowed IP addresses
  webhook_url TEXT,
  is_active BOOLEAN DEFAULT true,
  expires_at TIMESTAMP,
  last_used_at TIMESTAMP,
  usage_count BIGINT DEFAULT 0,
  created_by INTEGER REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- API Endpoints Registry
CREATE TABLE IF NOT EXISTS api_endpoints (
  id SERIAL PRIMARY KEY,
  endpoint_path VARCHAR(200) NOT NULL,
  http_method VARCHAR(10) NOT NULL,
  description TEXT,
  category VARCHAR(50), -- 'pos', 'analytics', 'admin', 'webhook'
  required_permissions TEXT[],
  rate_limit_override INTEGER,
  is_public BOOLEAN DEFAULT false,
  is_deprecated BOOLEAN DEFAULT false,
  version VARCHAR(10) DEFAULT 'v1',
  documentation_url TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(endpoint_path, http_method, version)
);

-- API Usage Analytics
CREATE TABLE IF NOT EXISTS api_usage_analytics (
  id SERIAL PRIMARY KEY,
  api_key_id INTEGER REFERENCES api_keys(id) ON DELETE CASCADE,
  endpoint_id INTEGER REFERENCES api_endpoints(id),
  tenant_id INTEGER REFERENCES tenants(id),
  request_timestamp TIMESTAMP DEFAULT NOW(),
  response_time_ms INTEGER,
  status_code INTEGER,
  request_size_bytes INTEGER,
  response_size_bytes INTEGER,
  ip_address INET,
  user_agent TEXT,
  error_message TEXT,
  request_id VARCHAR(100)
);

-- API Rate Limiting
CREATE TABLE IF NOT EXISTS api_rate_limits (
  id SERIAL PRIMARY KEY,
  api_key_id INTEGER REFERENCES api_keys(id) ON DELETE CASCADE,
  endpoint_id INTEGER REFERENCES api_endpoints(id),
  time_window VARCHAR(20) NOT NULL, -- 'hour', 'day', 'month'
  request_count INTEGER DEFAULT 0,
  window_start TIMESTAMP DEFAULT NOW(),
  last_reset TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- BILLING & SUBSCRIPTION TABLES
-- =====================================================

-- Subscription Plans
CREATE TABLE IF NOT EXISTS subscription_plans (
  id SERIAL PRIMARY KEY,
  plan_name VARCHAR(100) NOT NULL UNIQUE,
  plan_code VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  features JSONB NOT NULL DEFAULT '{}',
  limits JSONB NOT NULL DEFAULT '{}', -- API calls, users, locations, etc.
  is_active BOOLEAN DEFAULT true,
  trial_days INTEGER DEFAULT 14,
  setup_fee DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tenant Subscriptions
CREATE TABLE IF NOT EXISTS tenant_subscriptions (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  plan_id INTEGER REFERENCES subscription_plans(id),
  status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'trial', 'active', 'suspended', 'cancelled', 'expired'
  billing_cycle VARCHAR(20) DEFAULT 'monthly', -- 'monthly', 'yearly'
  current_period_start DATE NOT NULL,
  current_period_end DATE NOT NULL,
  trial_start DATE,
  trial_end DATE,
  cancelled_at TIMESTAMP,
  cancellation_reason TEXT,
  auto_renew BOOLEAN DEFAULT true,
  payment_method_id VARCHAR(100), -- Stripe/payment processor ID
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Billing Invoices
CREATE TABLE IF NOT EXISTS billing_invoices (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  subscription_id INTEGER REFERENCES tenant_subscriptions(id),
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'paid', 'failed', 'refunded'
  amount_due DECIMAL(10,2) NOT NULL,
  amount_paid DECIMAL(10,2) DEFAULT 0,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  discount_amount DECIMAL(10,2) DEFAULT 0,
  currency VARCHAR(3) DEFAULT 'USD',
  billing_period_start DATE,
  billing_period_end DATE,
  due_date DATE,
  paid_at TIMESTAMP,
  payment_method VARCHAR(50),
  payment_reference VARCHAR(100),
  invoice_data JSONB, -- Detailed line items
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Usage Tracking
CREATE TABLE IF NOT EXISTS usage_tracking (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  subscription_id INTEGER REFERENCES tenant_subscriptions(id),
  usage_date DATE NOT NULL,
  metric_name VARCHAR(50) NOT NULL, -- 'api_calls', 'transactions', 'users', 'storage_gb'
  usage_value INTEGER NOT NULL,
  limit_value INTEGER,
  overage_amount DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(tenant_id, usage_date, metric_name)
);

-- =====================================================
-- NOTIFICATION SYSTEM TABLES
-- =====================================================

-- Notification Templates
CREATE TABLE IF NOT EXISTS notification_templates (
  id SERIAL PRIMARY KEY,
  template_name VARCHAR(100) NOT NULL UNIQUE,
  template_type VARCHAR(50) NOT NULL, -- 'email', 'sms', 'push', 'webhook'
  category VARCHAR(50), -- 'billing', 'security', 'system', 'marketing'
  subject_template TEXT,
  body_template TEXT NOT NULL,
  variables JSONB DEFAULT '[]', -- Available template variables
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Notification Queue
CREATE TABLE IF NOT EXISTS notification_queue (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id),
  template_id INTEGER REFERENCES notification_templates(id),
  recipient_type VARCHAR(20) NOT NULL, -- 'tenant', 'admin', 'user'
  recipient_id INTEGER,
  recipient_email VARCHAR(255),
  recipient_phone VARCHAR(20),
  notification_type VARCHAR(50) NOT NULL,
  priority INTEGER DEFAULT 5, -- 1-10 (1 = highest)
  subject TEXT,
  message TEXT NOT NULL,
  variables JSONB DEFAULT '{}',
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'cancelled'
  scheduled_at TIMESTAMP DEFAULT NOW(),
  sent_at TIMESTAMP,
  failed_at TIMESTAMP,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Notification Preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES employees(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL,
  channel VARCHAR(20) NOT NULL, -- 'email', 'sms', 'push'
  is_enabled BOOLEAN DEFAULT true,
  frequency VARCHAR(20) DEFAULT 'immediate', -- 'immediate', 'daily', 'weekly'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(tenant_id, user_id, notification_type, channel)
);

-- =====================================================
-- INTEGRATION MANAGEMENT TABLES
-- =====================================================

-- Third-party Integrations
CREATE TABLE IF NOT EXISTS integrations (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
  integration_type VARCHAR(50) NOT NULL, -- 'payment', 'accounting', 'inventory', 'marketing'
  provider_name VARCHAR(100) NOT NULL, -- 'stripe', 'quickbooks', 'mailchimp', etc.
  configuration JSONB NOT NULL DEFAULT '{}',
  credentials JSONB, -- Encrypted credentials
  webhook_url TEXT,
  webhook_secret VARCHAR(100),
  is_active BOOLEAN DEFAULT true,
  last_sync_at TIMESTAMP,
  sync_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'syncing', 'completed', 'failed'
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Integration Sync Logs
CREATE TABLE IF NOT EXISTS integration_sync_logs (
  id SERIAL PRIMARY KEY,
  integration_id INTEGER REFERENCES integrations(id) ON DELETE CASCADE,
  sync_type VARCHAR(50) NOT NULL, -- 'full', 'incremental', 'webhook'
  status VARCHAR(20) NOT NULL,
  started_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP,
  records_processed INTEGER DEFAULT 0,
  records_success INTEGER DEFAULT 0,
  records_failed INTEGER DEFAULT 0,
  error_details JSONB,
  sync_data JSONB
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- API Management Indexes
CREATE INDEX IF NOT EXISTS idx_api_keys_tenant ON api_keys(tenant_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active, tenant_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_timestamp ON api_usage_analytics(request_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_tenant ON api_usage_analytics(tenant_id, request_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_key ON api_rate_limits(api_key_id, time_window);

-- Billing Indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_tenant ON tenant_subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON tenant_subscriptions(status, current_period_end);
CREATE INDEX IF NOT EXISTS idx_invoices_tenant ON billing_invoices(tenant_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON billing_invoices(status, due_date);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_tenant ON usage_tracking(tenant_id, usage_date DESC);

-- Notification Indexes
CREATE INDEX IF NOT EXISTS idx_notification_queue_status ON notification_queue(status, scheduled_at);
CREATE INDEX IF NOT EXISTS idx_notification_queue_tenant ON notification_queue(tenant_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user ON notification_preferences(tenant_id, user_id);

-- Integration Indexes
CREATE INDEX IF NOT EXISTS idx_integrations_tenant ON integrations(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_integration_logs_integration ON integration_sync_logs(integration_id, started_at DESC);

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Sample Subscription Plans
INSERT INTO subscription_plans (plan_name, plan_code, description, price_monthly, price_yearly, features, limits) VALUES
('Starter', 'starter', 'Perfect for small restaurants', 29.99, 299.99, 
 '{"pos": true, "basic_analytics": true, "email_support": true}',
 '{"api_calls": 1000, "users": 5, "locations": 1, "storage_gb": 1}'),
('Professional', 'professional', 'For growing restaurant chains', 79.99, 799.99,
 '{"pos": true, "advanced_analytics": true, "multi_location": true, "priority_support": true}',
 '{"api_calls": 10000, "users": 25, "locations": 5, "storage_gb": 10}'),
('Enterprise', 'enterprise', 'For large restaurant enterprises', 199.99, 1999.99,
 '{"pos": true, "advanced_analytics": true, "multi_location": true, "white_label": true, "dedicated_support": true}',
 '{"api_calls": 100000, "users": 100, "locations": 50, "storage_gb": 100}')
ON CONFLICT (plan_code) DO NOTHING;

-- Sample API Endpoints
INSERT INTO api_endpoints (endpoint_path, http_method, description, category, required_permissions) VALUES
('/api/v1/orders', 'GET', 'Retrieve orders', 'pos', ARRAY['orders:read']),
('/api/v1/orders', 'POST', 'Create new order', 'pos', ARRAY['orders:write']),
('/api/v1/analytics/revenue', 'GET', 'Get revenue analytics', 'analytics', ARRAY['analytics:read']),
('/api/v1/menu/items', 'GET', 'Get menu items', 'pos', ARRAY['menu:read']),
('/api/v1/menu/items', 'POST', 'Create menu item', 'pos', ARRAY['menu:write']),
('/api/v1/webhooks/payment', 'POST', 'Payment webhook', 'webhook', ARRAY[])
ON CONFLICT (endpoint_path, http_method, version) DO NOTHING;

-- Sample Notification Templates
INSERT INTO notification_templates (template_name, template_type, category, subject_template, body_template, variables) VALUES
('billing_invoice_due', 'email', 'billing', 'Invoice Due - {{invoice_number}}', 
 'Your invoice {{invoice_number}} for ${{amount}} is due on {{due_date}}.', 
 '["invoice_number", "amount", "due_date", "tenant_name"]'),
('security_alert', 'email', 'security', 'Security Alert - {{alert_type}}',
 'A security event has been detected: {{description}}. Please review immediately.',
 '["alert_type", "description", "timestamp", "ip_address"]'),
('system_maintenance', 'email', 'system', 'Scheduled Maintenance - {{date}}',
 'System maintenance is scheduled for {{date}} from {{start_time}} to {{end_time}}.',
 '["date", "start_time", "end_time", "duration"]')
ON CONFLICT (template_name) DO NOTHING;

-- Sample tenant subscription (for demo tenant)
INSERT INTO tenant_subscriptions (tenant_id, plan_id, status, current_period_start, current_period_end, trial_start, trial_end)
SELECT 1, sp.id, 'active', CURRENT_DATE, CURRENT_DATE + INTERVAL '30 days', 
       CURRENT_DATE - INTERVAL '14 days', CURRENT_DATE
FROM subscription_plans sp WHERE sp.plan_code = 'professional'
ON CONFLICT DO NOTHING;

COMMIT;
