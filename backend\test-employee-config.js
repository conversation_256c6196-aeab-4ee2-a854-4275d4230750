const http = require('http');

// Test adding an employee
const employeeData = JSON.stringify({
  name: "Test Manager",
  pin: "4321",
  role: "manager"
});

const employeeOptions = {
  hostname: 'localhost',
  port: 4000,
  path: '/employees',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': employeeData.length
  }
};

const employeeReq = http.request(employeeOptions, res => {
  console.log(`Add Employee Status: ${res.statusCode}`);
  let data = '';
  
  res.on('data', chunk => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Employee Response:', JSON.parse(data));

    // After adding employee, test updating system config
    const configData = JSON.stringify({
      value: JSON.stringify("Welcome to Bar POS!")
    });

    const configOptions = {
      hostname: 'localhost',
      port: 4000,
      path: '/config/receipt_header',
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': configData.length
      }
    };

    const configReq = http.request(configOptions, res => {
      console.log(`\nUpdate Config Status: ${res.statusCode}`);
      res.on('data', d => {
        process.stdout.write(d);
      });
    });

    configReq.on('error', error => {
      console.error('Config Error:', error);
    });

    configReq.write(configData);
    configReq.end();
  });
});

employeeReq.on('error', error => {
  console.error('Employee Error:', error);
});

employeeReq.write(employeeData);
employeeReq.end();
