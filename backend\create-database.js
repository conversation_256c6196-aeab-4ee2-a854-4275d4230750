const { Pool } = require('pg');

// Connect to default postgres database to create RESTROFLOW database
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'postgres', // Connect to default database first
  password: 'Chaand@0319',
  port: 5432,
});

async function createDatabase() {
  console.log('🔧 Creating RESTROFLOW database...');
  
  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL');

    // Check if database exists
    const checkResult = await client.query(`
      SELECT 1 FROM pg_database WHERE datname = 'RESTROFLOW'
    `);

    if (checkResult.rows.length > 0) {
      console.log('📋 RESTROFLOW database already exists');
    } else {
      // Create the database
      await client.query('CREATE DATABASE "RESTROFLOW"');
      console.log('✅ RESTROFLOW database created successfully');
    }

    client.release();
    
  } catch (error) {
    console.error('💥 Database creation failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run creation
createDatabase()
  .then(() => {
    console.log('🎉 Database creation completed!');
    console.log('📋 Now run: node setup-database.js');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Creation failed:', error.message);
    process.exit(1);
  });
