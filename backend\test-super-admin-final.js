const fetch = require('node-fetch');

async function testSuperAdminFinal() {
  try {
    console.log('🚀 Final Super Admin Test...');
    console.log('='.repeat(60));
    
    // Test login with PIN 999999 (Enhanced Admin)
    console.log('\n📋 Testing Enhanced Admin Login (PIN: 999999)');
    console.log('-'.repeat(40));
    
    const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '999999',
        tenant_slug: 'demo-restaurant'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginResult = await loginResponse.json();
    console.log(`✅ Login successful!`);
    console.log(`👤 Employee: ${loginResult.employee.name} (${loginResult.employee.role})`);
    console.log(`🏢 Tenant: ${loginResult.tenant.name}`);
    console.log(`🔑 Token: ${loginResult.token ? 'Received' : 'Missing'}`);

    if (loginResult.employee.role !== 'super_admin') {
      console.log(`❌ User is not super admin: ${loginResult.employee.role}`);
      return;
    }

    // Test tenants endpoint
    console.log('\n📋 Testing Tenants Endpoint');
    console.log('-'.repeat(40));
    
    const tenantsResponse = await fetch('http://localhost:4000/api/tenants', {
      headers: {
        'Authorization': `Bearer ${loginResult.token}`
      }
    });

    if (!tenantsResponse.ok) {
      console.log(`❌ Tenants fetch failed: ${tenantsResponse.status}`);
      return;
    }

    const tenants = await tenantsResponse.json();
    console.log(`✅ Fetched ${tenants.length} tenants`);
    
    console.log('\n📊 Tenant Details:');
    tenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ${tenant.business_name || tenant.name}`);
      console.log(`     Email: ${tenant.email || 'N/A'}`);
      console.log(`     Status: ${tenant.status || 'N/A'}`);
      console.log(`     Plan: ${tenant.plan_type || 'N/A'}`);
      console.log(`     Created: ${tenant.created_at ? new Date(tenant.created_at).toLocaleDateString() : 'N/A'}`);
      console.log('');
    });
    
    console.log('\n🏁 Final Test Completed!');
    console.log('='.repeat(60));
    
    console.log('\n📋 SUMMARY FOR FRONTEND:');
    console.log('✅ Backend API working perfectly');
    console.log('✅ Super Admin authentication working');
    console.log('✅ Tenant data available and correct');
    console.log(`✅ ${tenants.length} tenants ready for display`);
    
    console.log('\n🎯 FRONTEND SHOULD NOW WORK WITH:');
    console.log('🔐 Login URL: http://localhost:5174/super-admin.html');
    console.log('🔑 Super Admin PIN: 999999 (Enhanced Admin)');
    console.log('🔑 Super Admin PIN: 888888 (Main Admin)');
    console.log('🏢 Tenant: demo-restaurant');
    
    console.log('\n📊 EXPECTED DASHBOARD DISPLAY:');
    tenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ${tenant.business_name || tenant.name} (${tenant.status || 'active'})`);
    });
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testSuperAdminFinal();
