describe('Critical Path Testing for Combined App UI', () => {
  before(() => {
    // Visit the app root and wait for page load
    cy.visit('/');
    cy.get('input[type="text"]', { timeout: 10000 }).should('be.visible');
  });

  it('should allow user to login', () => {
    cy.get('input[type="text"]', { timeout: 10000 }).type('1234'); // Assuming PIN input
    cy.get('button', { timeout: 10000 }).contains('Login').click();
    cy.contains('POS', { timeout: 10000 }).should('be.visible');
  });

  it('should navigate through POS tabs and retain order state', () => {
    cy.get('button').contains('POS').click();
    cy.get('button').contains('Inventory').click();
    cy.get('button').contains('POS').click();
    cy.get('.product-grid').should('exist'); // Assuming product grid class
  });

  it('should create an order and proceed to payment', () => {
    cy.get('.product-item').first().click(); // Add product to order
    cy.get('button').contains('Checkout').click();
    cy.get('button').contains('Pay').click();
    cy.contains('Receipt').should('be.visible');
  });

  it('should logout successfully', () => {
    cy.get('button').contains('Logout').click();
    cy.contains('Enter your PIN to continue').should('be.visible');
  });

  it('should access tenant management page for tenant users', () => {
    // Simulate tenant user login or direct visit
    cy.visit('/tenant');
    cy.contains('Welcome to Tenant Management').should('be.visible');
  });
});
