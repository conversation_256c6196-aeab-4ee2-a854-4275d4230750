import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { BarChart3, TrendingUp, DollarSign, ShoppingCart, Users, Clock, RefreshCw, Calendar } from 'lucide-react';

interface AnalyticsData {
  sales_volume: {
    today: number;
    yesterday: number;
    this_week: number;
    this_month: number;
  };
  revenue: {
    today: number;
    yesterday: number;
    this_week: number;
    this_month: number;
  };
  orders: {
    today: number;
    yesterday: number;
    this_week: number;
    this_month: number;
  };
  top_items: Array<{
    name: string;
    quantity: number;
    revenue: number;
  }>;
  hourly_sales: Array<{
    hour: number;
    sales: number;
    orders: number;
  }>;
  order_types: {
    dine_in: number;
    takeout: number;
    delivery: number;
  };
}

const MVPAnalytics: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📊 Loading analytics data...');
        
        const response = await apiCall(`/api/analytics/sales?period=${selectedPeriod}`);
        if (response.ok) {
          const data = await response.json();
          setAnalyticsData(data);
          setLastUpdated(new Date());
          console.log('✅ Analytics loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading analytics:', error);
        setError('Failed to load analytics. Using mock data.');
        
        // Fallback to mock data
        const mockData: AnalyticsData = {
          sales_volume: {
            today: 2450.75,
            yesterday: 2180.50,
            this_week: 15420.25,
            this_month: 68750.80
          },
          revenue: {
            today: 2450.75,
            yesterday: 2180.50,
            this_week: 15420.25,
            this_month: 68750.80
          },
          orders: {
            today: 45,
            yesterday: 38,
            this_week: 285,
            this_month: 1240
          },
          top_items: [
            { name: 'Coffee', quantity: 170, revenue: 850.00 },
            { name: 'Burger', quantity: 50, revenue: 649.50 },
            { name: 'Sandwich', quantity: 50, revenue: 449.50 },
            { name: 'Salad', quantity: 35, revenue: 262.50 },
            { name: 'Tea', quantity: 80, revenue: 200.00 }
          ],
          hourly_sales: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            sales: Math.random() * 300 + 50,
            orders: Math.floor(Math.random() * 15) + 1
          })),
          order_types: {
            dine_in: 65,
            takeout: 25,
            delivery: 10
          }
        };
        setAnalyticsData(mockData);
        setLastUpdated(new Date());
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAnalytics();
  }, [apiCall, selectedPeriod]);

  const calculateGrowth = (current: number, previous: number): { percentage: number; isPositive: boolean } => {
    if (previous === 0) return { percentage: 0, isPositive: true };
    const percentage = ((current - previous) / previous) * 100;
    return { percentage: Math.abs(percentage), isPositive: percentage >= 0 };
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatTime = (hour: number): string => {
    return new Date(2024, 0, 1, hour).toLocaleTimeString('en-US', {
      hour: 'numeric',
      hour12: true
    });
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No analytics data available</p>
        </div>
      </div>
    );
  }

  const revenueGrowth = calculateGrowth(
    analyticsData.revenue.today,
    analyticsData.revenue.yesterday
  );

  const ordersGrowth = calculateGrowth(
    analyticsData.orders.today,
    analyticsData.orders.yesterday
  );

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Analytics Dashboard</h2>
            <p className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      <div className="flex-1 overflow-auto p-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Today's Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analyticsData.revenue.today)}
                </p>
                <div className="flex items-center mt-1">
                  <TrendingUp className={`h-4 w-4 mr-1 ${revenueGrowth.isPositive ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm ${revenueGrowth.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                    {revenueGrowth.isPositive ? '+' : '-'}{revenueGrowth.percentage.toFixed(1)}%
                  </span>
                  <span className="text-gray-500 text-sm ml-1">vs yesterday</span>
                </div>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Today's Orders</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.orders.today}</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className={`h-4 w-4 mr-1 ${ordersGrowth.isPositive ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm ${ordersGrowth.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                    {ordersGrowth.isPositive ? '+' : '-'}{ordersGrowth.percentage.toFixed(1)}%
                  </span>
                  <span className="text-gray-500 text-sm ml-1">vs yesterday</span>
                </div>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Average Order</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(analyticsData.revenue.today / analyticsData.orders.today)}
                </p>
                <p className="text-gray-500 text-sm mt-1">Per transaction</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Peak Hour</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatTime(analyticsData.hourly_sales.reduce((max, curr) => 
                    curr.sales > max.sales ? curr : max
                  ).hour)}
                </p>
                <p className="text-gray-500 text-sm mt-1">Busiest time</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Items */}
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Items</h3>
            <div className="space-y-3">
              {analyticsData.top_items.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{item.name}</p>
                      <p className="text-sm text-gray-500">{item.quantity} sold</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(item.revenue)}</p>
                    <p className="text-sm text-gray-500">revenue</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Types */}
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Types</h3>
            <div className="space-y-4">
              {Object.entries(analyticsData.order_types).map(([type, percentage]) => (
                <div key={type}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-700 capitalize">
                      {type.replace('_', ' ')}
                    </span>
                    <span className="text-sm text-gray-500">{percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        type === 'dine_in' ? 'bg-blue-500' :
                        type === 'takeout' ? 'bg-green-500' : 'bg-purple-500'
                      }`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Hourly Sales Chart */}
        <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Hourly Sales</h3>
          <div className="h-64 flex items-end space-x-1">
            {analyticsData.hourly_sales.map((data, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-blue-500 rounded-t"
                  style={{
                    height: `${(data.sales / Math.max(...analyticsData.hourly_sales.map(h => h.sales))) * 200}px`,
                    minHeight: '4px'
                  }}
                  title={`${formatTime(data.hour)}: ${formatCurrency(data.sales)}`}
                />
                <span className="text-xs text-gray-500 mt-1">
                  {data.hour === 0 ? '12a' : data.hour <= 12 ? `${data.hour}${data.hour === 12 ? 'p' : 'a'}` : `${data.hour - 12}p`}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Period Comparison */}
        <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Period Comparison</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-gray-500 text-sm">This Week</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(analyticsData.revenue.this_week)}</p>
              <p className="text-sm text-gray-500">{analyticsData.orders.this_week} orders</p>
            </div>
            <div className="text-center">
              <p className="text-gray-500 text-sm">This Month</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(analyticsData.revenue.this_month)}</p>
              <p className="text-sm text-gray-500">{analyticsData.orders.this_month} orders</p>
            </div>
            <div className="text-center">
              <p className="text-gray-500 text-sm">Average Daily</p>
              <p className="text-xl font-bold text-gray-900">
                {formatCurrency(analyticsData.revenue.this_month / 30)}
              </p>
              <p className="text-sm text-gray-500">{Math.round(analyticsData.orders.this_month / 30)} orders</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MVPAnalytics;
