// Mock API Service for Phase 2 Testing
// This simulates the backend API responses for development and testing

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  is_system_role: boolean;
  user_count: number;
  created_at: string;
}

export interface AdminUser {
  id: number;
  employee_name: string;
  employee_email: string;
  role_name: string;
  is_active: boolean;
  last_login: string;
  created_at: string;
}

export interface Permission {
  resource: string;
  action: string;
  description: string;
}

export interface Location {
  id: number;
  tenant_id: number;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  phone: string;
  email: string;
  timezone: string;
  is_primary: boolean;
  is_active: boolean;
  settings: any;
  analytics_count: number;
  created_at: string;
}

export interface AnalyticsData {
  date: string;
  total_orders: number;
  total_revenue: number;
  active_users: number;
  avg_order_value: number;
  customer_count: number;
  business_name: string;
  location_name: string;
}

// Phase 3 interfaces
export interface SystemMetric {
  id: number;
  metric_category: string;
  metric_name: string;
  metric_value: number;
  metric_unit: string;
  threshold_warning: number;
  threshold_critical: number;
  status: 'normal' | 'warning' | 'critical';
  timestamp: string;
}

export interface SecurityEvent {
  id: number;
  event_type: string;
  severity: 'info' | 'warning' | 'high' | 'critical';
  user_id?: number;
  user_name?: string;
  source_ip: string;
  event_details: any;
  risk_score: number;
  is_suspicious: boolean;
  is_blocked: boolean;
  geolocation?: any;
  created_at: string;
}

export interface BackupJob {
  id: number;
  job_name: string;
  backup_type: 'full' | 'incremental' | 'differential';
  target_type: 'database' | 'files' | 'configuration' | 'full_system';
  schedule_cron: string;
  retention_days: number;
  compression_enabled: boolean;
  encryption_enabled: boolean;
  storage_location: string;
  is_active: boolean;
  last_run_at?: string;
  next_run_at?: string;
  created_at: string;
}

// Phase 4 interfaces
export interface ApiKey {
  id: number;
  tenant_id: number;
  key_name: string;
  api_key: string;
  permissions: string[];
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  is_active: boolean;
  last_used_at?: string;
  usage_count: number;
  created_at: string;
}

export interface SubscriptionPlan {
  id: number;
  plan_name: string;
  plan_code: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: Record<string, boolean>;
  limits: Record<string, number>;
  is_active: boolean;
  trial_days: number;
}

export interface TenantSubscription {
  id: number;
  tenant_id: number;
  tenant_name: string;
  plan_name: string;
  status: 'trial' | 'active' | 'suspended' | 'cancelled' | 'expired';
  billing_cycle: 'monthly' | 'yearly';
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  auto_renew: boolean;
  mrr: number;
}

export interface NotificationTemplate {
  id: number;
  template_name: string;
  template_type: 'email' | 'sms' | 'push' | 'webhook';
  category: string;
  subject_template: string;
  body_template: string;
  variables: string[];
  is_active: boolean;
  usage_count: number;
  created_at: string;
}

class MockApiService {
  private delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Mock data
  private mockRoles: Role[] = [
    {
      id: 1,
      name: 'super_admin',
      description: 'Super Administrator with full system access',
      permissions: ['tenants:*', 'locations:*', 'analytics:*', 'system:*', 'users:*', 'audit:*'],
      is_system_role: true,
      user_count: 2,
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'tenant_manager',
      description: 'Tenant Manager with tenant and location management access',
      permissions: ['tenants:view', 'tenants:create', 'tenants:edit', 'locations:*', 'analytics:view'],
      is_system_role: true,
      user_count: 5,
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'analyst',
      description: 'Analytics Specialist with read-only access to reports',
      permissions: ['tenants:view', 'locations:view', 'analytics:*', 'audit:view'],
      is_system_role: true,
      user_count: 3,
      created_at: '2024-01-01T00:00:00Z'
    }
  ];

  private mockUsers: AdminUser[] = [
    {
      id: 1,
      employee_name: 'Enhanced Admin',
      employee_email: '<EMAIL>',
      role_name: 'super_admin',
      is_active: true,
      last_login: '2024-12-05T12:00:00Z',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      employee_name: 'Main Admin',
      employee_email: '<EMAIL>',
      role_name: 'super_admin',
      is_active: true,
      last_login: '2024-12-04T15:30:00Z',
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      employee_name: 'Tenant Manager',
      employee_email: '<EMAIL>',
      role_name: 'tenant_manager',
      is_active: true,
      last_login: '2024-12-05T09:15:00Z',
      created_at: '2024-02-01T00:00:00Z'
    }
  ];

  private mockPermissions: Permission[] = [
    { resource: 'tenants', action: 'view', description: 'View tenant information' },
    { resource: 'tenants', action: 'create', description: 'Create new tenants' },
    { resource: 'tenants', action: 'edit', description: 'Edit tenant information' },
    { resource: 'tenants', action: 'delete', description: 'Delete tenants' },
    { resource: 'locations', action: 'view', description: 'View location information' },
    { resource: 'locations', action: 'create', description: 'Create new locations' },
    { resource: 'locations', action: 'edit', description: 'Edit location information' },
    { resource: 'analytics', action: 'view', description: 'View analytics and reports' },
    { resource: 'analytics', action: 'export', description: 'Export analytics data' },
    { resource: 'system', action: 'monitor', description: 'Monitor system health' },
    { resource: 'users', action: 'view', description: 'View admin users' },
    { resource: 'users', action: 'create', description: 'Create admin users' }
  ];

  private mockLocations: Location[] = [
    {
      id: 1,
      tenant_id: 1,
      name: 'Demo Restaurant - Downtown',
      address: '123 Main Street',
      city: 'Toronto',
      state: 'Ontario',
      country: 'Canada',
      postal_code: 'M5V 3A8',
      phone: '******-555-0123',
      email: '<EMAIL>',
      timezone: 'America/Toronto',
      is_primary: true,
      is_active: true,
      settings: {},
      analytics_count: 45,
      created_at: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      tenant_id: 1,
      name: 'Demo Restaurant - Uptown',
      address: '456 Queen Street',
      city: 'Toronto',
      state: 'Ontario',
      country: 'Canada',
      postal_code: 'M4W 1J5',
      phone: '******-555-0124',
      email: '<EMAIL>',
      timezone: 'America/Toronto',
      is_primary: false,
      is_active: true,
      settings: {},
      analytics_count: 32,
      created_at: '2024-02-01T00:00:00Z'
    }
  ];

  private generateAnalyticsData(): AnalyticsData[] {
    const data: AnalyticsData[] = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    for (let i = 0; i < 30; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      data.push({
        date: date.toISOString().split('T')[0],
        total_orders: Math.floor(Math.random() * 100) + 50,
        total_revenue: Math.floor(Math.random() * 5000) + 2000,
        active_users: Math.floor(Math.random() * 50) + 20,
        avg_order_value: Math.floor(Math.random() * 50) + 25,
        customer_count: Math.floor(Math.random() * 80) + 30,
        business_name: 'Demo Restaurant',
        location_name: 'Downtown'
      });
    }

    return data;
  }

  // API Methods
  async getRoles(): Promise<Role[]> {
    await this.delay(500);
    return this.mockRoles;
  }

  async getUsers(): Promise<AdminUser[]> {
    await this.delay(500);
    return this.mockUsers;
  }

  async getPermissions(): Promise<Permission[]> {
    await this.delay(300);
    return this.mockPermissions;
  }

  async getLocations(tenantId: number): Promise<Location[]> {
    await this.delay(500);
    return this.mockLocations.filter(loc => loc.tenant_id === tenantId);
  }

  async getAnalytics(params: any): Promise<AnalyticsData[]> {
    await this.delay(800);
    return this.generateAnalyticsData();
  }

  async createRole(roleData: Partial<Role>): Promise<Role> {
    await this.delay(500);
    const newRole: Role = {
      id: this.mockRoles.length + 1,
      name: roleData.name || '',
      description: roleData.description || '',
      permissions: roleData.permissions || [],
      is_system_role: false,
      user_count: 0,
      created_at: new Date().toISOString()
    };
    this.mockRoles.push(newRole);
    return newRole;
  }

  async createUser(userData: Partial<AdminUser>): Promise<AdminUser> {
    await this.delay(500);
    const newUser: AdminUser = {
      id: this.mockUsers.length + 1,
      employee_name: userData.employee_name || '',
      employee_email: userData.employee_email || '',
      role_name: userData.role_name || '',
      is_active: true,
      last_login: '',
      created_at: new Date().toISOString()
    };
    this.mockUsers.push(newUser);
    return newUser;
  }

  async createLocation(tenantId: number, locationData: Partial<Location>): Promise<Location> {
    await this.delay(500);
    const newLocation: Location = {
      id: this.mockLocations.length + 1,
      tenant_id: tenantId,
      name: locationData.name || '',
      address: locationData.address || '',
      city: locationData.city || '',
      state: locationData.state || '',
      country: locationData.country || 'Canada',
      postal_code: locationData.postal_code || '',
      phone: locationData.phone || '',
      email: locationData.email || '',
      timezone: locationData.timezone || 'America/Toronto',
      is_primary: false,
      is_active: true,
      settings: locationData.settings || {},
      analytics_count: 0,
      created_at: new Date().toISOString()
    };
    this.mockLocations.push(newLocation);
    return newLocation;
  }

  async exportAnalytics(format: string, filters: any): Promise<{ downloadUrl: string }> {
    await this.delay(1000);
    return {
      downloadUrl: `/downloads/analytics-${Date.now()}.${format}`
    };
  }

  // Phase 3: System Monitoring & Security Methods
  async getSystemHealth(): Promise<SystemMetric[]> {
    await this.delay(500);

    return [
      {
        id: 1,
        metric_category: 'performance',
        metric_name: 'cpu_usage',
        metric_value: Math.random() * 30 + 40,
        metric_unit: 'percent',
        threshold_warning: 70,
        threshold_critical: 90,
        status: 'normal',
        timestamp: new Date().toISOString()
      },
      {
        id: 2,
        metric_category: 'performance',
        metric_name: 'memory_usage',
        metric_value: Math.random() * 20 + 60,
        metric_unit: 'percent',
        threshold_warning: 80,
        threshold_critical: 95,
        status: Math.random() > 0.8 ? 'warning' : 'normal',
        timestamp: new Date().toISOString()
      },
      {
        id: 3,
        metric_category: 'performance',
        metric_name: 'disk_usage',
        metric_value: Math.random() * 10 + 75,
        metric_unit: 'percent',
        threshold_warning: 85,
        threshold_critical: 95,
        status: Math.random() > 0.7 ? 'warning' : 'normal',
        timestamp: new Date().toISOString()
      }
    ];
  }

  async getSecurityEvents(): Promise<SecurityEvent[]> {
    await this.delay(800);

    return [
      {
        id: 1,
        event_type: 'login_attempt',
        severity: 'warning',
        user_name: '<EMAIL>',
        source_ip: '*************',
        event_details: { success: false, attempts: 3, method: 'pin' },
        risk_score: 65,
        is_suspicious: true,
        is_blocked: false,
        geolocation: { country: 'Canada', city: 'Toronto' },
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        event_type: 'permission_denied',
        severity: 'high',
        user_name: '<EMAIL>',
        source_ip: '*********',
        event_details: { resource: 'admin_users', action: 'delete' },
        risk_score: 45,
        is_suspicious: false,
        is_blocked: true,
        geolocation: { country: 'Canada', city: 'Vancouver' },
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      }
    ];
  }

  async getBackupJobs(): Promise<BackupJob[]> {
    await this.delay(600);

    return [
      {
        id: 1,
        job_name: 'Daily Database Backup',
        backup_type: 'full',
        target_type: 'database',
        schedule_cron: '0 2 * * *',
        retention_days: 30,
        compression_enabled: true,
        encryption_enabled: true,
        storage_location: '/backups/database/',
        is_active: true,
        last_run_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        next_run_at: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        job_name: 'Weekly Full System Backup',
        backup_type: 'full',
        target_type: 'full_system',
        schedule_cron: '0 1 * * 0',
        retention_days: 90,
        compression_enabled: true,
        encryption_enabled: true,
        storage_location: '/backups/system/',
        is_active: true,
        last_run_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        next_run_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: '2024-01-01T00:00:00Z'
      }
    ];
  }

  // Phase 4: API Management & Advanced Features Methods
  async getApiKeys(): Promise<ApiKey[]> {
    await this.delay(600);

    return [
      {
        id: 1,
        tenant_id: 1,
        key_name: 'Production API Key',
        api_key: 'pk_live_51234567890abcdef',
        permissions: ['orders:read', 'orders:write', 'menu:read', 'analytics:read'],
        rate_limit_per_hour: 1000,
        rate_limit_per_day: 10000,
        is_active: true,
        last_used_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        usage_count: 15420,
        created_at: '2024-01-15T00:00:00Z'
      },
      {
        id: 2,
        tenant_id: 1,
        key_name: 'Development API Key',
        api_key: 'pk_test_51234567890abcdef',
        permissions: ['orders:read', 'menu:read'],
        rate_limit_per_hour: 100,
        rate_limit_per_day: 1000,
        is_active: true,
        last_used_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        usage_count: 2340,
        created_at: '2024-02-01T00:00:00Z'
      }
    ];
  }

  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    await this.delay(500);

    return [
      {
        id: 1,
        plan_name: 'Starter',
        plan_code: 'starter',
        description: 'Perfect for small restaurants',
        price_monthly: 29.99,
        price_yearly: 299.99,
        features: {
          pos: true,
          basic_analytics: true,
          email_support: true,
          multi_location: false,
          advanced_analytics: false
        },
        limits: {
          api_calls: 1000,
          users: 5,
          locations: 1,
          storage_gb: 1
        },
        is_active: true,
        trial_days: 14
      },
      {
        id: 2,
        plan_name: 'Professional',
        plan_code: 'professional',
        description: 'For growing restaurant chains',
        price_monthly: 79.99,
        price_yearly: 799.99,
        features: {
          pos: true,
          basic_analytics: true,
          advanced_analytics: true,
          email_support: true,
          multi_location: true,
          priority_support: true
        },
        limits: {
          api_calls: 10000,
          users: 25,
          locations: 5,
          storage_gb: 10
        },
        is_active: true,
        trial_days: 14
      }
    ];
  }

  async getTenantSubscriptions(): Promise<TenantSubscription[]> {
    await this.delay(700);

    return [
      {
        id: 1,
        tenant_id: 1,
        tenant_name: 'Demo Restaurant',
        plan_name: 'Professional',
        status: 'active',
        billing_cycle: 'monthly',
        current_period_start: '2024-12-01',
        current_period_end: '2024-12-31',
        auto_renew: true,
        mrr: 79.99
      },
      {
        id: 2,
        tenant_id: 2,
        tenant_name: 'Pizza Palace',
        plan_name: 'Starter',
        status: 'trial',
        billing_cycle: 'monthly',
        current_period_start: '2024-12-01',
        current_period_end: '2024-12-15',
        trial_end: '2024-12-15',
        auto_renew: true,
        mrr: 0
      }
    ];
  }

  async getNotificationTemplates(): Promise<NotificationTemplate[]> {
    await this.delay(600);

    return [
      {
        id: 1,
        template_name: 'Welcome Email',
        template_type: 'email',
        category: 'onboarding',
        subject_template: 'Welcome to {{tenant_name}}!',
        body_template: 'Welcome {{user_name}} to {{tenant_name}}. Your account is now active.',
        variables: ['tenant_name', 'user_name'],
        is_active: true,
        usage_count: 245,
        created_at: '2024-01-15T00:00:00Z'
      },
      {
        id: 2,
        template_name: 'Invoice Due Reminder',
        template_type: 'email',
        category: 'billing',
        subject_template: 'Invoice {{invoice_number}} Due Soon',
        body_template: 'Your invoice {{invoice_number}} for {{amount}} is due on {{due_date}}.',
        variables: ['invoice_number', 'amount', 'due_date'],
        is_active: true,
        usage_count: 89,
        created_at: '2024-01-20T00:00:00Z'
      }
    ];
  }
}

export const mockApiService = new MockApiService();
