-- Phase 4: Advanced Payment & Hardware Integration Migration
-- Database: BARPOS, PostgreSQL
-- This migration adds comprehensive payment processing and hardware integration capabilities

-- =====================================================
-- ENHANCED PAYMENT METHODS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'moneris', 'cash', 'digital_wallet'
    is_active BOOLEAN DEFAULT true,
    processing_fee_percentage DECIMAL(5,4) DEFAULT 0,
    processing_fee_fixed DECIMAL(10,2) DEFAULT 0,
    requires_authorization BOOLEAN DEFAULT false,
    supports_tips BOOLEAN DEFAULT true,
    supports_split_payment BOOLEAN DEFAULT true,
    supports_refunds BOOLEAN DEFAULT true,
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_amount DECIMAL(10,2),
    configuration JSONB DEFAULT '{}',
    gateway_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, name)
);

-- =====================================================
-- ENHANCED PAYMENT TRANSACTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID,
    order_id UUID NOT NULL,
    payment_method_id UUID REFERENCES payment_methods(id),
    employee_id UUID,
    
    -- Financial details
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    processing_fee DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    
    -- Transaction details
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed, refunded, cancelled
    transaction_id VARCHAR(255) UNIQUE,
    authorization_code VARCHAR(100),
    gateway_transaction_id VARCHAR(255),
    gateway_response JSONB DEFAULT '{}',
    
    -- Customer and receipt info
    customer_info JSONB DEFAULT '{}',
    receipt_data JSONB DEFAULT '{}',
    receipt_printed BOOLEAN DEFAULT false,
    receipt_emailed BOOLEAN DEFAULT false,
    receipt_sms_sent BOOLEAN DEFAULT false,
    
    -- Performance tracking
    processing_started_at TIMESTAMP,
    processing_completed_at TIMESTAMP,
    processing_time_ms INTEGER,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SPLIT PAYMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS split_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_transaction_id UUID REFERENCES payment_transactions(id) ON DELETE CASCADE,
    payment_method_id UUID REFERENCES payment_methods(id),
    
    -- Split details
    split_sequence INTEGER NOT NULL, -- Order of this split in the transaction
    amount DECIMAL(10,2) NOT NULL,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    processing_fee DECIMAL(10,2) DEFAULT 0,
    
    -- Customer info for this split
    customer_name VARCHAR(100),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    
    -- Transaction details
    status VARCHAR(20) DEFAULT 'pending',
    transaction_id VARCHAR(255),
    authorization_code VARCHAR(100),
    gateway_response JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- HARDWARE DEVICES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS hardware_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID,
    
    -- Device identification
    device_type VARCHAR(50) NOT NULL, -- 'receipt_printer', 'barcode_scanner', 'cash_drawer', 'card_reader', 'kitchen_display'
    device_name VARCHAR(100) NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    firmware_version VARCHAR(50),
    
    -- Connection details
    connection_type VARCHAR(20) NOT NULL, -- 'usb', 'network', 'bluetooth', 'serial', 'wifi'
    connection_string VARCHAR(255),
    ip_address INET,
    port INTEGER,
    mac_address VARCHAR(17),
    
    -- Status and configuration
    is_active BOOLEAN DEFAULT true,
    is_connected BOOLEAN DEFAULT false,
    configuration JSONB DEFAULT '{}',
    capabilities JSONB DEFAULT '{}',
    driver_info JSONB DEFAULT '{}',
    
    -- Monitoring and maintenance
    last_connected TIMESTAMP,
    last_heartbeat TIMESTAMP,
    last_error TEXT,
    error_count INTEGER DEFAULT 0,
    total_jobs_processed INTEGER DEFAULT 0,
    uptime_percentage DECIMAL(5,2) DEFAULT 0,
    
    -- Maintenance schedule
    last_maintenance TIMESTAMP,
    next_maintenance TIMESTAMP,
    maintenance_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, device_name)
);

-- =====================================================
-- PAYMENT ANALYTICS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS payment_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id UUID,
    
    -- Time period
    date_recorded DATE NOT NULL,
    hour_recorded INTEGER CHECK (hour_recorded BETWEEN 0 AND 23),
    
    -- Payment method breakdown
    payment_method VARCHAR(50),
    payment_provider VARCHAR(50),
    
    -- Transaction metrics
    transaction_count INTEGER DEFAULT 0,
    successful_transactions INTEGER DEFAULT 0,
    failed_transactions INTEGER DEFAULT 0,
    refunded_transactions INTEGER DEFAULT 0,
    
    -- Financial metrics
    total_amount DECIMAL(12,2) DEFAULT 0,
    average_amount DECIMAL(10,2) DEFAULT 0,
    tip_total DECIMAL(10,2) DEFAULT 0,
    processing_fees DECIMAL(10,2) DEFAULT 0,
    net_revenue DECIMAL(12,2) DEFAULT 0,
    
    -- Performance metrics
    success_rate DECIMAL(5,4) DEFAULT 0, -- 0.0000 to 1.0000
    average_processing_time INTEGER DEFAULT 0, -- milliseconds
    min_processing_time INTEGER DEFAULT 0,
    max_processing_time INTEGER DEFAULT 0,
    
    -- Split payment metrics
    split_payment_count INTEGER DEFAULT 0,
    average_splits_per_transaction DECIMAL(4,2) DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, location_id, date_recorded, hour_recorded, payment_method)
);

-- =====================================================
-- HARDWARE DEVICE LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS hardware_device_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID REFERENCES hardware_devices(id) ON DELETE CASCADE,
    
    -- Log details
    log_level VARCHAR(10) NOT NULL, -- 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'
    event_type VARCHAR(50) NOT NULL, -- 'connection', 'print_job', 'scan', 'error', 'maintenance'
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    
    -- Context
    employee_id UUID,
    order_id UUID,
    transaction_id UUID,
    
    -- Performance data
    execution_time_ms INTEGER,
    memory_usage_mb INTEGER,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- RECEIPT TEMPLATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS receipt_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Template details
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL, -- 'customer', 'merchant', 'kitchen', 'gift_receipt'
    is_default BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Template content
    header_template TEXT,
    body_template TEXT,
    footer_template TEXT,
    css_styles TEXT,
    
    -- Print settings
    paper_width INTEGER DEFAULT 80, -- mm
    font_size INTEGER DEFAULT 12,
    line_spacing INTEGER DEFAULT 1,
    margins JSONB DEFAULT '{"top": 5, "bottom": 5, "left": 5, "right": 5}',
    
    -- Branding
    logo_url VARCHAR(500),
    business_info JSONB DEFAULT '{}',
    contact_info JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, template_name)
);

-- =====================================================
-- DIGITAL WALLET CONFIGURATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS digital_wallet_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    
    -- Wallet details
    wallet_type VARCHAR(30) NOT NULL, -- 'apple_pay', 'google_pay', 'samsung_pay', 'paypal'
    is_enabled BOOLEAN DEFAULT false,
    
    -- Configuration
    merchant_id VARCHAR(255),
    api_credentials JSONB DEFAULT '{}', -- Encrypted credentials
    webhook_url VARCHAR(500),
    webhook_secret VARCHAR(255),
    
    -- Settings
    supported_card_types TEXT[] DEFAULT ARRAY['visa', 'mastercard', 'amex'],
    min_amount DECIMAL(10,2) DEFAULT 0.01,
    max_amount DECIMAL(10,2) DEFAULT 10000.00,
    
    -- Status
    verification_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'verified', 'failed'
    last_verified TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, wallet_type)
);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default payment methods for existing tenants
INSERT INTO payment_methods (tenant_id, name, display_name, provider, processing_fee_percentage, processing_fee_fixed, supports_tips, supports_split_payment) 
SELECT 
    id as tenant_id,
    'cash' as name,
    'Cash' as display_name,
    'cash' as provider,
    0 as processing_fee_percentage,
    0 as processing_fee_fixed,
    true as supports_tips,
    true as supports_split_payment
FROM tenants
ON CONFLICT (tenant_id, name) DO NOTHING;

INSERT INTO payment_methods (tenant_id, name, display_name, provider, processing_fee_percentage, processing_fee_fixed, supports_tips, supports_split_payment) 
SELECT 
    id as tenant_id,
    'credit_card' as name,
    'Credit Card' as display_name,
    'stripe' as provider,
    0.029 as processing_fee_percentage,
    0.30 as processing_fee_fixed,
    true as supports_tips,
    true as supports_split_payment
FROM tenants
ON CONFLICT (tenant_id, name) DO NOTHING;

INSERT INTO payment_methods (tenant_id, name, display_name, provider, processing_fee_percentage, processing_fee_fixed, supports_tips, supports_split_payment) 
SELECT 
    id as tenant_id,
    'debit_card' as name,
    'Debit Card' as display_name,
    'moneris' as provider,
    0.015 as processing_fee_percentage,
    0.10 as processing_fee_fixed,
    true as supports_tips,
    true as supports_split_payment
FROM tenants
ON CONFLICT (tenant_id, name) DO NOTHING;

-- Insert default receipt templates
INSERT INTO receipt_templates (tenant_id, template_name, template_type, is_default, header_template, body_template, footer_template)
SELECT 
    id as tenant_id,
    'Default Customer Receipt' as template_name,
    'customer' as template_type,
    true as is_default,
    '{{business_name}}\n{{business_address}}\n{{business_phone}}\n\n' as header_template,
    'Receipt #{{receipt_number}}\nDate: {{date}}\nServer: {{server_name}}\n\n{{#items}}{{name}} x{{quantity}} - ${{total}}\n{{/items}}\n\nSubtotal: ${{subtotal}}\nTax: ${{tax}}\nTip: ${{tip}}\nTotal: ${{total}}\n\nPayment: {{payment_method}}\n' as body_template,
    '\nThank you for your visit!\nPlease come again!' as footer_template
FROM tenants
ON CONFLICT (tenant_id, template_name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payment_methods_tenant_active ON payment_methods(tenant_id, is_active);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_processing_time ON payment_transactions(processing_time_ms);
CREATE INDEX IF NOT EXISTS idx_hardware_devices_heartbeat ON hardware_devices(last_heartbeat);

-- Grant permissions to BARPOS user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "BARPOS";

-- Migration completed successfully
-- Phase 4: Advanced Payment & Hardware Integration database schema is now ready
