// Enhanced Payment Service for Phase 4
// Comprehensive payment processing with Stripe/Moneris integration and hardware support

const { Pool } = require('pg');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class EnhancedPaymentService {
  constructor() {
    this.supportedProviders = ['stripe', 'moneris', 'cash', 'digital_wallet'];
    this.processingTimeTarget = 3000; // 3 seconds target
  }

  // =====================================================
  // PAYMENT METHOD MANAGEMENT
  // =====================================================

  async getPaymentMethods(tenantId, locationId = null) {
    try {
      const client = await pool.connect();
      
      let query = `
        SELECT 
          pm.*,
          CASE 
            WHEN pm.provider = 'stripe' THEN 'Credit/Debit Card'
            WHEN pm.provider = 'moneris' THEN 'Canadian Debit'
            WHEN pm.provider = 'cash' THEN 'Cash Payment'
            WHEN pm.provider = 'digital_wallet' THEN 'Digital Wallet'
            ELSE pm.display_name
          END as provider_display
        FROM payment_methods pm
        WHERE pm.tenant_id = $1 AND pm.is_active = true
        ORDER BY 
          CASE pm.provider 
            WHEN 'cash' THEN 1
            WHEN 'stripe' THEN 2
            WHEN 'moneris' THEN 3
            WHEN 'digital_wallet' THEN 4
            ELSE 5
          END
      `;
      
      const result = await client.query(query, [tenantId]);
      client.release();
      
      return {
        success: true,
        methods: result.rows,
        count: result.rows.length
      };
    } catch (error) {
      console.error('❌ Error fetching payment methods:', error);
      return {
        success: false,
        error: error.message,
        methods: []
      };
    }
  }

  // =====================================================
  // PAYMENT PROCESSING
  // =====================================================

  async processPayment(paymentData) {
    const startTime = Date.now();
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Validate payment data
      const validation = await this.validatePaymentData(paymentData);
      if (!validation.valid) {
        throw new Error(`Payment validation failed: ${validation.error}`);
      }

      // Get payment method details
      const paymentMethod = await this.getPaymentMethodById(paymentData.payment_method_id, client);
      if (!paymentMethod) {
        throw new Error('Invalid payment method');
      }

      // Calculate processing fees
      const fees = this.calculateProcessingFees(paymentData.total_amount, paymentMethod);
      
      // Create payment transaction record
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const insertQuery = `
        INSERT INTO payment_transactions (
          tenant_id, location_id, order_id, payment_method_id, employee_id,
          subtotal, tax_amount, tip_amount, processing_fee, total_amount,
          status, transaction_id, customer_info, receipt_data,
          processing_started_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        RETURNING id
      `;
      
      const transactionResult = await client.query(insertQuery, [
        paymentData.tenant_id,
        paymentData.location_id,
        paymentData.order_id,
        paymentData.payment_method_id,
        paymentData.employee_id,
        paymentData.subtotal || paymentData.total_amount,
        paymentData.tax_amount || 0,
        paymentData.tip_amount || 0,
        fees.processing_fee,
        paymentData.total_amount,
        'processing',
        transactionId,
        JSON.stringify(paymentData.customer_info || {}),
        JSON.stringify(paymentData.receipt_data || {}),
        new Date()
      ]);
      
      const dbTransactionId = transactionResult.rows[0].id;

      // Process payment based on provider
      let paymentResult;
      switch (paymentMethod.provider) {
        case 'stripe':
          paymentResult = await this.processStripePayment(paymentData, paymentMethod);
          break;
        case 'moneris':
          paymentResult = await this.processMonerisPayment(paymentData, paymentMethod);
          break;
        case 'cash':
          paymentResult = await this.processCashPayment(paymentData, paymentMethod);
          break;
        case 'digital_wallet':
          paymentResult = await this.processDigitalWalletPayment(paymentData, paymentMethod);
          break;
        default:
          throw new Error(`Unsupported payment provider: ${paymentMethod.provider}`);
      }

      // Handle split payments if applicable
      if (paymentData.split_payments && paymentData.split_payments.length > 0) {
        paymentResult = await this.processSplitPayments(
          paymentData.split_payments, 
          dbTransactionId, 
          client
        );
      }

      // Update transaction with result
      const processingTime = Date.now() - startTime;
      const updateQuery = `
        UPDATE payment_transactions 
        SET 
          status = $1,
          authorization_code = $2,
          gateway_transaction_id = $3,
          gateway_response = $4,
          processing_completed_at = $5,
          processing_time_ms = $6
        WHERE id = $7
      `;
      
      await client.query(updateQuery, [
        paymentResult.success ? 'completed' : 'failed',
        paymentResult.authorization_code,
        paymentResult.gateway_transaction_id,
        JSON.stringify(paymentResult.gateway_response || {}),
        new Date(),
        processingTime,
        dbTransactionId
      ]);

      // Generate receipt if payment successful
      let receiptData = null;
      if (paymentResult.success) {
        receiptData = await this.generateReceipt(dbTransactionId, paymentData, client);
        
        // Update analytics
        await this.updatePaymentAnalytics(paymentData, paymentMethod, processingTime, true, client);
      } else {
        await this.updatePaymentAnalytics(paymentData, paymentMethod, processingTime, false, client);
      }

      await client.query('COMMIT');
      
      return {
        success: paymentResult.success,
        transaction_id: transactionId,
        db_transaction_id: dbTransactionId,
        authorization_code: paymentResult.authorization_code,
        processing_time: processingTime,
        processing_fee: fees.processing_fee,
        receipt_data: receiptData,
        gateway_response: paymentResult.gateway_response,
        error: paymentResult.error
      };

    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Payment processing error:', error);
      
      return {
        success: false,
        error: error.message,
        processing_time: Date.now() - startTime
      };
    } finally {
      client.release();
    }
  }

  // =====================================================
  // PAYMENT PROVIDER IMPLEMENTATIONS
  // =====================================================

  async processStripePayment(paymentData, paymentMethod) {
    try {
      // Mock Stripe integration - replace with actual Stripe API calls
      console.log('💳 Processing Stripe payment:', paymentData.total_amount);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
      
      // Mock successful response (95% success rate)
      const isSuccess = Math.random() > 0.05;
      
      if (isSuccess) {
        return {
          success: true,
          authorization_code: `AUTH_${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
          gateway_transaction_id: `pi_${Date.now()}`,
          gateway_response: {
            status: 'succeeded',
            amount: Math.round(paymentData.total_amount * 100),
            currency: 'cad',
            payment_method: 'card'
          }
        };
      } else {
        return {
          success: false,
          error: 'Card declined',
          gateway_response: {
            status: 'failed',
            decline_code: 'generic_decline'
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async processMonerisPayment(paymentData, paymentMethod) {
    try {
      // Mock Moneris integration - replace with actual Moneris API calls
      console.log('🇨🇦 Processing Moneris payment:', paymentData.total_amount);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 800 + 400));
      
      // Mock successful response (97% success rate for Canadian debit)
      const isSuccess = Math.random() > 0.03;
      
      if (isSuccess) {
        return {
          success: true,
          authorization_code: `MONERIS_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          gateway_transaction_id: `mon_${Date.now()}`,
          gateway_response: {
            response_code: '001',
            message: 'Transaction approved',
            amount: paymentData.total_amount,
            currency: 'CAD'
          }
        };
      } else {
        return {
          success: false,
          error: 'Transaction declined',
          gateway_response: {
            response_code: '051',
            message: 'Insufficient funds'
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async processCashPayment(paymentData, paymentMethod) {
    try {
      console.log('💵 Processing cash payment:', paymentData.total_amount);
      
      // Cash payments are always successful
      return {
        success: true,
        authorization_code: null,
        gateway_transaction_id: `cash_${Date.now()}`,
        gateway_response: {
          payment_type: 'cash',
          amount: paymentData.total_amount,
          received_at: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async processDigitalWalletPayment(paymentData, paymentMethod) {
    try {
      console.log('📱 Processing digital wallet payment:', paymentData.total_amount);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 600 + 300));
      
      // Mock successful response (98% success rate)
      const isSuccess = Math.random() > 0.02;
      
      if (isSuccess) {
        return {
          success: true,
          authorization_code: `WALLET_${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
          gateway_transaction_id: `wallet_${Date.now()}`,
          gateway_response: {
            wallet_type: paymentData.wallet_type || 'apple_pay',
            status: 'completed',
            amount: paymentData.total_amount
          }
        };
      } else {
        return {
          success: false,
          error: 'Digital wallet payment failed',
          gateway_response: {
            error_code: 'payment_failed'
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  calculateProcessingFees(amount, paymentMethod) {
    const percentageFee = amount * (paymentMethod.processing_fee_percentage || 0);
    const fixedFee = paymentMethod.processing_fee_fixed || 0;
    const totalFee = percentageFee + fixedFee;
    
    return {
      percentage_fee: percentageFee,
      fixed_fee: fixedFee,
      processing_fee: totalFee
    };
  }

  async validatePaymentData(paymentData) {
    // Basic validation
    if (!paymentData.total_amount || paymentData.total_amount <= 0) {
      return { valid: false, error: 'Invalid payment amount' };
    }
    
    if (!paymentData.payment_method_id) {
      return { valid: false, error: 'Payment method required' };
    }
    
    if (!paymentData.tenant_id) {
      return { valid: false, error: 'Tenant ID required' };
    }
    
    return { valid: true };
  }

  async getPaymentMethodById(paymentMethodId, client = null) {
    const conn = client || await pool.connect();
    
    try {
      const result = await conn.query(
        'SELECT * FROM payment_methods WHERE id = $1 AND is_active = true',
        [paymentMethodId]
      );
      
      return result.rows[0] || null;
    } finally {
      if (!client) conn.release();
    }
  }

  async generateReceipt(transactionId, paymentData, client) {
    // Receipt generation logic will be implemented in the next component
    return {
      receipt_number: `RCP_${Date.now()}`,
      generated_at: new Date().toISOString(),
      format: 'json',
      data: {
        transaction_id: transactionId,
        amount: paymentData.total_amount,
        payment_method: paymentData.payment_method_name
      }
    };
  }

  async updatePaymentAnalytics(paymentData, paymentMethod, processingTime, success, client) {
    const today = new Date();
    const currentHour = today.getHours();
    
    try {
      const upsertQuery = `
        INSERT INTO payment_analytics (
          tenant_id, location_id, date_recorded, hour_recorded,
          payment_method, payment_provider, transaction_count,
          successful_transactions, failed_transactions,
          total_amount, average_processing_time
        ) VALUES ($1, $2, $3, $4, $5, $6, 1, $7, $8, $9, $10)
        ON CONFLICT (tenant_id, location_id, date_recorded, hour_recorded, payment_method)
        DO UPDATE SET
          transaction_count = payment_analytics.transaction_count + 1,
          successful_transactions = payment_analytics.successful_transactions + $7,
          failed_transactions = payment_analytics.failed_transactions + $8,
          total_amount = payment_analytics.total_amount + $9,
          average_processing_time = (payment_analytics.average_processing_time + $10) / 2
      `;
      
      await client.query(upsertQuery, [
        paymentData.tenant_id,
        paymentData.location_id,
        today.toISOString().split('T')[0],
        currentHour,
        paymentMethod.name,
        paymentMethod.provider,
        success ? 1 : 0,
        success ? 0 : 1,
        paymentData.total_amount,
        processingTime
      ]);
    } catch (error) {
      console.error('❌ Error updating payment analytics:', error);
    }
  }
}

module.exports = EnhancedPaymentService;
